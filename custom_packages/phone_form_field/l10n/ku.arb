{"@@locale": "ku", "invalidPhoneNumber": "<PERSON><PERSON><PERSON> t<PERSON>lefonê nederbasdar e", "invalidCountry": "<PERSON><PERSON><PERSON>", "invalidMobilePhoneNumber": "<PERSON><PERSON><PERSON> t<PERSON>le<PERSON>na desta nederbasdar e", "invalidFixedLinePhoneNumber": "<PERSON><PERSON><PERSON> têlefonê ya xeta sabît nederbasdar e", "requiredPhoneNumber": "<PERSON><PERSON><PERSON> têlefonê ya pêwîst", "selectACountrySemanticLabel": "Welatek <PERSON>, bi<PERSON><PERSON>ha: {countryName} {dialCode}", "@selectACountrySemanticLabel": {"description": "semantic description of the country button", "placeholders": {"countryName": {"type": "String"}, "dialCode": {"type": "String"}}}, "phoneNumber": "Jimare telefon", "currentValueSemanticLabel": "<PERSON><PERSON><PERSON> niha: {currentValue}", "@currentValueSemanticLabel": {"description": "semantic description of the phone input. The label or hint will be dynamically added", "placeholders": {"currentValue": {"type": "String"}}}}