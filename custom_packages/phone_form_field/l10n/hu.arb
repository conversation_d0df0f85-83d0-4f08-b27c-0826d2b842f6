{"@@locale": "hu", "invalidPhoneNumber": "Érvénytelen telefonszám", "invalidCountry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "invalidMobilePhoneNumber": "Érvénytelen mobiltelefonszám", "invalidFixedLinePhoneNumber": "Érvénytelen vezetékes telefonszám", "requiredPhoneNumber": "Szükséges telefonszám", "selectACountrySemanticLabel": "<PERSON><PERSON><PERSON><PERSON>, jelenlegi választás: {countryName} {dialCode}", "@selectACountrySemanticLabel": {"description": "semantic description of the country button", "placeholders": {"countryName": {"type": "String"}, "dialCode": {"type": "String"}}}, "phoneNumber": "Telefonszám", "currentValueSemanticLabel": "<PERSON><PERSON><PERSON><PERSON> érték: {currentValue}", "@currentValueSemanticLabel": {"description": "semantic description of the phone input. The label or hint will be dynamically added", "placeholders": {"currentValue": {"type": "String"}}}}