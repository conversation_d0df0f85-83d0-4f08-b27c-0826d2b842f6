# URL Mapping Documentation

## Login URLs

### New Intuitive URLs (Recommended)
- **Engineers**: `/engineers/login`
- **Host Companies**: `/host-companies/login` 
- **Support Companies**: `/support-companies/login`

### Old URLs (Backward Compatible)
- **Engineers**: `/login?user_type=0`
- **Host Companies**: `/login?user_type=1`
- **Support Companies**: `/login?user_type=2`

## Implementation Details

### Router Configuration
- **Engineers login**: Added to `lib/routes/engineers_router.dart`
- **Host Companies login**: Added to `lib/routes/host_companies_router.dart`
- **Support Companies login**: Added to `lib/routes/host_support_agencies_router.dart`
- **Old routes**: Maintained in `lib/routes/general_router.dart` for backward compatibility
- **Automatic user type detection**: Based on URL path structure

### Helper Functions
- `getLoginUrlByUserType(int userType, {String? redirect})` - Get appropriate login URL
- `navigateToLogin(BuildContext context, int userType, {String? redirect})` - Navigate to login

### Redirect Support
Both old and new URLs support redirect parameter:
- New: `/engineers/login?redirect=/dashboard`
- Old: `/login?user_type=0&redirect=/dashboard`

### SNS Integration
SNS login redirects now use new intuitive URLs automatically based on user type.

## Benefits
1. **SEO Friendly**: Clear, descriptive URLs
2. **User Friendly**: Easy to remember and share
3. **Maintainable**: Clear separation by user type
4. **Backward Compatible**: Old URLs still work
