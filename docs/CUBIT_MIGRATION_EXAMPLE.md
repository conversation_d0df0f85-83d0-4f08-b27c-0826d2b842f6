# ChatMessagesBloc to ChatMessagesCubit Migration Guide

## Overview
Đã chuyển đổi ChatMessagesBloc từ Bloc pattern sang Cubit pattern để đơn giản hóa state management.

## Key Changes

### 1. **<PERSON> Pattern (Cũ)**
```dart
// Sử dụng events
class ChatMessagesBloc extends Bloc<ChatMessagesEvent, ChatMessagesState> {
  ChatMessagesBloc() : super(ChatMessagesState.empty()) {
    on<ChatMessagesEvent>((event, emit) {
      return emit.forEach<ChatMessagesState>(
        event.applyAsync(currentState: state, bloc: this),
        onData: (state) => state,
      );
    });
  }
}

// Gọi qua events
bloc.add(LoadChatMessagesEvent(groupId: groupId));
bloc.add(SendMessageEvent(message: message, groupId: groupId));
```

### 2. **Cubit Pattern (Mới)**
```dart
// Sử dụng methods trực tiếp
class ChatMessagesCubit extends Cubit<ChatMessagesState> {
  ChatMessagesCubit({required this.onFailedTranslate}) 
    : super(ChatMessagesState.empty());
}

// Gọi qua methods
cubit.loadChatMessages(groupId: groupId);
cubit.sendMessage(message: message, groupId: groupId);
```

## Migration Examples

### 1. **Load Messages**

**Trước (Bloc):**
```dart
_chatMessagesBloc.add(LoadChatMessagesEvent(
  groupId: widget.groupId,
  onSuccess: () => _markAllUnreadMessages(messages),
));
```

**Sau (Cubit):**
```dart
await _chatMessagesCubit.loadChatMessages(
  groupId: widget.groupId,
  onSuccess: () => _markAllUnreadMessages(messages),
);
```

### 2. **Send Message**

**Trước (Bloc):**
```dart
_chatMessagesBloc.add(SendMessageEvent(
  context: context,
  message: message,
  groupId: widget.groupId,
  onError: (error) => showError(error),
));
```

**Sau (Cubit):**
```dart
await _chatMessagesCubit.sendMessage(
  context: context,
  message: message,
  groupId: widget.groupId,
  onError: (error) => showError(error),
);
```

### 3. **Connect Socket**

**Trước (Bloc):**
```dart
_chatMessagesBloc.add(ConnectSocketEvent(
  groupId: widget.groupId,
  updateInterview: _updateInterview,
  onError: (error) => showError(error),
));
```

**Sau (Cubit):**
```dart
await _chatMessagesCubit.connectSocket(
  groupId: widget.groupId,
  updateInterview: _updateInterview,
  onError: (error) => showError(error),
);
```

### 4. **Translate Messages**

**Trước (Bloc):**
```dart
_chatMessagesBloc.add(TranslateMessagesEvent(
  messages: messages,
  groupId: widget.groupId,
  targetLanguage: targetLanguage,
));
```

**Sau (Cubit):**
```dart
await _chatMessagesCubit.translateMessages(
  messages: messages,
  groupId: widget.groupId,
  targetLanguage: targetLanguage,
);
```

## Widget Usage

### **BlocProvider Setup**
```dart
// Thay thế BlocProvider<ChatMessagesBloc>
BlocProvider<ChatMessagesCubit>(
  create: (context) => ChatMessagesCubit(
    onFailedTranslate: () => _showTranslateError(),
  ),
  child: ChatMessagesScreen(groupId: groupId),
)
```

### **BlocBuilder Usage**
```dart
// Không thay đổi - vẫn sử dụng BlocBuilder
BlocBuilder<ChatMessagesCubit, ChatMessagesState>(
  builder: (context, state) {
    if (state.isLoading) {
      return LoadingWidget();
    }
    return MessagesList(messages: state.messages.results);
  },
)
```

### **Access Cubit in Widget**
```dart
class _ChatMessagesScreenState extends State<ChatMessagesScreen> {
  late ChatMessagesCubit _chatMessagesCubit;

  @override
  void initState() {
    super.initState();
    _chatMessagesCubit = BlocProvider.of<ChatMessagesCubit>(context);
    
    // Load initial data
    _chatMessagesCubit.loadChatMessages(groupId: widget.groupId);
    _chatMessagesCubit.connectSocket(
      groupId: widget.groupId,
      updateInterview: _updateInterview,
      onError: _handleError,
    );
  }

  @override
  void dispose() {
    _chatMessagesCubit.closeSocket();
    super.dispose();
  }
}
```

## Benefits of Cubit

1. **Simpler**: Không cần events, gọi methods trực tiếp
2. **Less Boilerplate**: Ít code hơn, dễ đọc hơn
3. **Better Performance**: Không cần stream processing cho events
4. **Easier Testing**: Test methods trực tiếp thay vì events
5. **Type Safety**: Compile-time checking cho method parameters

## State Management Remains Same

- **ChatMessagesState**: Không thay đổi
- **BlocBuilder**: Vẫn hoạt động như cũ
- **BlocListener**: Vẫn hoạt động như cũ
- **State Properties**: messages, isLoading, errorMessage, etc.

## Complete Example

```dart
class ChatMessagesScreen extends StatefulWidget {
  final String groupId;
  const ChatMessagesScreen({required this.groupId});

  @override
  State<ChatMessagesScreen> createState() => _ChatMessagesScreenState();
}

class _ChatMessagesScreenState extends State<ChatMessagesScreen> {
  late ChatMessagesCubit _cubit;

  @override
  void initState() {
    super.initState();
    _cubit = BlocProvider.of<ChatMessagesCubit>(context);
    _initializeChat();
  }

  Future<void> _initializeChat() async {
    await _cubit.connectSocket(
      groupId: widget.groupId,
      updateInterview: _updateInterview,
      onError: _handleError,
    );
    
    await _cubit.loadChatMessages(
      groupId: widget.groupId,
      onSuccess: () => _markAllUnreadMessages(),
    );
  }

  Future<void> _sendMessage(String message) async {
    await _cubit.sendMessage(
      context: context,
      message: message,
      groupId: widget.groupId,
      onError: _handleError,
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ChatMessagesCubit, ChatMessagesState>(
      builder: (context, state) {
        return Scaffold(
          body: Column(
            children: [
              Expanded(
                child: MessagesList(messages: state.messages.results),
              ),
              ChatInput(onSend: _sendMessage),
            ],
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    _cubit.closeSocket();
    super.dispose();
  }
}
```
