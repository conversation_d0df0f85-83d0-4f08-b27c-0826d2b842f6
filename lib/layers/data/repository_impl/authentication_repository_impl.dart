import 'package:asiantech_link/layers/data/source/local/local_storage/auth_local_storage.dart';
import 'package:asiantech_link/layers/data/source/network/api/auth_api.dart';
import 'package:asiantech_link/layers/domain/repository/authentication_repository.dart';
import 'package:dio/dio.dart';
import 'package:openapi/openapi.dart';

class AuthenticationRepositoryImpl extends AuthenticationRepository {
  AuthenticationRepositoryImpl();

  final AuthApi _authenticationApi = AuthApi();
  final AuthLocalStorage _authLocalStorage = AuthLocalStorage();
  @override
  Future forgotPassword({required String email}) {
    // Todo: implement forgotPassword
    throw UnimplementedError();
  }

  @override
  Future<LoginSuccessResponseModel> login({
    required String email,
    required String password,
    String? captchaKey,
    String? captchaValue,
    int? userType,
  }) {
    return _authenticationApi.login(
      email: email,
      password: password,
      captchaKey: captcha<PERSON>ey,
      captchaValue: captchaValue,
      userType: userType,
    );
  }

  @override
  Future<void> logout({required String refreshToken}) {
    return _authenticationApi.logout(refreshToken: refreshToken);
  }

  @override
  Future<void> deleteAccount() {
    return _authenticationApi.deleteAccount();
  }

  @override
  Future<void> resendVerifyEmail(
      {required String email,
      required String captchaKey,
      required String captchaValue}) {
    return _authenticationApi.resendVerifyEmail(
        email: email, captchaKey: captchaKey, captchaValue: captchaValue);
  }

  @override
  Future register({required String email, required String password}) {
    // Todo: implement register
    throw UnimplementedError();
  }

  @override
  Future verifyEmail({required String token}) {
    return _authenticationApi.verifyEmail(token: token);
  }

  @override
  Future sendCode(
      {required String email,
      required String captchaKey,
      required String captchaValue,
      String? snsEmail}) {
    return _authenticationApi.sendCode(
        email: email,
        captchaKey: captchaKey,
        captchaValue: captchaValue,
        snsEmail: snsEmail);
  }

  @override
  Future<Response<CaptchaResponseModel>> captcha() {
    return _authenticationApi.captcha();
  }

  @override
  Future<Response<CheckCaptchaRequireInLoginResponseModel>>
      checkCaptchaRequiredInLogin(String email) async {
    return await _authenticationApi.checkCaptchaRequiredInLogin(email);
  }

  @override
  Future resetPassword({required String email}) {
    return _authenticationApi.resetPassword(email: email);
  }

  @override
  String? getTempAuth(String key) {
    return _authLocalStorage.getTempAuth(key);
  }

  @override
  Future<bool> removeTempAuth(String key) {
    return _authLocalStorage.removeTempAuth(key);
  }

  @override
  Future setTempAuth(String key, String value) async {
    return await _authLocalStorage.setTempAuth(key, value);
  }

  @override
  Future<ResponseModel> confirmLogin(
      String email, String password, String code) async {
    return await _authenticationApi.confirmLogin(email, password, code);
  }

  @override
  Future<Response<ResponseModel>> confirmWhatsApp(
      String phoneNumber, String code) async {
    return await _authenticationApi.confirmWhatsApp(phoneNumber, code);
  }

  @override
  Future<void> setAuthStorage(
      {required String refreshToken,
      required String accessToken,
      required String userType}) {
    return _authLocalStorage.setAuthStorage(
        refreshToken, accessToken, userType);
  }

  @override
  Future<void> resetPasswordConfirm({required String token}) {
    return _authenticationApi.resetPasswordConfirm(token: token);
  }

  @override
  Future<LoginWithSNSResponseData?> loginWithSNS(LoginWithSNS data) {
    return _authenticationApi.loginWithSNS(data);
  }
}
