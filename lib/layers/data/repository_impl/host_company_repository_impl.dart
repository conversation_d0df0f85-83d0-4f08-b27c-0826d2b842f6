import 'package:asiantech_link/layers/data/dto/paging_model_dto.dart';
import 'package:asiantech_link/layers/data/source/network/api/host_company_api.dart';
import 'package:asiantech_link/layers/domain/entity/company_model.dart';
import 'package:asiantech_link/layers/domain/repository/host_company_repository.dart';
import 'package:dio/dio.dart';
import 'package:openapi/openapi.dart';

class HostCompanyRepositoryImpl implements HostCompanyRepository {
  final HostCompanyApi _companyApi = HostCompanyApi();

  @override
  Future<void> subscribeSupportCompany({required String companyId}) {
    return _companyApi.subscribeSupportCompany(companyId: companyId);
  }

  @override
  Future<void> unSubscribeSupportCompany({required String companyId}) {
    return _companyApi.unSubscribeSupportCompany(companyId: companyId);
  }

  @override
  Future<List<CompanyModel>> getSupportCompany({String? email}) {
    return _companyApi.getSupportCompany(email: email);
  }

  @override
  Future<List<HostCompanyGetSupportCompany>> getSupportCompanySubscribed() {
    return _companyApi.getSupportCompanySubscribed();
  }

  @override
  Future<int?> requestInterview({required HostCompanyRequestInterview data}) {
    return _companyApi.requestInterview(data: data);
  }

  @override
  Future<void> updateApplyAdmission(
      {required HostCompanyUpdateInterviewAdmission data}) {
    return _companyApi.updateApplyAdmission(data: data);
  }

  @override
  Future<void> createRecruit({required CreateRecruitment createRecruitment}) {
    return _companyApi.createRecruit(createRecruitment: createRecruitment);
  }

  @override
  Future<void> deleteRecruit({required String id}) {
    return _companyApi.deleteRecruit(id: id);
  }

  @override
  Future<CompanyRecruitDetail?> getCompanyRecruitmentDetails(
      {required String id}) {
    return _companyApi.getCompanyRecruitmentDetails(id: id);
  }

  @override
  Future<PagingModelDto<RecruitUploadedSerializers>> getMyRecruits(
      {String? cursor,
      int? pageSize,
      required bool showOldPost,
      bool? showNewest,
      int? engineerId,
      int? displayFlag}) {
    return _companyApi.getMyRecruits(
      cursor: cursor,
      pageSize: pageSize,
      showOldPost: showOldPost,
      showNewest: showNewest,
      engineerId: engineerId,
      displayFlag: displayFlag,
    );
  }

  @override
  Future<void> updateCompanyRecruitmentDetails(
      {required String id, required UpdateRecruitment data}) {
    return _companyApi.updateCompanyRecruitmentDetails(
      id: id,
      data: data,
    );
  }

  @override
  Future<Response<RecruitmentDetailsResponseModel>> getRecruitDetails(
      {required String id}) {
    return _companyApi.getRecruitDetails(id: id);
  }
}
