import 'dart:typed_data';

import 'package:asiantech_link/layers/data/source/network/api/profile_api.dart';
import 'package:asiantech_link/layers/domain/repository/profile_repository.dart';
import 'package:dio/dio.dart';
import 'package:openapi/openapi.dart';

class ProfileRepositoryImpl extends ProfileRepository {
  final AppProfileApi _api = AppProfileApi();
  @override
  Future<Response<UserDetailsResponseModel>> getProfile({
    int? optionalEngineerId,
  }) async {
    return await _api.getProfile(
      optionalEngineerId: optionalEngineerId,
    );
  }

  @override
  Future<ResponseModel?> updatePassword(
      {required String oldPassword,
      required String newPassword,
      required String confirmPassword}) {
    return _api.updatePassword(
        oldPassword: oldPassword,
        newPassword: newPassword,
        confirmPassword: confirmPassword);
  }

  @override
  Future<UpdateUserResponseModel?> updateProfile(UpdateUser updateUser) async {
    final res = await _api.updateProfile(updateUser);
    return res.data;
  }

  @override
  Future<SelfAssessmentResponseModel?> getRemoteWorkSkills() async {
    final res = await _api.getRemoteWorkSkills();
    return res.data;
  }

  @override
  Future<ResponseModel?> saveRemoteWorkSkills(SelfAssessmentAnswer data) async {
    final res = await _api.saveRemoteWorkSkills(data);
    return res.data;
  }

  @override
  Future<SelfAssessmentResponseModel?> getGlobalResponsivenessSkills() async {
    final res = await _api.getGlobalResponsivenessSkills();
    return res.data;
  }

  @override
  Future<ResponseModel?> saveGlobalResponsivenessSkills(
      SelfAssessmentAnswer data) async {
    final res = await _api.saveGlobalResponsivenessSkills(data);
    return res.data;
  }

  @override
  Future<SelfAssessmentResponseModel?>
      getCommunicationSkillsSelfAssessment() async {
    final res = await _api.getCommunicationSkillsSelfAssessment();
    return res.data;
  }

  @override
  Future<ResponseModel?> saveCommunicationSkillsSelfAssessment(
      SelfAssessmentAnswer data) async {
    final res = await _api.saveCommunicationSkillsSelfAssessment(data);
    return res.data;
  }

  @override
  Future<SelfAssessmentResponseModel?>
      getReportingConsultationSkillsSelfEvaluation() async {
    final res = await _api.getReportingConsultationSkillsSelfEvaluation();
    return res.data;
  }

  @override
  Future<ResponseModel?> saveReportingConsultationSkillsSelfEvaluation(
      SelfAssessmentAnswer data) async {
    final res = await _api.saveReportingConsultationSkillsSelfEvaluation(data);
    return res.data;
  }

  @override
  Future<SelfAssessmentResponseModel?>
      getProjectManagementSkillsSelfEvaluation() async {
    final res = await _api.getProjectManagementSkillsSelfEvaluation();
    return res.data;
  }

  @override
  Future<ResponseModel?> saveProjectManagementSkillsSelfEvaluation(
      SelfAssessmentAnswer data) async {
    final res = await _api.saveProjectManagementSkillsSelfEvaluation(data);
    return res.data;
  }

  @override
  Future<void> saveCVUploaded({required SaveCVUploaded cvFile}) async {
    await _api.saveCVUploaded(cvFile: cvFile);
  }

  @override
  Future<UploadCVUploaded?> uploadCvPDF({
    required MultipartFile cvPdf,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final res = await _api.uploadCvPDF(
        cvPdf: cvPdf,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress);
    return res?.data;
  }

  @override
  Future<Uint8List?> downloadCv() async {
    final res = await _api.downloadCv();
    return res;
  }
}
