import 'package:dio/dio.dart';
import 'package:openapi/openapi.dart';

import '../../domain/repository/notify_repository.dart';
import '../source/network/api/notify_api.dart';

class NotifyRepositoryImpl extends NotifyRepository {
  final AppNotifyApi _api = AppNotifyApi();

  @override
  Future<Response<ListNotifyResponseModel>> getListNotify() async {
    return await _api.getListNotify();
  }

  @override
  Future<Response<CountUnreadNotifyResponseModel>>
      getCountUnreadNotify() async {
    return await _api.getCountUnreadNotify();
  }

  @override
  Future<Response<SetReadNotifyResponseModel>> setReadNotify(
      List<int> ids) async {
    return await _api.setReadNotify(ids);
  }

  @override
  Future<Response<SetReadNotifyResponseModel>> setReadAllNotify() async {
    return await _api.setReadAllNotify();
  }
}
