import 'package:asiantech_link/layers/data/source/network/api/register_api.dart';

import 'package:asiantech_link/layers/domain/entity/register_model.dart';

import '../../domain/repository/register_repository.dart';

/// Implementation of the register repository
class RegisterRepositoryImpl implements RegisterRepository {
  final RegisterApi _api = RegisterApi();

  @override
  Future<RegisterModel> register(String email, String password) async {
    RegisterModel data = await _api.register(email, password);
    return data;
  }
}
