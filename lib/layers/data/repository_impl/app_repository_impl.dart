import 'package:asiantech_link/layers/data/dto/country_currency_model_dto.dart';
import 'package:asiantech_link/layers/data/source/local/local_storage/app_local_storage.dart';
import 'package:asiantech_link/layers/domain/repository/app_repository.dart';

import '../dto/academic_level_model_dto.dart';
import '../dto/country_model_dto.dart';
import '../dto/currency_model_dto.dart';
import '../dto/employ_code_model_dto.dart';
import '../dto/job_code_model_dto.dart';
import '../dto/language_level_model_dto.dart';
import '../dto/language_model_dto.dart';
import '../dto/qualification_model_dto.dart';
import '../dto/skill_code_model_dto.dart';
import '../dto/state_model_dto.dart';
import '../dto/remote_code_model_dto.dart';

class AppRepositoryImpl extends AppRepository {
  final AppLocalStorage _localStorage = AppLocalStorage();
  @override
  String? getLanguage() {
    return _localStorage.getLanguage();
  }

  @override
  Future<bool> setLanguage(String value) async {
    return await _localStorage.setLanguage(value);
  }

  @override
  Future<LanguageModelDto> getListLanguage() async {
    return await _localStorage.getListLanguage();
  }

  @override
  Future<CountryModelDto> getListCountry() async {
    return await _localStorage.getListCountry();
  }

  @override
  Future<StateModelDto> getListState() async {
    return await _localStorage.getListState();
  }

  @override
  Future<AcademicLevelModelDto> getListAcademicLevel() async {
    return await _localStorage.getListAcademicLevel();
  }

  @override
  Future<CurrencyModelDto> getListCurrency() async {
    return await _localStorage.getListCurrency();
  }

  @override
  Future<EmployCodeModelDto> getEmployCode() async {
    return await _localStorage.getEmployCode();
  }

  @override
  Future<SkillCodeModelDto> getSkillCode() async {
    return await _localStorage.getSkillCode();
  }

  @override
  Future<QualificationModelDto> getQualification() async {
    return await _localStorage.getQualification();
  }

  @override
  Future<JobCodeModelDto> getJobCode() async {
    return await _localStorage.getJobCode();
  }

  @override
  Future<LanguageLevelModelDto> getLanguageLevel() async {
    return await _localStorage.getLanguageLevel();
  }

  @override
  Future<RemoteCodeModelDto> getRemoteCode() async {
    return await _localStorage.getRemoteCode();
  }

  @override
  Future<List<CountryCurrencyModelDto>> getCountryCurrency() async {
    return await _localStorage.getCountryCurrency();
  }
}
