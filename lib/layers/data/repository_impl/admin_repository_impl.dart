import 'dart:typed_data';

import 'package:asiantech_link/layers/data/dto/paging_model_dto.dart';
import 'package:asiantech_link/layers/data/source/network/api/app_admin_api.dart';
import 'package:asiantech_link/layers/domain/repository/admin_repository.dart';
import 'package:dio/dio.dart';
import 'package:openapi/openapi.dart';

class AdminRepositoryImpl extends AdminRepository {
  final AppAdminApi appAdminApi = AppAdminApi();
  @override
  Future<PagingModelDto<GetListEngineer>> getListEngineer({
    int? pageSize,
    String? cursor,
    String? ordering,
    DateTime? registerDateFrom,
    DateTime? registerDateTo,
    String? querySearch,
    String? createdUser,
    int? isDataPolicyAccept,
  }) {
    return appAdminApi.getListEngineer(
        pageSize: pageSize,
        cursor: cursor,
        ordering: ordering,
        registerDateFrom: registerDateFrom,
        registerDateTo: registerDateTo,
        querySearch: querySearch,
        createdUser: createdUser,
        isDataPolicyAccept: isDataPolicyAccept);
  }

  @override
  Future<List<String>> getListRegistrar() {
    return appAdminApi.getListRegistrar();
  }

  @override
  Future<void> deleteEngineers(DeleteEngineers data) {
    return appAdminApi.deleteEngineers(data);
  }

  @override
  Future<Response<GetEmailScheduleResponseModel>> getListOfEmailSchedules(
      int? pageSize,
      String? cursor,
      String? ordering,
      Date? dateFrom,
      Date? dateTo,
      String? querySearch,
      String? createdUser) {
    return appAdminApi.getListOfEmailSchedules(
        pageSize, cursor, ordering, dateFrom, dateTo, querySearch, createdUser);
  }

  @override
  Future<Response<EmailSchedule>> createEmailSchedule(
    int type,
    String subject,
    String body,
    String sendTime,
    int isValid,
    String? weekday,
    DateTime? sendDatetime,
    int? isRepeat,
    String? targetEmail,
  ) async {
    return appAdminApi.createEmailSchedule(
      type,
      subject,
      body,
      sendTime,
      isValid,
      weekday,
      sendDatetime,
      isRepeat,
      targetEmail,
    );
  }

  @override
  Future<Response<EmailSchedule>> updateEmailSchedule(
    String id,
    int type,
    String subject,
    String body,
    String sendTime,
    int isValid,
    String? weekday,
    DateTime? sendDatetime,
    int? isRepeat,
    String? targetEmail,
  ) async {
    return appAdminApi.updateEmailSchedule(
      id,
      type,
      subject,
      body,
      sendTime,
      isValid,
      weekday,
      sendDatetime,
      isRepeat,
      targetEmail,
    );
  }

  @override
  Future<Response<ResponseModel>> deleteEmailSchedule(String id) async {
    return appAdminApi.deleteEmailSchedule(id);
  }

  @override
  Future<Response<ResponseModel>> deleteListEmailSchedule(List<int> ids) async {
    return appAdminApi.deleteListEmailSchedule(ids);
  }

  @override
  Future<Response<GetEmailDetailResponseModel>> getEmailScheduleById(
      String id) async {
    return appAdminApi.getEmailScheduleById(id);
  }

  @override
  Future<void> updateEngineer(int engineerId, UpdateEngineer data) {
    return appAdminApi.updateEngineer(engineerId, data);
  }

  @override
  Future<Uint8List?> exportUserData(ExportUserDataBody data) {
    return appAdminApi.exportUserData(data);
  }
}
