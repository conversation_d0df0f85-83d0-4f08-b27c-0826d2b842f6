import 'package:asiantech_link/layers/data/dto/paging_model_dto.dart';
import 'package:asiantech_link/layers/data/source/network/api/support_company_api.dart';
import 'package:asiantech_link/layers/domain/repository/support_company_repository.dart';
import 'package:openapi/openapi.dart';

class SupportCompanyRepositoryImpl extends SupportCompanyRepository {
  final AppSupportCompanyApi _supportCompanyApi = AppSupportCompanyApi();

  @override
  Future<PagingModelDto<RecruitUploadedSerializers>>
      getListRecruitOfHostCompany(
          {required int hostCompanyId,
          int? pageSize,
          String? cursor,
          String? ordering}) {
    return _supportCompanyApi.getListRecruitOfHostCompany(
      hostCompanyId: hostCompanyId,
      pageSize: pageSize,
      cursor: cursor,
      ordering: ordering,
    );
  }

  @override
  Future<List<SPCompanyRegistered>> getListRegisteredCompany() {
    return _supportCompanyApi.getListRegisteredCompany();
  }

  @override
  Future<int?> supportRequestInterview({required SPRequestInterviewBody data}) {
    return _supportCompanyApi.supportRequestInterview(data: data);
  }

  @override
  Future<PagingModelDto<ManageHostCompany>> getListManageHostCompany({
    int? pageSize,
    String? cursor,
    String? sortBy,
    String? progressStatus,
    String? search,
  }) {
    return _supportCompanyApi.getListManageHostCompany(
      pageSize: pageSize,
      cursor: cursor,
      sortBy: sortBy,
      progressStatus: progressStatus,
      search: search,
    );
  }
}
