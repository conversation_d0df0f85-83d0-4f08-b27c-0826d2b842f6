import 'package:asiantech_link/layers/data/dto/paging_model_dto.dart';
import 'package:asiantech_link/layers/data/source/network/api/engineer_api.dart';
import 'package:asiantech_link/layers/domain/repository/engineer_repository.dart';
import 'package:built_collection/built_collection.dart';
import 'package:dio/dio.dart';
import 'package:openapi/openapi.dart';

class EngineerRepositoryImpl extends EngineerRepository {
  final AppEngineerApi _api = AppEngineerApi();

  @override
  Future<void> addAgencyCompany({required EngineerUpdateAgencyCompany data}) {
    return _api.addAgencyCompany(data: data);
  }

  @override
  Future<void> deleteAgencyCompany(
      {required EngineerRemoveAgencyCompany data}) {
    return _api.deleteAgencyCompany(data: data);
  }

  @override
  Future<PagingModelDto<EngineerBestCompany>> getListBestCompanies({
    int? pageSize,
    String? cursor,
  }) {
    return _api.getListBestCompanies(
      pageSize: pageSize,
      cursor: cursor,
    );
  }

  @override
  Future<Response<EngineerListAgencyCompanyResponseModel>>
      getListOfSelectAgencyCompanies({int? optionalEngineerId}) {
    return _api.getListOfSelectAgencyCompanies(
        optionalEngineerId: optionalEngineerId);
  }

  @override
  Future<Response<EngineerListAgencyCompanyResponseModel>>
      getListAgencyCompany() {
    return _api.getListAgencyCompany();
  }

  @override
  Future<Response<EngineerListApplyCompanyResponseModel>> getListApplyCompany({
    int? pageSize,
    String? cursor,
    String? ordering,
    int? applyStatusFilterCode,
  }) {
    return _api.getListApplyCompany(
      pageSize: pageSize,
      cursor: cursor,
      ordering: ordering,
      applyStatusFilterCode: applyStatusFilterCode,
    );
  }

  @override
  Future<Response<BoolResponseModel>> applyRecruit({
    required String recruitId,
    required String companyId,
  }) {
    return _api.applyRecruit(
      recruitId: recruitId,
      companyId: companyId,
    );
  }

  @override
  Future<void> cancelRecruit({required int id}) {
    return _api.cancelRecruit(id: id);
  }

  @override
  Future<Response<BoolResponseModel>> changeStatusInterested({
    required String recruitId,
  }) {
    return _api.changeStatusInterested(recruitId: recruitId);
  }

  @override
  Future<Response<RecruitContractDetailsResponseModel>> getContractDetails({
    required String recruitmentId,
  }) {
    return _api.getContractDetails(recruitmentId: recruitmentId);
  }

  @override
  Future<Response<RecruitCountFilterResponseModel>> countFilter({
    String? name,
    String? employCode,
    BuiltList<String>? places,
    String? jobCode,
    BuiltList<String>? skillCodes1,
    BuiltList<String>? skillCodes2,
    BuiltList<String>? skillCodes3,
    String? payrollCode,
    String? payrollPriceFrom,
    String? payrollPriceTo,
    BuiltList<String>? languageCodes,
    required bool showOldPost,
    int? companyId,
    BuiltList<String>? jobCodes,
    bool? waitingFlag,
    String? remoteCode,
  }) {
    return _api.countFilter(
      name: name,
      employCode: employCode,
      places: places,
      jobCode: jobCode,
      skillCodes1: skillCodes1,
      skillCodes2: skillCodes2,
      skillCodes3: skillCodes3,
      payrollCode: payrollCode,
      payrollPriceFrom: payrollPriceFrom,
      payrollPriceTo: payrollPriceTo,
      languageCodes: languageCodes,
      showOldPost: showOldPost,
      companyId: companyId,
      jobCodes: jobCodes,
      waitingFlag: waitingFlag,
      remoteCode: remoteCode,
    );
  }

  @override
  Future<Response<RecruitExploreResponseModel>> listRecruitsExplore({
    int? pageSize,
    String? ordering,
    String? name,
    String? employCode,
    BuiltList<String>? places,
    String? jobCode,
    BuiltList<String>? skillCodes1,
    BuiltList<String>? skillCodes2,
    BuiltList<String>? skillCodes3,
    String? payrollCode,
    String? payrollPriceFrom,
    String? payrollPriceTo,
    BuiltList<String>? languageCodes,
    required bool showOldPost,
    int? companyId,
    BuiltList<String>? jobCodes,
    bool? waitingFlag,
    String? cursor,
    String? remoteCode,
  }) {
    return _api.listRecruitsExplore(
      pageSize: pageSize,
      ordering: ordering,
      name: name,
      employCode: employCode,
      places: places,
      jobCode: jobCode,
      skillCodes1: skillCodes1,
      skillCodes2: skillCodes2,
      skillCodes3: skillCodes3,
      payrollCode: payrollCode,
      payrollPriceFrom: payrollPriceFrom,
      payrollPriceTo: payrollPriceTo,
      languageCodes: languageCodes,
      showOldPost: showOldPost,
      companyId: companyId,
      jobCodes: jobCodes,
      waitingFlag: waitingFlag,
      cursor: cursor,
      remoteCode: remoteCode,
    );
  }

  @override
  Future<List<FeaturedJob>> getListFeaturedJobs() {
    return _api.getListFeaturedJobs();
  }

  @override
  Future<Response<RecruitmentManagementDetailsResponseModel>>
      getRecruitmentManagementDetail({
    required String recruitmentId,
  }) {
    return _api.getRecruitmentManagementDetail(recruitmentId: recruitmentId);
  }

  @override
  Future<void> updateRecruitProgress({required String id}) {
    return _api.updateRecruitProgress(id: id);
  }

  @override
  Future<void> updateInterviewDatetime(
      {required int id, required DateTime dateTime}) {
    return _api.updateInterviewDatetime(id: id, dateTime: dateTime);
  }

  @override
  Future<void> updateDataPolicy({required EngineerUpdateDataPolicy data}) {
    return _api.updateDataPolicy(data: data);
  }

  @override
  Future<void> getUserAgencyCompany({required String userId}) {
    return _api.getUserAgencyCompany(userId: userId);
  }
}
