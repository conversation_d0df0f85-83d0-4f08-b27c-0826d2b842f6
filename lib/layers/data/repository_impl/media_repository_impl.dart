import 'package:asiantech_link/layers/domain/repository/media_repository.dart';
import 'package:dio/dio.dart';

import '../source/network/api/media_api.dart';

class MediaRepositoryImpl extends MediaRepository {
  MediaRepositoryImpl();

  final MediaApi _mediaApi = MediaApi();

  @override
  Future uploadImage({required MultipartFile image}) async {
    final response = await _mediaApi.uploadPassPort(image: image);
    return response.data;
  }

  @override
  Future deleteAvatar() async {
    final response = await _mediaApi.deleteAvatar();
    return response.data;
  }

  @override
  Future uploadPassportImage({required MultipartFile image}) async {
    final response = await _mediaApi.uploadPassportImage(image: image);
    return response.data;
  }

  @override
  Future deleteAvatarPassPort() async {
    final response = await _mediaApi.deleteAvatarPassport();
    return response.data;
  }

  @override
  Future<void> uploadCompanyLogo({required MultipartFile image}) {
    return _mediaApi.uploadCompanyLogo(image: image);
  }

  @override
  Future<void> uploadCompanyPrImage(
      {required MultipartFile image, required int index}) {
    return _mediaApi.uploadPrImageCompany(image: image, index: index);
  }

  @override
  Future<void> uploadContractImage(
      {required MultipartFile image, required String recruitId}) {
    return _mediaApi.uploadContractImage(image: image, recruitId: recruitId);
  }

  @override
  Future<String> uploadRecruitCover({required MultipartFile image}) {
    return _mediaApi.uploadRecruitCover(image: image);
  }

  @override
  Future<void> uploadSignatureApply(
      {required MultipartFile image, required String applyId}) {
    return _mediaApi.uploadSignatureApply(image: image, applyId: applyId);
  }

  @override
  Future<void> deleteAvatarEngineer({required int engineerId}) {
    return _mediaApi.deleteAvatarEngineer(engineerId: engineerId);
  }

  @override
  Future<void> uploadAvatarEngineer(
      {required MultipartFile image, required int engineerId}) {
    return _mediaApi.uploadAvatarEngineer(image: image, engineerId: engineerId);
  }
}
