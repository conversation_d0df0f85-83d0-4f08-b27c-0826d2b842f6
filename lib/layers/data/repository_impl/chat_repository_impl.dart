import 'package:asiantech_link/layers/data/dto/chat_dto.dart';
import 'package:asiantech_link/layers/data/source/network/api.dart';
import 'package:asiantech_link/layers/data/source/network/api/chat_app_api.dart';
import 'package:asiantech_link/layers/data/source/network/api_url.dart';
import 'package:asiantech_link/layers/domain/entity/chat_model.dart';
import 'package:asiantech_link/layers/domain/repository/chat_repository.dart';
import 'package:dio/dio.dart';

import '../../domain/entity/paging_model.dart';

class ChatRepositoryImpl extends ChatRepository {
  @override
  Future<List<ChatGroupModel>> chatGroups() async {
    final url = APIUrl.chatGroups;
    final response = await request(url, Method.get);
    return (response.data['data'] as List)
        .map((e) => ChatGroupModelDto.fromJson(e))
        .toList();
  }

  @override
  Future<GroupModel> createGroup(
      {required String name, required List<String> userIds}) async {
    final url = APIUrl.createGroup;
    final response = await request(url, Method.post, body: {
      "name": name,
      "user_ids": userIds,
    });
    return GroupModelDto.fromJson(response.data['data']);
  }

  @override
  Future<PagingModel<ChatModel>> getMessages(
      {required String groupId, String? cursor}) async {
    final url = APIUrl.messages.replaceAll("{group_id}", groupId);
    final response = await request(url, Method.get, params: {
      "page_size": 20,
      "cursor": cursor,
    });

    Uri? nextUri;
    if (response.data['data']['next'] != null) {
      nextUri = Uri.tryParse(response.data['data']['next']);
    } else {
      nextUri = null;
    }

    String? cursorNext = nextUri?.queryParameters['cursor'];
    List<ChatModel> list = (response.data['data']['results'] as List)
        .map((e) => ChatModelDto.fromJson(e))
        .toList();
    return PagingModel<ChatModel>(
        next: cursorNext,
        previous: response.data['data']['previous'],
        results: list);
  }

  @override
  Future<Response<dynamic>> updateLastMessage({required String groupId}) async {
    final url = APIUrl.updateLastMessage.replaceAll("{group_id}", groupId);
    final response = await request(url, Method.post);
    return response;
  }

  @override
  Future<ChatGroupModel> groupDetails({required String groupId}) async {
    final url = APIUrl.groupDetails.replaceAll("{group_id}", groupId);
    final response = await request(url, Method.get);
    return ChatGroupModelDto.fromJson(response.data['data']);
  }

  @override
  Future<void> leaveGroup({required String groupId}) async {
    final url = APIUrl.leaveGroup;
    await request(url, Method.post, body: {"group_id": groupId});
  }

  @override
  Future<void> sendMessage(
      {required String groupId, required String text}) async {
    final url = APIUrl.sendMessage;
    await request(url, Method.post, body: {"group_id": groupId, "text": text});
  }

  @override
  Future<void> markReadAllMessages({required String groupId}) async {
    final chatAppApi = ChatAppApi();
    await chatAppApi.markReadAllMessages(groupId: groupId);
  }
}
