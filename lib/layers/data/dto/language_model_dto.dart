import '../../domain/entity/language_model.dart';

class LanguageModelDto extends LanguageModel {
  LanguageModelDto({required super.languageCode});

  factory LanguageModelDto.fromJson(Map<String, dynamic> json) {
    return LanguageModelDto(
      languageCode: (json["language_code"] as List)
          .map((e) => LanguageCodeDto.fromJson(e))
          .toList(),
    );
  }
  Map<String, dynamic> toJson() => {
        "language_code": languageCode
            .map((languageCode) => LanguageCodeDto(
                  selectionListName: languageCode.selectionListName,
                  code: languageCode.code,
                  nameJp: languageCode.nameJp,
                  nameEn: languageCode.nameEn,
                  nameVn: languageCode.nameVn,
                  isChoose: languageCode.isChoose,
                ).toJson())
            .toList(),
      };
}

class LanguageCodeDto extends LanguageCode {
  LanguageCodeDto({
    required super.selectionListName,
    required super.code,
    required super.nameJp,
    required super.nameEn,
    required super.nameVn,
    required super.isChoose,
  });

  factory LanguageCodeDto.fromJson(Map<String, dynamic> json) {
    return LanguageCodeDto(
      selectionListName: json["selection_list_name"] ?? "",
      code: json["code"] ?? "",
      nameJp: json["name_jp"] ?? "",
      nameEn: json["name_en"] ?? "",
      nameVn: json["name_vn"] ?? "",
      isChoose: json["is_choose"] ?? false,
    );
  }

  Map<String, dynamic> toJson() => {
        "selection_list_name": selectionListName,
        "code": code,
        "name_jp": nameJp,
        "name_en": nameEn,
        "is_choose": isChoose,
      };
}
