import 'package:asiantech_link/layers/domain/entity/country_currency_model.dart';

class CountryCurrencyModelDto extends CountryCurrencyModel {
  const CountryCurrencyModelDto(
      {super.countryCode, super.currencyCode, super.countryName});
  factory CountryCurrencyModelDto.fromJson(Map<String, dynamic> json) {
    return CountryCurrencyModelDto(
      countryCode: json["country_code"] ?? "",
      currencyCode: json["currency_code"] ?? "",
      countryName: json["country_name"] ?? "",
    );
  }
  @override
  List<Object?> get props => [countryCode, currencyCode, countryName];
}
