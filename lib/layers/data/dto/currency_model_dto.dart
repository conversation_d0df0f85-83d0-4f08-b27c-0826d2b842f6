import 'package:asiantech_link/layers/domain/entity/currency_model.dart';

class CurrencyModelDto extends CurrencyModel {
  CurrencyModelDto({required super.currency});
  CurrencyModelDto.fromJson(Map<String, dynamic> json) {
    if (json['currency'] != null) {
      currency = <Currency>[];
      json['currency'].forEach((v) {
        currency!.add(CurrencyDto.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (currency != null) {
      data['currency'] = currency!
          .map((v) => CurrencyDto(
                code: v.code,
                nameJp: v.nameJp,
                nameEn: v.nameEn,
                nameVn: v.nameVn,
              ).toJson())
          .toList();
    }
    return data;
  }
}

class CurrencyDto extends Currency {
  CurrencyDto({
    required super.code,
    required super.nameJp,
    required super.nameEn,
    required super.nameVn,
  });
  CurrencyDto.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    nameJp = json['name_jp'];
    nameEn = json['name_en'];
    nameVn = json['name_vn'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['name_jp'] = nameJp;
    data['name_en'] = nameEn;
    data['name_vn'] = nameVn;
    return data;
  }
}
