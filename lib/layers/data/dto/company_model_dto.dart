import 'package:asiantech_link/layers/domain/entity/company_model.dart';

class CompanyModelDto extends CompanyModel {
  const CompanyModelDto({
    super.companyId,
    super.userType,
    super.name,
    super.aboutUs,
    super.businessDetails,
    super.employeesType,
    super.countryCode,
    super.addressCode,
    super.address,
    super.tel,
    super.logoImagePath,
    super.prImagePath1,
    super.prImagePath2,
    super.prImagePath3,
    super.contactMail,
    super.webUrl,
    super.introductionUrl,
    super.memo,
    super.openFlag,
    super.deleted,
    super.created,
    super.updated,
    super.benefits,
    super.capitalStock,
    super.capitalStockCurrCode,
    super.internationalTel,
    super.agentFee,
    super.agentFeeCurrCode,
    super.acceptingFee,
    super.acceptingFeeCurrCode,
    super.supportOutsourcingFee,
    super.supportOutsourcingFeeCurrCode,
    super.support,
    super.status,
    super.introductionPR,
  });

  factory CompanyModelDto.fromMap(Map<String, dynamic> json) {
    return CompanyModelDto(
        companyId: json['company_id'],
        userType: json['user_type'],
        name: json['name'],
        aboutUs: json['about_us'],
        businessDetails: json['business_details'],
        employeesType: json['employees_type'],
        countryCode: json['country_code'],
        addressCode: json['address_code'],
        address: json['address'],
        tel: json['tel'],
        logoImagePath: json['logo_image_path'],
        prImagePath1: json['pr_image_path1'],
        prImagePath2: json['pr_image_path2'],
        prImagePath3: json['pr_image_path3'],
        contactMail: json['contact_mail'],
        webUrl: json['web_url'],
        introductionUrl: json['introduction_url'],
        memo: json['memo'],
        openFlag: json['open_flag'],
        deleted: json['deleted'],
        created: json['created'],
        updated: json['updated'],
        benefits: json['benefits'],
        capitalStock: json['capital_stock'],
        capitalStockCurrCode: json['capital_stock_curr_code'],
        internationalTel: json['international_tel'],
        agentFee: double.tryParse("${json['agent_fee']}"),
        agentFeeCurrCode: json['agent_fee_curr_code'],
        acceptingFee: json['accepting_fee'],
        acceptingFeeCurrCode: json['accepting_fee_curr_code'],
        supportOutsourcingFee:
            double.tryParse("${json['support_outsourcing_fee']}"),
        supportOutsourcingFeeCurrCode:
            json['support_outsourcing_fee_curr_code'],
        support: json['support'],
        status: json['status'],
        introductionPR: json['introduction_pr']);
  }

  Map<String, dynamic> toMap() {
    return {
      'company_id': companyId,
      'user_type': userType,
      'name': name,
      'about_us': aboutUs,
      'business_details': businessDetails,
      'employees_type': employeesType,
      'country_code': countryCode,
      'address_code': addressCode,
      'address': address,
      'tel': tel,
      'logo_image_path': logoImagePath,
      'pr_image_path1': prImagePath1,
      'pr_image_path2': prImagePath2,
      'pr_image_path3': prImagePath3,
      'contact_mail': contactMail,
      'web_url': webUrl,
      'introduction_url': introductionUrl,
      'memo': memo,
      'open_flag': openFlag,
      'deleted': deleted,
      'created': created,
      'updated': updated,
      'benefits': benefits,
      'capital_stock': capitalStock,
      'capital_stock_curr_code': capitalStockCurrCode,
      'international_tel': internationalTel,
      'agent_fee': agentFee,
      'agent_fee_curr_code': agentFeeCurrCode,
      'accepting_fee': acceptingFee,
      'accepting_fee_curr_code': acceptingFeeCurrCode,
      'support_outsourcing_fee': supportOutsourcingFee,
      'support_outsourcing_fee_curr_code': supportOutsourcingFeeCurrCode,
      'support': support,
      'status': status,
      "introduction_pr": introductionPR,
    };
  }
}
