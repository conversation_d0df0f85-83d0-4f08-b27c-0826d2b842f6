import 'package:asiantech_link/layers/domain/entity/academic_level_model.dart';

class AcademicLevelModelDto extends AcademicLevelModel {
  AcademicLevelModelDto({required super.academicLevel});
  factory AcademicLevelModelDto.fromJson(Map<String, dynamic> json) {
    return AcademicLevelModelDto(
      academicLevel: json["academic_level"] == null
          ? []
          : List<AcademicLevelDto>.from(
              json["academic_level"]!.map((x) => AcademicLevelDto.fromJson(x))),
    );
  }

  Map<String, dynamic> toJson() => {
        "academic_level": academicLevel
            .map((x) => AcademicLevelDto(
                selectionListName: x.selectionListName,
                id: x.id,
                nameJp: x.nameJp,
                nameVn: x.nameVn,
                nameEn: x.nameEn))
            .toList(),
      };
}

class AcademicLevelDto extends AcademicLevel {
  AcademicLevelDto(
      {required super.selectionListName,
      required super.id,
      required super.nameJp,
      required super.nameVn,
      required super.nameEn});

  factory AcademicLevelDto.fromJson(Map<String, dynamic> json) {
    return AcademicLevelDto(
      selectionListName: json["selection_list_name"] ?? "",
      id: json["id"] ?? 0,
      nameJp: json["name_jp"] ?? "",
      nameEn: json["name_en"] ?? "",
      nameVn: json["name_vn"] ?? "",
    );
  }

  Map<String, dynamic> toJson() => {
        "selection_list_name": selectionListName,
        "id": id,
        "name_jp": nameJp,
        "name_en": nameEn,
      };
}
