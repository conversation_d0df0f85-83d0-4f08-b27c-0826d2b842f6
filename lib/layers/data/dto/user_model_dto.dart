import 'package:asiantech_link/layers/domain/entity/user_model.dart';

class UserModelDto extends UserModel {
  const UserModelDto(
      {required super.userId,
      required super.profileImagePath,
      required super.firstName,
      required super.lastName,
      required super.age,
      required super.languages,
      required super.skills,
      required super.experiences,
      required super.requirements,
      required super.lastAcademicCode,
      required super.totalExperience,
      required super.interestedFlag});

  factory UserModelDto.fromJson(Map<String, dynamic> json) {
    return UserModelDto(
      userId: json['user_id'],
      profileImagePath: json['profile_image_path'],
      firstName: json['first_name'],
      lastName: json['last_name'],
      age: json['age'],
      languages: (json['languages'] as List? ?? [])
          .map((e) => LanguageDto.fromJson(e))
          .toList(),
      skills: (json['skills'] as List? ?? [])
          .map((e) => SkillDto.fromJson(e))
          .toList(),
      experiences: (json['experiences'] as List? ?? [])
          .map((e) => ExperienceDto.fromJson(e))
          .toList(),
      requirements: json['requirements'] != null
          ? RequirementDto.fromJson(json['requirements'])
          : null,
      lastAcademicCode: json['last_academic_code'],
      totalExperience: (json['total_experience'] as List? ?? [])
          .map((e) => TotalExperienceDto.fromJson(e))
          .toList(),
      interestedFlag: json['interested_flag'],
    );
  }
  UserModelDto copyWith(
      {final int? userId,
      final String? profileImagePath,
      final String? firstName,
      final String? lastName,
      final int? age,
      final List<LanguageDto>? languages,
      final List<SkillDto>? skills,
      final List<ExperienceDto>? experiences,
      final RequirementDto? requirements,
      final String? lastAcademicCode,
      final List<TotalExperienceDto>? totalExperience,
      final int? interestedFlag}) {
    return UserModelDto(
      userId: userId ?? this.userId,
      profileImagePath: profileImagePath ?? this.profileImagePath,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      age: age ?? this.age,
      languages: languages ?? this.languages,
      skills: skills ?? this.skills,
      experiences: experiences ?? this.experiences,
      requirements: requirements ?? this.requirements,
      lastAcademicCode: lastAcademicCode ?? this.lastAcademicCode,
      totalExperience: totalExperience ?? this.totalExperience,
      interestedFlag: interestedFlag ?? this.interestedFlag,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'profile_image_path': profileImagePath,
      'first_name': firstName,
      'last_name': lastName,
      'age': age,
      'languages': languages?.map((e) => e.toJson()).toList(),
      'skills': skills?.map((e) => e.toJson()).toList(),
      'experiences': experiences?.map((e) => e.toJson()).toList(),
      'requirements': requirements?.toJson(),
      'last_academic_code': lastAcademicCode,
      'total_experience': totalExperience.map((e) => e.toJson()).toList(),
      'interested_flag': interestedFlag,
    };
  }
}

class EducationDto {
  final int? engineerId;
  final String? school;
  final int? type;
  final DateTime? outDate;
  final String? faculty;

  EducationDto({
    this.engineerId,
    this.school,
    this.type,
    this.outDate,
    this.faculty,
  });

  factory EducationDto.fromJson(Map<String, dynamic> json) {
    return EducationDto(
      engineerId: json['engineer_id'] as int?,
      school: json['school'] as String?,
      type: json['type'] as int?,
      outDate:
          json['out_date'] != null ? DateTime.parse(json['out_date']) : null,
      faculty: json['faculty'] as String?,
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'engineer_id': engineerId,
      'school': school,
      'type': type,
      'out_date': outDate?.toIso8601String(),
      'faculty': faculty,
    };
  }
}

class LanguageDto {
  final int? engineerId;
  final int? languageLevelType;
  final int? languageId;
  final String? languageCode;

  LanguageDto({
    this.engineerId,
    this.languageLevelType,
    this.languageId,
    this.languageCode,
  });

  factory LanguageDto.fromJson(Map<String, dynamic> json) {
    return LanguageDto(
      engineerId: json['engineer_id'],
      languageLevelType: json['language_level_type'],
      languageId: json['language_id'],
      languageCode: json['language_code'],
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'engineer_id': engineerId,
      'language_level_type': languageLevelType,
      'language_id': languageId,
      'language_code': languageCode,
    };
  }
}

class QualificationDto {
  final int? engineerId;
  final int? licenceId;
  final DateTime? getDate;

  QualificationDto({
    this.engineerId,
    this.licenceId,
    this.getDate,
  });

  factory QualificationDto.fromJson(Map<String, dynamic> json) {
    return QualificationDto(
      engineerId: json['engineer_id'] as int?,
      licenceId: json['licence_id'] as int?,
      getDate:
          json['get_date'] != null ? DateTime.parse(json['get_date']) : null,
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'engineer_id': engineerId,
      'licence_id': licenceId,
      'get_date': getDate?.toIso8601String(),
    };
  }
}

class SkillDto {
  final int? engineerId;
  final String? skillCode;
  final int? levelType;
  final int? skillId;

  SkillDto({
    this.engineerId,
    this.skillCode,
    this.levelType,
    this.skillId,
  });

  factory SkillDto.fromJson(Map<String, dynamic> json) {
    return SkillDto(
      engineerId: json['engineer_id'] as int?,
      skillCode: json['skill_code'] as String?,
      levelType: json['level_type'] as int?,
      skillId: json['skill_id'] as int?,
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'engineer_id': engineerId,
      'skill_code': skillCode,
      'level_type': levelType,
      'skill_id': skillId,
    };
  }
}

class ExperienceDto {
  final int? engineerId;
  final String? companyName;
  final String? companyDepartment;
  final DateTime? enteringDate;
  final DateTime? quittingDate;

  ExperienceDto({
    this.engineerId,
    this.companyName,
    this.companyDepartment,
    this.enteringDate,
    this.quittingDate,
  });

  factory ExperienceDto.fromJson(Map<String, dynamic> json) {
    return ExperienceDto(
      engineerId: json['engineer_id'] as int?,
      companyName: json['company_name'] as String?,
      companyDepartment: json['company_department'] as String?,
      enteringDate: json['entering_date'] != null
          ? DateTime.parse(json['entering_date'])
          : null,
      quittingDate: json['quitting_date'] != null
          ? DateTime.parse(json['quitting_date'])
          : null,
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'engineer_id': engineerId,
      'company_name': companyName,
      'company_department': companyDepartment,
      'entering_date': enteringDate?.toIso8601String(),
      'quitting_date': quittingDate?.toIso8601String(),
    };
  }
}

class RequirementDto {
  final int? engineerId;
  final String? jobCode1;
  final String? jobCode2;
  final String? jobCode3;
  final String? employCode;
  final String? placeCode1;
  final String? placeCode2;
  final String? placeCode3;
  final double? payrollPrice;
  final String? payrollCode;

  RequirementDto({
    this.engineerId,
    this.jobCode1,
    this.jobCode2,
    this.jobCode3,
    this.employCode,
    this.placeCode1,
    this.placeCode2,
    this.placeCode3,
    this.payrollPrice,
    this.payrollCode,
  });

  factory RequirementDto.fromJson(Map<String, dynamic> json) {
    return RequirementDto(
        engineerId: json['engineer_id'] as int?,
        jobCode1: json['job_code1'] as String?,
        jobCode2: json['job_code2'] as String?,
        jobCode3: json['job_code3'] as String?,
        employCode: json['employ_code'] as String?,
        placeCode1: json['place_code1'] as String?,
        placeCode2: json['place_code2'] as String?,
        placeCode3: json['place_code3'] as String?,
        payrollPrice: json['payroll_price'],
        payrollCode: json['payroll_code']);
  }
  Map<String, dynamic> toJson() {
    return {
      'engineer_id': engineerId,
      'job_code1': jobCode1,
      'job_code2': jobCode2,
      'job_code3': jobCode3,
      'employ_code': employCode,
      'place_code1': placeCode1,
      'place_code2': placeCode2,
      'place_code3': placeCode3,
      'payroll_price': payrollPrice,
      'payroll_code': payrollCode,
    };
  }
}

class TotalExperienceDto {
  final double? year;
  final String? jobCode;
  TotalExperienceDto({this.year, this.jobCode});

  factory TotalExperienceDto.fromJson(Map<String, dynamic> json) {
    return TotalExperienceDto(
      year: json['year'],
      jobCode: json['job_code'],
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'year': year,
      'job_code': jobCode,
    };
  }
}
