import '../../domain/entity/language_level_model.dart';

class LanguageLevelModelDto extends LanguageLevelModel {
  LanguageLevelModelDto({required super.languageLevel});

  factory LanguageLevelModelDto.fromJson(Map<String, dynamic> json) {
    return LanguageLevelModelDto(
      languageLevel: (json["language_level"] as List)
          .map((e) => LanguageLevelDto.fromJson(e))
          .toList(),
    );
  }
  Map<String, dynamic> toJson() => {
        "language_level": languageLevel
            .map((e) => LanguageLevelDto(
                  id: e.id,
                  levelEn: e.levelEn,
                  levelVn: e.levelVn,
                  levelJp: e.levelJp,
                ).toJson())
            .toList(),
      };
}

class LanguageLevelDto extends LanguageLevel {
  LanguageLevelDto({
    required super.id,
    required super.levelEn,
    required super.levelVn,
    required super.levelJp,
  });

  factory LanguageLevelDto.fromJson(Map<String, dynamic> json) {
    return LanguageLevelDto(
      id: json["id"] ?? "",
      levelEn: json["level_en"] ?? "",
      levelVn: json["level_vn"] ?? "",
      levelJp: json["level_jp"] ?? "",
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "level_en": levelEn,
        "level_vn": levelVn,
        "level_jp": levelJp,
      };
}
