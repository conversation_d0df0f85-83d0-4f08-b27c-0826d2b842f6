import 'package:asiantech_link/layers/domain/entity/paging_model.dart';

class PagingModelDto<T> extends PagingModel {
  const PagingModelDto({
    super.next,
    super.previous,
    super.results,
    super.totalCount,
    super.metaData,
  });

  factory PagingModelDto.fromJson(Map<String, dynamic> response,
      T Function(Map<String, dynamic> data) json) {
    return PagingModelDto(
        results:
            (response['results'] as List? ?? []).map((e) => json(e)).toList(),
        next: response['next'],
        previous: response['previous'],
        totalCount: response['totalCount'],
        metaData: response['metaData']);
  }
  String? get cursor =>
      next != null ? Uri.parse(next!).queryParameters['cursor'] : null;

  PagingModelDto<T> addList({required PagingModelDto<T> model}) {
    return PagingModelDto(
      next: model.next,
      previous: model.previous,
      results: (results ?? [] as List<T>) + (model.results ?? [] as List<T>),
      metaData: metaData,
    );
  }

  PagingModelDto<T> copyWith({List<T>? results}) {
    return PagingModelDto(
        next: next,
        previous: previous,
        results: results ?? this.results,
        totalCount: totalCount,
        metaData: metaData);
  }

  factory PagingModelDto.empty() {
    return PagingModelDto<T>(
      results: List.empty(),
      next: null,
      previous: null,
      totalCount: null,
      metaData: null,
    );
  }
}
