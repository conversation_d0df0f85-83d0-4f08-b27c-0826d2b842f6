import 'package:asiantech_link/layers/domain/entity/qualification_model.dart';

class QualificationModelDto extends QualificationModel {
  QualificationModelDto({required super.qualification});
  factory QualificationModelDto.fromJson(Map<String, dynamic> json) {
    return QualificationModelDto(
      qualification: json["qualification"] == null
          ? []
          : List<QualificationDto>.from(
              json["qualification"]!.map((x) => QualificationDto.fromJson(x))),
    );
  }

  Map<String, dynamic> toJson() => {
        "academic_level": qualification
            ?.map((x) => QualificationDto(
                  id: x.id,
                  name: x.name,
                ))
            .toList(),
      };
}

class QualificationDto extends Qualification {
  QualificationDto({
    required super.id,
    required super.name,
  });

  factory QualificationDto.fromJson(Map<String, dynamic> json) {
    return QualificationDto(
      id: json["id"] ?? "",
      name: json["name"] ?? "",
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
      };
}
