import '../../domain/entity/remote_code_model.dart';

class RemoteCodeModelDto extends RemoteCodeModel {
  RemoteCodeModelDto({required super.remoteCodes});

  factory RemoteCodeModelDto.fromJson(Map<String, dynamic> json) {
    return RemoteCodeModelDto(
      remoteCodes: (json["RemoteCode"] as List)
          .map((e) => RemoteCodeDto.fromJson(e))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() => {
        "RemoteCode": remoteCodes
            .map((e) => RemoteCodeDto(
                  id: e.id,
                  nameEn: e.nameEn,
                  nameVi: e.nameVi,
                  nameJp: e.nameJp,
                ).toJson())
            .toList(),
      };
}

class RemoteCodeDto extends RemoteCode {
  RemoteCodeDto({
    required super.id,
    required super.nameEn,
    required super.nameVi,
    required super.nameJp,
  });

  factory RemoteCodeDto.fromJson(Map<String, dynamic> json) {
    return RemoteCodeDto(
      id: json["id"] ?? "",
      nameEn: json["name_en"] ?? "",
      nameVi: json["name_vi"] ?? "",
      nameJp: json["name_jp"] ?? "",
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "name_en": nameEn,
        "name_vi": nameVi,
        "name_jp": nameJp,
      };
}
