import 'package:asiantech_link/layers/domain/entity/job_code_model.dart';

class JobCodeModelDto extends JobCodeModel {
  JobCodeModelDto({required super.jobCode});
  factory JobCodeModelDto.fromJson(Map<String, dynamic> json) {
    return JobCodeModelDto(
      jobCode: json["job_code"] == null
          ? []
          : List<JobCodeDto>.from(
              json["job_code"]!.map((x) => JobCodeDto.fromJson(x))),
    );
  }

  Map<String, dynamic> toJson() => {
        "job_code": jobCode
            ?.map((x) => JobCodeDto(
                  id: x.id,
                  nameEn: x.nameEn,
                  nameJp: x.nameJp,
                  nameVn: x.nameVn,
                  isChoose: x.isChoose,
                ))
            .toList(),
      };
}

class JobCodeDto extends JobCode {
  JobCodeDto({
    required super.id,
    required super.nameEn,
    required super.nameJp,
    required super.nameVn,
    required super.isChoose,
  });

  factory JobCodeDto.fromJson(Map<String, dynamic> json) {
    return JobCodeDto(
      id: json["id"] ?? 0,
      nameEn: json["name_en"] ?? "",
      nameJp: json["name_jp"] ?? "",
      nameVn: json["name_vn"] ?? "",
      isChoose: json["isChoose"] ?? false,
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "name_en": nameEn,
        "name_jp": nameJp,
        "name_vn": nameJp,
        "isChoose": isChoose,
      };
}
