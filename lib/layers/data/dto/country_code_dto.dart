import '../../domain/entity/country_code_model.dart';

class CountryCodeDto extends CountryCodeModel {
  const CountryCodeDto({
    required super.name,
    required super.dialCode,
    required super.code,
    required super.emoji,
  });
  factory CountryCodeDto.fromMap(Map<String, dynamic> json) => CountryCodeDto(
      name: json['name'],
      dialCode: json['dial_code'],
      code: json['code'],
      emoji: json['emoji'] ?? "");

  Map<String, dynamic> toMap() => {
        'name': name,
        'dial_code': dialCode,
        'code': code,
        'emoji': emoji,
      };
}
