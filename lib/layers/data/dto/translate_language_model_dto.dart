import 'package:asiantech_link/layers/domain/entity/translate_language_model.dart';

class TranslateLanguageModelDto extends TranslateLanguageModel {
  const TranslateLanguageModelDto(
      {required super.text,
      required super.sourceLanguage,
      required super.targetLanguage});
  factory TranslateLanguageModelDto.fromJson(Map<String, dynamic> json) {
    return TranslateLanguageModelDto(
      text: json["text"],
      sourceLanguage: json["source_language"],
      targetLanguage: json["target_language"],
    );
  }
  Map<String, dynamic> toMap() {
    return {
      "text": text,
      "source_language": sourceLanguage,
      "target_language": targetLanguage
    };
  }
}

class ChatTranslateModelDto extends ChatTranslateModel {
  const ChatTranslateModelDto(
      {required super.id,
      required super.groupId,
      required super.chatId,
      required super.originalText,
      required super.textEn,
      required super.textVi,
      required super.textJa,
      required super.created,
      required super.updated});

  factory ChatTranslateModelDto.from<PERSON>son(Map<String, dynamic> json) {
    return ChatTranslateModelDto(
      id: json["id"],
      groupId: json["group_id"],
      chatId: json["chat_id"],
      originalText: json["original_text"],
      textEn: json["text_en"],
      textVi: json["text_vi"],
      textJa: json["text_ja"],
      created: json["created"],
      updated: json["updated"],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      "id": id,
      "group_id": groupId,
      "chat_id": chatId,
      "original_text": originalText,
      "text_en": textEn,
      "text_vi": textVi,
      "text_ja": textJa,
      "created": created,
      "updated": updated
    };
  }
}
