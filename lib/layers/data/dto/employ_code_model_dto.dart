import 'package:asiantech_link/layers/domain/entity/employ_code_model.dart';

class EmployCodeModelDto extends EmployCodeModel {
  EmployCodeModelDto({required super.employCode});

  factory EmployCodeModelDto.fromJson(Map<String, dynamic> json) {
    return EmployCodeModelDto(
      employCode: (json["employ_code"] as List)
          .map((e) => EmployCodeDto.fromJson(e))
          .toList(),
    );
  }
  Map<String, dynamic> toJson() => {
        "employ_code": employCode
            .map((e) => EmployCodeDto(
                  id: e.id,
                  nameJp: e.nameJp,
                  nameEn: e.nameEn,
                  nameVn: e.nameVn,
                  isChoose: e.isChoose,
                ).toJson())
            .toList(),
      };
}

class EmployCodeDto extends EmployCode {
  EmployCodeDto({
    required super.id,
    required super.nameJp,
    required super.nameEn,
    required super.nameVn,
    required super.isChoose,
  });

  factory EmployCodeDto.fromJson(Map<String, dynamic> json) {
    return EmployCodeDto(
      id: json["id"] ?? "",
      nameJp: json["name_jp"] ?? "",
      nameEn: json["name_en"] ?? "",
      nameVn: json["name_vn"] ?? "",
      isChoose: json["is_choose"] ?? false,
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "name_jp": nameJp,
        "is_choose": isChoose,
      };
}
