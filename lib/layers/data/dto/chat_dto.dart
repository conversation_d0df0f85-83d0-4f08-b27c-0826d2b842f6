import 'dart:convert';

import 'package:asiantech_link/layers/domain/entity/chat_model.dart';
import 'package:openapi/openapi.dart';

import '../../presentation/resources/enum.dart';

class GroupModelDto extends GroupModel {
  const GroupModelDto({super.groupId, super.name, super.created});
  factory GroupModelDto.fromJson(Map<String, dynamic> json) {
    return GroupModelDto(
      groupId: json["group_id"]?.toString() ?? "",
      name: json["name"] ?? "",
      created: json["created"] ?? "",
    );
  }
}

class ChatModelDto extends ChatModel {
  const ChatModelDto({
    super.chatId,
    super.group,
    super.text,
    super.send,
    super.created,
    super.chatEnum,
    super.user,
    super.translatedText,
    super.targetLanguage,
    super.translating,
  });
  factory ChatModelDto.fromJson(Map<String, dynamic> json) {
    return ChatModelDto(
      translatedText: json['translated_text'],
      chatId: json["chat_id"]?.toString() ?? "",
      group: json["group"]?.toString(),
      text: json["text"] ?? "",
      send: json["send"] ?? "",
      created: json["created"] ?? "",
      user: json["user"] == null
          ? null
          : standardSerializers.fromJson(
              UserChat.serializer, jsonEncode(json["user"])),
      chatEnum: ChatEnum.sent,
      targetLanguage: json['target_language'],
      translating: false,
    );
  }
  ChatModelDto copyWith({
    final String? chatId,
    final String? group,
    final String? text,
    final String? send,
    final String? created,
    final UserChat? user,
    final ChatEnum? chatEnum,
    final String? translatedText,
    final String? targetLanguage,
    final bool? translating,
  }) {
    return ChatModelDto(
      chatId: chatId ?? this.chatId,
      group: group ?? this.group,
      text: text ?? this.text,
      send: send ?? this.send,
      created: created ?? this.created,
      user: user ?? this.user,
      chatEnum: chatEnum ?? this.chatEnum,
      translatedText: translatedText ?? this.translatedText,
      targetLanguage: targetLanguage ?? this.targetLanguage,
      translating: translating ?? this.translating,
    );
  }

  Map<String, dynamic> toJson() => {
        "chat_id": chatId,
        "group": group,
        "text": text,
        "send": send,
        "created": created,
        "user": user,
        "translated_text": translatedText,
        "target_language": targetLanguage,
        "translating": translating,
      };

  @override
  List<Object?> get props => [
        chatId,
        group,
        text,
        send,
        created,
        user,
        chatEnum,
        translatedText,
        targetLanguage,
        translating,
      ];
}

class ChatGroupModelDto extends ChatGroupModel {
  const ChatGroupModelDto(
      {super.group,
      super.users,
      super.chat,
      super.updated,
      super.created,
      super.signContract});
  factory ChatGroupModelDto.fromJson(Map<String, dynamic> json) {
    return ChatGroupModelDto(
      group:
          json["group"] == null ? null : GroupModelDto.fromJson(json["group"]),
      users: (json["users"] as List)
          .map((userJson) => standardSerializers.fromJson(
              UserChat.serializer, jsonEncode(userJson)))
          .where((user) => user != null)
          .cast<UserChat>()
          .toList(),
      chat: json["chat"] == null ? null : ChatModelDto.fromJson(json["chat"]),
      updated: json["updated"] ?? "",
      created: json["created"] ?? "",
      signContract: standardSerializers.fromJson(
          SignContract.serializer, jsonEncode(json['apply'])),
    );
  }
}
