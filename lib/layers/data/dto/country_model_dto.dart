import '../../domain/entity/country_model.dart';

class CountryModelDto extends CountryModel {
  CountryModelDto({required super.country});

  factory CountryModelDto.fromJson(Map<String, dynamic> json) {
    return CountryModelDto(
      country:
          (json["country"] as List).map((e) => CountryDto.fromJson(e)).toList(),
    );
  }
  Map<String, dynamic> toJson() => {
        "country": country
            .map((country) => CountryDto(
                  id: country.id,
                  nameJp: country.nameJp,
                  nameEn: country.nameEn,
                  nameVn: country.nameVn,
                  countryCode: country.countryCode,
                  remarks: country.remarks,
                ).toJson())
            .toList(),
      };
}

class CountryDto extends Country {
  CountryDto(
      {required super.id,
      required super.nameJp,
      required super.nameEn,
      required super.nameVn,
      required super.countryCode,
      required super.remarks});

  factory CountryDto.fromJson(Map<String, dynamic> json) {
    return CountryDto(
      id: json["id"] ?? "",
      nameJp: json["name_jp"] ?? "",
      nameEn: json["name_en"] ?? "",
      nameVn: json["name_vn"] ?? "",
      countryCode: json["country_code"] ?? "",
      remarks: json["remarks"] ?? "",
    );
  }

  @override
  Map<String, dynamic> toJson() => {
        "id": id,
        "name_jp": nameJp,
        "name_en": nameEn,
        "name_vn": nameVn,
        "country_code": countryCode,
        "remarks": remarks,
      };
}
