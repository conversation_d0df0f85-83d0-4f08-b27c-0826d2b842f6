// data/mappers/register_model_mapper.dart
import 'package:asiantech_link/layers/domain/entity/register_model.dart';

class RegisterModelDto extends RegisterModel {
  const RegisterModelDto(
      {required super.errors, required super.message, required super.data});

  factory RegisterModelDto.fromJson(Map<String, dynamic> json) {
    return RegisterModelDto(
      errors: json["errors"] == null
          ? []
          : List<Error>.from(json["errors"].map((item) => Error(
                message: item["message"] ?? "",
                field: item["field"] ?? "",
              ))),
      message: json["message"] ?? "",
      data: json["data"] == null ? null : Data(json: json["data"]),
    );
  }
}
