import 'package:asiantech_link/layers/data/dto/company_model_dto.dart';
import 'package:asiantech_link/layers/domain/entity/recruit_model.dart';

class RecruitModelDto extends RecruitModel {
  const RecruitModelDto({
    super.recruitId,
    super.supportCompany,
    super.saveType,
    super.title,
    super.catchCopy,
    super.startDate,
    super.endDate,
    super.displayFlag,
    super.jobCode,
    super.employCode,
    super.content,
    super.placeCode1,
    super.placeCode2,
    super.placeCode3,
    super.payrollCode,
    super.payrollPriceFrom,
    super.payrollPriceTo,
    super.countryCode,
    super.ageFrom,
    super.ageTo,
    super.sexType,
    super.prefCode1,
    super.prefCode2,
    super.prefCode3,
    super.lastAcademicCode,
    super.languageCode1,
    super.languageLevelType1,
    super.languageCode2,
    super.languageLevelType2,
    super.experiencedJobCode,
    super.yearsOfExperience,
    super.skillJobCode1,
    super.skillCode1,
    super.skillLevelType1,
    super.skillJobCode2,
    super.skillCode2,
    super.skillLevelType2,
    super.skillJobCode3,
    super.skillCode3,
    super.skillLevelType3,
    super.createAgentId,
    super.created,
    super.updateAgentId,
    super.updated,
    super.licenceCode1,
    super.licencePoint1,
    super.licenceCode2,
    super.licencePoint2,
    super.licenceCode3,
    super.licencePoint3,
    super.hostCompany,
    super.hostAgent,
    super.supportAgent,
  });

  factory RecruitModelDto.fromJson(Map<String, dynamic> json) {
    return RecruitModelDto(
      recruitId: json['recruit_id'],
      supportCompany: json['support_company'] != null
          ? CompanyModelDto.fromMap(json['support_company'])
          : null,
      saveType: json['save_type'],
      title: json['title'],
      catchCopy: json['catch_copy'],
      startDate: json['start_date'],
      endDate: json['end_date'],
      displayFlag: json['display_flag'],
      jobCode: json['job_code'],
      employCode: json['employ_code'],
      content: json['content'],
      placeCode1: json['place_code1'],
      placeCode2: json['place_code2'],
      placeCode3: json['place_code3'],
      payrollCode: json['payroll_code']?.toString() ?? "",
      payrollPriceFrom: json['payroll_price_from'],
      payrollPriceTo: json['payroll_price_to'],
      countryCode: json['country_code'],
      ageFrom: json['age_from'],
      ageTo: json['age_to'],
      sexType: json['sex_type'],
      prefCode1: json['pref_code1'],
      prefCode2: json['pref_code2'],
      prefCode3: json['pref_code3'],
      lastAcademicCode: json['last_academic_code'],
      languageCode1: json['language_code1'],
      languageLevelType1: json['language_level_type1'],
      languageCode2: json['language_code2'],
      languageLevelType2: json['language_level_type2'],
      experiencedJobCode: json['experienced_job_code'],
      yearsOfExperience: json['years_of_experience'],
      skillJobCode1: json['skill_job_code1'],
      skillCode1: json['skill_code1'],
      skillLevelType1: json['skill_level_type1'],
      skillJobCode2: json['skill_job_code2'],
      skillCode2: json['skill_code2'],
      skillLevelType2: json['skill_level_type2'],
      skillJobCode3: json['skill_job_code3'],
      skillCode3: json['skill_code3'],
      skillLevelType3: json['skill_level_type3'],
      createAgentId: json['create_agent_id'],
      created: json['created'],
      updateAgentId: json['update_agent_id'],
      updated: json['updated'],
      licenceCode1: json['licence_code1'],
      licencePoint1: json['licence_point1'],
      licenceCode2: json['licence_code2'],
      licencePoint2: json['licence_point2'],
      licenceCode3: json['licence_code3'],
      licencePoint3: json['licence_point3'],
      hostCompany: json['support_company'] is Map
          ? CompanyModelDto.fromMap(json['support_company'])
          : null,
      hostAgent: json['host_agent'],
      supportAgent: json['support_agent'],
    );
  }
  Map<String, dynamic> toMap() {
    return {
      'recruit_id': recruitId,
      'support_company': supportCompany != null
          ? (supportCompany as CompanyModelDto).toMap()
          : null,
      'save_type': saveType,
      'title': title,
      'catch_copy': catchCopy,
      'start_date': startDate,
      'end_date': endDate,
      'display_flag': displayFlag,
      'job_code': jobCode,
      'employ_code': employCode,
      'content': content,
      'place_code1': placeCode1,
      'place_code2': placeCode2,
      'place_code3': placeCode3,
      'payroll_code': payrollCode,
      'payroll_price_from': payrollPriceFrom,
      'payroll_price_to': payrollPriceTo,
      'country_code': countryCode,
      'age_from': ageFrom,
      'age_to': ageTo,
      'sex_type': sexType,
      'pref_code1': prefCode1,
      'pref_code2': prefCode2,
      'pref_code3': prefCode3,
      'last_academic_code': lastAcademicCode,
      'language_code1': languageCode1,
      'language_level_type1': languageLevelType1,
      'language_code2': languageCode2,
      'language_level_type2': languageLevelType2,
      'experienced_job_code': experiencedJobCode,
      'years_of_experience': yearsOfExperience,
      'skill_job_code1': skillJobCode1,
      'skill_code1': skillCode1,
      'skill_level_type1': skillLevelType1,
      'skill_job_code2': skillJobCode2,
      'skill_code2': skillCode2,
      'skill_level_type2': skillLevelType2,
      'skill_job_code3': skillJobCode3,
      'skill_code3': skillCode3,
      'skill_level_type3': skillLevelType3,
      'create_agent_id': createAgentId,
      'created': created,
      'update_agent_id': updateAgentId,
      'updated': updated,
      'licence_code1': licenceCode1,
      'licence_point1': licencePoint1,
      'licence_code2': licenceCode2,
      'licence_point2': licencePoint2,
      'licence_code3': licenceCode3,
      'licence_point3': licencePoint3,
      'host_company': hostCompany,
      'host_agent': hostAgent,
      'support_agent': supportAgent,
    };
  }
}
