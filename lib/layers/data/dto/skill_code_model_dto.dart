import 'package:asiantech_link/layers/domain/entity/skill_code_model.dart';

class SkillCodeModelDto extends SkillCodeModel {
  SkillCodeModelDto({required super.skillCode});

  factory SkillCodeModelDto.fromJson(Map<String, dynamic> json) {
    return SkillCodeModelDto(
      skillCode: (json["skill_code"] as List)
          .map((e) => SkillCodeDto.fromJson(e))
          .toList(),
    );
  }
  Map<String, dynamic> toJson() => {
        "skill_code": skillCode
            .map((e) => SkillCodeDto(
                  id: e.id,
                  name: e.name,
                  type: e.type,
                  categoryId: e.categoryId,
                  isChoose: e.isChoose,
                ).toJson())
            .toList(),
      };
}

class SkillCodeDto extends SkillCode {
  SkillCodeDto({
    required super.id,
    required super.name,
    required super.type,
    required super.categoryId,
    required super.isChoose,
  });

  factory SkillCodeDto.fromJson(Map<String, dynamic> json) {
    return SkillCodeDto(
      id: json["id"] ?? "",
      name: json["name"] ?? "",
      type: json["type"] ?? "",
      categoryId: json["category_id"] ?? "",
      isChoose: json["isChoose"] ?? false,
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "type": type,
        "isChoose": isChoose,
        "categoryId": categoryId,
      };
}
