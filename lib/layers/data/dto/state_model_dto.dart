import '../../domain/entity/state_model.dart';

class StateModelDto extends StateModel {
  StateModelDto({required super.state});

  factory StateModelDto.fromJson(Map<String, dynamic> json) {
    return StateModelDto(
      state: (json["states"] as List).map((e) => StateDto.fromJson(e)).toList(),
    );
  }
  Map<String, dynamic> toJson() => {
        "city": state
            .map((city) => StateDto(
                  code: city.code,
                  id: city.id,
                  selectionListName: city.selectionListName,
                  nameJp: city.nameJp,
                  nameEn: city.nameEn,
                  nameVn: city.nameVn,
                ).toJson())
            .toList(),
      };
}

class StateDto extends CountryState {
  StateDto(
      {required super.id,
      required super.selectionListName,
      required super.nameJp,
      required super.nameEn,
      required super.nameVn,
      required super.code});

  factory StateDto.fromJson(Map<String, dynamic> json) {
    return StateDto(
      id: json["id"] ?? "",
      selectionListName: json["selection_list_name"] ?? "",
      nameJp: json["name_jp"] ?? "",
      nameEn: json["name_en"] ?? "",
      nameVn: json["name_vn"] ?? "",
      code: json["code"] ?? "",
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "selection_list_name": selectionListName,
        "name_jp": nameJp,
        "name_en": nameEn,
        "name_vn": nameVn,
        "code": code,
      };
}
