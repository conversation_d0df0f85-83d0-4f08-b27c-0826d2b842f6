import 'package:asiantech_link/layers/data/source/local/local_storage/jwt_local_storage.dart';

import '../local_storage_service.dart';

class AuthLocalStorage {
  final LocalStorageService _local = LocalStorageService();
  final JWTLocalStorage _jwtLocalStorage = JWTLocalStorage();
  Future<void> setAuthStorage(
      String refreshToken, String accessToken, String userType) async {
    return _jwtLocalStorage.persistToken(
      accessToken: accessToken,
      refreshToken: refreshToken,
      userType: userType,
    );
  }

  setTempAuth(String key, String value) async {
    return await _local.setString(key, value);
  }

  getTempAuth(String key) {
    return _local.getString(key);
  }

  removeTempAuth(String key) {
    return _local.remove(key);
  }
}
