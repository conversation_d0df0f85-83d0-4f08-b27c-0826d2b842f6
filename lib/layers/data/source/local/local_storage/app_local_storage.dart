import 'dart:convert';

import 'package:asiantech_link/layers/data/dto/academic_level_model_dto.dart';
import 'package:asiantech_link/layers/data/dto/country_currency_model_dto.dart';

import 'package:asiantech_link/layers/data/dto/country_model_dto.dart';
import 'package:asiantech_link/layers/data/dto/employ_code_model_dto.dart';
import 'package:asiantech_link/layers/data/dto/job_code_model_dto.dart';
import 'package:asiantech_link/layers/data/dto/qualification_model_dto.dart';
import 'package:asiantech_link/layers/data/dto/skill_code_model_dto.dart';
import 'package:asiantech_link/layers/data/dto/remote_code_model_dto.dart';
import 'package:asiantech_link/layers/presentation/utils/extensions.dart';
import 'package:asiantech_link/layers/presentation/utils/helper.dart';
import 'package:flutter/services.dart';

import '../../../../presentation/resources/config_export.dart';
import '../../../dto/currency_model_dto.dart';
import '../../../dto/language_level_model_dto.dart';
import '../../../dto/language_model_dto.dart';
import '../../../dto/state_model_dto.dart';
import '../local_storage_service.dart';

class AppLocalStorage {
  final LocalStorageService _local = LocalStorageService();
  Future<bool> setLanguage(String value) async {
    String key = AppConstants.languageKey;
    return await _local.setString(key, value);
  }

  String? getLanguage() {
    String key = AppConstants.languageKey;
    return _local.getString(key);
  }

  Future<LanguageModelDto> getListLanguage() async {
    // Load JSON from assets
    String jsonString =
        await rootBundle.loadString('assets/json/language_code.json');
    var currentLocale = globalContext?.loc.localeName ?? "en";
    var currentLanguageCode =
        Helper.mapLanguageCodeToCountryCode(currentLocale);
    var data = json.decode(jsonString);
    var list = data["language_code"] as List;
    for (var item in list) {
      if (item['name_$currentLanguageCode'] != null) {
        var value = item['name_$currentLanguageCode'];
        item['name_jp'] = value;
        item['name_en'] = value;
        item['name_vn'] = value;
      }
    }
    var jsonData = {"language_code": list};
    return LanguageModelDto.fromJson(jsonData);
  }

  Future<CountryModelDto> getListCountry() async {
    // Load JSON from assets
    String jsonString = await rootBundle.loadString('assets/json/country.json');
    var currentLocale = globalContext?.loc.localeName ?? "en";
    var currentLanguageCode =
        Helper.mapLanguageCodeToCountryCode(currentLocale);
    var data = json.decode(jsonString);
    var list = data["country"] as List;
    for (int i = 0; i < list.length; i++) {
      var item = list[i];
      var value = item['name_$currentLanguageCode'];
      item['name_jp'] = value;
      item['name_en'] = value;
      item['name_vn'] = value;
      list[i] = item;
    }
    var jsonData = {"country": list};
    return CountryModelDto.fromJson(jsonData);
  }

  Future<StateModelDto> getListState() async {
    // Load JSON from assets
    String jsonString = await rootBundle.loadString('assets/json/states.json');
    var currentLocale = globalContext?.loc.localeName ?? "en";
    var currentLanguageCode =
        Helper.mapLanguageCodeToCountryCode(currentLocale);
    var data = json.decode(jsonString);
    var list = data["states"] as List;
    for (int i = 0; i < list.length; i++) {
      var item = list[i];
      var value = item['name_$currentLanguageCode'];
      item['name_jp'] = value;
      item['name_en'] = value;
      item['name_vn'] = value;
      list[i] = item;
    }
    var jsonData = {"states": list};
    return StateModelDto.fromJson(jsonData);
  }

  Future<AcademicLevelModelDto> getListAcademicLevel() async {
    // Load JSON from assets
    String jsonString =
        await rootBundle.loadString('assets/json/academic_level.json');
    return AcademicLevelModelDto.fromJson(json.decode(jsonString));
  }

  Future<CurrencyModelDto> getListCurrency() async {
    // Load JSON from assets
    String jsonString =
        await rootBundle.loadString('assets/json/currency.json');
    return CurrencyModelDto.fromJson(json.decode(jsonString));
  }

  Future<EmployCodeModelDto> getEmployCode() async {
    // Load JSON from assets
    String jsonString =
        await rootBundle.loadString('assets/json/employ_code.json');
    return EmployCodeModelDto.fromJson(json.decode(jsonString));
  }

  Future<SkillCodeModelDto> getSkillCode() async {
    // Load JSON from assets
    String jsonString =
        await rootBundle.loadString('assets/json/skill_code.json');
    return SkillCodeModelDto.fromJson(json.decode(jsonString));
  }

  Future<QualificationModelDto> getQualification() async {
    // Load JSON from assets
    String jsonString =
        await rootBundle.loadString('assets/json/qualification.json');
    return QualificationModelDto.fromJson(json.decode(jsonString));
  }

  Future<JobCodeModelDto> getJobCode() async {
    // Load JSON from assets
    String jsonString =
        await rootBundle.loadString('assets/json/job_code.json');
    return JobCodeModelDto.fromJson(json.decode(jsonString));
  }

  Future<LanguageLevelModelDto> getLanguageLevel() async {
    // Load JSON from assets
    String jsonString =
        await rootBundle.loadString('assets/json/language_level.json');
    return LanguageLevelModelDto.fromJson(json.decode(jsonString));
  }

  Future<RemoteCodeModelDto> getRemoteCode() async {
    // Load JSON from assets
    String jsonString =
        await rootBundle.loadString('assets/json/remote_code.json');
    return RemoteCodeModelDto.fromJson(json.decode(jsonString));
  }

  Future<List<CountryCurrencyModelDto>> getCountryCurrency() async {
    // Load JSON from assets
    String jsonString =
        await rootBundle.loadString('assets/json/country_currencies.json');
    var data = json.decode(jsonString);
    var list = data["data"] as List;
    return list.map((e) => CountryCurrencyModelDto.fromJson(e)).toList();
  }
}
