import 'package:asiantech_link/layers/data/source/local/session_storage_service.dart';
import 'package:asiantech_link/layers/presentation/resources/app_constants.dart';

import 'package:asiantech_link/layers/data/source/local/local_storage_service.dart';

import 'package:jwt_decoder/jwt_decoder.dart';

class JWTLocalStorage {
  final LocalStorageService _local = LocalStorageService();
  final SessionStorageService _session = SessionStorageService();
  SessionStorageService get session => _session;
  Future<void> setJWT(
      String accessToken, String refreshToken, String userType) async {
    // Login have check remember to store refresh token
    final isRemember = _local.getBool(AppConstants.isRememberToken) ?? true;

    await _session.setString(AppConstants.accessTokenKey, accessToken);
    if (isRemember) {
      await _session.setString(AppConstants.refreshTokenKey, refreshToken);
    }
    await _session.setString(AppConstants.userType, userType);

    final jwt = JwtDecoder.decode(accessToken);
    await setAccountId(jwt['user_id']);
    await setAccountRole(accessToken, refreshToken, userType);
  }

  Future<void> setAccountRole(
      String accessToken, String refreshToken, String userType) async {
    final userTypeMap = {
      UserTypeConstants.engineer.toString(): [
        AppConstants.accessTokenEngineerKey,
        AppConstants.refreshTokenEngineerKey
      ],
      UserTypeConstants.referralAgencyStaff.toString(): [
        AppConstants.accessTokenReferralAgencyKey,
        AppConstants.refreshTokenReferralAgencyKey
      ],
      UserTypeConstants.hostCompanyStaff.toString(): [
        AppConstants.accessTokenHostCompanyKey,
        AppConstants.refreshTokenHostCompanyKey
      ],
      UserTypeConstants.admin.toString(): [
        AppConstants.accessTokenAdminKey,
        AppConstants.refreshTokenAdminKey
      ],
      UserTypeConstants.hostSupportAgencyStaff.toString(): [
        AppConstants.accessTokenHostSupportAgencyKey,
        AppConstants.refreshTokenHostSupportAgencyKey
      ],
      UserTypeConstants.otherSupportAgencyStaff.toString(): [
        AppConstants.accessTokenOtherSupportAgencyKey,
        AppConstants.refreshTokenOtherSupportAgencyKey
      ],
    };

    final keys = userTypeMap[userType];
    if (keys != null) {
      await _local.setString(keys[0], accessToken);
      await _local.setString(keys[1], refreshToken);
    }
  }

  Future setAccountId(int accountId) async {
    String key = AppConstants.accountIdKey;
    return _session.setInt(key, accountId);
  }

  String? getAccessToken() {
    String key = AppConstants.accessTokenKey;
    return _session.getString(key);
  }

  String? getAccessTokenByUserType(int userType) {
    if (userType == UserTypeConstants.engineer) {
      return _local.getString(AppConstants.accessTokenEngineerKey);
    } else if (userType == UserTypeConstants.hostCompanyStaff) {
      return _local.getString(AppConstants.accessTokenHostCompanyKey);
    } else if (userType == UserTypeConstants.hostSupportAgencyStaff) {
      return _local.getString(AppConstants.accessTokenHostSupportAgencyKey);
    } else if (userType == UserTypeConstants.referralAgencyStaff) {
      return _local.getString(AppConstants.accessTokenReferralAgencyKey);
    } else if (userType == UserTypeConstants.otherSupportAgencyStaff) {
      return _local.getString(AppConstants.accessTokenOtherSupportAgencyKey);
    } else if (userType == UserTypeConstants.admin) {
      return _local.getString(AppConstants.accessTokenAdminKey);
    }
    return null;
  }

  String? getRefreshTokenByUserType(int userType) {
    if (userType == UserTypeConstants.engineer) {
      return _local.getString(AppConstants.refreshTokenEngineerKey);
    } else if (userType == UserTypeConstants.hostCompanyStaff) {
      return _local.getString(AppConstants.refreshTokenHostCompanyKey);
    } else if (userType == UserTypeConstants.hostSupportAgencyStaff) {
      return _local.getString(AppConstants.refreshTokenHostSupportAgencyKey);
    } else if (userType == UserTypeConstants.referralAgencyStaff) {
      return _local.getString(AppConstants.refreshTokenReferralAgencyKey);
    } else if (userType == UserTypeConstants.otherSupportAgencyStaff) {
      return _local.getString(AppConstants.refreshTokenOtherSupportAgencyKey);
    } else if (userType == UserTypeConstants.admin) {
      return _local.getString(AppConstants.refreshTokenAdminKey);
    }
    return null;
  }

  String? getRefreshToken() {
    String key = AppConstants.refreshTokenKey;
    return _session.getString(key);
  }

  int? getAccountId() {
    String key = AppConstants.accountIdKey;
    return _session.getInt(key);
  }

  int? getJWTExpire(String accessToken) {
    final jwt = JwtDecoder.decode(accessToken);
    var exp = jwt['exp'];
    return exp;
  }

  Future<bool> removeAccessToken() {
    return _session.remove(AppConstants.accessTokenKey);
  }

  Future<bool> removeAccountId() {
    return _session.remove(AppConstants.accountIdKey);
  }

  Future<bool> removeRefreshToken() {
    return _session.remove(AppConstants.refreshTokenKey);
  }

  Future<bool> removeUserType() {
    return _session.remove(AppConstants.userType);
  }

  Future<void> persistToken(
      {required String accessToken,
      required String refreshToken,
      required String userType}) async {
    await setJWT(accessToken, refreshToken, userType);
  }

  bool isTokenExpired(String accessToken) {
    try {
      final expireTime = getJWTExpire(accessToken);
      if (expireTime == null) {
        return true;
      }
      DateTime expiryDate =
          DateTime.fromMillisecondsSinceEpoch(expireTime * 1000);
      DateTime now = DateTime.now();

      bool isExpired = now.isAfter(expiryDate);
      return isExpired;
    } catch (e) {
      return true;
    }
  }

  Future<int?> clearLocalUserInfo() async {
    var userTypeTemp = _session.getString(AppConstants.userTypeTemp);
    var userType = _session.getString(AppConstants.userType);
    if (userTypeTemp != null) {
      await _session.setString(AppConstants.userTypeTemp, userTypeTemp);
    }
    if (userType != null) {
      await _session.clear();
      await _local.clear(userTypeToDelete: int.tryParse(userType));
      return int.tryParse(userType);
    }
    return null;
  }

  int? getUserType() {
    return _session.getInt(AppConstants.userType);
  }
}
