import 'package:intl/intl.dart';
import 'package:universal_html/html.dart' as html;

class CookieService {
  static void setCookie(String name, String value, int expiryDays) {
    DateTime oneHourFromNow =
        DateTime.now().toUtc().add(Duration(days: expiryDays));
    DateFormat rfc1123 = DateFormat('EEE, dd MMM yyyy HH:mm:ss', 'en_US');
    String cookieExpires = rfc1123.format(oneHourFromNow);
    String cookie = "$name=$value; expires=$cookieExpires; path=/";

    html.document.cookie = cookie;
  }

  static String? getCookie(String name) {
    final cookies = html.document.cookie;

    if (cookies != null && cookies.isNotEmpty) {
      return cookies
          .split('; ')
          .map((String cookie) {
            List<String> split = cookie.split('=');
            return MapEntry(split[0], split[1]);
          })
          .where((cookie) => cookie.key == name)
          .first
          .value;
    }
    return null;
  }

  static void deleteCookie(String name) {
    setCookie(name, "", -1);
  }
}
