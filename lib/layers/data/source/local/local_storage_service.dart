import 'package:asiantech_link/app.dart';
import 'package:asiantech_link/layers/presentation/resources/config_export.dart';
import 'package:asiantech_link/layers/presentation/utils/helper.dart';
import 'package:flutter/foundation.dart';
import 'package:universal_html/html.dart' as html;

class LocalStorageService {
  Future<bool> setString(String key, String value) async {
    if (kIsWeb) {
      html.window.localStorage[key] = value;
      return true;
    }
    return sharedPreferences.setString(key, value);
  }

  String? getString(String key) {
    if (kIsWeb) {
      return html.window.localStorage[key];
    }
    return sharedPreferences.getString(key);
  }

  Future<bool> remove(String key) async {
    if (kIsWeb) {
      html.window.localStorage.remove(key);
      return true;
    }
    return sharedPreferences.remove(key);
  }

  Future<bool> setInt(String key, int value) async {
    if (kIsWeb) {
      html.window.localStorage[key] = value.toString();
      return true;
    }
    return sharedPreferences.setInt(key, value);
  }

  int? getInt(String key) {
    if (kIsWeb) {
      return stringToInt(html.window.localStorage[key] ?? '0');
    }
    return sharedPreferences.getInt(key);
  }

  bool? getBool(String key) {
    if (kIsWeb) {
      var value = html.window.localStorage[key];
      return bool.tryParse("$value");
    }
    return sharedPreferences.getBool(key);
  }

  Future<bool> setBool(String key, bool value) async {
    if (kIsWeb) {
      html.window.localStorage[key] = value.toString();
      return true;
    }
    return sharedPreferences.setBool(key, value);
  }

  Future<List<String>> getStringList(String key) async {
    if (kIsWeb) {
      return html.window.localStorage[key]?.split(',') ?? [];
    }
    return sharedPreferences.getStringList(key) ?? [];
  }

  Future<bool> setStringList(String key, List<String> value) async {
    if (kIsWeb) {
      html.window.localStorage[key] = value.join(',');
      return true;
    }
    return sharedPreferences.setStringList(key, value);
  }

  Future<bool> clear({int? userTypeToDelete}) async {
    var userType = userTypeToDelete?.toString();
    final keysToKeep = [
      AppConstants.appVersionKey,
      AppConstants.appBuildNumberKey,
      AppConstants.listNumberWhatsAppKey,
      AppConstants.selectedNumber,
      AppConstants.confirmedWhatsAppNumbersKey,
      AppConstants.accessTokenAdminKey,
      AppConstants.refreshTokenAdminKey,
      AppConstants.accessTokenEngineerKey,
      AppConstants.refreshTokenEngineerKey,
      AppConstants.accessTokenHostCompanyKey,
      AppConstants.refreshTokenHostCompanyKey,
      AppConstants.accessTokenHostSupportAgencyKey,
      AppConstants.refreshTokenHostSupportAgencyKey,
      AppConstants.accessTokenOtherSupportAgencyKey,
      AppConstants.refreshTokenOtherSupportAgencyKey,
      AppConstants.accessTokenReferralAgencyKey,
      AppConstants.refreshTokenReferralAgencyKey,
      AppConstants.exploreUserParams,
      AppConstants.languageKey,
      AppConstants.userType,
      AppConstants.userTypeTemp,
    ];

    if (userType != null) {
      if (userType == UserTypeConstants.engineer.toString()) {
        keysToKeep.remove(AppConstants.accessTokenEngineerKey);
        keysToKeep.remove(AppConstants.refreshTokenEngineerKey);
      } else if (userType == UserTypeConstants.hostCompanyStaff.toString()) {
        keysToKeep.remove(AppConstants.accessTokenHostCompanyKey);
        keysToKeep.remove(AppConstants.refreshTokenHostCompanyKey);
      } else if (userType == UserTypeConstants.admin.toString()) {
        keysToKeep.remove(AppConstants.accessTokenAdminKey);
        keysToKeep.remove(AppConstants.refreshTokenAdminKey);
      } else if (userType ==
          UserTypeConstants.otherSupportAgencyStaff.toString()) {
        keysToKeep.remove(AppConstants.accessTokenOtherSupportAgencyKey);
        keysToKeep.remove(AppConstants.refreshTokenOtherSupportAgencyKey);
      } else if (userType ==
          UserTypeConstants.hostSupportAgencyStaff.toString()) {
        keysToKeep.remove(AppConstants.accessTokenHostSupportAgencyKey);
        keysToKeep.remove(AppConstants.refreshTokenHostSupportAgencyKey);
      } else if (userType == UserTypeConstants.referralAgencyStaff.toString()) {
        keysToKeep.remove(AppConstants.accessTokenReferralAgencyKey);
        keysToKeep.remove(AppConstants.refreshTokenReferralAgencyKey);
      }
    }

    if (kIsWeb) {
      final valuesToKeep = {
        for (var key in keysToKeep) key: html.window.localStorage[key]
      };
      html.window.localStorage.clear();
      await Future.delayed(const Duration(milliseconds: 200));
      valuesToKeep.forEach((key, value) {
        if (value != null) {
          html.window.localStorage[key] = value;
        }
      });
      return true;
    } else {
      final valuesToKeep = {
        for (var key in keysToKeep) key: sharedPreferences.getStringList(key)
      };
      await sharedPreferences.clear();
      valuesToKeep.forEach((key, value) {
        if (value != null) {
          sharedPreferences.setStringList(key, value);
        }
      });
      return true;
    }
  }
}
