import 'package:universal_html/html.dart' as html;

class SessionStorageService {
  Future<bool> setString(String key, String value) async {
    html.window.sessionStorage[key] = value;
    return true;
  }

  String? getString(String key) {
    return html.window.sessionStorage[key];
  }

  int? getInt(String key) {
    return int.tryParse(html.window.sessionStorage[key] ?? "");
  }

  void setInt(String key, int value) {
    html.window.sessionStorage[key] = value.toString();
  }

  bool? getBool(String key) {
    return bool.tryParse(html.window.sessionStorage[key] ?? "");
  }

  void setBool(String key, bool value) {
    html.window.sessionStorage[key] = value.toString();
  }

  Future<bool> remove(String key) async {
    html.window.sessionStorage.remove(key);
    return Future.value(true);
  }

  Future<bool> clear() async {
    html.window.sessionStorage.clear();
    return Future.value(true);
  }
}
