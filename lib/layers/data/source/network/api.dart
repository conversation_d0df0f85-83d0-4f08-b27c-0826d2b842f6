import 'dart:collection';
import 'dart:convert';
import 'dart:typed_data';
import 'package:asiantech_link/layers/data/source/network/api/api_interceptor.dart';
import 'package:asiantech_link/layers/presentation/env/env.dart';
import 'package:asiantech_link/layers/presentation/utils/helper.dart';
import 'package:dio/dio.dart';
import 'package:openapi/openapi.dart';
import 'package:built_value/serializer.dart';
import 'package:built_value/standard_json_plugin.dart';
import 'package:asiantech_link/layers/presentation/utils/log.dart' as log_app;

enum Method { post, put, patch, delete, get }

class Api {
  late Dio _dio;
  Api() {
    _dio = Dio(BaseOptions(
      baseUrl: AppConfig().baseUrl ?? "",
    ));
    _dio.interceptors.add(ApiInterceptor());
  }

  Openapi initOpenApi({bool isRequireToken = true}) {
    var customSerializers = (serializers.toBuilder()
          ..addPlugin(StandardJsonPlugin())
          ..add(DoubleSerializer()))
        .build();
    return Openapi(dio: _dio, serializers: customSerializers);
  }

  dynamic parseSerializer<T>(serializer, data) {
    final jsonString = standardSerializers.toJson<T>(serializer, data);
    final json = jsonDecode(jsonString);
    return json;
  }

  Future<Response> request(
    url,
    Method method, {
    body,
    Map<String, dynamic>? params,
    headersOverwrite,
    customContentType,
    Map<String, dynamic>? customHeader,
    Map<String, dynamic>? headerAddition,
    Function(int, int)? onSendProgress,
    Options? cacheOptions,
    bool isWithoutException = false,
    Function(dynamic data)? rawResponseCallBack,
    isHaveJWT = false,
    bool isKeepAlive = true,
  }) async {
    Map headers = {
      'cache-control': 'cache',
      'Content-Type': customContentType ?? 'application/json',
    };
    if (isKeepAlive) {
      headers['Connection'] = 'keep-alive';
    }

    var combinedMap = headers;
    if (headersOverwrite != null) {
      var mapList = [headers, headersOverwrite];
      combinedMap = mapList.reduce((map1, map2) => map1..addAll(map2));
    }
    Map<String, dynamic> header = customHeader ?? HashMap.from(combinedMap);
    if (headerAddition != null) {
      header.addAll(headerAddition);
    }

    try {
      late Response response;
      if (method == Method.post) {
        response = await _dio.post(url,
            data: body,
            options: cacheOptions != null
                ? cacheOptions.copyWith(headers: header)
                : Options(headers: header),
            queryParameters: params,
            onSendProgress: onSendProgress);
      } else if (method == Method.put) {
        response = await _dio.put(
          url,
          data: body,
          options: Options(headers: header),
          queryParameters: params,
          onSendProgress: onSendProgress,
        );
      } else if (method == Method.patch) {
        response = await _dio.patch(url,
            data: body,
            options: Options(headers: header),
            queryParameters: params);
      } else if (method == Method.delete) {
        response = await _dio.delete(url,
            options: Options(headers: header),
            data: body,
            queryParameters: params);
      } else {
        response = await _dio.get(url,
            options: cacheOptions != null
                ? cacheOptions.copyWith(headers: header)
                : Options(headers: header),
            queryParameters: params);
      }
      if (response.data is! Uint8List) {
        log_app.log("🐛 response api: ${response.data}");
      }
      rawResponseCallBack?.call(response.data);
      return response;
    } catch (e) {
      log_app.logError(e, information: [
        {"url": url},
        {"body": body},
        {"params": params},
      ]);
      logErrorToCloud(e);

      rethrow;
    }
  }
}

class DoubleSerializer implements PrimitiveSerializer<double> {
  @override
  final Iterable<Type> types = const [double];
  @override
  final String wireName = 'double';

  @override
  Object serialize(Serializers serializers, double value,
      {FullType specifiedType = FullType.unspecified}) {
    return value.toString();
  }

  @override
  double deserialize(Serializers serializers, Object serialized,
      {FullType specifiedType = FullType.unspecified}) {
    return double.tryParse("$serialized") ?? 0.0;
  }
}
