import 'package:asiantech_link/layers/data/source/network/api.dart';
import 'package:dio/dio.dart';
import 'package:openapi/openapi.dart';

class AuthApi extends Api {
  late var authenticationApi =
      initOpenApi(isRequireToken: false).getAuthenticationApi();

  Future<LoginSuccessResponseModel> login(
      {required String email,
      required String password,
      String? captchaKey,
      String? captchaValue,
      int? userType}) async {
    final response = await authenticationApi.login(data: Login((obj) {
      obj.email = email;
      obj.password = password;
      obj.captchaKey = captchaKey;
      obj.captchaValue = captchaValue;
      obj.userType = userType;
    }));
    return response.data!;
  }

  Future<void> verifyEmail({required String token}) async {
    await authenticationApi.verifyEmail(data: VerifyEmail((obj) {
      obj.token = token;
    }));
  }

  Future<void> sendCode(
      {required String email,
      required String captchaKey,
      required String captchaValue,
      String? snsEmail}) async {
    await authenticationApi.sendCode(data: SendCodeRequest((obj) {
      obj.email = email;
      obj.captchaKey = captchaKey;
      obj.captchaValue = captchaValue;
      obj.snsEmail = snsEmail;
    }));
  }

  Future<Response<CaptchaResponseModel>> captcha() async {
    return await authenticationApi.generateCaptcha();
  }

  Future<Response<CheckCaptchaRequireInLoginResponseModel>>
      checkCaptchaRequiredInLogin(String email) async {
    return await authenticationApi.checkCaptchaRequiredInLogin(
        data: CheckCaptchaRequireInLogin((obj) {
      obj.email = email;
    }));
  }

  Future<void> resetPassword({required String email}) async {
    await authenticationApi.resetPassword(data: ResetPassword((obj) {
      obj.email = email;
    }));
  }

  Future<ResponseModel> confirmLogin(
      String email, String password, String code) async {
    final res = await authenticationApi.confirmLogin(data: ConfirmLogin((obj) {
      obj.email = email;
      obj.password = password;
      obj.code = code;
    }));
    return res.data ?? ResponseModel();
  }

  Future<Response<ResponseModel>> sendOTPWhatsApp(String phoneNumber) async {
    final res =
        await authenticationApi.sendWhatsappCode(data: SendWhatsappCode((obj) {
      obj.phoneNumber = phoneNumber;
    }));
    return res;
  }

  Future<Response<ResponseModel>> confirmWhatsApp(
      String phoneNumber, String code) async {
    final res = await authenticationApi.confirmWhatsappCode(
        data: ConfirmWhatsappCode((obj) {
      obj.phoneNumber = phoneNumber;
      obj.code = code;
    }));
    return res;
  }

  Future<void> resetPasswordConfirm({required String token}) async {
    await authenticationApi.resetPasswordConfirm(
        data: ResetPasswordConfirm((obj) {
      obj.token = token;
    }));
  }

  Future<void> logout({required String refreshToken}) async {
    authenticationApi = initOpenApi().getAuthenticationApi();
    await authenticationApi.logout(data: Logout((obj) {
      obj.refresh = refreshToken;
    }));
  }

  Future<void> deleteAccount() async {
    authenticationApi = initOpenApi().getAuthenticationApi();
    await authenticationApi.deleteAccount();
  }

  Future<void> resendVerifyEmail(
      {required String email,
      required String captchaKey,
      required String captchaValue}) async {
    authenticationApi = initOpenApi().getAuthenticationApi();
    await authenticationApi.resendVerificationEmail(
        data: SendCodeRequest((obj) {
      obj.email = email;
      obj.captchaKey = captchaKey;
      obj.captchaValue = captchaValue;
    }));
  }

  Future<LoginWithSNSResponseData?> loginWithSNS(LoginWithSNS data) async {
    final res = await authenticationApi.loginWithSns(data: data);
    return res.data?.data;
  }
}
