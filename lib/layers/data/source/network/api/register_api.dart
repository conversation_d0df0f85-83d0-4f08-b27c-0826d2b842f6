import 'package:asiantech_link/layers/data/dto/register_model_dto.dart';
import 'package:openapi/openapi.dart';

import '../../../../presentation/utils/parse_openapi.dart';
import '../api.dart';

class RegisterApi extends Api {
  late final AuthenticationApi _api =
      initOpenApi(isRequireToken: false).getAuthenticationApi();

  Future<RegisterModelDto> register(String email, String password) async {
    try {
      final res = await _api.register(data: Register(
        (b) {
          b.email = email;
          b.password = password;
        },
      ));

      RegisterModelDto data = RegisterModelDto.fromJson(
          ParseOpenapi.parseJsonFromObject(res.data, ResponseModel.serializer));

      return data;
    } catch (e) {
      rethrow;
    }
  }
}
