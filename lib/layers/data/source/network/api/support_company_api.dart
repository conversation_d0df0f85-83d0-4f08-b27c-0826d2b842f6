import 'package:asiantech_link/layers/data/dto/paging_model_dto.dart';
import 'package:asiantech_link/layers/data/source/network/api.dart';
import 'package:openapi/openapi.dart';

class AppSupportCompanyApi extends Api {
  late final SupportAgenciesApi _supportCompanyApi =
      initOpenApi().getSupportAgenciesApi();
  Future<List<SPCompanyRegistered>> getListRegisteredCompany() async {
    final response = await _supportCompanyApi.getListRegisteredCompany();
    return response.data?.data.toList() ?? [];
  }

  Future<PagingModelDto<RecruitUploadedSerializers>>
      getListRecruitOfHostCompany({
    required int hostCompanyId,
    int? pageSize,
    String? cursor,
    String? ordering,
  }) async {
    final response = await _supportCompanyApi.getListRecruitOfHostCompany(
      hostCompanyId: hostCompanyId,
      pageSize: pageSize,
      cursor: cursor,
      ordering: ordering,
    );
    final model = response.data?.data;
    return PagingModelDto<RecruitUploadedSerializers>(
      next: model?.next,
      previous: model?.previous,
      results: model?.results.toList() ?? [],
    );
  }

  Future<int?> supportRequestInterview({
    required SPRequestInterviewBody data,
  }) async {
    final response =
        await _supportCompanyApi.supportRequestInterview(data: data);
    return response.data?.data.applyId;
  }

  Future<PagingModelDto<ManageHostCompany>> getListManageHostCompany({
    int? pageSize,
    String? cursor,
    String? sortBy,
    String? progressStatus,
    String? search,
  }) async {
    final response = await _supportCompanyApi.getListManageHostCompany(
      pageSize: pageSize,
      cursor: cursor,
      sortBy: sortBy,
      progressStatus: progressStatus,
      search: search,
    );

    final model = response.data?.data;
    return PagingModelDto<ManageHostCompany>(
      next: model?.next,
      previous: model?.previous,
      results: model?.results.toList() ?? [],
    );
  }
}
