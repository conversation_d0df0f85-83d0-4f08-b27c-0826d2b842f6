import 'package:asiantech_link/layers/data/source/network/api.dart';
import 'package:dio/dio.dart';
import 'package:openapi/openapi.dart';

class MediaApi extends Api {
  late var mediaApi = initOpenApi().getImageApi();

  Future<Response<MediaResponseModel>> uploadPassPort(
      {required MultipartFile image}) async {
    return await mediaApi.uploadImage(image: image);
  }

  Future<Response<ResponseModel>> deleteAvatar() async {
    return await mediaApi.deleteAvatar();
  }

  Future<Response<MediaResponseModel>> uploadPassportImage(
      {required MultipartFile image}) async {
    return await mediaApi.uploadPassportImage(image: image);
  }

  Future<Response<ResponseModel>> deleteAvatarPassport() async {
    return await mediaApi.deletePassportImage();
  }

  Future<void> uploadCompanyLogo({required MultipartFile image}) async {
    await mediaApi.uploadCompanyLogo(image: image);
  }

  Future<void> uploadPrImageCompany(
      {required MultipartFile image, required int index}) async {
    await mediaApi.uploadCompanyPr(image: image, index: index);
  }

  Future<void> uploadContractImage(
      {required MultipartFile image, required String recruitId}) async {
    await mediaApi.uploadContractImage(image: image, recruitId: recruitId);
  }

  Future<String> uploadRecruitCover({required MultipartFile image}) async {
    final response = await mediaApi.uploadRecruitCover(image: image);
    return response.data?.data.image ?? "";
  }

  Future<void> uploadSignatureApply(
      {required MultipartFile image, required String applyId}) async {
    await mediaApi.uploadCompanyContractImage(image: image, applyId: applyId);
  }

  Future<void> uploadAvatarEngineer(
      {required MultipartFile image, required int engineerId}) async {
    await mediaApi.adminUploadAvatar(image: image, engineerId: engineerId);
  }

  Future<void> deleteAvatarEngineer({required int engineerId}) async {
    await mediaApi.adminDeleteAvatarEngineer(engineerId: engineerId);
  }
}
