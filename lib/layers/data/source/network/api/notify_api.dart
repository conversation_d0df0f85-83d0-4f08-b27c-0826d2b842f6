import 'package:dio/dio.dart';
import 'package:openapi/openapi.dart';

import '../api.dart';

class AppNotifyApi extends Api {
  late final NotifyApi _api = initOpenApi().getNotifyApi();

  Future<Response<ListNotifyResponseModel>> getListNotify() async {
    return await _api.getListNotify();
  }

  Future<Response<CountUnreadNotifyResponseModel>>
      getCountUnreadNotify() async {
    return await _api.getCountUnreadNotify();
  }

  Future<Response<SetReadNotifyResponseModel>> setReadNotify(
      List<int> ids) async {
    return await _api.setReadNotify(
        data: SetReadNotify((b) => b..notifyIds.addAll(ids)));
  }

  Future<Response<SetReadNotifyResponseModel>> setReadAllNotify() async {
    return await _api.setReadAllNotify();
  }
}
