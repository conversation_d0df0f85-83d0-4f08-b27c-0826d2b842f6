import 'dart:typed_data';

import 'package:asiantech_link/layers/data/dto/paging_model_dto.dart';
import 'package:asiantech_link/layers/data/source/network/api.dart';
import 'package:built_collection/built_collection.dart';
import 'package:dio/dio.dart';
import 'package:openapi/openapi.dart';

class AppAdminApi extends Api {
  late final AdminApi _adminApi = initOpenApi().getAdminApi();

  Future<PagingModelDto<GetListEngineer>> getListEngineer({
    int? pageSize,
    String? cursor,
    String? ordering,
    DateTime? registerDateFrom,
    DateTime? registerDateTo,
    String? querySearch,
    String? createdUser,
    int? isDataPolicyAccept,
  }) async {
    final response = await _adminApi.getEngineerList(
      pageSize: pageSize,
      cursor: cursor,
      ordering: ordering,
      registerDateFrom: registerDateFrom,
      registerDateTo: registerDateTo,
      querySearch: querySearch,
      createdUser: createdUser,
      isDataPolicyAccept: isDataPolicyAccept,
    );
    return PagingModelDto<GetListEngineer>(
      next: response.data?.data.next,
      totalCount: response.data?.data.totalCount,
      previous: response.data?.data.previous,
      results: response.data?.data.results.toList() ?? [],
    );
  }

  Future<List<String>> getListRegistrar() async {
    final response = await _adminApi.getListRegistrar();
    return response.data?.data.toList() ?? [];
  }

  Future<void> deleteEngineers(DeleteEngineers data) async {
    await _adminApi.deleteEngineers(data: data);
  }

  Future<Response<GetEmailScheduleResponseModel>> getListOfEmailSchedules(
    int? pageSize,
    String? cursor,
    String? ordering,
    Date? dateFrom,
    Date? dateTo,
    String? querySearch,
    String? createdUser,
  ) async {
    final response = await _adminApi.getListOfEmailSchedules(
      pageSize: pageSize,
      cursor: cursor,
      ordering: ordering,
      dateFrom: dateFrom,
      dateTo: dateTo,
      querySearch: querySearch,
      createdUser: createdUser,
    );
    return response;
  }

  Future<Response<EmailSchedule>> createEmailSchedule(
    int type,
    String subject,
    String body,
    String sendTime,
    int isValid,
    String? weekday,
    DateTime? sendDatetime,
    int? isRepeat,
    String? targetEmail,
  ) async {
    final response =
        await _adminApi.createEmailSchedule(data: CreateEmailSchedule(
      (b) {
        b.type = type;
        b.subject = subject;
        b.body = body;
        b.sendTime = sendTime;
        b.sendDatetime = sendDatetime;
        b.weekday = weekday;
        b.isValid = isValid;
        b.isRepeat = isRepeat;
        b.targetEmail = targetEmail;
      },
    ));
    return response;
  }

  Future<Response<EmailSchedule>> updateEmailSchedule(
    String id,
    int type,
    String subject,
    String body,
    String sendTime,
    int isValid,
    String? weekday,
    DateTime? sendDatetime,
    int? isRepeat,
    String? targetEmail,
  ) async {
    final response = await _adminApi.updateEmailSchedule(
        id: id,
        data: CreateEmailSchedule(
          (b) {
            b.type = type;
            b.subject = subject;
            b.body = body;
            b.sendTime = sendTime;
            b.isValid = isValid;
            b.weekday = weekday;
            b.sendDatetime = sendDatetime;
            b.isRepeat = isRepeat;
            b.targetEmail = targetEmail;
          },
        ));
    return response;
  }

  Future<Response<ResponseModel>> deleteEmailSchedule(String id) async {
    final response = await _adminApi.deleteEmailSchedule(id: id);
    return response;
  }

  Future<Response<ResponseModel>> deleteListEmailSchedule(List<int> ids) async {
    final response = await _adminApi.deleteEmailScheduleList(
        emailScheduleIds: BuiltList<int>.from(ids));

    return response;
  }

  Future<Response<GetEmailDetailResponseModel>> getEmailScheduleById(
      String id) async {
    final response = await _adminApi.getEmailScheduleById(
      id: id,
    );
    return response;
  }

  Future<void> updateEngineer(int engineerId, UpdateEngineer data) async {
    await _adminApi.updateEngineer(
      userId: engineerId,
      data: data,
    );
  }

  Future<Uint8List?> exportUserData(ExportUserDataBody data) async {
    final response = await _adminApi.exportUserData(
      data: data,
    );
    return response.data;
  }
}
