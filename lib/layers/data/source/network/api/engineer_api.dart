import 'package:asiantech_link/layers/data/dto/paging_model_dto.dart';

import 'package:asiantech_link/layers/data/source/network/api.dart';
import 'package:built_collection/built_collection.dart';
import 'package:dio/dio.dart';
import 'package:openapi/openapi.dart';

class AppEngineerApi extends Api {
  late final EngineersApi _api = initOpenApi().getEngineersApi();

  Future<void> addAgencyCompany(
      {required EngineerUpdateAgencyCompany data}) async {
    await _api.addAgencyCompany(data: data);
  }

  Future<void> deleteAgencyCompany(
      {required EngineerRemoveAgencyCompany data}) async {
    await _api.deleteAgencyCompany(data: data);
  }

  Future<PagingModelDto<EngineerBestCompany>> getListBestCompanies({
    int? pageSize,
    String? cursor,
  }) async {
    final response = await _api.getBestCompanies(
      cursor: cursor,
      pageSize: pageSize,
    );
    final model = response.data?.data;
    return PagingModelDto<EngineerBestCompany>(
      next: model?.next,
      previous: model?.previous,
      results: model?.results.toList() ?? [],
    );
  }

  Future<Response<EngineerListAgencyCompanyResponseModel>>
      getListOfSelectAgencyCompanies({int? optionalEngineerId}) async {
    return await _api.getListOfSelectAgencyCompanies(
        optionalEngineerId: optionalEngineerId);
  }

  Future<Response<EngineerListAgencyCompanyResponseModel>>
      getListAgencyCompany() async {
    return await _api.getListAgencyCompany();
  }

  Future<Response<EngineerListApplyCompanyResponseModel>> getListApplyCompany({
    int? pageSize,
    String? cursor,
    String? ordering,
    int? applyStatusFilterCode,
  }) async {
    return await _api.getListApplyCompany(
      pageSize: pageSize,
      cursor: cursor,
      ordering: ordering,
      applyStatusFilterCode: applyStatusFilterCode,
    );
  }

  Future<Response<BoolResponseModel>> applyRecruit({
    required String recruitId,
    required String companyId,
  }) async {
    return await _api.applyRecruit(
      recruitId: recruitId,
      companyId: companyId,
    );
  }

  Future<void> cancelRecruit({required int id}) async {
    await _api.cancelRecruit(recruitId: id);
  }

  Future<Response<BoolResponseModel>> changeStatusInterested({
    required String recruitId,
  }) async {
    return await _api.changeStatusInterested(
      recruitId: recruitId,
    );
  }

  Future<Response<RecruitContractDetailsResponseModel>> getContractDetails(
      {required String recruitmentId}) async {
    return await _api.getContractDetails(recruitmentId: recruitmentId);
  }

  Future<Response<RecruitCountFilterResponseModel>> countFilter({
    String? name,
    String? employCode,
    BuiltList<String>? places,
    String? jobCode,
    BuiltList<String>? skillCodes1,
    BuiltList<String>? skillCodes2,
    BuiltList<String>? skillCodes3,
    String? payrollCode,
    String? payrollPriceFrom,
    String? payrollPriceTo,
    BuiltList<String>? languageCodes,
    required bool showOldPost,
    int? companyId,
    BuiltList<String>? jobCodes,
    bool? waitingFlag,
    String? remoteCode,
  }) async {
    final response = await _api.countFilter(
      name: name,
      employCode: employCode,
      places: places,
      jobCode: jobCode,
      skillCodes1: skillCodes1,
      skillCodes2: skillCodes2,
      skillCodes3: skillCodes3,
      payrollCode: payrollCode,
      payrollPriceFrom: payrollPriceFrom,
      payrollPriceTo: payrollPriceTo,
      languageCodes: languageCodes,
      showOldPost: showOldPost,
      companyId: companyId,
      jobCodes: jobCodes,
      waitingFlag: (waitingFlag ?? false) ? 1 : null,
      remoteCode: remoteCode,
    );
    return response;
  }

  Future<Response<RecruitExploreResponseModel>> listRecruitsExplore({
    int? pageSize,
    String? ordering,
    String? name,
    String? employCode,
    BuiltList<String>? places,
    String? jobCode,
    BuiltList<String>? skillCodes1,
    BuiltList<String>? skillCodes2,
    BuiltList<String>? skillCodes3,
    String? payrollCode,
    String? payrollPriceFrom,
    String? payrollPriceTo,
    BuiltList<String>? languageCodes,
    required bool showOldPost,
    int? companyId,
    BuiltList<String>? jobCodes,
    bool? waitingFlag,
    String? cursor,
    String? remoteCode,
  }) async {
    final response = await _api.listRecruitsExplore(
      pageSize: pageSize,
      ordering: ordering,
      name: name,
      employCode: employCode,
      places: places,
      jobCode: jobCode,
      skillCodes1: skillCodes1,
      skillCodes2: skillCodes2,
      skillCodes3: skillCodes3,
      payrollCode: payrollCode,
      payrollPriceFrom: payrollPriceFrom,
      payrollPriceTo: payrollPriceTo,
      languageCodes: languageCodes,
      showOldPost: showOldPost,
      companyId: companyId,
      jobCodes: jobCodes,
      waitingFlag: (waitingFlag ?? false) ? 1 : null,
      cursor: cursor,
      remoteCode: remoteCode,
    );
    return response;
  }

  Future<List<FeaturedJob>> getListFeaturedJobs() async {
    final response = await _api.getListFeaturedJobs();
    return response.data?.data.toList() ?? [];
  }

  Future<Response<RecruitmentManagementDetailsResponseModel>>
      getRecruitmentManagementDetail({required String recruitmentId}) async {
    final res =
        await _api.getRecruitmentManagementDetail(recruitmentId: recruitmentId);
    return res;
  }

  Future<void> updateRecruitProgress({required String id}) async {
    await _api.updateRecruitProgress(recruitmentId: id);
  }

  Future<void> updateInterviewDatetime(
      {required int id, required DateTime dateTime}) async {
    await _api.updateInterviewDatetime(
        recruitId: id,
        data: UpdateInterviewDatetime((b) {
          b.interviewDatetime = dateTime;
        }));
  }

  Future<void> updateDataPolicy(
      {required EngineerUpdateDataPolicy data}) async {
    await _api.updateDataPolicy(data: data);
  }

  Future<void> getUserAgencyCompany({required String userId}) async {
    await _api.getUserAgencyCompany(userId: userId);
  }
}
