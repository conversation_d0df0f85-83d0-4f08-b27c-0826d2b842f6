import 'dart:typed_data';

import 'package:asiantech_link/layers/data/source/network/api.dart';
import 'package:dio/dio.dart';
import 'package:openapi/openapi.dart';

class AppProfileApi extends Api {
  late final ProfileApi _api = initOpenApi().getProfileApi();

  Future<Response<UserDetailsResponseModel>> getProfile({
    int? optionalEngineerId,
  }) async {
    final response = await _api.getProfile(
      optionalEngineerId: optionalEngineerId,
    );
    return response;
  }

  Future<ResponseModel?> updatePassword(
      {required String oldPassword,
      required String newPassword,
      required String confirmPassword}) async {
    final response = await _api.updatePassword(data: UpdatePassword((obj) {
      obj.currentPassword = oldPassword;
      obj.newPassword = newPassword;
    }));
    return response.data;
  }

  Future<Response<UpdateUserResponseModel>> updateProfile(
      UpdateUser updateUser) async {
    final response = await _api.updateProfile(data: updateUser);
    return response;
  }

  Future<Response<SelfAssessmentResponseModel>> getRemoteWorkSkills() async {
    final response = await _api.getRemoteWorkSkills();
    return response;
  }

  Future<Response<ResponseModel>> saveRemoteWorkSkills(
      SelfAssessmentAnswer data) async {
    final response = await _api.saveRemoteWorkSkills(data: data);
    return response;
  }

  Future<Response<SelfAssessmentResponseModel>>
      getGlobalResponsivenessSkills() async {
    final response = await _api.getGlobalResponsivenessSkills();
    return response;
  }

  Future<Response<ResponseModel>> saveGlobalResponsivenessSkills(
      SelfAssessmentAnswer data) async {
    final response = await _api.saveGlobalResponsivenessSkills(data: data);
    return response;
  }

  Future<Response<SelfAssessmentResponseModel>>
      getCommunicationSkillsSelfAssessment() async {
    final response = await _api.getCommunicationSkillsSelfAssessment();
    return response;
  }

  Future<Response<ResponseModel>> saveCommunicationSkillsSelfAssessment(
      SelfAssessmentAnswer data) async {
    final response =
        await _api.saveCommunicationSkillsSelfAssessment(data: data);
    return response;
  }

  Future<Response<SelfAssessmentResponseModel>>
      getReportingConsultationSkillsSelfEvaluation() async {
    final response = await _api.getReportingConsultationSkillsSelfEvaluation();
    return response;
  }

  Future<Response<ResponseModel>> saveReportingConsultationSkillsSelfEvaluation(
      SelfAssessmentAnswer data) async {
    final response =
        await _api.saveReportingConsultationSkillsSelfEvaluation(data: data);
    return response;
  }

  Future<Response<SelfAssessmentResponseModel>>
      getProjectManagementSkillsSelfEvaluation() async {
    final response = await _api.getProjectManagementSkillsSelfEvaluation();
    return response;
  }

  Future<Response<ResponseModel>> saveProjectManagementSkillsSelfEvaluation(
      SelfAssessmentAnswer data) async {
    final response =
        await _api.saveProjectManagementSkillsSelfEvaluation(data: data);
    return response;
  }

  Future<void> saveCVUploaded({required SaveCVUploaded cvFile}) async {
    await _api.saveCVUploaded(data: cvFile);
  }

  Future<UploadCVUploadedResponse?> uploadCvPDF({
    required MultipartFile cvPdf,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final response = await _api.uploadCVPDF(
        cvPdf: cvPdf,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress);
    return response.data;
  }

  Future<Uint8List?> downloadCv() async {
    final response = await _api.downloadCv();
    return response.data;
  }
}
