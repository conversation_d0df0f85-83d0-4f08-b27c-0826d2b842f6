import 'package:asiantech_link/layers/data/dto/company_model_dto.dart';
import 'package:asiantech_link/layers/data/dto/paging_model_dto.dart';
import 'package:asiantech_link/layers/data/source/network/api.dart';
import 'package:asiantech_link/layers/domain/entity/company_model.dart';
import 'package:asiantech_link/layers/presentation/resources/constants/sort_constants.dart';
import 'package:asiantech_link/layers/presentation/utils/parse_openapi.dart';

import 'package:dio/dio.dart';
import 'package:openapi/openapi.dart';

class HostCompanyApi extends Api {
  late final HostCompaniesApi _hostCompanyApi =
      initOpenApi().getHostCompaniesApi();

  Future<void> subscribeSupportCompany({required String companyId}) async {
    await _hostCompanyApi.subscribeSupportCompany(
        data: HostCompanySubscribeSupportCompany((b) {
      b.supportCompanyId = companyId;
    }));
  }

  Future<void> unSubscribeSupportCompany({required String companyId}) async {
    await _hostCompanyApi.unsubscribeSupportCompany(
        data: HostCompanyUnsubscribeSupportCompany((b) {
      b.supportCompanyId = companyId;
    }));
  }

  Future<List<CompanyModel>> getSupportCompany({String? email}) async {
    final response = await _hostCompanyApi.getListSupportCompany(email: email);
    var data = ParseOpenapi.parseJsonFromObject(
        response.data, HostCompanyListSupportCompanyResponseModel.serializer);
    return (data['data'] as List)
        .map((e) => CompanyModelDto.fromMap(e))
        .toList();
  }

  Future<List<HostCompanyGetSupportCompany>>
      getSupportCompanySubscribed() async {
    final response = await _hostCompanyApi.getSupportCompanySubscribed();

    return response.data?.data.toList() ?? [];
  }

  Future<int?> requestInterview(
      {required HostCompanyRequestInterview data}) async {
    final response = await _hostCompanyApi.requestInterview(data: data);
    return response.data?.data.applyId;
  }

  Future<void> updateApplyAdmission(
      {required HostCompanyUpdateInterviewAdmission data}) async {
    await _hostCompanyApi.updateApplyAdmission(data: data);
  }

  Future<void> createRecruit(
      {required CreateRecruitment createRecruitment}) async {
    await _hostCompanyApi.createRecruitment(data: createRecruitment);
  }

  Future<CompanyRecruitDetail?> getCompanyRecruitmentDetails(
      {required String id}) async {
    try {
      final response = await _hostCompanyApi.getCompanyRecruitmentDetails(
        recruitmentId: id,
      );
      var data = response.data?.data;
      return data;
    } catch (e) {
      return null;
    }
  }

  Future<PagingModelDto<RecruitUploadedSerializers>> getMyRecruits({
    String? cursor,
    int? pageSize,
    required bool showOldPost,
    bool? showNewest,
    int? engineerId,
    int? displayFlag,
  }) async {
    final response = await _hostCompanyApi.listRecruitsUploaded(
      cursor: cursor,
      pageSize: pageSize,
      showOldPost: showOldPost,
      ordering: showNewest == true
          ? SortConstants.createdDESC
          : SortConstants.createdASC,
      engineerId: engineerId,
      displayFlag: displayFlag,
    );

    final model = response.data?.data;
    if (model != null) {
      return PagingModelDto<RecruitUploadedSerializers>(
        next: model.next,
        previous: model.previous,
        results: model.results.toList(),
        totalCount: model.totalCount,
      );
    }
    return const PagingModelDto<RecruitUploadedSerializers>(
      next: null,
      previous: null,
      results: [],
    );
  }

  Future<void> updateCompanyRecruitmentDetails(
      {required String id, required UpdateRecruitment data}) async {
    await _hostCompanyApi.updateCompanyRecruitmentDetails(
        recruitmentId: id, data: data);
  }

  Future<void> deleteRecruit({required String id}) async {
    await _hostCompanyApi.deleteRecruitment(data: DeleteRecruitment((b) {
      b.recruitId = id;
    }));
  }

  Future<Response<RecruitmentDetailsResponseModel>> getRecruitDetails(
      {required String id}) async {
    final response = await _hostCompanyApi.getRecruitmentDetails(
      recruitmentId: id,
    );
    return response;
  }
}
