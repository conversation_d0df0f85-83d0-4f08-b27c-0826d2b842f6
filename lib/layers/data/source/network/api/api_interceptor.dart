import 'package:asiantech_link/app.dart';
import 'package:asiantech_link/layers/data/source/local/local_storage/jwt_local_storage.dart';
import 'package:asiantech_link/layers/data/source/local/local_storage_service.dart';
import 'package:asiantech_link/layers/data/source/network/api_url.dart';
import 'package:asiantech_link/layers/presentation/resources/app_constants.dart';
import 'package:asiantech_link/layers/presentation/screens/general/authentication/email_verify/email_verify_screen.dart';
import 'package:asiantech_link/layers/presentation/screens/general/authentication/forgot_password/forgot_password_screen.dart';
import 'package:asiantech_link/layers/presentation/screens/general/authentication/login/login_screen.dart';
import 'package:asiantech_link/layers/presentation/screens/general/authentication/password_reset_confirm/password_reset_confirm.dart';
import 'package:asiantech_link/layers/presentation/screens/general/authentication/register_complete_screen/register_complete_screen.dart';
import 'package:asiantech_link/layers/presentation/screens/general/authentication/register_screen/register_screen.dart';
import 'package:asiantech_link/layers/presentation/screens/general/authentication/two_step_auth/two_step_auth_screen.dart';
import 'package:asiantech_link/layers/presentation/screens/general/authentication/whatsapp/input_number_whatsapp_screen.dart';
import 'package:asiantech_link/layers/presentation/screens/general/authentication/whatsapp/two_step_whats_app_screen.dart';
import 'package:asiantech_link/layers/presentation/screens/general/common/error_screen.dart';
import 'package:asiantech_link/layers/presentation/screens/general/common/internal_server_error_screen.dart';
import 'package:asiantech_link/layers/presentation/screens/general/common/not_found_screen.dart';
import 'package:asiantech_link/layers/presentation/screens/general/setting_screen/setting_screen.dart';
import 'package:asiantech_link/layers/presentation/screens/general/setting_screen/update_language/update_language_screen.dart';
import 'package:asiantech_link/layers/presentation/services/auth_services.dart';
import 'package:asiantech_link/layers/presentation/utils/helper.dart';
import 'package:asiantech_link/routes/engineers_router.dart';
import 'package:asiantech_link/routes/host_support_agencies_router.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../../presentation/utils/log.dart' as log_app;

final Map<String, CancelToken> _ongoingRequests = {};
final List<String> whiteListHandleCancelToken = [
  "/api/engineers/recruit/explore/",
  "/api/engineers/recruit/count-filter/",
];
final List<String> whiteListHandleRoute401 = [
  "/${NotFoundScreen.routeName}",
  "/${InternalServerErrorScreen.routeName}",
  "/${ErrorScreen.routeName}",
  "/${LoginScreen.routeName}",
  "/${TwoStepAuthScreen.routeName}",
  "/${UpdateLanguageScreen.routeName}",
  "/${EngineersRouter.routeName}",
  "/${InputNumberWhatsAppScreen.routeName}",
  "/${EmailVerifyScreen.routeName}",
  "/${RegisterScreen.routeName}",
  "/${RegisterCompleteScreen.routeName}",
  "/${ForgotPasswordScreen.routeName}",
  "/${PasswordResetConfirm.routeName}",
  "/${SettingScreen.routeName}",
  "/${TwoStepWhatsAppScreen.routeName}",
  "/${HostSupportAgenciesRouter.routeName}",
];

final Map<String, int> _retryCounts = {};
const int maxRetries = 3;

class ApiInterceptor extends Interceptor {
  static final JWTLocalStorage _jwtLocalStorage = JWTLocalStorage();
  final LocalStorageService _localStorageService = LocalStorageService();
  @override
  void onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    // Add authorization token
    options.headers['authorization'] = "Bearer ${await getAuthToken()}";

    // Add any other common headers
    options.headers['Accept-Language'] = getCurrentLocale();

    // Add currency config
    options.headers.addAll(getCurrencyConfig());

    // Add cancel token
    handleCancelToken(options);

    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    log_app.log("🐛 response api: ${response.data}");
    final uri = Uri.parse(response.requestOptions.path);
    if (whiteListHandleCancelToken.contains(uri.path)) {
      // Remove the completed request from the map
      _ongoingRequests.remove(uri.path);
    }
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    var accessToken = _jwtLocalStorage.getAccessToken();
    final url = err.requestOptions.uri.toString();
    final uri = Uri.parse(url);
    final method = err.requestOptions.method;

    // Log error information
    log_app.log("🐛 error at api: $url");

    // Check if the error is due to token expired
    if (err.response?.statusCode == 401 && (accessToken?.isNotEmpty ?? false)) {
      await _jwtLocalStorage.clearLocalUserInfo();
    }

    // Check if the error is due to cancellation
    if (err.type == DioExceptionType.cancel) {
      _ongoingRequests.remove(uri.path);
      return; // Do not call handler.next(err) to prevent further propagation
    }

    // Handle specific API paths
    if (whiteListHandleCancelToken.contains(uri.path)) {
      processResponse(err.response?.statusCode ?? 0);
      _ongoingRequests.remove(uri.path);
    }

    // Handle GET request errors with navigation
    var context = globalContext;
    if (method == 'GET' && err.response?.statusCode != 401 && context != null) {
      final path = uri.path;

      // Handle server error (500) or connection error
      if ((err.response?.statusCode == 500 ||
              (err.response?.statusCode == 502 &&
                  err.type == DioExceptionType.badResponse) ||
              err.type == DioExceptionType.connectionError) &&
          path != "/${InternalServerErrorScreen.routeName}") {
        // Increment retry count for this endpoint
        _retryCounts[url] = (_retryCounts[url] ?? 0) + 1;

        // If we've reached max retries, show connection error popup
        if (_retryCounts[url]! >= maxRetries) {
          _retryCounts.remove(url); // Reset retry count
          return;
        }

        // If not at max retries, try again
        if (context.mounted) {
          Router.neglect(context, () {
            globalContext?.push("/${InternalServerErrorScreen.routeName}");
          });
        }
      }
    }

    super.onError(err, handler);
  }

  /// Process response status code
  void processResponse(int statusCode) {
    // Token die return to login screen
    if (statusCode == 401) {
      popToLogin();
    }
  }

  void popToLogin() async {
    final context = globalContext;
    if (context != null) {
      if (!isOpenLoginBottomSheet) {
        AuthService.showLoginBottomSheet(context);
        isOpenLoginBottomSheet = true;
      }
    }
  }

  Future<String?> refreshToken() async {
    log_app.log("🐛 Token expired, try to refresh!");
    try {
      var url = APIUrl.refreshJWT;

      var refreshToken = _jwtLocalStorage.getRefreshToken();
      final response = await Dio().request(url,
          options: Options(
            method: "POST",
          ),
          data: {
            "refresh": refreshToken,
          });
      var data = response.data;
      var accessToken = data['access'];
      refreshToken = data['refresh'];
      final userType = data['user_type'];
      await _jwtLocalStorage.persistToken(
          accessToken: accessToken ?? "",
          refreshToken: refreshToken ?? "",
          userType: userType ?? "");
      return accessToken;
    } catch (e) {
      return null;
    }
  }

  Future<String?> getAuthToken() async {
    try {
      var accessToken = _jwtLocalStorage.getAccessToken();
      bool isExpired = _jwtLocalStorage.isTokenExpired(accessToken ?? "");
      if (isExpired) {
        accessToken = await refreshToken();
        return accessToken;
      } else {
        return accessToken;
      }
    } catch (e) {
      return null;
    }
  }

  String? getCurrentLocale() {
    return _localStorageService.getString(AppConstants.languageKey);
  }

  Map<String, String> getCurrencyConfig() {
    return {
      AppConstants.engineerCurrencyKey:
          _localStorageService.getString(AppConstants.engineerCurrencyKey) ??
              "",
      AppConstants.hostCompanyCurrencyKey:
          _localStorageService.getString(AppConstants.hostCompanyCurrencyKey) ??
              "",
      AppConstants.hostSupportAgencyCurrencyKey: _localStorageService
              .getString(AppConstants.hostSupportAgencyCurrencyKey) ??
          "",
      AppConstants.otherSupportAgencyCurrencyKey: _localStorageService
              .getString(AppConstants.otherSupportAgencyCurrencyKey) ??
          "",
      AppConstants.referralAgencyCurrencyKey: _localStorageService
              .getString(AppConstants.referralAgencyCurrencyKey) ??
          "",
      AppConstants.adminCurrencyKey:
          _localStorageService.getString(AppConstants.adminCurrencyKey) ?? "",
    };
  }

  handleCancelToken(RequestOptions options) {
    final uri = Uri.parse(options.path);
    if (whiteListHandleCancelToken.contains(uri.path)) {
      // Cancel the previous request if it exists
      _ongoingRequests[uri.path]?.cancel();

      // Create a new CancelToken for the new request
      final cancelToken = CancelToken();
      _ongoingRequests[uri.path] = cancelToken;
      options.cancelToken = cancelToken;
    }
  }
}
