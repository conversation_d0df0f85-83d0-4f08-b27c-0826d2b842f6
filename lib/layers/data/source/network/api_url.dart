import 'package:asiantech_link/layers/presentation/env/env.dart';

class APIUrl {
  static final String domain = AppConfig().baseUrl ?? "";
  static final String chatDomain = AppConfig().chatUrl ?? "";

  static const String fontUrl =
      "https://fonts.gstatic.com/ea/notosansjapanese/v6/NotoSansJP-Regular.otf";
  static final String refreshJWT = "$domain/token/refresh/";

  //chat
  static final String createGroup = '$chatDomain/chat/create-group';
  static final String leaveGroup = '$chatDomain/chat/leave-group';
  static final String groupDetails = '$chatDomain/chat/group-detail/{group_id}';
  static final String sendMessage = '$chatDomain/chat/send-message';
  static final String messages = '$chatDomain/chat/{group_id}/messages';
  static final String updateLastMessage =
      '$chatDomain/chat/{group_id}/update-last-message';
  static final String chatGroups = '$chatDomain/chat/groups';

  // Zalo
  static const String zaloOAuth = 'https://oauth.zaloapp.com/v4/permission';
  static const String zaloAccessToken =
      'https://oauth.zaloapp.com/v4/access_token';
  static const String zaloInfo = 'https://graph.zalo.me/v2.0/me';

  // LinkedIn
  static const String linkedinOAuth =
      'https://www.linkedin.com/oauth/v2/authorization';
  // Facebook
  static const String facebookOAuth =
      'https://www.facebook.com/v12.0/dialog/oauth';
}
