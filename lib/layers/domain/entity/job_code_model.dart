class JobCodeModel {
  List<JobCode>? jobCode;

  JobCodeModel({this.jobCode});
}

class JobCode {
  String? id;
  String? nameJp;
  String? nameEn;
  String? nameVn;
  bool isChoose;

  JobCode(
      {this.id, this.nameJp, this.nameEn, this.nameVn, this.isChoose = false});

  JobCode clone() {
    return JobCode(
        id: id,
        nameJp: nameJp,
        nameEn: nameEn,
        nameVn: nameVn,
        isChoose: isChoose);
  }

  JobCode changeChoose() {
    return JobCode(
        id: id,
        nameJp: nameJp,
        nameEn: nameEn,
        nameVn: nameVn,
        isChoose: !isChoose);
  }
}
