import 'package:openapi/openapi.dart';

class EditEducationModel {
  final UpdateEngAcademic education;
  final bool isExpanded;

  EditEducationModel({
    required this.education,
    required this.isExpanded,
  });

  changeExpanded() {
    return EditEducationModel(
      education: education,
      isExpanded: !isExpanded,
    );
  }

  changeEducation(UpdateEngAcademic education) {
    return EditEducationModel(
      education: education,
      isExpanded: isExpanded,
    );
  }

  changeSchoolName(String name) {
    return EditEducationModel(
      education: education.rebuild((b) => b.school = name),
      isExpanded: isExpanded,
    );
  }

  changeFacultyName(String name) {
    return EditEducationModel(
      education: education.rebuild((b) => b.faculty = name),
      isExpanded: isExpanded,
    );
  }
}
