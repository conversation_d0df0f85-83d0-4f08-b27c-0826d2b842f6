import 'package:asiantech_link/layers/domain/entity/company_model.dart';
import 'package:equatable/equatable.dart';

class RecruitModel extends Equatable {
  final int? recruitId;
  final CompanyModel? supportCompany;
  final int? saveType;
  final String? title;
  final String? catchCopy;
  final String? startDate;
  final String? endDate;
  final int? displayFlag;
  final String? jobCode;
  final String? employCode;
  final String? content;
  final String? placeCode1;
  final String? placeCode2;
  final String? placeCode3;
  final String? payrollCode;
  final double? payrollPriceFrom;
  final double? payrollPriceTo;
  final String? countryCode;
  final int? ageFrom;
  final int? ageTo;
  final int? sexType;
  final String? prefCode1;
  final String? prefCode2;
  final String? prefCode3;
  final String? lastAcademicCode;
  final String? languageCode1;
  final int? languageLevelType1;
  final String? languageCode2;
  final int? languageLevelType2;
  final String? experiencedJobCode;
  final int? yearsOfExperience;
  final String? skillJobCode1;
  final String? skillCode1;
  final int? skillLevelType1;
  final String? skillJobCode2;
  final String? skillCode2;
  final int? skillLevelType2;
  final String? skillJobCode3;
  final String? skillCode3;
  final int? skillLevelType3;
  final int? createAgentId;
  final String? created;
  final int? updateAgentId;
  final String? updated;
  final String? licenceCode1;
  final int? licencePoint1;
  final String? licenceCode2;
  final int? licencePoint2;
  final String? licenceCode3;
  final int? licencePoint3;
  final CompanyModel? hostCompany;
  final int? hostAgent;
  final int? supportAgent;

  const RecruitModel({
    this.recruitId,
    this.supportCompany,
    this.saveType,
    this.title,
    this.catchCopy,
    this.startDate,
    this.endDate,
    this.displayFlag,
    this.jobCode,
    this.employCode,
    this.content,
    this.placeCode1,
    this.placeCode2,
    this.placeCode3,
    this.payrollCode,
    this.payrollPriceFrom,
    this.payrollPriceTo,
    this.countryCode,
    this.ageFrom,
    this.ageTo,
    this.sexType,
    this.prefCode1,
    this.prefCode2,
    this.prefCode3,
    this.lastAcademicCode,
    this.languageCode1,
    this.languageLevelType1,
    this.languageCode2,
    this.languageLevelType2,
    this.experiencedJobCode,
    this.yearsOfExperience,
    this.skillJobCode1,
    this.skillCode1,
    this.skillLevelType1,
    this.skillJobCode2,
    this.skillCode2,
    this.skillLevelType2,
    this.skillJobCode3,
    this.skillCode3,
    this.skillLevelType3,
    this.createAgentId,
    this.created,
    this.updateAgentId,
    this.updated,
    this.licenceCode1,
    this.licencePoint1,
    this.licenceCode2,
    this.licencePoint2,
    this.licenceCode3,
    this.licencePoint3,
    this.hostCompany,
    this.hostAgent,
    this.supportAgent,
  });

  @override
  List<Object?> get props => [
        recruitId,
        supportCompany,
        saveType,
        title,
        catchCopy,
        startDate,
        endDate,
        displayFlag,
        jobCode,
        employCode,
        content,
        placeCode1,
        placeCode2,
        placeCode3,
        payrollCode,
        payrollPriceFrom,
        payrollPriceTo,
        countryCode,
        ageFrom,
        ageTo,
        sexType,
        prefCode1,
        prefCode2,
        prefCode3,
        lastAcademicCode,
        languageCode1,
        languageLevelType1,
        languageCode2,
        languageLevelType2,
        experiencedJobCode,
        yearsOfExperience,
        skillJobCode1,
        skillCode1,
        skillLevelType1,
        skillJobCode2,
        skillCode2,
        skillLevelType2,
        skillJobCode3,
        skillCode3,
        skillLevelType3,
        createAgentId,
        created,
        updateAgentId,
        updated,
        licenceCode1,
        licencePoint1,
        licenceCode2,
        licencePoint2,
        licenceCode3,
        licencePoint3,
        hostCompany,
        hostAgent,
        supportAgent,
      ];
}
