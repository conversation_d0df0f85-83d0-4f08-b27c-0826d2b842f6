import '../../data/dto/employ_code_model_dto.dart';

class EmployCodeModel {
  List<EmployCode> employCode;

  EmployCodeModel({required this.employCode});
}

class EmployCode {
  String? id;
  String? nameJp;
  String? nameEn;
  String? nameVn;
  bool isChoose;

  EmployCode(
      {this.id, this.nameJp, this.nameEn, this.nameVn, this.isChoose = false});

  EmployCodeDto changeChoose() {
    return EmployCodeDto(
      id: id,
      nameJp: nameJp,
      nameEn: nameEn,
      nameVn: nameVn,
      isChoose: !isChoose,
    );
  }

  EmployCodeDto changeFalseAll() {
    return EmployCodeDto(
      id: id,
      nameJp: nameJp,
      nameEn: nameEn,
      nameVn: nameVn,
      isChoose: false,
    );
  }
}
