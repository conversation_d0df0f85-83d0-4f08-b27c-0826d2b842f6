import 'package:asiantech_link/layers/presentation/resources/enum.dart';
import 'package:asiantech_link/layers/presentation/utils/extensions.dart';
import 'package:built_collection/built_collection.dart';
import 'package:openapi/openapi.dart';

class ProfileWithWeight {
  final String? profileImagePath;

  final String? firstName;
  final int firstNameWeight;
  final String? lastName;
  final int lastNameWeight;
  final int? sexType;
  final int sexTypeWeight;
  final Date? birthDate;
  final int birthDateWeight;
  final String? countryCode;
  final int countryCodeWeight;
  final String? tel;
  final int telWeight;
  final String? addressCode;
  final int addressCodeWeight;
  final String? cityName;
  final int cityNameWeight;
  final String? passportNumber;
  final int passportNumberWeight;
  final String? passportImagePath;
  final int passportImagePathWeight;
  final List<Education>? educations;
  final int educationsWeight;
  final List<Language>? languages;
  final int languagesWeight;
  final List<Qualification>? qualifications;
  final int qualificationsWeight;
  final List<Experience>? experiences;
  final int experiencesWeight;
  final List<Skill>? skills;
  final int skillsWeight;
  final EngHope? requirements;
  final int requirementsWeight;
  final String? pr;
  final int prWeight;
  final String? selfIntroductionUrl;
  final int selfIntroductionUrlWeight;
  final String? professionalSummary;
  final int professionalSummaryWeight;
  final List<EngHighLightProject>? highlightProjects;
  final int highlightProjectsWeight;

  ProfileWithWeight({
    this.profileImagePath,
    this.firstName,
    this.firstNameWeight = 1,
    this.lastName,
    this.lastNameWeight = 1,
    this.sexType,
    this.sexTypeWeight = 1,
    this.birthDate,
    this.birthDateWeight = 1,
    this.countryCode,
    this.countryCodeWeight = 1,
    this.tel,
    this.telWeight = 1,
    this.addressCode,
    this.addressCodeWeight = 1,
    this.cityName,
    this.cityNameWeight = 1,
    this.passportNumber,
    this.passportNumberWeight = 1,
    this.passportImagePath,
    this.passportImagePathWeight = 1,
    this.educations,
    this.educationsWeight = 1,
    this.languages,
    this.languagesWeight = 1,
    this.qualifications,
    this.qualificationsWeight = 1,
    this.experiences,
    this.experiencesWeight = 1,
    this.skills,
    this.skillsWeight = 1,
    this.requirements,
    this.requirementsWeight = 4,
    this.pr,
    this.prWeight = 1,
    this.selfIntroductionUrl,
    this.selfIntroductionUrlWeight = 1,
    this.professionalSummary,
    this.professionalSummaryWeight = 1,
    this.highlightProjects,
    this.highlightProjectsWeight = 1,
  });

  int calculateTotalWeight() {
    int processWeight = 0;
    int totalWeight = 0;
    final educations = this.educations;
    final languages = this.languages;
    final qualifications = this.qualifications;
    final experiences = this.experiences;
    final skills = this.skills;
    if (firstName != null) processWeight += firstNameWeight;
    if (lastName != null) processWeight += lastNameWeight;
    if (sexType != null) processWeight += sexTypeWeight;
    if (birthDate != null) processWeight += birthDateWeight;
    if (countryCode != null) processWeight += countryCodeWeight;
    if (tel != null) processWeight += telWeight;
    if (addressCode != null) processWeight += addressCodeWeight;
    if (cityName != null) processWeight += cityNameWeight;
    if (passportNumber != null) processWeight += passportNumberWeight;
    if (passportImagePath != null) processWeight += passportImagePathWeight;
    if (educations != null && educations.isNotEmpty) {
      processWeight += educationsWeight;
    }
    if (languages != null && languages.isNotEmpty) {
      processWeight += languagesWeight;
    }
    if (qualifications != null && qualifications.isNotEmpty) {
      processWeight += qualificationsWeight;
    }
    if (skills != null && skills.isNotEmpty) {
      processWeight += skillsWeight;
    }

    if (experiences != null && experiences.isNotEmpty) {
      processWeight += experiencesWeight;
    }
    if (requirements != null) {
      if (requirements?.jobSkills != null) {
        processWeight += 1;
      }
      if (requirements?.payrollCode != null) {
        processWeight += 1;
      }
      if (requirements?.employCode != null) {
        processWeight += 1;
      }
      if (requirements?.placeCode1 != null ||
          requirements?.placeCode2 != null ||
          requirements?.placeCode3 != null) {
        processWeight += 1;
      }
    }
    if (pr != null) processWeight += prWeight;
    if (selfIntroductionUrl != null) processWeight += selfIntroductionUrlWeight;
    if (professionalSummary != null) {
      processWeight += professionalSummaryWeight;
    }
    if (highlightProjects?.isNotEmpty ?? false) {
      processWeight += highlightProjectsWeight;
    }

    totalWeight = firstNameWeight +
        lastNameWeight +
        sexTypeWeight +
        birthDateWeight +
        countryCodeWeight +
        telWeight +
        addressCodeWeight +
        cityNameWeight +
        passportNumberWeight +
        passportImagePathWeight +
        educationsWeight +
        languagesWeight +
        qualificationsWeight +
        skillsWeight +
        experiencesWeight +
        requirementsWeight +
        prWeight +
        selfIntroductionUrlWeight +
        professionalSummaryWeight +
        highlightProjectsWeight;
    return (processWeight / totalWeight * 100).round();
  }

  bool checkCompleteBasicInformation() {
    if (firstName == null) return false;
    if (lastName == null) return false;
    if (sexType == null) return false;
    if (birthDate == null) return false;
    if (countryCode == null) return false;
    if (tel == null) return false;
    if (addressCode == null) return false;
    if (cityName == null) return false;
    // if (passportNumber == null) return false;
    // if (passportImagePath == null) return false;
    return true;
  }

  bool checkCompleteEducation() {
    final educations = this.educations;
    if (educations != null && educations.isNotEmpty) {
      return true;
    }
    return false;
  }

  bool checkCompleteLanguages() {
    final languages = this.languages;
    if (languages != null && languages.isNotEmpty) {
      return true;
    }
    return false;
  }

  bool checkCompleteQualification() {
    final qualifications = this.qualifications;
    if (qualifications != null && qualifications.isNotEmpty) {
      return true;
    }
    return false;
  }

  bool checkCompleteExperiences() {
    if (experiences?.isEmpty ?? true) {
      return false;
    }
    for (final experience in experiences ?? <Experience>[]) {
      if (experience.companyName == null) return false;
      if (experience.enteringDate == null) return false;
      if (experience.quittingDate == null &&
          experience.careerType == CareerType.retire.value) {
        return false;
      }
      if (experience.engineerId == null) return false;
    }
    return true;
  }

  bool checkCompleteSkills() {
    if (skills?.isEmpty ?? true) {
      return false;
    }
    for (final skill in skills ?? <Skill>[]) {
      if (skill.skillCode == null) return false;
      if (skill.levelType == null) return false;
    }
    return true;
  }

  bool checkCompleteRequirements() {
    if (requirements == null) {
      return false;
    }
    if (requirements != null) {
      for (final jobSkill in requirements?.jobSkills ?? <HopeJobSkill>[]) {
        if (jobSkill.jobCode == null) return false;
        if (jobSkill.skillCode == null) return false;
      }
      if (requirements?.employCode == null) return false;
      if (requirements?.placeCode1 == null) return false;
      if (requirements?.payrollPrice == null) return false;
      if (requirements?.payrollCode == null) return false;
    }
    return true;
  }

  bool checkCompleteAppealPoint() {
    if (pr == null) return false;
    if (selfIntroductionUrl == null) return false;
    return true;
  }

  bool checkCompleteProfessionalSummary() {
    if (professionalSummary == null || professionalSummary == "") return false;
    return true;
  }

  bool checkCompleteHighlightProjects() {
    if (highlightProjects?.isEmpty ?? true) {
      return false;
    }
    return true;
  }

  factory ProfileWithWeight.fromProfile(UserDetailsSerializers profile) =>
      ProfileWithWeight(
        profileImagePath: profile.profileImagePath,
        firstName: profile.firstName,
        lastName: profile.lastName,
        sexType: profile.sexType,
        birthDate: profile.birthDate,
        countryCode: profile.countryCode,
        tel: profile.tel,
        addressCode: profile.addressCode,
        cityName: profile.cityName,
        passportNumber: profile.passportNumber,
        passportImagePath: profile.passportImagePath,
        educations: profile.educations
            ?.map((education) => Education(
                  engineerId: education.engineerId,
                  school: education.school,
                  type: education.type,
                  outDate: education.outDate,
                  faculty: education.faculty,
                ))
            .toList(),
        languages: profile.languages
            ?.map((language) => Language(
                  engineerId: language.engineerId,
                  languageLevelType: language.languageLevelType,
                  languageId: language.languageId,
                  languageCode: language.languageCode,
                ))
            .toList(),
        qualifications: profile.qualifications
            ?.map((qualification) => Qualification(
                  engineerId: qualification.engineerId,
                  licenceCode: qualification.licenceCode,
                  getDate: qualification.getDate,
                ))
            .toList(),
        skills: profile.skills
            ?.map((skill) => Skill(
                  engineerId: skill.engineerId,
                  skillCode: skill.skillCode,
                  levelType: skill.levelType?.toString(),
                  skillId: skill.skillId,
                ))
            .toList(),
        experiences: profile.experiences
            ?.map((experience) => Experience(
                  engineerId: experience.engineerId,
                  companyName: experience.companyName,
                  jobSkill: experience.careerJobSkills,
                  enteringDate: experience.enteringDate,
                  quittingDate: experience.quittingDate,
                  careerType: experience.careerType,
                ))
            .toList(),
        requirements: profile.requirements,
        pr: profile.pr,
        selfIntroductionUrl: profile.selfIntroductionUrl,
        professionalSummary: profile.professionalSummary,
        highlightProjects: profile.highlightProjects?.toList(),
      );
}

class Education {
  final int? engineerId;
  final String? school;
  final int? type;
  final Date? outDate;
  final String? faculty;

  Education({
    this.engineerId,
    this.school,
    this.type,
    this.outDate,
    this.faculty,
  });
}

class Language {
  final int? engineerId;
  final int? languageLevelType;
  final int? languageId;
  final String? languageCode;

  Language({
    this.engineerId,
    this.languageLevelType,
    this.languageId,
    this.languageCode,
  });
}

class Qualification {
  final int? engineerId;
  final String? licenceCode;
  final String? getDate;

  Qualification({
    this.engineerId,
    this.licenceCode,
    this.getDate,
  });
}

class Skill {
  final int? engineerId;
  final String? skillCode;
  final String? levelType;
  final int? skillId;

  Skill({
    this.engineerId,
    this.skillCode,
    this.levelType,
    this.skillId,
  });
}

class Experience {
  final int? engineerId;
  final String? companyName;
  final BuiltList<EngCareerJobSkill>? jobSkill;
  final int? careerType;
  final Date? enteringDate;
  final Date? quittingDate;

  Experience({
    this.engineerId,
    this.companyName,
    this.careerType,
    this.jobSkill,
    this.enteringDate,
    this.quittingDate,
  });
}

class Requirement {
  final int? engineerId;
  final String? jobCode1;
  final String? jobCode2;
  final String? jobCode3;
  final String? employCode;
  final String? placeCode1;
  final String? placeCode2;
  final String? placeCode3;
  final double? payrollPrice;

  Requirement({
    this.engineerId,
    this.jobCode1,
    this.jobCode2,
    this.jobCode3,
    this.employCode,
    this.placeCode1,
    this.placeCode2,
    this.placeCode3,
    this.payrollPrice,
  });
}
