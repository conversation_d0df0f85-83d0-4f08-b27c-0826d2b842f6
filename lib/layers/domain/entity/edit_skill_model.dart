import 'package:openapi/openapi.dart';

class EditSkillModel {
  final UpdateEngSkill skill;
  final bool isExpanded;
  final String? selectJobCode;

  EditSkillModel({
    required this.skill,
    required this.isExpanded,
    this.selectJobCode,
  });

  changeExpanded() {
    return EditSkillModel(
      skill: skill,
      isExpanded: !isExpanded,
      selectJobCode: selectJobCode,
    );
  }

  changeSkill(UpdateEngSkill skill) {
    return EditSkillModel(
      skill: skill,
      isExpanded: isExpanded,
      selectJobCode: selectJobCode,
    );
  }

  changeJobCode(String? selectJobCode) {
    return EditSkillModel(
      skill: skill,
      isExpanded: isExpanded,
      selectJobCode: selectJobCode,
    );
  }

  changeYearOfExperience(int yearOfExperience) {
    return EditSkillModel(
      skill: skill.rebuild((b) => b.levelType = yearOfExperience),
      isExpanded: isExpanded,
      selectJobCode: selectJobCode,
    );
  }
}
