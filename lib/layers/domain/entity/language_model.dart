class LanguageModel {
  LanguageModel({
    required this.languageCode,
  });

  final List<LanguageCode> languageCode;
}

class LanguageCode {
  LanguageCode({
    required this.selectionListName,
    required this.code,
    required this.nameJp,
    required this.nameEn,
    required this.nameVn,
    this.isChoose = false,
  });

  final String selectionListName;
  final String code;
  final String nameJp;
  final String nameEn;
  final String nameVn;
  final bool isChoose;

  LanguageCode clone() {
    return LanguageCode(
        selectionListName: selectionListName,
        code: code,
        nameJp: nameJp,
        nameEn: nameEn,
        nameVn: nameVn,
        isChoose: isChoose);
  }

  LanguageCode changeChoose() {
    return LanguageCode(
        selectionListName: selectionListName,
        code: code,
        nameJp: nameJp,
        nameEn: nameEn,
        nameVn: nameVn,
        isChoose: !isChoose);
  }
}
