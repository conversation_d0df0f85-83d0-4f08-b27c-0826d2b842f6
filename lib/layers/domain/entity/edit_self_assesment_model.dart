import 'package:openapi/openapi.dart';

class EditSelfAssesmentModel {
  final UpdateEngSelfAssesment data;

  EditSelfAssesmentModel({required this.data});

  factory EditSelfAssesmentModel.fromEngSelfAssesment(EngSelfAssesment data) {
    UpdateEngSelfAssesment? updateEngSelfAssesment =
        serializers.deserializeWith(
      UpdateEngSelfAssesment.serializer,
      serializers.serializeWith(EngSelfAssesment.serializer, data),
    );

    return EditSelfAssesmentModel(
        data: updateEngSelfAssesment ?? UpdateEngSelfAssesment());
  }

  changeValue(UpdateEngSelfAssesment language) {
    return EditSelfAssesmentModel(data: language);
  }
}
