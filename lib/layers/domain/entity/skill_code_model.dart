class SkillCodeModel {
  List<SkillCode> skillCode;

  SkillCodeModel({required this.skillCode});
}

class SkillCode {
  String? id;
  String? name;
  String? type;
  String? categoryId;
  bool isChoose;
  int? yearsOfExperience;

  SkillCode({
    this.id,
    this.name,
    this.type,
    this.categoryId,
    this.yearsOfExperience,
    this.isChoose = false,
  });

  SkillCode clone() {
    return SkillCode(
      id: id,
      name: name,
      type: type,
      yearsOfExperience: yearsOfExperience,
      categoryId: categoryId,
      isChoose: isChoose,
    );
  }
  
  SkillCode getDefault() {
    return SkillCode(
      id: id,
      name: name,
      type: type,
      yearsOfExperience: yearsOfExperience,
      categoryId: categoryId,
      isChoose: false,
    );
  }

  changeChoose() {
    return SkillCode(
      id: id,
      name: name,
      type: type,
      categoryId: categoryId,
      yearsOfExperience: 1,
      isChoose: !isChoose,
    );
  }
}
