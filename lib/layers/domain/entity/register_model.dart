// domain/entities/register_model.dart
import 'package:equatable/equatable.dart';

class RegisterModel extends Equatable {
  const RegisterModel({
    required this.errors,
    required this.message,
    required this.data,
    this.email,
  });

  final List<Error> errors;
  final String message;
  final Data? data;
  final String? email;
  @override
  List<Object?> get props => [
        errors,
        message,
        data,
      ];
}

class Data extends Equatable {
  const Data({required this.json});
  final Map<String, dynamic> json;

  @override
  List<Object?> get props => [];
}

class Error extends Equatable {
  const Error({
    required this.message,
    required this.field,
  });

  final String message;
  final String field;

  @override
  List<Object?> get props => [
        message,
        field,
      ];
}
