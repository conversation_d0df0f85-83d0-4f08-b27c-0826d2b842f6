import 'package:asiantech_link/layers/data/dto/user_model_dto.dart';
import 'package:equatable/equatable.dart';

class UserModel extends Equatable {
  final int? userId;

  final String? profileImagePath;

  final String? firstName;

  final String? lastName;

  final int? age;

  final List<LanguageDto>? languages;

  final List<SkillDto>? skills;

  final List<ExperienceDto>? experiences;

  final RequirementDto? requirements;

  final String? lastAcademicCode;

  final List<TotalExperienceDto> totalExperience;

  final int? interestedFlag;

  const UserModel(
      {required this.userId,
      required this.profileImagePath,
      required this.firstName,
      required this.lastName,
      required this.age,
      required this.languages,
      required this.skills,
      required this.experiences,
      required this.requirements,
      required this.lastAcademicCode,
      required this.totalExperience,
      required this.interestedFlag});

  @override
  List<Object?> get props => [
        userId,
        profileImagePath,
        firstName,
        lastName,
        age,
        languages,
        skills,
        experiences,
        requirements,
        lastAcademicCode,
        totalExperience,
        interestedFlag
      ];
}
