import 'package:equatable/equatable.dart';

class TranslateLanguageModel extends Equatable {
  final String? text;
  final String? sourceLanguage;
  final String? targetLanguage;

  const TranslateLanguageModel(
      {required this.text,
      required this.sourceLanguage,
      required this.targetLanguage});
  @override
  List<Object?> get props => [text, sourceLanguage, targetLanguage];
}

class ChatTranslateModel extends Equatable {
  final String? id;
  final String? groupId;
  final String? chatId;
  final String? originalText;
  final String? textEn;
  final String? textVi;
  final String? textJa;
  final String? created;
  final String? updated;

  const ChatTranslateModel(
      {required this.id,
      required this.groupId,
      required this.chatId,
      required this.originalText,
      required this.textEn,
      required this.textVi,
      required this.textJa,
      required this.created,
      required this.updated});
  @override
  List<Object?> get props => [
        id,
        groupId,
        chatId,
        originalText,
        textEn,
        textVi,
        textJa,
        created,
        updated
      ];
}
