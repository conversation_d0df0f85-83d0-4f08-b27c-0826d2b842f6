import 'package:openapi/openapi.dart';

class EditLicenseModel {
  final UpdateEngLicense license;
  final bool isExpanded;

  EditLicenseModel({required this.license, required this.isExpanded});

  changeExpanded() {
    return EditLicenseModel(license: license, isExpanded: !isExpanded);
  }

  changeLicense(UpdateEngLicense license) {
    return EditLicenseModel(license: license, isExpanded: isExpanded);
  }
}
