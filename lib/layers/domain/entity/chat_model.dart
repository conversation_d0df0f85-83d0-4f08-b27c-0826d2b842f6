import 'package:equatable/equatable.dart';
import 'package:openapi/openapi.dart';

import '../../presentation/resources/enum.dart';

class GroupModel extends Equatable {
  final String? groupId;
  final String? name;
  final String? created;

  const GroupModel({this.groupId, this.name, this.created});

  @override
  List<Object?> get props => [groupId, name, created];
}

class ChatModel extends Equatable {
  final String? chatId;
  final String? group;
  final String? text;
  final String? send;
  final String? created;
  final UserChat? user;
  final ChatEnum chatEnum;
  final String? translatedText;
  final String? targetLanguage;
  final bool? translating;

  const ChatModel(
      {this.chatId,
      this.group,
      this.text,
      this.send,
      this.created,
      this.user,
      this.chatEnum = ChatEnum.sent,
      this.translatedText,
      this.targetLanguage,
      this.translating});

  @override
  List<Object?> get props => [
        chatId,
        group,
        text,
        send,
        created,
        user,
        chatEnum,
        translatedText,
        targetLanguage,
        translating,
      ];
}

class ChatGroupModel extends Equatable {
  final GroupModel? group;
  final List<UserChat>? users;
  final ChatModel? chat;
  final String? updated;
  final String? created;
  final SignContract? signContract;

  const ChatGroupModel({
    this.group,
    this.users,
    this.chat,
    this.updated,
    this.created,
    this.signContract,
  });

  @override
  List<Object?> get props =>
      [group, users, chat, updated, created, signContract];
}
