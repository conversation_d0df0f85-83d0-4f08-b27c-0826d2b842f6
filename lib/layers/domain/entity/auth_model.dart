// domain/entities/register_model.dart
import 'package:equatable/equatable.dart';
import 'package:openapi/openapi.dart';

class AuthModel extends Equatable {
  const AuthModel({
    required this.message,
    this.loginSuccessResponseModel,
    this.email,
    this.facebookId,
    this.linkedinId,
    this.zaloId,
    this.whatsAppId,
    this.lastName,
    this.firstName,
    this.imageUrl,
  });

  final String message;
  final LoginSuccessResponseModel? loginSuccessResponseModel;
  final String? email;
  final String? facebookId;
  final String? linkedinId;
  final String? zaloId;
  final String? whatsAppId;
  final String? lastName;
  final String? firstName;
  final String? imageUrl;
  @override
  List<Object?> get props => [
        message,
        loginSuccessResponseModel,
        email,
        facebookId,
      ];
}
