import 'package:asiantech_link/layers/domain/entity/job_code_model.dart';
import 'package:asiantech_link/layers/domain/entity/skill_code_model.dart';
import 'package:built_collection/built_collection.dart';
import 'package:openapi/openapi.dart';

class EditRequirementModel {
  final UpdateEngHope requirement;
  final bool isExpanded;
  final JobCode? selectJobCode;

  EditRequirementModel(
      {required this.requirement,
      required this.isExpanded,
      this.selectJobCode});

  changeExpanded() {
    return EditRequirementModel(
        requirement: requirement,
        isExpanded: !isExpanded,
        selectJobCode: selectJobCode);
  }

  changeRequirement(UpdateEngHope? requirement) {
    return EditRequirementModel(
        requirement: requirement ?? this.requirement,
        isExpanded: isExpanded,
        selectJobCode: selectJobCode);
  }

  changeSelectJobCode(JobCode jobCode) {
    return EditRequirementModel(
        requirement: requirement.rebuild(
          (b) {
            b.jobSkills = ListBuilder([UpdateHopeJobSkill()]);
          },
        ),
        isExpanded: isExpanded,
        selectJobCode: jobCode);
  }

  changeSkillCode(int index, SkillCode skillCode) {
    return EditRequirementModel(
        requirement: requirement.rebuild(
          (b) {
            b.jobSkills = requirement.jobSkills.isNotEmpty
                ? requirement.jobSkills.rebuild(
                    (p0) {
                      p0[index] = p0[index].rebuild((p1) {
                        p1.jobCode = selectJobCode?.id;
                        p1.skillCode = skillCode.id;
                      });
                    },
                  ).toBuilder()
                : ListBuilder([
                    UpdateHopeJobSkill(
                      (b) {
                        b.jobCode = selectJobCode?.id;
                        b.skillCode = skillCode.id;
                      },
                    )
                  ]);
          },
        ),
        isExpanded: isExpanded,
        selectJobCode: selectJobCode);
  }

  addSkillCode() {
    return EditRequirementModel(
        requirement: requirement.rebuild(
          (b) {
            b.jobSkills = requirement.jobSkills.rebuild(
              (p0) {
                p0.add(UpdateHopeJobSkill());
              },
            ).toBuilder();
          },
        ),
        isExpanded: isExpanded,
        selectJobCode: selectJobCode);
  }

  removeSkillCode(int index) {
    return EditRequirementModel(
        requirement: requirement.rebuild(
          (b) {
            b.jobSkills = requirement.jobSkills.rebuild(
              (p0) {
                p0.removeAt(index);
              },
            ).toBuilder();
          },
        ),
        isExpanded: isExpanded,
        selectJobCode: selectJobCode);
  }
}
