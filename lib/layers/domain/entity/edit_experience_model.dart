import 'package:built_collection/built_collection.dart';
import 'package:openapi/openapi.dart';

import 'job_code_model.dart';

class EditExperienceModel {
  final UpdateEngCareer experience;
  final bool isExpanded;
  final bool isCurrentWork;
  final JobCode? selectJobCode;

  EditExperienceModel({
    required this.experience,
    required this.isExpanded,
    this.isCurrentWork = false,
    this.selectJobCode,
  });

  changeExpanded() {
    return EditExperienceModel(
      experience: experience,
      isExpanded: !isExpanded,
      selectJobCode: selectJobCode,
      isCurrentWork: isCurrentWork,
    );
  }

  changeExperience(UpdateEngCareer experience) {
    return EditExperienceModel(
      experience: experience,
      isExpanded: isExpanded,
      selectJobCode: selectJobCode,
      isCurrentWork: isCurrentWork,
    );
  }

  changeCurrentWork() {
    return EditExperienceModel(
      experience: experience,
      isExpanded: isExpanded,
      selectJobCode: selectJobCode,
      isCurrentWork: !isCurrentWork,
    );
  }

  removeCurrentWork() {
    return EditExperienceModel(
      experience: experience,
      isExpanded: isExpanded,
      selectJobCode: selectJobCode,
      isCurrentWork: false,
    );
  }

  changeJobCode(JobCode jobCode) {
    return EditExperienceModel(
      experience: UpdateEngCareer(
        (b) {
          b.companyName = experience.companyName;
          b.careerJobSkills.add(UpdateCareerJobSkill(
            (b) {
              b.jobCode = jobCode.id;
            },
          ));
          b.enteringDate = experience.enteringDate;
          b.quittingDate = experience.quittingDate;
          b.jobDescription = experience.jobDescription;
        },
      ),
      isExpanded: isExpanded,
      selectJobCode: jobCode,
      isCurrentWork: isCurrentWork,
    );
  }

  removeSkill(int indexSkill) {
    return EditExperienceModel(
      experience: UpdateEngCareer(
        (b) {
          b.companyName = experience.companyName;
          b.careerJobSkills = experience.careerJobSkills.rebuild((b) {
            b.removeAt(indexSkill);
          }).toBuilder();
          b.enteringDate = experience.enteringDate;
          b.quittingDate = experience.quittingDate;
          b.jobDescription = experience.jobDescription;
        },
      ),
      isExpanded: isExpanded,
      selectJobCode: selectJobCode,
      isCurrentWork: isCurrentWork,
    );
  }

  changeSkillCode(int index, String id) {
    return EditExperienceModel(
      experience: UpdateEngCareer(
        (b) {
          b.companyName = experience.companyName;
          b.careerJobSkills = experience.careerJobSkills.rebuild((b) {
            b[index] = b[index].rebuild((b) {
              b.skillCode = id;
              b.jobCode = selectJobCode?.id;
              b.yearsOfExperience =
                  experience.careerJobSkills[index].yearsOfExperience;
            });
          }).toBuilder();
          b.enteringDate = experience.enteringDate;
          b.quittingDate = experience.quittingDate;
          b.jobDescription = experience.jobDescription;
        },
      ),
      isExpanded: isExpanded,
      selectJobCode: selectJobCode,
      isCurrentWork: isCurrentWork,
    );
  }

  changeYearOfExperience(int index, int value) {
    return EditExperienceModel(
      experience: UpdateEngCareer(
        (b) {
          b.companyName = experience.companyName;
          b.careerJobSkills = experience.careerJobSkills.rebuild((b) {
            b[index] = b[index].rebuild((b) {
              b.skillCode = experience.careerJobSkills[index].skillCode;
              b.jobCode = selectJobCode?.id;
              b.yearsOfExperience = value;
            });
          }).toBuilder();
          b.enteringDate = experience.enteringDate;
          b.quittingDate = experience.quittingDate;
          b.jobDescription = experience.jobDescription;
        },
      ),
      isExpanded: isExpanded,
      selectJobCode: selectJobCode,
      isCurrentWork: isCurrentWork,
    );
  }

  changeCompanyName(String name) {
    return EditExperienceModel(
      experience: UpdateEngCareer(
        (b) {
          b.companyName = name;
          b.careerJobSkills = ListBuilder(experience.careerJobSkills);
          b.enteringDate = experience.enteringDate;
          b.quittingDate = experience.quittingDate;
          b.jobDescription = experience.jobDescription;
        },
      ),
      isExpanded: isExpanded,
      selectJobCode: selectJobCode,
      isCurrentWork: isCurrentWork,
    );
  }

  changeJobDescription(String description) {
    return EditExperienceModel(
      experience: UpdateEngCareer(
        (b) {
          b.companyName = experience.companyName;
          b.jobDescription = description;
          b.careerJobSkills = ListBuilder(experience.careerJobSkills);
          b.enteringDate = experience.enteringDate;
          b.quittingDate = experience.quittingDate;
        },
      ),
      isExpanded: isExpanded,
      selectJobCode: selectJobCode,
      isCurrentWork: isCurrentWork,
    );
  }

  changeJobType(ListBuilder<UpdateCareerJobSkill>? careerJobSkills) {
    return EditExperienceModel(
      experience: UpdateEngCareer(
        (b) {
          b.companyName = experience.companyName;
          b.careerJobSkills = careerJobSkills;
          b.enteringDate = experience.enteringDate;
          b.quittingDate = experience.quittingDate;
          b.jobDescription = experience.jobDescription;
        },
      ),
      isExpanded: isExpanded,
      selectJobCode: selectJobCode,
      isCurrentWork: isCurrentWork,
    );
  }

  EditExperienceModel changeRolesJob(List<String> roles) {
  final csv = roles.join(',');

  return EditExperienceModel(
    experience: UpdateEngCareer((b) {
      b.companyName = experience.companyName;
      b.careerType = experience.careerType;
      b.enteringDate = experience.enteringDate;
      b.quittingDate = experience.quittingDate;
      b.jobDescription = experience.jobDescription;
      b.careerJobSkills = experience.careerJobSkills.toBuilder();
      b.roleName = csv;
    }),
    isExpanded: isExpanded,
    selectJobCode: selectJobCode,
    isCurrentWork: isCurrentWork,
  );
}
}
