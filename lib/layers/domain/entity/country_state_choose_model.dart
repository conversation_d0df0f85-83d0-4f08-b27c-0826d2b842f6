import 'package:asiantech_link/layers/domain/entity/country_model.dart';

import 'state_model.dart';

class CountryStateChooseModel {
  final Country country;
  final List<CountryState> states;
  final bool isChoose;
  final bool isExpanded;

  CountryStateChooseModel(
      {required this.country,
      required this.states,
      this.isChoose = false,
      this.isExpanded = false});

  changeChoose() {
    List<CountryState> updatedStates = states
        .map((e) => CountryState(
              code: e.code,
              id: e.id,
              nameEn: e.nameEn,
              nameJp: e.nameJp,
              nameVn: e.nameVn,
              selectionListName: e.selectionListName,
              isChoose: !isChoose,
            ))
        .toList();
    return CountryStateChooseModel(
      country: country,
      states: updatedStates,
      isChoose: !isChoose,
      isExpanded: isExpanded,
    );
  }

  changeExpanded() {
    for (var element in states) {
      element = element.changeChoose();
    }
    return CountryStateChooseModel(
      country: country,
      states: states,
      isChoose: isChoose,
      isExpanded: !isExpanded,
    );
  }

  changeChooseState(CountryState state) {
    // Map through the states and update the chosen state
    final updatedStates = states.map((e) {
      if (e.code == state.code) {
        return state.changeChoose();
      } else {
        return e;
      }
    }).toList();

    // Check if any state has isChoose set to false
    final anyStateNotChosen = updatedStates.any((e) => !e.isChoose);

    return CountryStateChooseModel(
        country: country,
        states: updatedStates,
        isChoose: !anyStateNotChosen,
        isExpanded: isExpanded);
  }
}
