class ListCountryModel {
  ListCountryModel({
    required this.country,
  });

  final List<Country> country;

  factory ListCountryModel.fromJson(Map<String, dynamic> json) {
    return ListCountryModel(
      country: json["country"] == null
          ? []
          : List<Country>.from(
              json["country"]!.map((x) => Country.fromJson(x))),
    );
  }

  Map<String, dynamic> toJson() => {
        "country": country.map((x) => x.toJson()).toList(),
      };
}

class Country {
  Country({
    required this.id,
    required this.nameJp,
    required this.nameEn,
    required this.nameVn,
    required this.countryCode,
    required this.remarks,
  });

  final String id;
  final String nameJp;
  final String nameEn;
  final String nameVn;
  final String countryCode;
  final String remarks;

  factory Country.fromJson(Map<String, dynamic> json) {
    return Country(
      id: json["id"] ?? "",
      nameJp: json["name_jp"] ?? "",
      nameEn: json["name_en"] ?? "",
      nameVn: json["name_vn"] ?? "",
      countryCode: json["country_code"] ?? "",
      remarks: json["remarks"] ?? "",
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "name_jp": nameJp,
        "name_en": nameEn,
        "name_vn": nameVn,
        "country_code": countryCode,
        "remarks": remarks,
      };
}
