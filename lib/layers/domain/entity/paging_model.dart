import 'package:equatable/equatable.dart';

class PagingModel<T> extends Equatable {
  final String? next;
  final String? previous;
  final List<T>? results;
  final int? totalCount;
  final Map<String, dynamic>? metaData;

  const PagingModel({
    required this.next,
    required this.previous,
    required this.results,
    this.totalCount,
    this.metaData,
  });

  @override
  List<Object?> get props => [next, previous, results, totalCount, metaData];
}
