import 'package:openapi/openapi.dart';

class EditHighlightProjectModel {
  final EngHighLightProject project;
  final bool isExpanded;
  final bool isCurrentWork;

  EditHighlightProjectModel({
    required this.project,
    this.isExpanded = true,
    this.isCurrentWork = false,
  });

  EditHighlightProjectModel changeExpanded() {
    return EditHighlightProjectModel(
      project: project,
      isExpanded: !isExpanded,
    );
  }

    EditHighlightProjectModel changeCurrentWork() {
    return EditHighlightProjectModel(
      project: project,
      isExpanded: isExpanded,
      isCurrentWork: !isCurrentWork,
    );
  }

  EditHighlightProjectModel updateProject(
  void Function(EngHighLightProjectBuilder) updates,
) {
  return EditHighlightProjectModel(
    project: project.rebuild(updates),
    isExpanded: isExpanded,
    isCurrentWork: isCurrentWork,
  );
}
}
