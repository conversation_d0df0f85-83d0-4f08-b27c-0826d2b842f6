import 'package:openapi/openapi.dart';

class EditLanguageModel {
  final UpdateEngLanguage language;
  final bool isExpanded;

  EditLanguageModel({required this.language, required this.isExpanded});

  changeExpanded() {
    return EditLanguageModel(language: language, isExpanded: !isExpanded);
  }

  changeLanguage(UpdateEngLanguage language) {
    return EditLanguageModel(language: language, isExpanded: isExpanded);
  }
}
