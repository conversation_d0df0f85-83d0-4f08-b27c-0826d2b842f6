import 'package:asiantech_link/layers/data/dto/state_model_dto.dart';

class StateModel {
  StateModel({
    required this.state,
  });

  final List<StateDto> state;
}

class CountryState {
  CountryState({
    required this.selectionListName,
    required this.id,
    required this.nameJp,
    required this.nameEn,
    required this.nameVn,
    required this.code,
    this.isChoose = false,
  });

  final String selectionListName;
  final String id;
  final String nameJp;
  final String nameEn;
  final String nameVn;
  final String code;
  final bool isChoose;

  CountryState changeChoose() {
    return CountryState(
      selectionListName: selectionListName,
      id: id,
      nameJp: nameJp,
      nameEn: nameEn,
      nameVn: nameVn,
      code: code,
      isChoose: !isChoose,
    );
  }
}
