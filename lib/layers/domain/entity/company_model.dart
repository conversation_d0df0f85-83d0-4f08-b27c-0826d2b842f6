import 'package:equatable/equatable.dart';

class CompanyModel extends Equatable {
  final int? companyId;
  final int? userType;
  final String? name;
  final String? aboutUs;
  final String? businessDetails;
  final int? employeesType;
  final String? countryCode;
  final String? addressCode;
  final String? address;
  final String? tel;
  final String? logoImagePath;
  final String? prImagePath1;
  final String? prImagePath2;
  final String? prImagePath3;
  final String? contactMail;
  final String? webUrl;
  final String? introductionUrl;
  final String? memo;
  final String? openFlag;
  final String? deleted;
  final String? created;
  final String? updated;
  final String? benefits;
  final String? capitalStock;
  final String? capitalStockCurrCode;
  final String? internationalTel;
  final double? agentFee;
  final String? agentFeeCurrCode;
  final double? acceptingFee;
  final String? acceptingFeeCurrCode;
  final double? supportOutsourcingFee;
  final String? supportOutsourcingFeeCurrCode;
  final String? support;
  final int? status;
  final String? introductionPR;

  const CompanyModel({
    required this.companyId,
    required this.userType,
    required this.name,
    required this.aboutUs,
    required this.businessDetails,
    required this.employeesType,
    required this.countryCode,
    required this.addressCode,
    required this.address,
    required this.tel,
    required this.logoImagePath,
    required this.prImagePath1,
    required this.prImagePath2,
    required this.prImagePath3,
    required this.contactMail,
    required this.webUrl,
    required this.introductionUrl,
    required this.memo,
    required this.openFlag,
    required this.deleted,
    required this.created,
    required this.updated,
    required this.benefits,
    required this.capitalStock,
    required this.capitalStockCurrCode,
    required this.internationalTel,
    required this.agentFee,
    required this.agentFeeCurrCode,
    required this.acceptingFee,
    required this.acceptingFeeCurrCode,
    required this.supportOutsourcingFee,
    required this.supportOutsourcingFeeCurrCode,
    required this.support,
    required this.status,
    required this.introductionPR,
  });

  @override
  List<Object?> get props => [
        companyId,
        userType,
        name,
        aboutUs,
        businessDetails,
        employeesType,
        countryCode,
        addressCode,
        address,
        tel,
        logoImagePath,
        prImagePath1,
        prImagePath2,
        prImagePath3,
        contactMail,
        webUrl,
        introductionUrl,
        memo,
        openFlag,
        deleted,
        created,
        updated,
        benefits,
        capitalStock,
        capitalStockCurrCode,
        internationalTel,
        agentFee,
        agentFeeCurrCode,
        acceptingFee,
        acceptingFeeCurrCode,
        supportOutsourcingFee,
        supportOutsourcingFeeCurrCode,
        support,
        status,
        introductionPR,
      ];
}
