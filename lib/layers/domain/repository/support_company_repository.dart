import 'package:asiantech_link/layers/data/dto/paging_model_dto.dart';
import 'package:openapi/openapi.dart';

abstract class SupportCompanyRepository {
  Future<List<SPCompanyRegistered>> getListRegisteredCompany();
  Future<PagingModelDto<RecruitUploadedSerializers>>
      getListRecruitOfHostCompany({
    required int hostCompanyId,
    int? pageSize,
    String? cursor,
    String? ordering,
  });
  Future<int?> supportRequestInterview({
    required SPRequestInterviewBody data,
  });
  Future<PagingModelDto<ManageHostCompany>> getListManageHostCompany({
    int? pageSize,
    String? cursor,
    String? sortBy,
    String? progressStatus,
    String? search,
  });
}
