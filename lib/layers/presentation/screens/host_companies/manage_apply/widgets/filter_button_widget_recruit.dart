import 'package:asiantech_link/assets.gen.dart';
import 'package:asiantech_link/layers/presentation/resources/app_colors.dart';
import 'package:asiantech_link/layers/presentation/resources/app_text_styles.dart';
import 'package:asiantech_link/layers/presentation/utils/app_locale.dart';
import 'package:asiantech_link/layers/presentation/widgets/gesture_detector_custom.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class FilterButtonWidgetRecruit extends StatelessWidget {
  final VoidCallback onPressed;
  const FilterButtonWidgetRecruit({super.key, required this.onPressed});

  @override
  Widget build(BuildContext context) {
    return GestureDetectorCustom(
      onTap: onPressed,
      child: Stack(
        children: [
          Container(
            width: 400,
            height: 48,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(24),
              color: AppColors.hostCompanyPrimary,
            ),
            child: Center(
              child: Text(
                AppText.value(context).filter_conditions,
                style: AppTextStyles.textStyle16(context,
                    color: AppColors.white, fontWeight: FontWeight.w700),
              ),
            ),
          ),
          Positioned(
            left: 24,
            top: 0,
            bottom: 0,
            child: Center(child: SvgPicture.asset(Assets.svg.filterIconCompanySVG)),)
        ],
      ),
    );
  }
}
