import 'package:flutter/material.dart';
import 'package:asiantech_link/layers/presentation/widgets/gesture_detector_custom.dart';
import 'package:asiantech_link/layers/presentation/widgets/check_box_title_custom.dart';
import 'package:asiantech_link/layers/presentation/resources/app_colors.dart';
import 'package:asiantech_link/layers/presentation/resources/app_text_styles.dart';
import 'package:asiantech_link/layers/presentation/resources/app_constants.dart';
import 'package:asiantech_link/layers/presentation/utils/helper.dart';

class StatusFilter extends StatefulWidget {
  final String label;
  final List<int> selectedValues;
  final bool isExpanded;
  final ValueChanged<List<int>> onChanged;

  const StatusFilter({
    Key? key,
    required this.label,
    required this.selectedValues,
    this.isExpanded = false,
    required this.onChanged,
  }) : super(key: key);

  @override
  StatusFilterState createState() => StatusFilterState();
}

class StatusFilterState extends State<StatusFilter> {
  bool _expanded = false;
  late List<int> _selectedValues;

  @override
  void initState() {
    super.initState();
    _expanded = widget.isExpanded;
    _selectedValues = List.from(widget.selectedValues);
  }

  @override
  void didUpdateWidget(StatusFilter oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedValues != widget.selectedValues) {
      _selectedValues = List.from(widget.selectedValues);
    }
  }

  // Status list similar to SelectStatusDialog
  static const List<int> _statusList = [
    RecruitProgressCodeType.requestingAnAgent,
    RecruitProgressCodeType.application,
    RecruitProgressCodeType.interviewRequest,
    RecruitProgressCodeType.interviewScheduling,
    RecruitProgressCodeType.interviewDateConfirmed,
    RecruitProgressCodeType.interviewCompleted,
    RecruitProgressCodeType.jobOffer,
    RecruitProgressCodeType.offerAccepted,
    RecruitProgressCodeType.employed,
    RecruitProgressCodeType.notPassed,
    RecruitProgressCodeType.applicationWithdrawn,
    RecruitProgressCodeType.interviewWithdrawn,
    RecruitProgressCodeType.offerDeclined,
    RecruitProgressCodeType.otherCompanyOffer,
  ];

  String _getStatusName(BuildContext context, int statusCode) {
    return Helper.getRecruitProgressCodeName(
        context: context, code: statusCode);
  }

  void _toggleStatus(int statusCode) {
    setState(() {
      if (_selectedValues.contains(statusCode)) {
        _selectedValues.remove(statusCode);
      } else {
        _selectedValues.add(statusCode);
      }
    });
    widget.onChanged(_selectedValues);
  }

  @override
  Widget build(BuildContext context) {
    final hasSelection = _selectedValues.isNotEmpty;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetectorCustom(
          onTap: () => setState(() => _expanded = !_expanded),
          child: Container(
            padding: const EdgeInsets.only(bottom: 16),
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(color: AppColors.lineColor),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.check_rounded,
                      color: hasSelection
                          ? AppColors.hoverPrimaryColor
                          : Colors.transparent,
                      size: 11,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      widget.label,
                      style: AppTextStyles.paragraph02v3(
                        color: AppColors.hoverPrimaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                Icon(
                  _expanded ? Icons.remove_rounded : Icons.add_rounded,
                  color: AppColors.hostCompanyPrimary,
                  size: 20,
                ),
              ],
            ),
          ),
        ),
        AnimatedSwitcher(
          duration: const Duration(milliseconds: 300),
          transitionBuilder: (child, anim) =>
              SizeTransition(sizeFactor: anim, child: child),
          child: _expanded
              ? LayoutBuilder(
                  builder: (context, constraints) {
                    final isMobile = constraints.maxWidth < 600;
                    final columns = isMobile ? 2 : 3;
                    return Column(
                      children: List.generate(
                        (_statusList.length / columns).ceil(),
                        (rowIndex) {
                          return Padding(
                            padding: const EdgeInsets.only(top: 16),
                            child: Row(
                              children: List.generate(columns, (colIndex) {
                                final itemIndex = rowIndex * columns + colIndex;
                                if (itemIndex >= _statusList.length) {
                                  return const Expanded(child: SizedBox());
                                }

                                final statusCode = _statusList[itemIndex];
                                final statusName =
                                    _getStatusName(context, statusCode);
                                final isSelected =
                                    _selectedValues.contains(statusCode);

                                return Expanded(
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 5),
                                    child: CheckBoxTitleCustom(
                                      fillColor:
                                          WidgetStateProperty.resolveWith<
                                              Color>((Set<WidgetState> states) {
                                        return isSelected
                                            ? AppColors.primary
                                            : AppColors.white;
                                      }),
                                      value: isSelected,
                                      onChanged: (value) =>
                                          _toggleStatus(statusCode),
                                      title: statusName,
                                    ),
                                  ),
                                );
                              }),
                            ),
                          );
                        },
                      ),
                    );
                  },
                )
              : const SizedBox.shrink(),
        ),
      ],
    );
  }
}
