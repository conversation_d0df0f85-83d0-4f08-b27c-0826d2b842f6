import 'package:asiantech_link/layers/presentation/utils/app_locale.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'helper.dart';
import 'parse_error.dart';

/// Global file validation service for reusable file validation across the app
class FileValidationService {
  // File size constants (in bytes)
  static const int oneMB = 1024 * 1024;
  static const  int fiveMB = 5 * oneMB;
  static const int tenMB = 10 * oneMB;
  static const int fiftyMB = 50 * oneMB;

  /// Validates file size against the specified limit
  /// Returns null if valid, ParseError if invalid
  static ParseError? validateFileSize({
    required PlatformFile file,
    required int maxSizeInMB,
    BuildContext? context,
  }) {
    final maxSizeInBytes = maxSizeInMB * oneMB;
    final fileSize = file.size;

    if (fileSize > maxSizeInBytes) {
      final errorMessage = _getFileSizeErrorMessage(maxSizeInMB, context);
      return ParseError(
        code: 'FILE_SIZE_ERROR',
        message: errorMessage,
      );
    }
    return null;
  }

  /// Validates file type against allowed extensions
  /// Returns null if valid, ParseError if invalid
  static ParseError? validateFileType({
    required PlatformFile file,
    required List<String> allowedExtensions,
    BuildContext? context,
  }) {
    final fileName = file.name.toLowerCase();
    final fileExtension = fileName.split('.').last;

    if (!allowedExtensions.contains(fileExtension)) {
      final errorMessage = _getFileTypeErrorMessage(allowedExtensions, context);
      return ParseError(
        code: 'FILE_TYPE_ERROR',
        message: errorMessage,
      );
    }
    return null;
  }

  /// Comprehensive file validation (size + type)
  /// Returns null if valid, ParseError if invalid
  static ParseError? validateFile({
    required PlatformFile file,
    required int maxSizeInMB,
    required List<String> allowedExtensions,
    BuildContext? context,
  }) {
    // Check file size first
    final sizeError = validateFileSize(
      file: file,
      maxSizeInMB: maxSizeInMB,
      context: context,
    );
    if (sizeError != null) return sizeError;

    // Check file type
    final typeError = validateFileType(
      file: file,
      allowedExtensions: allowedExtensions,
      context: context,
    );
    if (typeError != null) return typeError;

    return null; // File is valid
  }

  /// Predefined validation for CV files (PDF, 5MB limit)
  static ParseError? validateCVFile({
    required PlatformFile file,
    BuildContext? context,
  }) {
    return validateFile(
      file: file,
      maxSizeInMB: 5,
      allowedExtensions: ['pdf'],
      context: context,
    );
  }

  static ParseError? validateImageFile({
    required PlatformFile file,
    BuildContext? context,
    int maxSizeInMB = 5,
  }) {
    return validateFile(
      file: file,
      maxSizeInMB: maxSizeInMB,
      allowedExtensions: ['jpg', 'jpeg', 'png'],
      context: context,
    );
  }

  static ParseError? validateDocumentFile({
    required PlatformFile file,
    BuildContext? context,
    int maxSizeInMB = 10,
  }) {
    return validateFile(
      file: file,
      maxSizeInMB: maxSizeInMB,
      allowedExtensions: ['pdf', 'doc', 'docx'],
      context: context,
    );
  }

  /// Validates XFile (from image_picker) size against the specified limit
  /// Returns null if valid, ParseError if invalid
  static Future<ParseError?> validateXFileSize({
    required XFile file,
    required int maxSizeInMB,
    BuildContext? context,
  }) async {
    final maxSizeInBytes = maxSizeInMB * oneMB;
    final fileSize = await file.length();

    if (fileSize > maxSizeInBytes) {
      // Get error message before async gap to avoid BuildContext issues
      final errorMessage = context != null && context.mounted
          ? _getFileSizeErrorMessage(maxSizeInMB, context)
          : 'File size must be less than ${maxSizeInMB}MB';
      return ParseError(
        code: 'FILE_SIZE_ERROR',
        message: errorMessage,
      );
    }
    return null;
  }

  /// Predefined validation for XFile images (10MB limit)
  static Future<ParseError?> validateXFileImage({
    required XFile file,
    BuildContext? context,
    int maxSizeInMB = 10,
  }) async {
    return await validateXFileSize(
      file: file,
      maxSizeInMB: maxSizeInMB,
      context: context,
    );
  }

  /// Gets localized file size error message
  static String _getFileSizeErrorMessage(int maxSizeInMB, BuildContext? context) {
    final ctx = context ?? globalContext;
    
    if (ctx != null) {
      try {
        // Try to use localized message if available
        return AppText.value(ctx).file_size_error_message_x_mb(maxSizeInMB);
      } catch (_) {
        // Fallback if localization fails
      }
    }
    
    // Default fallback message
    return 'File size must be less than ${maxSizeInMB}MB';
  }

  /// Gets localized file type error message
  static String _getFileTypeErrorMessage(List<String> allowedExtensions, BuildContext? context) {
    final ctx = context ?? globalContext;
    final extensionsText = allowedExtensions.join(', ').toUpperCase();
    
    if (ctx != null) {
      try {
        // Try to use localized message if available
        return 'Only $extensionsText files are allowed';
      } catch (_) {
        // Fallback if localization fails
      }
    }
    
    // Default fallback message
    return 'Only $extensionsText files are allowed';
  }

  /// Utility method to format file size for display
  static String formatFileSize(int sizeInBytes) {
    if (sizeInBytes < oneMB) {
      return '${(sizeInBytes / 1024).toStringAsFixed(1)} KB';
    } else if (sizeInBytes < oneMB * 1024) {
      return '${(sizeInBytes / oneMB).toStringAsFixed(1)} MB';
    } else {
      return '${(sizeInBytes / (oneMB * 1024)).toStringAsFixed(1)} GB';
    }
  }

  /// Utility method to get file extension
  static String getFileExtension(String fileName) {
    return fileName.split('.').last.toLowerCase();
  }

  /// Utility method to check if file is an image
  static bool isImageFile(String fileName) {
    final extension = getFileExtension(fileName);
    return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(extension);
  }

  /// Utility method to check if file is a document
  static bool isDocumentFile(String fileName) {
    final extension = getFileExtension(fileName);
    return ['pdf', 'doc', 'docx', 'txt', 'rtf'].contains(extension);
  }

  /// Utility method to check if file is a video
  static bool isVideoFile(String fileName) {
    final extension = getFileExtension(fileName);
    return ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'].contains(extension);
  }
}

/// File validation result for more detailed feedback
class FileValidationResult {
  final bool isValid;
  final ParseError? error;
  final String? warningMessage;

  const FileValidationResult({
    required this.isValid,
    this.error,
    this.warningMessage,
  });

  factory FileValidationResult.valid() {
    return const FileValidationResult(isValid: true);
  }

  factory FileValidationResult.invalid(ParseError error) {
    return FileValidationResult(isValid: false, error: error);
  }

  factory FileValidationResult.warning(String message) {
    return FileValidationResult(isValid: true, warningMessage: message);
  }
}
