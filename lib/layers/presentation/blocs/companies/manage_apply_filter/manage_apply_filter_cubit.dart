import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:asiantech_link/layers/data/repository_impl/general_company_repository_impl.dart';
import 'package:asiantech_link/layers/domain/repository/general_company_repository.dart';
import 'package:asiantech_link/layers/domain/usecase/general_company_usecase/get_manage_apply_usecase.dart';
import 'package:asiantech_link/layers/presentation/blocs/companies/manage_apply_filter/manage_apply_filter_state.dart';
import 'package:built_collection/built_collection.dart';
import 'package:openapi/openapi.dart';

class ManageApplyFilterCubit extends Cubit<ManageApplyFilterState> {
  final GeneralCompanyRepository _generalCompanyRepository = GeneralCompanyRepositoryImpl();
  late final GetManageApplyUsecase _getManageApplyUsecase = GetManageApplyUsecase(
    generalCompanyRepository: _generalCompanyRepository,
  );

  Timer? _debounceTimer;

  ManageApplyFilterCubit({
    RecruitUploadedSerializers? initialRecruitModel,
    List<int>? initialProgressCodes,
    String? initialSearch,
    int? initialHostCompanyId,
  }) : super(ManageApplyFilterState(
          recruitModel: initialRecruitModel,
          progressCodes: initialProgressCodes,
          search: initialSearch ?? '',
          hostCompanyId: initialHostCompanyId,
          totalCount: 0,
          isLoading: false,
        ));

  /// Update search text and get new count with debouncing
  Future<void> updateSearch(String search) async {
    emit(state.copyWith(search: search));

    // Cancel previous timer
    _debounceTimer?.cancel();

    // Start new timer for debouncing
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      _updateTotalCount();
    });
  }

  /// Update recruit model and get new count
  Future<void> updateRecruitModel(RecruitUploadedSerializers? recruitModel) async {
    emit(state.copyWith(recruitModel: recruitModel));
    await _updateTotalCount();
  }

  /// Update progress codes and get new count
  Future<void> updateProgressCodes(List<int>? progressCodes) async {
    emit(state.copyWith(progressCodes: progressCodes));
    await _updateTotalCount();
  }

  /// Clear all filters and reset to initial state
  Future<void> clearFilters() async {
    emit(state.copyWith(
      recruitModel: null,
      progressCodes: null,
      search: '',
      totalCount: 0,
    ));
    await _updateTotalCount();
  }

  /// Get total count from API based on current filters
  Future<void> _updateTotalCount() async {
    try {
      emit(state.copyWith(isLoading: true));
      
      final model = await _getManageApplyUsecase.call(
        ordering: "created", // Default ordering for count
        search: state.search.isEmpty ? null : state.search,
        recruitIds: state.recruitModel?.recruitId != null
            ? BuiltList([state.recruitModel!.recruitId])
            : null,
        applyStatusFilterCodes: state.progressCodes != null
            ? BuiltList(state.progressCodes!)
            : null,
        hostCompanyId: state.hostCompanyId,
        page: 1,
      );

      emit(state.copyWith(
        totalCount: model.count,
        isLoading: false,
      ));
    } catch (e) {
      emit(state.copyWith(
        totalCount: 0,
        isLoading: false,
      ));
    }
  }

  /// Initialize with current count
  Future<void> initialize() async {
    await _updateTotalCount();
  }

  @override
  Future<void> close() {
    _debounceTimer?.cancel();
    return super.close();
  }
}
