import 'package:equatable/equatable.dart';
import 'package:openapi/openapi.dart';

class ManageApplyFilterState extends Equatable {
  final RecruitUploadedSerializers? recruitModel;
  final List<int>? progressCodes;
  final String search;
  final int? hostCompanyId;
  final int totalCount;
  final bool isLoading;

  const ManageApplyFilterState({
    this.recruitModel,
    this.progressCodes,
    required this.search,
    this.hostCompanyId,
    required this.totalCount,
    required this.isLoading,
  });

  @override
  List<Object?> get props => [
        recruitModel,
        progressCodes,
        search,
        hostCompanyId,
        totalCount,
        isLoading,
      ];

  ManageApplyFilterState copyWith({
    RecruitUploadedSerializers? recruitModel,
    List<int>? progressCodes,
    String? search,
    int? hostCompanyId,
    int? totalCount,
    bool? isLoading,
  }) {
    return ManageApplyFilterState(
      recruitModel: recruitModel ?? this.recruitModel,
      progressCodes: progressCodes ?? this.progressCodes,
      search: search ?? this.search,
      hostCompanyId: hostCompanyId ?? this.hostCompanyId,
      totalCount: totalCount ?? this.totalCount,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}
