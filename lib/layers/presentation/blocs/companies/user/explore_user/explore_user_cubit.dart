import 'dart:async';
import 'dart:convert';

import 'package:asiantech_link/layers/data/repository_impl/general_company_repository_impl.dart';
import 'package:asiantech_link/layers/data/source/local/local_storage_service.dart';
import 'package:asiantech_link/layers/domain/repository/general_company_repository.dart';
import 'package:asiantech_link/layers/domain/usecase/general_company_usecase/explore_user_usecase.dart';
import 'package:asiantech_link/layers/domain/usecase/general_company_usecase/get_user_explore_count_usecase.dart';
import 'package:asiantech_link/layers/presentation/resources/app_constants.dart';
import 'package:asiantech_link/layers/presentation/resources/enum.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/user/explore_user/explore_user_screen.dart';
import 'package:asiantech_link/layers/presentation/utils/common_dialog.dart';
import 'package:asiantech_link/layers/presentation/utils/extensions.dart';
import 'package:asiantech_link/layers/presentation/utils/helper.dart';
import 'package:built_collection/built_collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:openapi/openapi.dart';
import 'package:asiantech_link/layers/presentation/utils/helper.dart' as helper;

import 'explore_user_state.dart';

class ExploreUserCubit extends Cubit<ExploreUserState> {
  // Dependencies
  final GeneralCompanyRepository _generalCompanyRepository =
      GeneralCompanyRepositoryImpl();
  late final ExploreUserUsecase _exploreUserUsecase =
      ExploreUserUsecase(generalCompanyRepository: _generalCompanyRepository);
  late final GetUserExploreCountUsecase _getUserExploreCountUsecase =
      GetUserExploreCountUsecase(
          generalCompanyRepository: _generalCompanyRepository);
  final LocalStorageService localStorageService = LocalStorageService();

  ExploreUserCubit() : super(ExploreUserState.empty());

  // Reset to uninitialized state
  Future<void> resetToUninitialized() async {
    emit(ExploreUserState.empty());
  }

  // Load explore user data
  Future<void> loadExploreUser({
    bool? isFilterOn,
    bool isResetPage = false,
    BuildContext? context,
    String? recruitId,
    Function()? onDone,
  }) async {
    try {
      var currentState = state;
      if (context != null &&
          (bool.tryParse(currentState.showFavorite ?? '') == false)) {
        var currentPath = GoRouterState.of(context.nonNullable).uri.path;
        var parts = currentPath.split('/');
        var lastPart = parts.last;
        if (lastPart.contains(ExploreUserScreen.routeName)) {
          helper.updateUrlQueryParams(context, {});
        }
      }

      var searchType = state.selectedSearchType;
      var page = state.page;
      if (isResetPage) {
        page = 1;
      }

      emit(state.copyWith(
          isLoading: true, totalCountFilter: currentState.totalCountFilter));

      final result = await _exploreUserUsecase.call(
        page: page,
        searchType: searchType.valueRequestType,
        pageSize: 20,
        filterId: currentState.filterId,
        ordering: currentState.ordering,
        ageFrom: currentState.ageFrom?.toInt(),
        ageTo: currentState.ageTo?.toInt(),
        sexType: currentState.sexType,
        countryCode: currentState.countryCode,
        addressCode1: currentState.addressCode1,
        addressCode2: currentState.addressCode2,
        lastAcademicCode: currentState.lastAcademicCode,
        languageCode1: currentState.languageCode1,
        languageLevelType1: currentState.languageLevelType1?.toInt(),
        languageCode2: currentState.languageCode2,
        languageLevelType2: currentState.languageLevelType2?.toInt(),
        experiencedJobCode1: currentState.experiencedJobCode1,
        yearsOfExperience1: currentState.yearsOfExperience1?.toInt(),
        licenceCode1: currentState.licenceCode1,
        licencePoint1: currentState.licencePoint1?.toInt(),
        licenceCode2: currentState.licenceCode2,
        licencePoint2: currentState.licencePoint2?.toInt(),
        licenceCode3: currentState.licenceCode3,
        licencePoint3: currentState.licencePoint3?.toInt(),
        recruitingJobCode: currentState.recruitingJobCode,
        recruitingEmployCode: currentState.recruitingEmployCode,
        workPlaceCode1: currentState.workPlaceCode1,
        workPlaceCode2: currentState.workPlaceCode2,
        workPlaceCode3: currentState.workPlaceCode3,
        payrollCode: currentState.payrollCode,
        payrollPriceFrom: currentState.payrollPriceFrom,
        payrollPriceTo: currentState.payrollPriceTo,
        agentFee: currentState.agentFee,
        agentFeeCurrCode: currentState.agentFeeCurrCode,
        showFavorite: bool.tryParse(currentState.showFavorite ?? "false"),
        remoteWorkSkillPointType:
            currentState.remoteWorkSkillPointType?.toInt(),
        globalSkillPointType: currentState.globalSkillPointType?.toInt(),
        communicationSkillPointType:
            currentState.communicationSkillPointType?.toInt(),
        horensoSkillPointType: currentState.horensoSkillPointType?.toInt(),
        projectManagementSkillPointType:
            currentState.projectManagementSkillPointType?.toInt(),
        globalWorkExperience: currentState.globalWorkExperience?.toInt(),
        searchQuery: currentState.searchQuery,
        careerType: currentState.careerType,
        skills: BuiltList<String>(currentState.skills ?? []),
        recruitId: recruitId,
      );

      emit(state.copyWith(
        exploreUser: result,
        page: page,
        selectedSearchType: searchType,
        isLoading: false,
        errorMessage: null,
      ));

      onDone?.call();
      await updateTotalCountItemsPage(
          totalCountItemsPage: result.totalCount ?? 0);
      await updateTotalCountFilter();
    } catch (e) {
      onDone?.call();
      emit(state.copyWith(
        isLoading: false,
        errorMessage: e.parseErrorMessage(),
      ));
    }
  }

  // Update total count filter
  Future<void> updateTotalCountFilter() async {
    try {
      var totalCountFilter = await _getUserExploreCountUsecase.call(
        pageSize: 20,
        filterId: state.filterId,
        ordering: state.ordering,
        ageFrom: state.ageFrom?.toInt(),
        ageTo: state.ageTo?.toInt(),
        sexType: state.sexType,
        countryCode: state.countryCode,
        addressCode1: state.addressCode1,
        addressCode2: state.addressCode2,
        lastAcademicCode: state.lastAcademicCode,
        languageCode1: state.languageCode1,
        languageLevelType1: state.languageLevelType1?.toInt(),
        languageCode2: state.languageCode2,
        languageLevelType2: state.languageLevelType2?.toInt(),
        experiencedJobCode1: state.experiencedJobCode1,
        yearsOfExperience1: state.yearsOfExperience1?.toInt(),
        licenceCode1: state.licenceCode1,
        licencePoint1: state.licencePoint1?.toInt(),
        licenceCode2: state.licenceCode2,
        licencePoint2: state.licencePoint2?.toInt(),
        licenceCode3: state.licenceCode3,
        licencePoint3: state.licencePoint3?.toInt(),
        recruitingJobCode: state.recruitingJobCode,
        recruitingEmployCode: state.recruitingEmployCode,
        workPlaceCode1: state.workPlaceCode1,
        workPlaceCode2: state.workPlaceCode2,
        workPlaceCode3: state.workPlaceCode3,
        payrollCode: state.payrollCode,
        payrollPriceFrom: state.payrollPriceFrom,
        payrollPriceTo: state.payrollPriceTo,
        agentFee: state.agentFee,
        agentFeeCurrCode: state.agentFeeCurrCode,
        showFavorite: bool.tryParse(state.showFavorite ?? ""),
        remoteWorkSkillPointType: state.remoteWorkSkillPointType?.toInt(),
        globalSkillPointType: state.globalSkillPointType?.toInt(),
        communicationSkillPointType: state.communicationSkillPointType?.toInt(),
        horensoSkillPointType: state.horensoSkillPointType?.toInt(),
        projectManagementSkillPointType:
            state.projectManagementSkillPointType?.toInt(),
        globalWorkExperience: state.globalWorkExperience?.toInt(),
        searchQuery: state.searchQuery,
        careerType: state.careerType,
        searchType: state.selectedSearchType.valueRequestType,
        skills: BuiltList<String>(state.skills ?? []),
      );

      emit(state.copyWith(
        totalCountFilter: totalCountFilter,
      ));
    } catch (e) {
      // Handle error silently or emit error state if needed
    }
  }

  // Update total count items page
  Future<void> updateTotalCountItemsPage(
      {required int totalCountItemsPage}) async {
    emit(state.copyWith(
      totalCountItemsPage: totalCountItemsPage,
    ));
  }

  // Update single filter
  Future<void> updateFilter({
    required String key,
    required dynamic value,
    Function()? onDone,
  }) async {
    final data = state.toMap();
    data[key] = value;
    emit(state.updateFilter(data));
    onDone?.call();
    await updateTotalCountFilter();
  }

  // Update single filter with debounce
  Future<void> updateFilterDebounce({
    required String key,
    required dynamic value,
    Function()? onDone,
  }) async {
    final data = state.toMap();
    data[key] = value;
    emit(state.updateFilter(data));
    onDone?.call();
    await updateTotalCountFilter();
  }

  // Update multiple filters
  Future<void> updateFilters({
    required Function()? onDone,
    required Map<String, dynamic> filters,
  }) async {
    final data = state.toMap();
    for (var x in filters.keys) {
      data[x] = filters[x]?.toString();
    }

    emit(state.updateFilter(data));
    onDone?.call();
    await updateTotalCountFilter();
    await updateTotalCountItemsPage(
        totalCountItemsPage: state.totalCountItemsPage);
  }

  // Update favorite user
  Future<void> updateFavoriteUser({
    required int index,
    required bool isFavorite,
  }) async {
    try {
      var list = state.exploreUser.results!.toList()
          as List<GeneralCompanyExploreUser>;
      var data = list[index].toBuilder();
      data.interestedFlag = isFavorite ? 1 : 0;
      list[index] = data.build();

      emit(state.copyWith(
        exploreUser: state.exploreUser.copyWith(
          results: list,
        ),
      ));
      var userId = list[index].userId;

      await _generalCompanyRepository.addFavoriteUser(
          data: GeneralCompanyUpdateFavoriteUser((update) {
        update.isFavorite = isFavorite;
        update.userId = userId;
      }));
    } catch (e) {
      CommonDialog.showErrorDialog(
        context: globalContext,
        error: e.parseError(),
      );
    }
  }

  // Update favorite user state
  Future<void> updateFavoriteUserState({
    required int userId,
    required bool isFavorite,
  }) async {
    var list =
        state.exploreUser.results!.toList() as List<GeneralCompanyExploreUser>;
    var index = list.indexWhere((element) => element.userId == userId);
    if (index != -1) {
      var data = list[index].toBuilder();
      data.interestedFlag = isFavorite ? 1 : 0;
      list[index] = data.build();

      emit(state.copyWith(
        exploreUser: state.exploreUser.copyWith(
          results: list,
        ),
      ));
    }
  }

  // Update state directly
  Future<void> updateState({required ExploreUserState newState}) async {
    emit(newState);
    await updateTotalCountFilter();
    await updateTotalCountItemsPage(
        totalCountItemsPage: state.totalCountItemsPage);
  }

  // Update filter to local storage
  Future<void> updateFilterToLocalStorage() async {
    var filterData = state.toMap();
    filterData.remove("totalCountFilter");
    filterData.remove("totalCountItemsPage");
    filterData.remove("page");
    filterData.removeWhere((key, value) => value == null);
    filterData =
        filterData.map((key, value) => MapEntry(key, value.toString()));
    var jsonString = jsonEncode(filterData);
    await localStorageService.setString(
        AppConstants.exploreUserParams, jsonString);
  }

  // Get filter from local storage
  Future<void> getFilterFromLocalStorage(
      {required Function(Map<String, dynamic>?) onDone}) async {
    var jsonString =
        localStorageService.getString(AppConstants.exploreUserParams);
    if (jsonString != null) {
      var filterData = jsonDecode(jsonString);
      onDone(filterData);
    } else {
      onDone(null);
    }
  }

  // Update search type
  Future<void> updateSearchType({
    required SearchType searchType,
    VoidCallback? onDone,
  }) async {
    emit(state.copyWith(
        page: 1, selectedSearchType: searchType, showFavorite: 'false'));
    onDone?.call();
  }
}
