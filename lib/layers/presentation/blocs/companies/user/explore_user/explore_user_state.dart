import 'package:asiantech_link/layers/data/dto/paging_model_dto.dart';
import 'package:asiantech_link/layers/presentation/resources/enum.dart';
import 'package:equatable/equatable.dart';
import 'package:openapi/openapi.dart';

class ExploreUserState extends Equatable {
  // Core data
  final PagingModelDto<GeneralCompanyExploreUser> exploreUser;
  final bool isLoading;
  final String? errorMessage;

  // Filter and pagination properties
  final int? filterId;
  final int totalCountFilter;
  final int totalCountItemsPage;
  final String? showFavorite;
  final String? ordering;
  final SearchType selectedSearchType;
  final int page;
  final String? ageFrom;
  final String? ageTo;
  final String? sexType;
  final String? countryCode;
  final String? addressCode1;
  final String? addressCode2;
  final String? lastAcademicCode;
  final String? languageCode1;
  final String? languageLevelType1;
  final String? languageCode2;
  final String? languageLevelType2;
  final String? experiencedJobCode1;
  final String? yearsOfExperience1;
  final String? licenceCode1;
  final String? licencePoint1;
  final String? licenceCode2;
  final String? licencePoint2;
  final String? licenceCode3;
  final String? licencePoint3;
  final String? recruitingJobCode;
  final String? recruitingEmployCode;
  final String? workPlaceCode1;
  final String? workPlaceCode2;
  final String? workPlaceCode3;
  final String? payrollCode;
  final String? payrollPriceFrom;
  final String? payrollPriceTo;
  final double? agentFee;
  final String? agentFeeCurrCode;
  final String? remoteWorkSkillPointType;
  final String? globalSkillPointType;
  final String? communicationSkillPointType;
  final String? horensoSkillPointType;
  final String? projectManagementSkillPointType;
  final String? globalWorkExperience;
  final String? searchQuery;
  final int? careerType;
  final List<String>? skills;

  const ExploreUserState({
    required this.exploreUser,
    this.isLoading = false,
    this.errorMessage,
    this.showFavorite,
    this.filterId,
    this.ordering = "-created",
    this.selectedSearchType = SearchType.all,
    this.page = 1,
    this.ageFrom,
    this.ageTo,
    this.sexType,
    this.countryCode,
    this.addressCode1,
    this.addressCode2,
    this.lastAcademicCode,
    this.languageCode1,
    this.languageLevelType1,
    this.languageCode2,
    this.languageLevelType2,
    this.experiencedJobCode1,
    this.yearsOfExperience1,
    this.licenceCode1,
    this.licencePoint1,
    this.licenceCode2,
    this.licencePoint2,
    this.licenceCode3,
    this.licencePoint3,
    this.recruitingJobCode,
    this.recruitingEmployCode,
    this.workPlaceCode1,
    this.workPlaceCode2,
    this.workPlaceCode3,
    this.payrollCode,
    this.payrollPriceFrom,
    this.payrollPriceTo,
    this.agentFee,
    this.agentFeeCurrCode,
    this.totalCountFilter = 0,
    this.totalCountItemsPage = 0,
    this.remoteWorkSkillPointType,
    this.globalSkillPointType,
    this.communicationSkillPointType,
    this.horensoSkillPointType,
    this.projectManagementSkillPointType,
    this.globalWorkExperience,
    this.searchQuery = "",
    this.careerType,
    this.skills,
  });

  factory ExploreUserState.empty() {
    return const ExploreUserState(
      exploreUser: PagingModelDto<GeneralCompanyExploreUser>(
        next: null,
        previous: null,
        results: [],
      ),
      isLoading: false,
      errorMessage: null,
      totalCountFilter: 0,
      totalCountItemsPage: 0,
    );
  }

  factory ExploreUserState.loading() {
    return const ExploreUserState(
      exploreUser: PagingModelDto<GeneralCompanyExploreUser>(
        next: null,
        previous: null,
        results: [],
      ),
      isLoading: true,
      errorMessage: null,
      totalCountFilter: 0,
      totalCountItemsPage: 0,
    );
  }

  factory ExploreUserState.error(String errorMessage) {
    return ExploreUserState(
      exploreUser: const PagingModelDto<GeneralCompanyExploreUser>(
        next: null,
        previous: null,
        results: [],
      ),
      isLoading: false,
      errorMessage: errorMessage,
      totalCountFilter: 0,
      totalCountItemsPage: 0,
    );
  }

  ExploreUserState copyWith({
    PagingModelDto<GeneralCompanyExploreUser>? exploreUser,
    bool? isLoading,
    String? errorMessage,
    int? filterId,
    int? totalCountFilter,
    int? totalCountItemsPage,
    String? showFavorite,
    String? ordering,
    SearchType? selectedSearchType,
    int? page,
    String? ageFrom,
    String? ageTo,
    String? sexType,
    String? countryCode,
    String? addressCode1,
    String? addressCode2,
    String? lastAcademicCode,
    String? languageCode1,
    String? languageLevelType1,
    String? languageCode2,
    String? languageLevelType2,
    String? experiencedJobCode1,
    String? yearsOfExperience1,
    String? licenceCode1,
    String? licencePoint1,
    String? licenceCode2,
    String? licencePoint2,
    String? licenceCode3,
    String? licencePoint3,
    String? recruitingJobCode,
    String? recruitingEmployCode,
    String? workPlaceCode1,
    String? workPlaceCode2,
    String? workPlaceCode3,
    String? payrollCode,
    String? payrollPriceFrom,
    String? payrollPriceTo,
    double? agentFee,
    String? agentFeeCurrCode,
    String? remoteWorkSkillPointType,
    String? globalSkillPointType,
    String? communicationSkillPointType,
    String? horensoSkillPointType,
    String? projectManagementSkillPointType,
    String? globalWorkExperience,
    String? searchQuery,
    int? careerType,
    List<String>? skills,
  }) {
    return ExploreUserState(
      exploreUser: exploreUser ?? this.exploreUser,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      filterId: filterId ?? this.filterId,
      totalCountFilter: totalCountFilter ?? this.totalCountFilter,
      totalCountItemsPage: totalCountItemsPage ?? this.totalCountItemsPage,
      showFavorite: showFavorite ?? this.showFavorite,
      ordering: ordering ?? this.ordering,
      selectedSearchType: selectedSearchType ?? this.selectedSearchType,
      page: page ?? this.page,
      ageFrom: ageFrom ?? this.ageFrom,
      ageTo: ageTo ?? this.ageTo,
      sexType: sexType ?? this.sexType,
      countryCode: countryCode ?? this.countryCode,
      addressCode1: addressCode1 ?? this.addressCode1,
      addressCode2: addressCode2 ?? this.addressCode2,
      lastAcademicCode: lastAcademicCode ?? this.lastAcademicCode,
      languageCode1: languageCode1 ?? this.languageCode1,
      languageLevelType1: languageLevelType1 ?? this.languageLevelType1,
      languageCode2: languageCode2 ?? this.languageCode2,
      languageLevelType2: languageLevelType2 ?? this.languageLevelType2,
      experiencedJobCode1: experiencedJobCode1 ?? this.experiencedJobCode1,
      yearsOfExperience1: yearsOfExperience1 ?? this.yearsOfExperience1,
      licenceCode1: licenceCode1 ?? this.licenceCode1,
      licencePoint1: licencePoint1 ?? this.licencePoint1,
      licenceCode2: licenceCode2 ?? this.licenceCode2,
      licencePoint2: licencePoint2 ?? this.licencePoint2,
      licenceCode3: licenceCode3 ?? this.licenceCode3,
      licencePoint3: licencePoint3 ?? this.licencePoint3,
      recruitingJobCode: recruitingJobCode ?? this.recruitingJobCode,
      recruitingEmployCode: recruitingEmployCode ?? this.recruitingEmployCode,
      workPlaceCode1: workPlaceCode1 ?? this.workPlaceCode1,
      workPlaceCode2: workPlaceCode2 ?? this.workPlaceCode2,
      workPlaceCode3: workPlaceCode3 ?? this.workPlaceCode3,
      payrollCode: payrollCode ?? this.payrollCode,
      payrollPriceFrom: payrollPriceFrom ?? this.payrollPriceFrom,
      payrollPriceTo: payrollPriceTo ?? this.payrollPriceTo,
      agentFee: agentFee ?? this.agentFee,
      agentFeeCurrCode: agentFeeCurrCode ?? this.agentFeeCurrCode,
      remoteWorkSkillPointType:
          remoteWorkSkillPointType ?? this.remoteWorkSkillPointType,
      globalSkillPointType: globalSkillPointType ?? this.globalSkillPointType,
      communicationSkillPointType:
          communicationSkillPointType ?? this.communicationSkillPointType,
      horensoSkillPointType:
          horensoSkillPointType ?? this.horensoSkillPointType,
      projectManagementSkillPointType: projectManagementSkillPointType ??
          this.projectManagementSkillPointType,
      globalWorkExperience: globalWorkExperience ?? this.globalWorkExperience,
      searchQuery: searchQuery ?? this.searchQuery,
      careerType: careerType ?? this.careerType,
      skills: skills ?? this.skills,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'showFavorite': showFavorite,
      'filterId': filterId,
      'ordering': ordering,
      'selectedSearchMode': selectedSearchType.valueRequestType,
      'page': page,
      'ageFrom': ageFrom,
      'ageTo': ageTo,
      'sexType': sexType,
      'countryCode': countryCode,
      'addressCode1': addressCode1,
      'addressCode2': addressCode2,
      'lastAcademicCode': lastAcademicCode,
      'languageCode1': languageCode1,
      'languageLevelType1': languageLevelType1,
      'languageCode2': languageCode2,
      'languageLevelType2': languageLevelType2,
      'experiencedJobCode1': experiencedJobCode1,
      'yearsOfExperience1': yearsOfExperience1,
      'licenceCode1': licenceCode1,
      'licencePoint1': licencePoint1,
      'licenceCode2': licenceCode2,
      'licencePoint2': licencePoint2,
      'licenceCode3': licenceCode3,
      'licencePoint3': licencePoint3,
      'recruitingJobCode': recruitingJobCode,
      'recruitingEmployCode': recruitingEmployCode,
      'workPlaceCode1': workPlaceCode1,
      'workPlaceCode2': workPlaceCode2,
      'workPlaceCode3': workPlaceCode3,
      'payrollCode': payrollCode,
      'payrollPriceFrom': payrollPriceFrom,
      'payrollPriceTo': payrollPriceTo,
      'agentFee': agentFee,
      'agentFeeCurrCode': agentFeeCurrCode,
      "totalCountFilter": totalCountFilter,
      'totalCountItemsPage': totalCountItemsPage,
      "remoteWorkSkillPointType": remoteWorkSkillPointType,
      "globalSkillPointType": globalSkillPointType,
      "communicationSkillPointType": communicationSkillPointType,
      "horensoSkillPointType": horensoSkillPointType,
      "projectManagementSkillPointType": projectManagementSkillPointType,
      'globalWorkExperience': globalWorkExperience,
      'searchQuery': searchQuery,
      'careerType': careerType,
      'skills': skills
    };
  }

  ExploreUserState updateFilter(Map<String, dynamic> data) {
    final newState = ExploreUserState(
      exploreUser: exploreUser,
      showFavorite: data['showFavorite'],
      totalCountFilter: data['totalCountFilter'] ?? totalCountFilter,
      totalCountItemsPage: data['totalCountItemsPage'] ?? totalCountItemsPage,
      page: data['page'] ?? page,
      filterId: data['filterId'],
      ordering: data['ordering'],
      ageFrom: data['ageFrom'],
      ageTo: data['ageTo'],
      sexType: data['sexType'],
      countryCode: data['countryCode'],
      addressCode1: data['addressCode1'],
      addressCode2: data['addressCode2'],
      lastAcademicCode: data['lastAcademicCode'],
      languageCode1: data['languageCode1'],
      languageLevelType1: data['languageLevelType1'],
      languageCode2: data['languageCode2'],
      languageLevelType2: data['languageLevelType2'],
      experiencedJobCode1: data['experiencedJobCode1'],
      yearsOfExperience1: data['yearsOfExperience1'],
      licenceCode1: data['licenceCode1'],
      licencePoint1: data['licencePoint1'],
      licenceCode2: data['licenceCode2'],
      licencePoint2: data['licencePoint2'],
      licenceCode3: data['licenceCode3'],
      licencePoint3: data['licencePoint3'],
      recruitingJobCode: data['recruitingJobCode'],
      recruitingEmployCode: data['recruitingEmployCode'],
      workPlaceCode1: data['workPlaceCode1'],
      workPlaceCode2: data['workPlaceCode2'],
      workPlaceCode3: data['workPlaceCode3'],
      payrollCode: data['payrollCode'],
      payrollPriceFrom: data['payrollPriceFrom'],
      payrollPriceTo: data['payrollPriceTo'],
      agentFee: data['agentFee'],
      agentFeeCurrCode: data['agentFeeCurrCode'],
      remoteWorkSkillPointType: data['remoteWorkSkillPointType'],
      globalSkillPointType: data['globalSkillPointType'],
      communicationSkillPointType: data['communicationSkillPointType'],
      horensoSkillPointType: data['horensoSkillPointType'],
      projectManagementSkillPointType: data['projectManagementSkillPointType'],
      globalWorkExperience: data['globalWorkExperience'],
      searchQuery: data['searchQuery'],
      careerType: data['careerType'],
      skills: data['skills'],
      selectedSearchType: SearchType.fromValue(
          data['selectedSearchMode'] ?? selectedSearchType.valueRequestType),
    );
    return newState;
  }

  /// Pre-built map of "empty" filters
  static const Map<String, dynamic> _emptyFilterData = {
    'filterId': null,
    'ageFrom': null,
    'ageTo': null,
    'sexType': null,
    'countryCode': null,
    'addressCode1': null,
    'addressCode2': null,
    'lastAcademicCode': null,
    'languageCode1': null,
    'languageLevelType1': null,
    'languageCode2': null,
    'languageLevelType2': null,
    'experiencedJobCode1': null,
    'yearsOfExperience1': null,
    'licenceCode1': null,
    'licencePoint1': null,
    'licenceCode2': null,
    'licencePoint2': null,
    'licenceCode3': null,
    'licencePoint3': null,
    'recruitingJobCode': null,
    'recruitingEmployCode': null,
    'workPlaceCode1': null,
    'workPlaceCode2': null,
    'workPlaceCode3': null,
    'payrollCode': null,
    'payrollPriceFrom': null,
    'payrollPriceTo': null,
    'agentFee': null,
    'agentFeeCurrCode': null,
    'remoteWorkSkillPointType': null,
    'globalSkillPointType': null,
    'communicationSkillPointType': null,
    'horensoSkillPointType': null,
    'projectManagementSkillPointType': null,
    'globalWorkExperience': null,
    'searchQuery': null,
    'careerType': null,
  };

  ExploreUserState clearAllFilters() {
    return updateFilter(_emptyFilterData);
  }

  @override
  List<Object?> get props => [
        exploreUser,
        isLoading,
        errorMessage,
        filterId,
        totalCountFilter,
        totalCountItemsPage,
        showFavorite,
        ordering,
        selectedSearchType.valueRequestType,
        page,
        ageFrom,
        ageTo,
        sexType,
        countryCode,
        addressCode1,
        addressCode2,
        lastAcademicCode,
        languageCode1,
        languageLevelType1,
        languageCode2,
        languageLevelType2,
        experiencedJobCode1,
        yearsOfExperience1,
        licenceCode1,
        licencePoint1,
        licenceCode2,
        licencePoint2,
        licenceCode3,
        licencePoint3,
        recruitingJobCode,
        recruitingEmployCode,
        workPlaceCode1,
        workPlaceCode2,
        workPlaceCode3,
        payrollCode,
        payrollPriceFrom,
        payrollPriceTo,
        agentFee,
        agentFeeCurrCode,
        remoteWorkSkillPointType,
        globalSkillPointType,
        communicationSkillPointType,
        horensoSkillPointType,
        projectManagementSkillPointType,
        globalWorkExperience,
        searchQuery,
        careerType,
        skills,
      ];
}
