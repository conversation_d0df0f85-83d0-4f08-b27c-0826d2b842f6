import 'dart:math';
import 'package:flutter/material.dart';
import '../resources/app_colors.dart';
import '../resources/app_constants.dart';
import '../resources/app_text_styles.dart';
import '../resources/enum.dart';
import '../utils/app_locale.dart';
import '../screens/host_companies/user/explore_user/widgets/ordering_filter_dropdown.dart';

/// Reusable pagination and sorting header component
/// Shows total count, pagination range, and sorting dropdown
class PaginationSortHeader extends StatelessWidget {
  final int totalCount;
  final int currentPage;
  final String totalCountLabel;
  final String rangeDisplayFunction;
  final OrderingOption? selectedOrdering;
  final Function(OrderingOption?)? onOrderingChanged;
  final String? sortByLabel;
  final EdgeInsets? padding;
  final int? itemsPerPage;
  final String? sortLabel;
  final VoidCallback? onSortPressed;
  final bool isSimpleMode;

  const PaginationSortHeader({
    super.key,
    required this.totalCount,
    required this.currentPage,
    required this.totalCountLabel,
    required this.rangeDisplayFunction,
    required this.selectedOrdering,
    required this.onOrderingChanged,
    required this.sortByLabel,
    this.padding,
    this.itemsPerPage,
  }) : sortLabel = null,
       onSortPressed = null,
       isSimpleMode = false;

  /// Factory constructor for engineer-related screens
  factory PaginationSortHeader.forEngineers({
    required BuildContext context,
    required int totalCount,
    required int currentPage,
    required OrderingOption selectedOrdering,
    required Function(OrderingOption?) onOrderingChanged,
    EdgeInsets? padding,
    int? itemsPerPage,
  }) {
    return PaginationSortHeader(
      totalCount: totalCount,
      currentPage: currentPage,
      totalCountLabel: AppText.value(context).relevant_engineer,
      rangeDisplayFunction: 'displayed_range_engineers',
      selectedOrdering: selectedOrdering,
      onOrderingChanged: onOrderingChanged,
      sortByLabel: AppText.value(context).sort_by,
      padding: padding,
      itemsPerPage: itemsPerPage,
    );
  }

  /// Factory constructor for applications/manage apply screens with simple sort
  factory PaginationSortHeader.forApplicationsSimple({
    required BuildContext context,
    required String totalCount,
    required String sortLabel,
    required VoidCallback onSortPressed,
    EdgeInsets? padding,
  }) {
    return PaginationSortHeader._simple(
      totalCountLabel: AppText.value(context).x_results(totalCount),
      sortLabel: sortLabel,
      onSortPressed: onSortPressed,
      padding: padding,
    );
  }

  /// Private constructor for simple version (no dropdown, just sort button)
  const PaginationSortHeader._simple({
    required this.totalCountLabel,
    required this.sortLabel,
    required this.onSortPressed,
    this.padding,
  }) : totalCount = 0,
       currentPage = 0,
       rangeDisplayFunction = '',
       selectedOrdering = null,
       onOrderingChanged = null,
       sortByLabel = null,
       itemsPerPage = null,
       isSimpleMode = true;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          isSimpleMode ? _buildSimpleInfo(context) : _buildPaginationInfo(context),
          isSimpleMode ? _buildSimpleSortButton(context) : _buildSortingDropdown(context),
        ],
      ),
    );
  }

  Widget _buildPaginationInfo(BuildContext context) {
    return Builder(builder: (context) {
      final int itemsPerPageValue = itemsPerPage ?? AppConstants.itemsPerPage;
      final int total = totalCount;
      int page = currentPage > 0 ? currentPage : 1;
      final int lastPage = (total / itemsPerPageValue)
          .ceil()
          .clamp(1, double.infinity)
          .toInt();
      final int currentPageClamped = page > lastPage ? lastPage : page;
      final bool shouldShowRange = total > itemsPerPageValue && total > 0;
      int startIndex = 1;
      int endIndex = total;
      
      if (shouldShowRange) {
        startIndex = (currentPageClamped - 1) * itemsPerPageValue + 1;
        endIndex = min(currentPageClamped * itemsPerPageValue, total);
      }

      return RichText(
        text: TextSpan(
          style: AppTextStyles.paragraph02v1(
              color: AppColors.iconColor,
              fontWeight: FontWeight.w500),
          children: [
            TextSpan(text: '$totalCountLabel: '),
            TextSpan(
                text: total.toString(),
                style: AppTextStyles.heading04(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w700)),
            if (shouldShowRange)
              WidgetSpan(
                child: Padding(
                  padding: const EdgeInsets.only(left: 4),
                  child: Text(
                    _getDisplayRangeText(context, startIndex, endIndex),
                    style: AppTextStyles.paragraph02v1(
                        color: AppColors.iconColor,
                        fontWeight: FontWeight.w500),
                  ),
                ),
              ),
          ],
        ),
      );
    });
  }

  Widget _buildSimpleInfo(BuildContext context) {
    return Text(
      totalCountLabel,
      style: AppTextStyles.paragraph02v2(),
    );
  }

  Widget _buildSimpleSortButton(BuildContext context) {
    return GestureDetector(
      onTap: onSortPressed,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.primary),
          borderRadius: BorderRadius.circular(5),
        ),
        child: Text(
          sortLabel ?? '',
          style: AppTextStyles.paragraph02v1(),
        ),
      ),
    );
  }

  Widget _buildSortingDropdown(BuildContext context) {
    if (selectedOrdering == null || onOrderingChanged == null || sortByLabel == null) {
      return const SizedBox.shrink();
    }
    return OrderingDropdown(
      selected: selectedOrdering!,
      onChanged: onOrderingChanged!,
      label: sortByLabel!,
    );
  }

  String _getDisplayRangeText(BuildContext context, int startIndex, int endIndex) {
    // Handle different range display functions based on the type
    switch (rangeDisplayFunction) {
      case 'displayed_range_engineers':
        return AppText.value(context).displayed_range_engineers(endIndex, startIndex);
      case 'displayed_range_applications':
        // If you have this function in your localization, use it
        // Otherwise, provide a fallback
        try {
          return AppText.value(context).displayed_range_engineers(endIndex, startIndex);
        } catch (e) {
          return '($startIndex-$endIndex)';
        }
      default:
        return '($startIndex-$endIndex)';
    }
  }
}

/// Extension to make it easier to use the component
extension PaginationSortHeaderExtension on Widget {
  /// Wrap with pagination sort header above this widget
  Widget withPaginationSortHeader({
    required BuildContext context,
    required int totalCount,
    required int currentPage,
    required OrderingOption selectedOrdering,
    required Function(OrderingOption?) onOrderingChanged,
    String? totalCountLabel,
    String? sortByLabel,
    EdgeInsets? padding,
    int? itemsPerPage,
  }) {
    return Column(
      children: [
        PaginationSortHeader(
          totalCount: totalCount,
          currentPage: currentPage,
          totalCountLabel: totalCountLabel ?? 'Items',
          rangeDisplayFunction: 'displayed_range_engineers',
          selectedOrdering: selectedOrdering,
          onOrderingChanged: onOrderingChanged,
          sortByLabel: sortByLabel ?? 'Sort by',
          padding: padding,
          itemsPerPage: itemsPerPage,
        ),
        this,
      ],
    );
  }
}
