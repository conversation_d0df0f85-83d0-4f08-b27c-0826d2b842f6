import 'package:asiantech_link/assets.gen.dart';
import 'package:asiantech_link/layers/presentation/resources/config_export.dart';
import 'package:asiantech_link/layers/presentation/utils/app_locale.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

class AiLoadingWidget extends StatefulWidget {
  final String? message;
  final double? size;

  const AiLoadingWidget({
    super.key,
    this.message,
    this.size,
  });

  @override
  State<AiLoadingWidget> createState() => _AiLoadingWidgetState();
}

class _AiLoadingWidgetState extends State<AiLoadingWidget>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;

  @override
  void initState() {
    super.initState();
    _rotationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Lottie Animation with error handling
          SizedBox(
            width: widget.size ?? 220,
            height: widget.size ?? 220,
            child: Lottie.asset(
              Assets.json.animationAiLoadingJSON,
              width: widget.size ?? 220,
              height: widget.size ?? 220,
              fit: BoxFit.contain,
              repeat: true,
              animate: true,
              errorBuilder: (context, error, stackTrace) {
                // Fallback to a simple AI icon with rotation animation
                return RotationTransition(
                  turns: _rotationController,
                  child: Icon(
                    Icons.psychology,
                    size: (widget.size ?? 120) * 0.6,
                    color: AppColors.primary,
                  ),
                );
              },
            ),
          ),

          const SizedBox(height: 16),

          // AI Loading Text
          Row(
            spacing: 8,
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  color: AppColors.primary,
                  strokeWidth: 2,
                ),
              ),
              Text(
                widget.message ?? AppText.value(context).ai_is_analyzing,
                style: AppTextStyles.paragraph01().copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Subtitle
          Text(
            AppText.value(context).please_wait_while_we_find_the_best_matches,
            style: AppTextStyles.paragraph02v1().copyWith(
              color: AppColors.iconColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
