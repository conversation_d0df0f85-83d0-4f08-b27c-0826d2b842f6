import 'package:asiantech_link/layers/presentation/blocs/companies/recruit/my_recruits/index.dart';
import 'package:asiantech_link/layers/presentation/resources/config_export.dart';
import 'package:asiantech_link/layers/presentation/utils/app_locale.dart';
import 'package:asiantech_link/layers/presentation/widgets/button/function_button/cancel_button.dart';
import 'package:asiantech_link/layers/presentation/widgets/button/function_button/primary_button.dart';
import 'package:asiantech_link/layers/presentation/widgets/error_app_widget.dart';
import 'package:asiantech_link/layers/presentation/widgets/gesture_detector_custom.dart';
import 'package:asiantech_link/layers/presentation/widgets/platform_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:openapi/openapi.dart';

class RecruitSelectionDialog extends StatefulWidget {
  const RecruitSelectionDialog({
    super.key,
  });

  @override
  State<RecruitSelectionDialog> createState() => _RecruitSelectionDialogState();
}

class _RecruitSelectionDialogState extends State<RecruitSelectionDialog> {
  RecruitUploadedSerializers? selectedRecruit;
  final MyRecruitsBloc _myRecruitBloc =
      MyRecruitsBloc(const UnMyRecruitsState());

  @override
  void initState() {
    super.initState();
    // Load recruits when dialog opens
    _myRecruitBloc.add(LoadMyRecruitsEvent(isLoadMore: false, displayFlag: 1));
  }

  @override
  void dispose() {
    _myRecruitBloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      backgroundColor: Colors.white,
      child: BlocBuilder<MyRecruitsBloc, MyRecruitsState>(
          bloc: _myRecruitBloc,
          builder: (context, state) {
            return Container(
              constraints: const BoxConstraints(
                maxWidth: 500,
                maxHeight: 600,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Header
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(12),
                        topRight: Radius.circular(12),
                      ),
                    ),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.work_outline,
                          color: Colors.white,
                          size: 24,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: PlatformText(
                            AppText.value(context)
                                .select_recruit_for_ai_analysis,
                            style: AppTextStyles.heading04().copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        GestureDetectorCustom(
                          onTap: () => Navigator.of(context).pop(),
                          child: const Icon(
                            Icons.close,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Content
                  Flexible(
                    child: Builder(builder: (context) {
                      if (state is LoadingMyRecruitsState) {
                        return const Center(
                          child: CircularProgressIndicator(),
                        );
                      }
                      if (state is ErrorMyRecruitsState) {
                        return ErrorAppWidget(
                          onPressed: () {
                            _myRecruitBloc.add(LoadMyRecruitsEvent(
                                onDone: () {}, isLoadMore: false));
                          },
                        );
                      }
                      if (state is InMyRecruitsState) {
                        var list = state.listRecruitModel.results ?? [];

                        if (list.isEmpty) {
                          return Expanded(
                            child: Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(
                                    Icons.work_off,
                                    size: 48,
                                    color: AppColors.iconColor,
                                  ),
                                  const SizedBox(height: 16),
                                  PlatformText(
                                    AppText.value(context)
                                        .no_recruits_available,
                                    style: AppTextStyles.paragraph01().copyWith(
                                      color: AppColors.iconColor,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }

                        return Container(
                          padding: const EdgeInsets.all(20),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Description
                              PlatformText(
                                AppText.value(context)
                                    .choose_recruit_for_ai_analysis,
                                style: AppTextStyles.paragraph02v1().copyWith(
                                  color: AppColors.iconColor,
                                ),
                              ),

                              const SizedBox(height: 16),

                              // Recruit list
                              Flexible(
                                child: ListView.separated(
                                  shrinkWrap: true,
                                  itemCount: list.length,
                                  separatorBuilder: (context, index) =>
                                      const Divider(
                                    height: 1,
                                    color: AppColors.lineColor,
                                  ),
                                  itemBuilder: (context, index) {
                                    final recruit = list[index]
                                        as RecruitUploadedSerializers;
                                    final isSelected =
                                        selectedRecruit?.recruitId ==
                                            recruit.recruitId;

                                    return GestureDetectorCustom(
                                      onTap: () {
                                        setState(() {
                                          selectedRecruit = recruit;
                                        });
                                      },
                                      child: Container(
                                        padding: const EdgeInsets.all(16),
                                        decoration: BoxDecoration(
                                          color: isSelected
                                              ? AppColors.primary
                                                  .withValues(alpha: 0.1)
                                              : Colors.transparent,
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        child: Row(
                                          children: [
                                            // Radio button
                                            Container(
                                              width: 20,
                                              height: 20,
                                              decoration: BoxDecoration(
                                                shape: BoxShape.circle,
                                                border: Border.all(
                                                  color: isSelected
                                                      ? AppColors.primary
                                                      : AppColors.iconColor,
                                                  width: 2,
                                                ),
                                                color: isSelected
                                                    ? AppColors.primary
                                                    : Colors.transparent,
                                              ),
                                              child: isSelected
                                                  ? const Icon(
                                                      Icons.check,
                                                      size: 12,
                                                      color: Colors.white,
                                                    )
                                                  : null,
                                            ),

                                            const SizedBox(width: 12),

                                            // Recruit info
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  PlatformText(
                                                    recruit.title ??
                                                        AppText.value(context)
                                                            .not_set,
                                                    style: AppTextStyles
                                                            .paragraph01()
                                                        .copyWith(
                                                      fontWeight:
                                                          FontWeight.w600,
                                                      color: isSelected
                                                          ? AppColors.primary
                                                          : AppColors.black,
                                                    ),
                                                    maxLines: 2,
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                  ),
                                                  if (recruit.catchCopy
                                                          ?.isNotEmpty ==
                                                      true) ...[
                                                    const SizedBox(height: 4),
                                                    PlatformText(
                                                      recruit.catchCopy!,
                                                      style: AppTextStyles
                                                              .paragraph02v1()
                                                          .copyWith(
                                                        color:
                                                            AppColors.iconColor,
                                                      ),
                                                      maxLines: 1,
                                                      overflow:
                                                          TextOverflow.ellipsis,
                                                    ),
                                                  ],
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),
                        );
                      }

                      // Fallback - should not reach here
                      return const Center(
                        child: CircularProgressIndicator(),
                      );
                    }),
                  ),

                  // Footer

                  Container(
                    padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
                    child: Row(
                      children: [
                        Expanded(
                          child: CancelButton(
                            buttonText: AppText.value(context).cancel,
                            onPressed: () => Navigator.of(context).pop(),
                            height: 40,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: PrimaryButton(
                            buttonText: AppText.value(context).key_continue,
                            onPressed: selectedRecruit != null
                                ? () =>
                                    Navigator.of(context).pop(selectedRecruit)
                                : () {},
                            height: 40,
                            valid: selectedRecruit != null,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          }),
    );
  }
}
