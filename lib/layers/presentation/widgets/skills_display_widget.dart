import 'dart:math';
import 'package:flutter/material.dart';
import '../resources/app_colors.dart';
import '../resources/app_text_styles.dart';
import '../services/app_services.dart';
import '../utils/app_locale.dart';
import 'gesture_detector_custom.dart';

/// Reusable widget for displaying skills with show more/less functionality
/// Shows skills as chips with experience levels and expandable view
class SkillsDisplayWidget extends StatefulWidget {
  final List<dynamic> skills;
  final Color? primaryColor;
  final int displayCount;
  final bool showExperience;
  final EdgeInsets? chipPadding;
  final double? chipSpacing;
  final double? runSpacing;

  const SkillsDisplayWidget({
    super.key,
    required this.skills,
    this.primaryColor,
    this.displayCount = 10,
    this.showExperience = true,
    this.chipPadding,
    this.chipSpacing,
    this.runSpacing,
  });

  @override
  State<SkillsDisplayWidget> createState() => _SkillsDisplayWidgetState();
}

class _SkillsDisplayWidgetState extends State<SkillsDisplayWidget> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    if (widget.skills.isEmpty) {
      return const SizedBox.shrink();
    }

    // Sort skills by level type (highest first)
    final skillsList = widget.skills.toList()
      ..sort((a, b) => (b.levelType ?? 0).compareTo(a.levelType ?? 0));

    final int total = skillsList.length;
    final int showCount = _isExpanded ? total : min(widget.displayCount, total);

    List<Widget> chips = [];
    for (var i = 0; i < showCount; i++) {
      final skill = skillsList[i];
      chips.add(_buildSkillChip(skill, context));
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Wrap(
          spacing: widget.chipSpacing ?? 8,
          runSpacing: widget.runSpacing ?? 8,
          children: chips,
        ),
        if (total > widget.displayCount) ...[
          const SizedBox(height: 8),
          _buildToggleButton(context),
        ],
      ],
    );
  }

  /// Build individual skill chip
  Widget _buildSkillChip(dynamic skill, BuildContext context) {
    String skillText = skill.skillName ?? '';
    
    if (widget.showExperience && skill.levelType != null) {
      final experience = AppServices.getYearsOfExperience(skill.levelType, context);
      skillText = "$skillText ($experience)";
    }

    return Container(
      padding: widget.chipPadding ?? const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: widget.primaryColor ?? AppColors.primary,
        borderRadius: BorderRadius.circular(5),
      ),
      child: Text(
        skillText,
        style: AppTextStyles.bodySmall(
          color: AppColors.whiteText,
          fontWeight: FontWeight.w700,
        ),
      ),
    );
  }

  /// Build show more/less toggle button
  Widget _buildToggleButton(BuildContext context) {
    return GestureDetectorCustom(
      onTap: () {
        setState(() {
          _isExpanded = !_isExpanded;
        });
      },
      child: Container(
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: widget.primaryColor ?? AppColors.primary,
              width: 0.8,
            ),
          ),
        ),
        child: Text(
          _isExpanded 
              ? AppText.value(context).show_less 
              : AppText.value(context).show_more,
          style: AppTextStyles.bodySmall(
            color: widget.primaryColor ?? AppColors.primary,
            fontWeight: FontWeight.w700,
          ),
        ),
      ),
    );
  }
}

/// Extension to easily add skills display to any widget
extension SkillsDisplayExtension on List<dynamic> {
  /// Convert skills list to SkillsDisplayWidget
  Widget toSkillsDisplay({
    Color? primaryColor,
    int displayCount = 10,
    bool showExperience = true,
    EdgeInsets? chipPadding,
    double? chipSpacing,
    double? runSpacing,
  }) {
    return SkillsDisplayWidget(
      skills: this,
      primaryColor: primaryColor,
      displayCount: displayCount,
      showExperience: showExperience,
      chipPadding: chipPadding,
      chipSpacing: chipSpacing,
      runSpacing: runSpacing,
    );
  }
}

/// Factory constructors for common use cases
extension SkillsDisplayFactory on SkillsDisplayWidget {
  /// Create skills display for user profiles
  static SkillsDisplayWidget forUserProfile({
    required List<dynamic> skills,
    Color? primaryColor,
  }) {
    return SkillsDisplayWidget(
      skills: skills,
      primaryColor: primaryColor,
      displayCount: 10,
      showExperience: true,
    );
  }

  /// Create skills display for compact views (like cards)
  static SkillsDisplayWidget forCompactView({
    required List<dynamic> skills,
    Color? primaryColor,
  }) {
    return SkillsDisplayWidget(
      skills: skills,
      primaryColor: primaryColor,
      displayCount: 5,
      showExperience: false,
      chipPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      chipSpacing: 4,
      runSpacing: 4,
    );
  }

  /// Create skills display without experience levels
  static SkillsDisplayWidget withoutExperience({
    required List<dynamic> skills,
    Color? primaryColor,
    int displayCount = 10,
  }) {
    return SkillsDisplayWidget(
      skills: skills,
      primaryColor: primaryColor,
      displayCount: displayCount,
      showExperience: false,
    );
  }
}
