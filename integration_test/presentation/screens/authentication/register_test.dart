import 'dart:math';
import 'package:asiantech_link/layers/presentation/screens/general/authentication/register_complete_screen/register_complete_screen.dart';
import 'package:asiantech_link/layers/presentation/screens/general/authentication/register_screen/register_screen.dart';
import 'package:asiantech_link/layers/presentation/screens/general/home_screen/home_screen.dart';
import 'package:asiantech_link/layers/presentation/utils/app_locale.dart';
import 'package:asiantech_link/layers/presentation/widgets/bottom_navigator.dart/bottom_navigator.dart';
import 'package:asiantech_link/layers/presentation/widgets/button/function_button/primary_button.dart';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:asiantech_link/layers/presentation/env/env_test.dart' as app;
import 'package:integration_test/integration_test.dart';

void main() {
  final binding = IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  binding.framePolicy = LiveTestWidgetsFlutterBindingFramePolicy.fullyLive;

  String generateRandomString(int length) {
    const characters = 'abcdefghijklmnopqrstuvwxyz0********9';
    final random = Random();
    return String.fromCharCodes(
      Iterable.generate(
        length,
        (_) => characters.codeUnitAt(random.nextInt(characters.length)),
      ),
    );
  }

  String generateRandomEmail() {
    final randomString = generateRandomString(10);
    return 'user$<EMAIL>';
  }

  testWidgets('Register with valid password and email',
      (WidgetTester tester) async {
    await app.main();
    await tester.pump(const Duration(seconds: 10));

    expect(find.byType(HomeScreen), findsOneWidget);

    final bottomNavigator = find.byType(BottomNavigator);
    expect(bottomNavigator, findsOneWidget);

    final lastIcon = find.byType(SvgPicture).last;
    await tester.pump(const Duration(seconds: 5));
    await tester.tap(lastIcon);
    await tester.pump(const Duration(seconds: 5));

    final Finder richTextFinder = find.byWidgetPredicate(
      (Widget widget) =>
          widget is RichText &&
          widget.text.toPlainText().contains(AppText.valueGlobal.createAccount),
    );

    await tester.tap(richTextFinder);
    await tester.pumpAndSettle();
    expect(find.byType(RegisterScreen), findsOneWidget);

    final emailField = find.byKey(const Key('email_register'));
    await tester.pump(const Duration(seconds: 5));
    final randomEmail = generateRandomEmail();
    await tester.enterText(emailField, randomEmail);

    final passwordField = find.byKey(const Key('password_register'));
    await tester.pump(const Duration(seconds: 3));
    await tester.enterText(passwordField, 'WeT\$KM3HT/Aq');

    final agreeCheckbox = find.byKey(const Key('agree_checkbox'));
    await tester.pump(const Duration(seconds: 3));
    await tester.tap(agreeCheckbox);

    final registerButton = find.byType(PrimaryButton);
    await tester.pump(const Duration(seconds: 3));
    await tester.tap(registerButton);
    await tester.pump(const Duration(seconds: 5));

    expect(find.byType(RegisterCompleteScreen), findsOneWidget);
  });

  testWidgets('Registered but haven\'t clicked the check mark yet',
      (WidgetTester tester) async {
    await app.main();
    await tester.pump(const Duration(seconds: 10));

    expect(find.byType(HomeScreen), findsOneWidget);

    final bottomNavigator = find.byType(BottomNavigator);
    expect(bottomNavigator, findsOneWidget);

    final lastIcon = find.byType(SvgPicture).last;
    await tester.pump(const Duration(seconds: 5));
    await tester.tap(lastIcon);
    await tester.pump(const Duration(seconds: 5));

    final Finder richTextFinder = find.byWidgetPredicate(
      (Widget widget) =>
          widget is RichText &&
          widget.text.toPlainText().contains(AppText.valueGlobal.createAccount),
    );

    await tester.tap(richTextFinder);
    await tester.pumpAndSettle();
    expect(find.byType(RegisterScreen), findsOneWidget);

    final emailField = find.byKey(const Key('email_register'));
    await tester.pump(const Duration(seconds: 5));
    final randomEmail = generateRandomEmail();
    await tester.enterText(emailField, randomEmail);

    final passwordField = find.byKey(const Key('password_register'));
    await tester.pump(const Duration(seconds: 3));
    await tester.enterText(passwordField, 'WeT\$KM3HT/Aq');

    final registerButton = find.byType(PrimaryButton);
    await tester.pump(const Duration(seconds: 3));
    await tester.tap(registerButton);
    await tester.pump(const Duration(seconds: 5));

    expect(find.byType(RegisterScreen), findsOneWidget);
  });

  testWidgets('Registered with password less than 8 characters',
      (WidgetTester tester) async {
    await app.main();
    await tester.pump(const Duration(seconds: 10));

    expect(find.byType(HomeScreen), findsOneWidget);

    final bottomNavigator = find.byType(BottomNavigator);
    expect(bottomNavigator, findsOneWidget);

    final lastIcon = find.byType(SvgPicture).last;
    await tester.pump(const Duration(seconds: 5));
    await tester.tap(lastIcon);
    await tester.pump(const Duration(seconds: 5));

    final Finder richTextFinder = find.byWidgetPredicate(
      (Widget widget) =>
          widget is RichText &&
          widget.text.toPlainText().contains(AppText.valueGlobal.createAccount),
    );

    await tester.tap(richTextFinder);
    await tester.pumpAndSettle();
    expect(find.byType(RegisterScreen), findsOneWidget);

    final emailField = find.byKey(const Key('email_register'));
    await tester.pump(const Duration(seconds: 3));
    final randomEmail = generateRandomEmail();
    await tester.enterText(emailField, randomEmail);

    final passwordField = find.byKey(const Key('password_register'));
    await tester.pump(const Duration(seconds: 3));
    await tester.enterText(passwordField, '1234567');

    final registerButton = find.byType(PrimaryButton);
    await tester.pump(const Duration(seconds: 3));
    await tester.tap(registerButton);
    await tester.pump(const Duration(seconds: 5));

    expect(find.text(AppText.valueGlobal.password_length), findsOneWidget);
  });

  testWidgets('Registered with password more than 20 characters',
      (WidgetTester tester) async {
    await app.main();
    await tester.pump(const Duration(seconds: 10));

    expect(find.byType(HomeScreen), findsOneWidget);

    final bottomNavigator = find.byType(BottomNavigator);
    expect(bottomNavigator, findsOneWidget);

    final lastIcon = find.byType(SvgPicture).last;
    await tester.pump(const Duration(seconds: 5));
    await tester.tap(lastIcon);
    await tester.pump(const Duration(seconds: 5));

    final Finder richTextFinder = find.byWidgetPredicate(
      (Widget widget) =>
          widget is RichText &&
          widget.text.toPlainText().contains(AppText.valueGlobal.createAccount),
    );

    await tester.tap(richTextFinder);
    await tester.pumpAndSettle();
    expect(find.byType(RegisterScreen), findsOneWidget);

    final emailField = find.byKey(const Key('email_register'));
    await tester.pump(const Duration(seconds: 3));
    final randomEmail = generateRandomEmail();
    await tester.enterText(emailField, randomEmail);

    final passwordField = find.byKey(const Key('password_register'));
    await tester.pump(const Duration(seconds: 3));
    await tester.enterText(passwordField, '********90********901');

    final registerButton = find.byType(PrimaryButton);
    await tester.pump(const Duration(seconds: 3));
    await tester.tap(registerButton);
    await tester.pump(const Duration(seconds: 5));

    expect(find.text(AppText.valueGlobal.password_length), findsOneWidget);
  });

  testWidgets(
      'Registered with password must contain at least one uppercase letter',
      (WidgetTester tester) async {
    await app.main();
    await tester.pump(const Duration(seconds: 10));

    expect(find.byType(HomeScreen), findsOneWidget);

    final bottomNavigator = find.byType(BottomNavigator);
    expect(bottomNavigator, findsOneWidget);

    final lastIcon = find.byType(SvgPicture).last;
    await tester.pump(const Duration(seconds: 5));
    await tester.tap(lastIcon);
    await tester.pump(const Duration(seconds: 5));

    final Finder richTextFinder = find.byWidgetPredicate(
      (Widget widget) =>
          widget is RichText &&
          widget.text.toPlainText().contains(AppText.valueGlobal.createAccount),
    );

    await tester.tap(richTextFinder);
    await tester.pumpAndSettle();
    expect(find.byType(RegisterScreen), findsOneWidget);

    final emailField = find.byKey(const Key('email_register'));
    await tester.pump(const Duration(seconds: 3));
    final randomEmail = generateRandomEmail();
    await tester.enterText(emailField, randomEmail);

    final passwordField = find.byKey(const Key('password_register'));
    await tester.pump(const Duration(seconds: 3));
    await tester.enterText(passwordField, '********');

    final registerButton = find.byType(PrimaryButton);
    await tester.pump(const Duration(seconds: 3));
    await tester.tap(registerButton);
    await tester.pump(const Duration(seconds: 5));

    expect(
        find.text(AppText.valueGlobal.password_requirements), findsOneWidget);
  });

  testWidgets(
      'Registered with password must contain at least one lowercase letter',
      (WidgetTester tester) async {
    await app.main();
    await tester.pump(const Duration(seconds: 10));

    expect(find.byType(HomeScreen), findsOneWidget);

    final bottomNavigator = find.byType(BottomNavigator);
    expect(bottomNavigator, findsOneWidget);

    final lastIcon = find.byType(SvgPicture).last;
    await tester.pump(const Duration(seconds: 5));
    await tester.tap(lastIcon);
    await tester.pump(const Duration(seconds: 5));

    final Finder richTextFinder = find.byWidgetPredicate(
      (Widget widget) =>
          widget is RichText &&
          widget.text.toPlainText().contains(AppText.valueGlobal.createAccount),
    );

    await tester.tap(richTextFinder);
    await tester.pumpAndSettle();
    expect(find.byType(RegisterScreen), findsOneWidget);

    final emailField = find.byKey(const Key('email_register'));
    await tester.pump(const Duration(seconds: 3));
    final randomEmail = generateRandomEmail();
    await tester.enterText(emailField, randomEmail);

    final passwordField = find.byKey(const Key('password_register'));
    await tester.pump(const Duration(seconds: 3));
    await tester.enterText(passwordField, '********A');

    final registerButton = find.byType(PrimaryButton);
    await tester.pump(const Duration(seconds: 3));
    await tester.tap(registerButton);
    await tester.pump(const Duration(seconds: 5));

    expect(
        find.text(AppText.valueGlobal.password_requirements), findsOneWidget);
  });

  testWidgets(
      'Registered with password must contain at least one special character',
      (WidgetTester tester) async {
    await app.main();
    await tester.pump(const Duration(seconds: 10));

    expect(find.byType(HomeScreen), findsOneWidget);

    final bottomNavigator = find.byType(BottomNavigator);
    expect(bottomNavigator, findsOneWidget);

    final lastIcon = find.byType(SvgPicture).last;
    await tester.pump(const Duration(seconds: 5));
    await tester.tap(lastIcon);
    await tester.pump(const Duration(seconds: 5));

    final Finder richTextFinder = find.byWidgetPredicate(
      (Widget widget) =>
          widget is RichText &&
          widget.text.toPlainText().contains(AppText.valueGlobal.createAccount),
    );

    await tester.tap(richTextFinder);
    await tester.pumpAndSettle();
    expect(find.byType(RegisterScreen), findsOneWidget);

    final emailField = find.byKey(const Key('email_register'));
    await tester.pump(const Duration(seconds: 3));
    final randomEmail = generateRandomEmail();
    await tester.enterText(emailField, randomEmail);

    final passwordField = find.byKey(const Key('password_register'));
    await tester.pump(const Duration(seconds: 3));
    await tester.enterText(passwordField, '********Aa');

    final registerButton = find.byType(PrimaryButton);
    await tester.pump(const Duration(seconds: 3));
    await tester.tap(registerButton);
    await tester.pump(const Duration(seconds: 5));

    expect(
        find.text(AppText.valueGlobal.password_requirements), findsOneWidget);
  });

  testWidgets('Registered with email contains the \'@\' character',
      (WidgetTester tester) async {
    await app.main();
    await tester.pump(const Duration(seconds: 10));

    expect(find.byType(HomeScreen), findsOneWidget);

    final bottomNavigator = find.byType(BottomNavigator);
    expect(bottomNavigator, findsOneWidget);

    final lastIcon = find.byType(SvgPicture).last;
    await tester.pump(const Duration(seconds: 5));
    await tester.tap(lastIcon);
    await tester.pump(const Duration(seconds: 5));

    final Finder richTextFinder = find.byWidgetPredicate(
      (Widget widget) =>
          widget is RichText &&
          widget.text.toPlainText().contains(AppText.valueGlobal.createAccount),
    );

    await tester.tap(richTextFinder);
    await tester.pumpAndSettle();
    expect(find.byType(RegisterScreen), findsOneWidget);

    final emailField = find.byKey(const Key('email_register'));
    await tester.pump(const Duration(seconds: 3));
    await tester.enterText(emailField, 'example.com');

    final passwordField = find.byKey(const Key('password_register'));
    await tester.pump(const Duration(seconds: 3));
    await tester.enterText(passwordField, 'k&KzxAA9s(D\'');

    final registerButton = find.byType(PrimaryButton);
    await tester.pump(const Duration(seconds: 3));
    await tester.tap(registerButton);
    await tester.pump(const Duration(seconds: 5));

    expect(
        find.text(AppText.valueGlobal.email_single_at_symbol), findsOneWidget);
  });

  testWidgets('Registered with email without domain name',
      (WidgetTester tester) async {
    await app.main();
    await tester.pump(const Duration(seconds: 10));

    expect(find.byType(HomeScreen), findsOneWidget);

    final bottomNavigator = find.byType(BottomNavigator);
    expect(bottomNavigator, findsOneWidget);

    final lastIcon = find.byType(SvgPicture).last;
    await tester.pump(const Duration(seconds: 5));
    await tester.tap(lastIcon);
    await tester.pump(const Duration(seconds: 5));

    final Finder richTextFinder = find.byWidgetPredicate(
      (Widget widget) =>
          widget is RichText &&
          widget.text.toPlainText().contains(AppText.valueGlobal.createAccount),
    );

    await tester.tap(richTextFinder);
    await tester.pumpAndSettle();
    expect(find.byType(RegisterScreen), findsOneWidget);

    final emailField = find.byKey(const Key('email_register'));
    await tester.pump(const Duration(seconds: 3));
    await tester.enterText(emailField, 'example@');

    final passwordField = find.byKey(const Key('password_register'));
    await tester.pump(const Duration(seconds: 3));
    await tester.enterText(passwordField, 'k&KzxAA9s(D\'');

    final registerButton = find.byType(PrimaryButton);
    await tester.pump(const Duration(seconds: 3));
    await tester.tap(registerButton);
    await tester.pump(const Duration(seconds: 5));

    expect(find.text(AppText.valueGlobal.email_invalid_format), findsOneWidget);
  });

  testWidgets('Registered with email without user name',
      (WidgetTester tester) async {
    await app.main();
    await tester.pump(const Duration(seconds: 10));

    expect(find.byType(HomeScreen), findsOneWidget);

    final bottomNavigator = find.byType(BottomNavigator);
    expect(bottomNavigator, findsOneWidget);

    final lastIcon = find.byType(SvgPicture).last;
    await tester.pump(const Duration(seconds: 5));
    await tester.tap(lastIcon);
    await tester.pump(const Duration(seconds: 5));

    final Finder richTextFinder = find.byWidgetPredicate(
      (Widget widget) =>
          widget is RichText &&
          widget.text.toPlainText().contains(AppText.valueGlobal.createAccount),
    );

    await tester.tap(richTextFinder);
    await tester.pumpAndSettle();
    expect(find.byType(RegisterScreen), findsOneWidget);

    final emailField = find.byKey(const Key('email_register'));
    await tester.pump(const Duration(seconds: 3));
    await tester.enterText(emailField, '@example.com');

    final passwordField = find.byKey(const Key('password_register'));
    await tester.pump(const Duration(seconds: 3));
    await tester.enterText(passwordField, 'k&KzxAA9s(D\'');

    final registerButton = find.byType(PrimaryButton);
    await tester.pump(const Duration(seconds: 3));
    await tester.tap(registerButton);
    await tester.pump(const Duration(seconds: 5));

    expect(find.text(AppText.valueGlobal.email_invalid_format), findsOneWidget);
  });

  testWidgets('Registered with email contains invalid character',
      (WidgetTester tester) async {
    await app.main();
    await tester.pump(const Duration(seconds: 10));

    expect(find.byType(HomeScreen), findsOneWidget);

    final bottomNavigator = find.byType(BottomNavigator);
    expect(bottomNavigator, findsOneWidget);

    final lastIcon = find.byType(SvgPicture).last;
    await tester.pump(const Duration(seconds: 5));
    await tester.tap(lastIcon);
    await tester.pump(const Duration(seconds: 5));

    final Finder richTextFinder = find.byWidgetPredicate(
      (Widget widget) =>
          widget is RichText &&
          widget.text.toPlainText().contains(AppText.valueGlobal.createAccount),
    );

    await tester.tap(richTextFinder);
    await tester.pumpAndSettle();
    expect(find.byType(RegisterScreen), findsOneWidget);

    final emailField = find.byKey(const Key('email_register'));
    await tester.pump(const Duration(seconds: 3));
    await tester.enterText(emailField, '<EMAIL>');

    final passwordField = find.byKey(const Key('password_register'));
    await tester.pump(const Duration(seconds: 3));
    await tester.enterText(passwordField, 'k&KzxAA9s(D\'');

    final registerButton = find.byType(PrimaryButton);
    await tester.pump(const Duration(seconds: 3));
    await tester.tap(registerButton);
    await tester.pump(const Duration(seconds: 5));

    expect(find.text(AppText.valueGlobal.email_invalid_format), findsOneWidget);
  });

  testWidgets('Registered with email is only one \'@\' character is allowed',
      (WidgetTester tester) async {
    await app.main();
    await tester.pump(const Duration(seconds: 10));

    expect(find.byType(HomeScreen), findsOneWidget);

    final bottomNavigator = find.byType(BottomNavigator);
    expect(bottomNavigator, findsOneWidget);

    final lastIcon = find.byType(SvgPicture).last;
    await tester.pump(const Duration(seconds: 5));
    await tester.tap(lastIcon);
    await tester.pump(const Duration(seconds: 5));

    final Finder richTextFinder = find.byWidgetPredicate(
      (Widget widget) =>
          widget is RichText &&
          widget.text.toPlainText().contains(AppText.valueGlobal.createAccount),
    );

    await tester.tap(richTextFinder);
    await tester.pumpAndSettle();
    expect(find.byType(RegisterScreen), findsOneWidget);

    final emailField = find.byKey(const Key('email_register'));
    await tester.pump(const Duration(seconds: 3));
    await tester.enterText(emailField, 'exa@<EMAIL>');

    final passwordField = find.byKey(const Key('password_register'));
    await tester.pump(const Duration(seconds: 3));
    await tester.enterText(passwordField, 'k&KzxAA9s(D\'');

    final registerButton = find.byType(PrimaryButton);
    await tester.pump(const Duration(seconds: 3));
    await tester.tap(registerButton);
    await tester.pump(const Duration(seconds: 5));

    expect(
        find.text(AppText.valueGlobal.email_single_at_symbol), findsOneWidget);
  });

  testWidgets('Registered with email has invalid domain name',
      (WidgetTester tester) async {
    await app.main();
    await tester.pump(const Duration(seconds: 10));

    expect(find.byType(HomeScreen), findsOneWidget);

    final bottomNavigator = find.byType(BottomNavigator);
    expect(bottomNavigator, findsOneWidget);

    final lastIcon = find.byType(SvgPicture).last;
    await tester.pump(const Duration(seconds: 5));
    await tester.tap(lastIcon);
    await tester.pump(const Duration(seconds: 5));

    final Finder richTextFinder = find.byWidgetPredicate(
      (Widget widget) =>
          widget is RichText &&
          widget.text.toPlainText().contains(AppText.valueGlobal.createAccount),
    );

    await tester.tap(richTextFinder);
    await tester.pumpAndSettle();
    expect(find.byType(RegisterScreen), findsOneWidget);

    final emailField = find.byKey(const Key('email_register'));
    await tester.pump(const Duration(seconds: 3));
    await tester.enterText(emailField, 'example@examplecom');

    final passwordField = find.byKey(const Key('password_register'));
    await tester.pump(const Duration(seconds: 3));
    await tester.enterText(passwordField, 'k&KzxAA9s(D\'');

    final registerButton = find.byType(PrimaryButton);
    await tester.pump(const Duration(seconds: 3));
    await tester.tap(registerButton);
    await tester.pump(const Duration(seconds: 5));

    expect(
        find.text(AppText.valueGlobal.email_single_at_symbol), findsOneWidget);
  });

  testWidgets(
      'Registered with email has an invalid or missing Domain Extension',
      (WidgetTester tester) async {
    await app.main();
    await tester.pump(const Duration(seconds: 10));

    expect(find.byType(HomeScreen), findsOneWidget);

    final bottomNavigator = find.byType(BottomNavigator);
    expect(bottomNavigator, findsOneWidget);

    final lastIcon = find.byType(SvgPicture).last;
    await tester.pump(const Duration(seconds: 5));
    await tester.tap(lastIcon);
    await tester.pump(const Duration(seconds: 5));

    final Finder richTextFinder = find.byWidgetPredicate(
      (Widget widget) =>
          widget is RichText &&
          widget.text.toPlainText().contains(AppText.valueGlobal.createAccount),
    );

    await tester.tap(richTextFinder);
    await tester.pumpAndSettle();
    expect(find.byType(RegisterScreen), findsOneWidget);

    final emailField = find.byKey(const Key('email_register'));
    await tester.pump(const Duration(seconds: 3));
    await tester.enterText(emailField, 'example@example.c');

    final passwordField = find.byKey(const Key('password_register'));
    await tester.pump(const Duration(seconds: 3));
    await tester.enterText(passwordField, 'k&KzxAA9s(D\'');

    final registerButton = find.byType(PrimaryButton);
    await tester.pump(const Duration(seconds: 3));
    await tester.tap(registerButton);
    await tester.pump(const Duration(seconds: 5));

    expect(
        find.text(AppText.valueGlobal.email_single_at_symbol), findsOneWidget);
  });

  testWidgets('Registered with email contains spaces',
      (WidgetTester tester) async {
    await app.main();
    await tester.pump(const Duration(seconds: 10));

    expect(find.byType(HomeScreen), findsOneWidget);

    final bottomNavigator = find.byType(BottomNavigator);
    expect(bottomNavigator, findsOneWidget);

    final lastIcon = find.byType(SvgPicture).last;
    await tester.pump(const Duration(seconds: 5));
    await tester.tap(lastIcon);
    await tester.pump(const Duration(seconds: 5));

    final Finder richTextFinder = find.byWidgetPredicate(
      (Widget widget) =>
          widget is RichText &&
          widget.text.toPlainText().contains(AppText.valueGlobal.createAccount),
    );

    await tester.tap(richTextFinder);
    await tester.pumpAndSettle();
    expect(find.byType(RegisterScreen), findsOneWidget);

    final emailField = find.byKey(const Key('email_register'));
    await tester.pump(const Duration(seconds: 3));
    await tester.enterText(emailField, 'exa <EMAIL>');

    final passwordField = find.byKey(const Key('password_register'));
    await tester.pump(const Duration(seconds: 3));
    await tester.enterText(passwordField, 'k&KzxAA9s(D\'');

    final registerButton = find.byType(PrimaryButton);
    await tester.pump(const Duration(seconds: 3));
    await tester.tap(registerButton);
    await tester.pump(const Duration(seconds: 5));

    expect(
        find.text(AppText.valueGlobal.email_single_at_symbol), findsOneWidget);
  });

  testWidgets(
      'Registered with email uses square brackets for the IP address in the domain name, which is invalid',
      (WidgetTester tester) async {
    await app.main();
    await tester.pump(const Duration(seconds: 10));

    expect(find.byType(HomeScreen), findsOneWidget);

    final bottomNavigator = find.byType(BottomNavigator);
    expect(bottomNavigator, findsOneWidget);

    final lastIcon = find.byType(SvgPicture).last;
    await tester.pump(const Duration(seconds: 5));
    await tester.tap(lastIcon);
    await tester.pump(const Duration(seconds: 5));

    final Finder richTextFinder = find.byWidgetPredicate(
      (Widget widget) =>
          widget is RichText &&
          widget.text.toPlainText().contains(AppText.valueGlobal.createAccount),
    );

    await tester.tap(richTextFinder);
    await tester.pumpAndSettle();
    expect(find.byType(RegisterScreen), findsOneWidget);

    final emailField = find.byKey(const Key('email_register'));
    await tester.pump(const Duration(seconds: 3));
    await tester.enterText(emailField, 'example@[192.168.1.1]');

    final passwordField = find.byKey(const Key('password_register'));
    await tester.pump(const Duration(seconds: 3));
    await tester.enterText(passwordField, 'k&KzxAA9s(D\'');

    final registerButton = find.byType(PrimaryButton);
    await tester.pump(const Duration(seconds: 3));
    await tester.tap(registerButton);
    await tester.pump(const Duration(seconds: 5));

    expect(
        find.text(AppText.valueGlobal.email_single_at_symbol), findsOneWidget);
  });

  testWidgets(
      'Registered with email uses square brackets for the IP address in the domain name, which is invalid',
      (WidgetTester tester) async {
    await app.main();
    await tester.pump(const Duration(seconds: 10));

    expect(find.byType(HomeScreen), findsOneWidget);

    final bottomNavigator = find.byType(BottomNavigator);
    expect(bottomNavigator, findsOneWidget);

    final lastIcon = find.byType(SvgPicture).last;
    await tester.pump(const Duration(seconds: 5));
    await tester.tap(lastIcon);
    await tester.pump(const Duration(seconds: 5));

    final Finder richTextFinder = find.byWidgetPredicate(
      (Widget widget) =>
          widget is RichText &&
          widget.text.toPlainText().contains(AppText.valueGlobal.createAccount),
    );

    await tester.tap(richTextFinder);
    await tester.pumpAndSettle();
    expect(find.byType(RegisterScreen), findsOneWidget);

    final emailField = find.byKey(const Key('email_register'));
    await tester.pump(const Duration(seconds: 3));
    await tester.enterText(emailField, 'example@[192.168.1.1]');

    final passwordField = find.byKey(const Key('password_register'));
    await tester.pump(const Duration(seconds: 3));
    await tester.enterText(passwordField, 'k&KzxAA9s(D\'');

    final registerButton = find.byType(PrimaryButton);
    await tester.pump(const Duration(seconds: 3));
    await tester.tap(registerButton);
    await tester.pump(const Duration(seconds: 5));

    expect(
        find.text(AppText.valueGlobal.email_single_at_symbol), findsOneWidget);
  });

  testWidgets(
      'Registered with email uses square brackets for the IP address in the domain name, which is invalid',
      (WidgetTester tester) async {
    await app.main();
    await tester.pump(const Duration(seconds: 10));

    expect(find.byType(HomeScreen), findsOneWidget);

    final bottomNavigator = find.byType(BottomNavigator);
    expect(bottomNavigator, findsOneWidget);

    final lastIcon = find.byType(SvgPicture).last;
    await tester.pump(const Duration(seconds: 5));
    await tester.tap(lastIcon);
    await tester.pump(const Duration(seconds: 5));

    final Finder richTextFinder = find.byWidgetPredicate(
      (Widget widget) =>
          widget is RichText &&
          widget.text.toPlainText().contains(AppText.valueGlobal.createAccount),
    );

    await tester.tap(richTextFinder);
    await tester.pumpAndSettle();
    expect(find.byType(RegisterScreen), findsOneWidget);

    final emailField = find.byKey(const Key('email_register'));
    await tester.pump(const Duration(seconds: 3));
    await tester.enterText(emailField, 'example@[192.168.1.1]');

    final passwordField = find.byKey(const Key('password_register'));
    await tester.pump(const Duration(seconds: 3));
    await tester.enterText(passwordField, 'k&KzxAA9s(D\'');

    final registerButton = find.byType(PrimaryButton);
    await tester.pump(const Duration(seconds: 3));
    await tester.tap(registerButton);
    await tester.pump(const Duration(seconds: 5));

    expect(
        find.text(AppText.valueGlobal.email_single_at_symbol), findsOneWidget);
  });

  testWidgets('Registered email and password are empty',
      (WidgetTester tester) async {
    await app.main();
    await tester.pump(const Duration(seconds: 10));

    expect(find.byType(HomeScreen), findsOneWidget);

    final bottomNavigator = find.byType(BottomNavigator);
    expect(bottomNavigator, findsOneWidget);

    final lastIcon = find.byType(SvgPicture).last;
    await tester.pump(const Duration(seconds: 5));
    await tester.tap(lastIcon);
    await tester.pump(const Duration(seconds: 5));

    final Finder richTextFinder = find.byWidgetPredicate(
      (Widget widget) =>
          widget is RichText &&
          widget.text.toPlainText().contains(AppText.valueGlobal.createAccount),
    );

    await tester.tap(richTextFinder);
    await tester.pumpAndSettle();
    expect(find.byType(RegisterScreen), findsOneWidget);

    final emailField = find.byKey(const Key('email_register'));
    await tester.pump(const Duration(seconds: 3));
    await tester.enterText(emailField, '');

    final passwordField = find.byKey(const Key('password_register'));
    await tester.pump(const Duration(seconds: 3));
    await tester.enterText(passwordField, '');

    final registerButton = find.byType(PrimaryButton);
    await tester.pump(const Duration(seconds: 3));
    await tester.tap(registerButton);
    await tester.pump(const Duration(seconds: 5));

    expect(find.text(AppText.valueGlobal.please_enter_email), findsOneWidget);
    expect(
        find.text(AppText.valueGlobal.please_enter_password), findsOneWidget);
  });

  testWidgets('Registered email is already registered',
      (WidgetTester tester) async {
    await app.main();
    await tester.pump(const Duration(seconds: 10));

    expect(find.byType(HomeScreen), findsOneWidget);

    final bottomNavigator = find.byType(BottomNavigator);
    expect(bottomNavigator, findsOneWidget);

    final lastIcon = find.byType(SvgPicture).last;
    await tester.pump(const Duration(seconds: 5));
    await tester.tap(lastIcon);
    await tester.pump(const Duration(seconds: 5));

    final Finder richTextFinder = find.byWidgetPredicate(
      (Widget widget) =>
          widget is RichText &&
          widget.text.toPlainText().contains(AppText.valueGlobal.createAccount),
    );

    await tester.tap(richTextFinder);
    await tester.pumpAndSettle();
    expect(find.byType(RegisterScreen), findsOneWidget);

    final emailField = find.byKey(const Key('email_register'));
    await tester.pump(const Duration(seconds: 3));
    await tester.enterText(emailField, '<EMAIL>');

    final passwordField = find.byKey(const Key('password_register'));
    await tester.pump(const Duration(seconds: 3));
    await tester.enterText(passwordField, '********Kk!');

    final agreeCheckbox = find.byKey(const Key('agree_checkbox'));
    await tester.pump(const Duration(seconds: 3));
    await tester.tap(agreeCheckbox);
    await tester.pump(const Duration(seconds: 5));

    final registerButton = find.byType(PrimaryButton);
    await tester.pump(const Duration(seconds: 3));
    await tester.tap(registerButton);
    await tester.pump(const Duration(seconds: 5));
    expect(find.text(AppText.valueGlobal.usedEmail), findsOneWidget);
  });
}
