import 'package:asiantech_link/layers/presentation/resources/app_errors.dart';
import 'package:asiantech_link/layers/presentation/screens/general/authentication/forgot_password/forgot_password_screen.dart';
import 'package:asiantech_link/layers/presentation/screens/general/authentication/login/login_bottom_sheet.dart';
import 'package:asiantech_link/layers/presentation/screens/general/authentication/two_step_auth/two_step_auth_screen.dart';
import 'package:asiantech_link/layers/presentation/screens/general/home_screen/home_screen.dart';
import 'package:asiantech_link/layers/presentation/utils/app_locale.dart';
import 'package:asiantech_link/layers/presentation/widgets/bottom_navigator.dart/bottom_navigator.dart';
import 'package:asiantech_link/layers/presentation/widgets/button/function_button/primary_button.dart';

import 'package:asiantech_link/layers/presentation/widgets/title_text_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:asiantech_link/layers/presentation/env/env_test.dart' as app;
import 'package:integration_test/integration_test.dart';

void main() {
  final binding = IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  binding.framePolicy = LiveTestWidgetsFlutterBindingFramePolicy.fullyLive;
  testWidgets('Login with valid password and email',
      (WidgetTester tester) async {
    await app.main();
    await tester.pump(const Duration(seconds: 15));

    expect(find.byType(HomeScreen), findsOneWidget);

    final bottomNavigator = find.byType(BottomNavigator);
    expect(bottomNavigator, findsOneWidget);

    final lastIcon = find.byType(SvgPicture).last;
    await tester.pump(const Duration(seconds: 10));
    await tester.tap(lastIcon);
    await tester.pump(const Duration(seconds: 10));
    expect(find.byType(LoginBottomSheet), findsOneWidget);

    final emailField = find.byWidgetPredicate((widget) =>
        widget is TitleTextField &&
        widget.title == AppText.valueGlobal.userNameOrEmail);
    await tester.pump(const Duration(seconds: 5));
    await tester.enterText(emailField, '<EMAIL>');

    final passwordField = find.byWidgetPredicate((widget) =>
        widget is TitleTextField &&
        widget.title == AppText.valueGlobal.password);
    await tester.pump(const Duration(seconds: 5));
    await tester.enterText(passwordField, 'WeT\$KM3HT/Aq');

    final loginButton = find.byType(PrimaryButton);
    await tester.pump(const Duration(seconds: 5));
    await tester.tap(loginButton);
    await tester.pump(const Duration(seconds: 10));
    expect(find.byType(TwoStepAuthScreen), findsOneWidget);
  });

  //Error button UI
  // testWidgets('Captcha popup appears when pressing the resend button',
  //     (WidgetTester tester) async {
  //   await app.main();
  //   await tester.pump(const Duration(seconds: 15));

  //   expect(find.byType(HomeScreen), findsOneWidget);

  //   final bottomNavigator = find.byType(BottomNavigator);
  //   expect(bottomNavigator, findsOneWidget);

  //   final lastIcon = find.byType(SvgPicture).last;
  //   await tester.pump(const Duration(seconds: 10));
  //   await tester.tap(lastIcon);
  //   await tester.pump(const Duration(seconds: 10));
  //   expect(find.byType(LoginBottomSheet), findsOneWidget);

  //   final emailField = find.byWidgetPredicate((widget) =>
  //       widget is TitleTextField &&
  //       widget.title == AppText.valueGlobal.userNameOrEmail);
  //   await tester.pump(const Duration(seconds: 5));
  //   await tester.enterText(emailField, '<EMAIL>');

  //   final passwordField = find.byWidgetPredicate((widget) =>
  //       widget is TitleTextField && widget.title == AppText.valueGlobal.password);
  //   await tester.pump(const Duration(seconds: 5));
  //   await tester.enterText(passwordField, '3.}~5bx{A#WX');

  //   final loginButton = find.byType(PrimaryButton);
  //   await tester.pump(const Duration(seconds: 5));
  //   await tester.tap(loginButton);
  //   await tester.pump(const Duration(seconds: 10));

  //   final safeTap = find.byKey(const Key('resend_two_step_code'));
  //   await tester.pump(const Duration(seconds: 5));
  //   await tester.tap(safeTap);
  //   await tester.pump(const Duration(seconds: 5));
  //   expect(find.byType(CaptchaLoginPopUp), findsOneWidget);

  //   final captchaTextFieldFinder = find.byType(AppTextField);
  //   await tester.pump(const Duration(seconds: 5));
  //   await tester.enterText(captchaTextFieldFinder, '1234');

  //   final PrimaryButton = find.byKey(const Key('submit_two_step_code'));
  //   await tester.pump(const Duration(seconds: 5));
  //   await tester.tap(PrimaryButton);
  //   await tester.pump(const Duration(seconds: 10));
  //   expect(find.text('Error captcha, please try again'), findsOneWidget);
  //   expect(find.byType(CaptchaLoginPopUp), findsOneWidget);
  // });

  testWidgets(
      'Check login with registered email and password but not confirmed by email.',
      (WidgetTester tester) async {
    await app.main();
    await tester.pump(const Duration(seconds: 15));

    expect(find.byType(HomeScreen), findsOneWidget);

    final bottomNavigator = find.byType(BottomNavigator);
    expect(bottomNavigator, findsOneWidget);

    final lastIcon = find.byType(SvgPicture).last;
    await tester.pump(const Duration(seconds: 5));
    await tester.tap(lastIcon);
    await tester.pump(const Duration(seconds: 5));
    expect(find.byType(LoginBottomSheet), findsOneWidget);

    final emailField = find.byWidgetPredicate((widget) =>
        widget is TitleTextField &&
        widget.title == AppText.valueGlobal.userNameOrEmail);
    await tester.pump(const Duration(seconds: 5));
    await tester.enterText(emailField, '<EMAIL>');

    final passwordField = find.byWidgetPredicate((widget) =>
        widget is TitleTextField &&
        widget.title == AppText.valueGlobal.password);
    await tester.pump(const Duration(seconds: 5));
    await tester.enterText(passwordField, '12345678Kk!');

    final loginButton = find.byType(PrimaryButton);
    await tester.pump(const Duration(seconds: 5));
    await tester.tap(loginButton);
    await tester.pump(const Duration(seconds: 5));

    expect(
        find.text(
            'You need to verify your email address now in order to sign in to Asiantech Link'),
        findsOneWidget);
  });

  testWidgets('Check to log in with email and wrong password',
      (WidgetTester tester) async {
    await app.main();
    await tester.pump(const Duration(seconds: 15));

    expect(find.byType(HomeScreen), findsOneWidget);

    final bottomNavigator = find.byType(BottomNavigator);
    expect(bottomNavigator, findsOneWidget);

    final lastIcon = find.byType(SvgPicture).last;
    await tester.pump(const Duration(seconds: 10));
    await tester.tap(lastIcon);
    await tester.pump(const Duration(seconds: 10));
    expect(find.byType(LoginBottomSheet), findsOneWidget);

    final emailField = find.byWidgetPredicate((widget) =>
        widget is TitleTextField &&
        widget.title == AppText.valueGlobal.userNameOrEmail);
    await tester.pump(const Duration(seconds: 5));
    await tester.enterText(emailField, '<EMAIL>');

    final passwordField = find.byWidgetPredicate((widget) =>
        widget is TitleTextField &&
        widget.title == AppText.valueGlobal.password);
    await tester.pump(const Duration(seconds: 5));
    await tester.enterText(passwordField, '12345678Kksd!');

    final loginButton = find.byType(PrimaryButton);
    await tester.pump(const Duration(seconds: 5));
    await tester.tap(loginButton);
    await tester.pump(const Duration(seconds: 5));

    expect(find.text(AppErrors.invalidEmailOrPassword), findsOneWidget);
  });

  testWidgets('Check login with unregistered email and password.',
      (WidgetTester tester) async {
    await app.main();
    await tester.pump(const Duration(seconds: 15));

    expect(find.byType(HomeScreen), findsOneWidget);

    final bottomNavigator = find.byType(BottomNavigator);
    expect(bottomNavigator, findsOneWidget);

    final lastIcon = find.byType(SvgPicture).last;
    await tester.pump(const Duration(seconds: 10));
    await tester.tap(lastIcon);
    await tester.pump(const Duration(seconds: 10));
    expect(find.byType(LoginBottomSheet), findsOneWidget);

    final emailField = find.byWidgetPredicate((widget) =>
        widget is TitleTextField &&
        widget.title == AppText.valueGlobal.userNameOrEmail);
    await tester.pump(const Duration(seconds: 5));
    await tester.enterText(emailField, '<EMAIL>');

    final passwordField = find.byWidgetPredicate((widget) =>
        widget is TitleTextField &&
        widget.title == AppText.valueGlobal.password);
    await tester.pump(const Duration(seconds: 5));
    await tester.enterText(passwordField, '12345678Kksd!');

    final loginButton = find.byType(PrimaryButton);
    await tester.pump(const Duration(seconds: 5));
    await tester.tap(loginButton);
    await tester.pump(const Duration(seconds: 5));

    expect(find.text("User not found!"), findsOneWidget);
  });

  testWidgets('Tap X button to close login bottom sheet',
      (WidgetTester tester) async {
    await app.main();
    await tester.pump(const Duration(seconds: 15));

    expect(find.byType(HomeScreen), findsOneWidget);

    final bottomNavigator = find.byType(BottomNavigator);
    expect(bottomNavigator, findsOneWidget);

    final lastIcon = find.byType(SvgPicture).last;
    await tester.pump(const Duration(seconds: 10));
    await tester.tap(lastIcon);
    await tester.pump(const Duration(seconds: 10));
    expect(find.byType(LoginBottomSheet), findsOneWidget);

    final closeButton = find.byKey(const Key('close_login_bottom_sheet'));
    await tester.pump(const Duration(seconds: 5));
    await tester.tap(closeButton);
    await tester.pump(const Duration(seconds: 5));

    expect(find.byType(HomeScreen), findsOneWidget);
  });

  testWidgets('Tap "Cancel" button to close login bottom sheet',
      (WidgetTester tester) async {
    await app.main();
    await tester.pump(const Duration(seconds: 15));

    expect(find.byType(HomeScreen), findsOneWidget);

    final bottomNavigator = find.byType(BottomNavigator);
    expect(bottomNavigator, findsOneWidget);

    final lastIcon = find.byType(SvgPicture).last;
    await tester.pump(const Duration(seconds: 10));
    await tester.tap(lastIcon);
    await tester.pump(const Duration(seconds: 10));
    expect(find.byType(LoginBottomSheet), findsOneWidget);

    final closeButton = find.byKey(const Key('cancel_login_bottom_sheet'));
    await tester.pump(const Duration(seconds: 5));
    await tester.tap(closeButton);
    await tester.pump(const Duration(seconds: 5));

    expect(find.byType(HomeScreen), findsOneWidget);
  });

  testWidgets(
      'Tap "Did you forget your password" button to close login bottom sheet',
      (WidgetTester tester) async {
    await app.main();
    await tester.pump(const Duration(seconds: 15));

    expect(find.byType(HomeScreen), findsOneWidget);

    final bottomNavigator = find.byType(BottomNavigator);
    expect(bottomNavigator, findsOneWidget);

    final lastIcon = find.byType(SvgPicture).last;
    await tester.pump(const Duration(seconds: 10));
    await tester.tap(lastIcon);
    await tester.pump(const Duration(seconds: 10));
    expect(find.byType(LoginBottomSheet), findsOneWidget);

    final closeButton = find.byKey(const Key('forgot_password'));
    await tester.pump(const Duration(seconds: 5));
    await tester.tap(closeButton);
    await tester.pump(const Duration(seconds: 5));

    expect(find.byType(ForgotPasswordScreen), findsOneWidget);
  });

  testWidgets('Toggle obscure text on password field',
      (WidgetTester tester) async {
    await app.main();
    await tester.pump(const Duration(seconds: 8));

    expect(find.byType(HomeScreen), findsOneWidget);

    final bottomNavigator = find.byType(BottomNavigator);
    expect(bottomNavigator, findsOneWidget);

    final lastIcon = find.byType(SvgPicture).last;
    await tester.pump(const Duration(seconds: 10));
    await tester.tap(lastIcon);
    await tester.pump(const Duration(seconds: 10));
    expect(find.byType(LoginBottomSheet), findsOneWidget);

    final emailField = find.byWidgetPredicate((widget) =>
        widget is TitleTextField &&
        widget.title == AppText.valueGlobal.userNameOrEmail);
    await tester.pump(const Duration(seconds: 5));
    await tester.enterText(emailField, '<EMAIL>');

    final passwordField = find.byWidgetPredicate((widget) =>
        widget is TitleTextField &&
        widget.title == AppText.valueGlobal.password);
    await tester.pump(const Duration(seconds: 5));
    await tester.enterText(passwordField, 'WeT\$KM3HT/Aq');

    // Find icon Icons.visibility_off
    final obscureButton = find.byIcon(Icons.visibility_off);
    expect(obscureButton, findsOneWidget);

    // Find icon to change status
    await tester.tap(obscureButton);
    await tester.pump(const Duration(seconds: 5));

    // Check icon is changed to Icons.visibility
    final visibleButton = find.byIcon(Icons.visibility);
    expect(visibleButton, findsOneWidget);

    TitleTextField passwordFieldWidget = tester.widget(passwordField);
    expect(passwordFieldWidget.obscureText, isFalse);

    await tester.tap(visibleButton);
    await tester.pump(const Duration(seconds: 5));

    final obscureButtonAgain = find.byIcon(Icons.visibility_off);
    expect(obscureButtonAgain, findsOneWidget);

    passwordFieldWidget = tester.widget(passwordField);
    expect(passwordFieldWidget.obscureText, isTrue);
  });

  testWidgets('Do not enter the email and enter the password',
      (WidgetTester tester) async {
    await app.main();
    await tester.pump(const Duration(seconds: 8));

    expect(find.byType(HomeScreen), findsOneWidget);

    final bottomNavigator = find.byType(BottomNavigator);
    expect(bottomNavigator, findsOneWidget);

    final lastIcon = find.byType(SvgPicture).last;
    await tester.pump(const Duration(seconds: 10));
    await tester.tap(lastIcon);
    await tester.pump(const Duration(seconds: 10));
    expect(find.byType(LoginBottomSheet), findsOneWidget);

    final passwordField = find.byWidgetPredicate((widget) =>
        widget is TitleTextField &&
        widget.title == AppText.valueGlobal.password);
    await tester.pump(const Duration(seconds: 5));
    await tester.enterText(passwordField, 'WeT\$KM3HT/Aq');

    final loginButton = find.byType(PrimaryButton);
    await tester.pump(const Duration(seconds: 5));
    await tester.tap(loginButton);
    await tester.pump(const Duration(seconds: 5));

    expect(find.text(AppText.valueGlobal.please_enter_email), findsOneWidget);
  });

  testWidgets('Enter the email and do not enter the password',
      (WidgetTester tester) async {
    await app.main();
    await tester.pump(const Duration(seconds: 8));

    expect(find.byType(HomeScreen), findsOneWidget);

    final bottomNavigator = find.byType(BottomNavigator);
    expect(bottomNavigator, findsOneWidget);

    final lastIcon = find.byType(SvgPicture).last;
    await tester.pump(const Duration(seconds: 10));
    await tester.tap(lastIcon);
    await tester.pump(const Duration(seconds: 10));
    expect(find.byType(LoginBottomSheet), findsOneWidget);

    final emailField = find.byWidgetPredicate((widget) =>
        widget is TitleTextField &&
        widget.title == AppText.valueGlobal.userNameOrEmail);
    await tester.pump(const Duration(seconds: 5));
    await tester.enterText(emailField, '<EMAIL>');

    final loginButton = find.byType(PrimaryButton);
    await tester.pump(const Duration(seconds: 5));
    await tester.tap(loginButton);
    await tester.pump(const Duration(seconds: 5));

    expect(
        find.text(AppText.valueGlobal.please_enter_password), findsOneWidget);
  });

  testWidgets('Do not enter the email and do not enter the password',
      (WidgetTester tester) async {
    await app.main();
    await tester.pump(const Duration(seconds: 8));

    expect(find.byType(HomeScreen), findsOneWidget);

    final bottomNavigator = find.byType(BottomNavigator);
    expect(bottomNavigator, findsOneWidget);

    final lastIcon = find.byType(SvgPicture).last;
    await tester.pump(const Duration(seconds: 5));
    await tester.tap(lastIcon);
    await tester.pump(const Duration(seconds: 5));
    expect(find.byType(LoginBottomSheet), findsOneWidget);

    final loginButton = find.byType(PrimaryButton);
    await tester.pump(const Duration(seconds: 5));
    await tester.tap(loginButton);
    await tester.pump(const Duration(seconds: 5));

    expect(find.text(AppText.valueGlobal.please_enter_email), findsOneWidget);
    expect(
        find.text(AppText.valueGlobal.please_enter_password), findsOneWidget);
  });
}
