import 'package:flutter_test/flutter_test.dart';
import 'package:asiantech_link/layers/presentation/env/env_test.dart' as app;
import 'package:integration_test/integration_test.dart';

void main() {
  final binding = IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  binding.framePolicy = LiveTestWidgetsFlutterBindingFramePolicy.fullyLive;
  testWidgets('Test HomeScreen widget', (WidgetTester tester) async {
    await app.main();
    await tester.pumpAndSettle();
    await tester.tap(find.text("Go to the Details screen"));
    await tester.pumpAndSettle();
  });
}
