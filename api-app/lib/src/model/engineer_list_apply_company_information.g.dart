// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'engineer_list_apply_company_information.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$EngineerListApplyCompanyInformation
    extends EngineerListApplyCompanyInformation {
  @override
  final String? name;
  @override
  final String? logoImagePath;

  factory _$EngineerListApplyCompanyInformation(
          [void Function(EngineerListApplyCompanyInformationBuilder)?
              updates]) =>
      (new EngineerListApplyCompanyInformationBuilder()..update(updates))
          ._build();

  _$EngineerListApplyCompanyInformation._({this.name, this.logoImagePath})
      : super._();

  @override
  EngineerListApplyCompanyInformation rebuild(
          void Function(EngineerListApplyCompanyInformationBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  EngineerListApplyCompanyInformationBuilder toBuilder() =>
      new EngineerListApplyCompanyInformationBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is EngineerListApplyCompanyInformation &&
        name == other.name &&
        logoImagePath == other.logoImagePath;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, logoImagePath.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'EngineerListApplyCompanyInformation')
          ..add('name', name)
          ..add('logoImagePath', logoImagePath))
        .toString();
  }
}

class EngineerListApplyCompanyInformationBuilder
    implements
        Builder<EngineerListApplyCompanyInformation,
            EngineerListApplyCompanyInformationBuilder> {
  _$EngineerListApplyCompanyInformation? _$v;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _logoImagePath;
  String? get logoImagePath => _$this._logoImagePath;
  set logoImagePath(String? logoImagePath) =>
      _$this._logoImagePath = logoImagePath;

  EngineerListApplyCompanyInformationBuilder() {
    EngineerListApplyCompanyInformation._defaults(this);
  }

  EngineerListApplyCompanyInformationBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _name = $v.name;
      _logoImagePath = $v.logoImagePath;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(EngineerListApplyCompanyInformation other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$EngineerListApplyCompanyInformation;
  }

  @override
  void update(
      void Function(EngineerListApplyCompanyInformationBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  EngineerListApplyCompanyInformation build() => _build();

  _$EngineerListApplyCompanyInformation _build() {
    final _$result = _$v ??
        new _$EngineerListApplyCompanyInformation._(
          name: name,
          logoImagePath: logoImagePath,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
