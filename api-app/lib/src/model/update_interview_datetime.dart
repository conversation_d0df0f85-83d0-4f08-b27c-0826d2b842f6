//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'update_interview_datetime.g.dart';

/// UpdateInterviewDatetime
///
/// Properties:
/// * [interviewDatetime] 
@BuiltValue()
abstract class UpdateInterviewDatetime implements Built<UpdateInterviewDatetime, UpdateInterviewDatetimeBuilder> {
  @BuiltValueField(wireName: r'interview_datetime')
  DateTime? get interviewDatetime;

  UpdateInterviewDatetime._();

  factory UpdateInterviewDatetime([void updates(UpdateInterviewDatetimeBuilder b)]) = _$UpdateInterviewDatetime;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(UpdateInterviewDatetimeBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<UpdateInterviewDatetime> get serializer => _$UpdateInterviewDatetimeSerializer();
}

class _$UpdateInterviewDatetimeSerializer implements PrimitiveSerializer<UpdateInterviewDatetime> {
  @override
  final Iterable<Type> types = const [UpdateInterviewDatetime, _$UpdateInterviewDatetime];

  @override
  final String wireName = r'UpdateInterviewDatetime';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    UpdateInterviewDatetime object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.interviewDatetime != null) {
      yield r'interview_datetime';
      yield serializers.serialize(
        object.interviewDatetime,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    UpdateInterviewDatetime object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required UpdateInterviewDatetimeBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'interview_datetime':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.interviewDatetime = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  UpdateInterviewDatetime deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = UpdateInterviewDatetimeBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

