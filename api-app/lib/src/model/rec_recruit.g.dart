// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'rec_recruit.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$RecRecruit extends RecRecruit {
  @override
  final String? title;
  @override
  final int? recruitId;
  @override
  final String? jobCode;
  @override
  final String? catchCopy;
  @override
  final int hostCompanyId;
  @override
  final RecruitCompany hostCompany;

  factory _$RecRecruit([void Function(RecRecruitBuilder)? updates]) =>
      (new RecRecruitBuilder()..update(updates))._build();

  _$RecRecruit._(
      {this.title,
      this.recruitId,
      this.jobCode,
      this.catchCopy,
      required this.hostCompanyId,
      required this.hostCompany})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        hostCompanyId, r'RecRecruit', 'hostCompanyId');
    BuiltValueNullFieldError.checkNotNull(
        hostCompany, r'RecRecruit', 'hostCompany');
  }

  @override
  RecRecruit rebuild(void Function(RecRecruitBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  RecRecruitBuilder toBuilder() => new RecRecruitBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is RecRecruit &&
        title == other.title &&
        recruitId == other.recruitId &&
        jobCode == other.jobCode &&
        catchCopy == other.catchCopy &&
        hostCompanyId == other.hostCompanyId &&
        hostCompany == other.hostCompany;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, title.hashCode);
    _$hash = $jc(_$hash, recruitId.hashCode);
    _$hash = $jc(_$hash, jobCode.hashCode);
    _$hash = $jc(_$hash, catchCopy.hashCode);
    _$hash = $jc(_$hash, hostCompanyId.hashCode);
    _$hash = $jc(_$hash, hostCompany.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'RecRecruit')
          ..add('title', title)
          ..add('recruitId', recruitId)
          ..add('jobCode', jobCode)
          ..add('catchCopy', catchCopy)
          ..add('hostCompanyId', hostCompanyId)
          ..add('hostCompany', hostCompany))
        .toString();
  }
}

class RecRecruitBuilder implements Builder<RecRecruit, RecRecruitBuilder> {
  _$RecRecruit? _$v;

  String? _title;
  String? get title => _$this._title;
  set title(String? title) => _$this._title = title;

  int? _recruitId;
  int? get recruitId => _$this._recruitId;
  set recruitId(int? recruitId) => _$this._recruitId = recruitId;

  String? _jobCode;
  String? get jobCode => _$this._jobCode;
  set jobCode(String? jobCode) => _$this._jobCode = jobCode;

  String? _catchCopy;
  String? get catchCopy => _$this._catchCopy;
  set catchCopy(String? catchCopy) => _$this._catchCopy = catchCopy;

  int? _hostCompanyId;
  int? get hostCompanyId => _$this._hostCompanyId;
  set hostCompanyId(int? hostCompanyId) =>
      _$this._hostCompanyId = hostCompanyId;

  RecruitCompanyBuilder? _hostCompany;
  RecruitCompanyBuilder get hostCompany =>
      _$this._hostCompany ??= new RecruitCompanyBuilder();
  set hostCompany(RecruitCompanyBuilder? hostCompany) =>
      _$this._hostCompany = hostCompany;

  RecRecruitBuilder() {
    RecRecruit._defaults(this);
  }

  RecRecruitBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _title = $v.title;
      _recruitId = $v.recruitId;
      _jobCode = $v.jobCode;
      _catchCopy = $v.catchCopy;
      _hostCompanyId = $v.hostCompanyId;
      _hostCompany = $v.hostCompany.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(RecRecruit other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$RecRecruit;
  }

  @override
  void update(void Function(RecRecruitBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  RecRecruit build() => _build();

  _$RecRecruit _build() {
    _$RecRecruit _$result;
    try {
      _$result = _$v ??
          new _$RecRecruit._(
            title: title,
            recruitId: recruitId,
            jobCode: jobCode,
            catchCopy: catchCopy,
            hostCompanyId: BuiltValueNullFieldError.checkNotNull(
                hostCompanyId, r'RecRecruit', 'hostCompanyId'),
            hostCompany: hostCompany.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'hostCompany';
        hostCompany.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'RecRecruit', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
