// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'eng_work_history.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$EngWorkHistory extends EngWorkHistory {
  @override
  final int? careerId;
  @override
  final int engineerId;
  @override
  final String? companyName;
  @override
  final String? jobDescription;
  @override
  final int? careerType;
  @override
  final Date? enteringDate;
  @override
  final Date? quittingDate;
  @override
  final String? roleName;

  factory _$EngWorkHistory([void Function(EngWorkHistoryBuilder)? updates]) =>
      (new EngWorkHistoryBuilder()..update(updates))._build();

  _$EngWorkHistory._(
      {this.careerId,
      required this.engineerId,
      this.companyName,
      this.jobDescription,
      this.careerType,
      this.enteringDate,
      this.quittingDate,
      this.roleName})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        engineerId, r'EngWorkHistory', 'engineerId');
  }

  @override
  EngWorkHistory rebuild(void Function(EngWorkHistoryBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  EngWorkHistoryBuilder toBuilder() =>
      new EngWorkHistoryBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is EngWorkHistory &&
        careerId == other.careerId &&
        engineerId == other.engineerId &&
        companyName == other.companyName &&
        jobDescription == other.jobDescription &&
        careerType == other.careerType &&
        enteringDate == other.enteringDate &&
        quittingDate == other.quittingDate &&
        roleName == other.roleName;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, careerId.hashCode);
    _$hash = $jc(_$hash, engineerId.hashCode);
    _$hash = $jc(_$hash, companyName.hashCode);
    _$hash = $jc(_$hash, jobDescription.hashCode);
    _$hash = $jc(_$hash, careerType.hashCode);
    _$hash = $jc(_$hash, enteringDate.hashCode);
    _$hash = $jc(_$hash, quittingDate.hashCode);
    _$hash = $jc(_$hash, roleName.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'EngWorkHistory')
          ..add('careerId', careerId)
          ..add('engineerId', engineerId)
          ..add('companyName', companyName)
          ..add('jobDescription', jobDescription)
          ..add('careerType', careerType)
          ..add('enteringDate', enteringDate)
          ..add('quittingDate', quittingDate)
          ..add('roleName', roleName))
        .toString();
  }
}

class EngWorkHistoryBuilder
    implements Builder<EngWorkHistory, EngWorkHistoryBuilder> {
  _$EngWorkHistory? _$v;

  int? _careerId;
  int? get careerId => _$this._careerId;
  set careerId(int? careerId) => _$this._careerId = careerId;

  int? _engineerId;
  int? get engineerId => _$this._engineerId;
  set engineerId(int? engineerId) => _$this._engineerId = engineerId;

  String? _companyName;
  String? get companyName => _$this._companyName;
  set companyName(String? companyName) => _$this._companyName = companyName;

  String? _jobDescription;
  String? get jobDescription => _$this._jobDescription;
  set jobDescription(String? jobDescription) =>
      _$this._jobDescription = jobDescription;

  int? _careerType;
  int? get careerType => _$this._careerType;
  set careerType(int? careerType) => _$this._careerType = careerType;

  Date? _enteringDate;
  Date? get enteringDate => _$this._enteringDate;
  set enteringDate(Date? enteringDate) => _$this._enteringDate = enteringDate;

  Date? _quittingDate;
  Date? get quittingDate => _$this._quittingDate;
  set quittingDate(Date? quittingDate) => _$this._quittingDate = quittingDate;

  String? _roleName;
  String? get roleName => _$this._roleName;
  set roleName(String? roleName) => _$this._roleName = roleName;

  EngWorkHistoryBuilder() {
    EngWorkHistory._defaults(this);
  }

  EngWorkHistoryBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _careerId = $v.careerId;
      _engineerId = $v.engineerId;
      _companyName = $v.companyName;
      _jobDescription = $v.jobDescription;
      _careerType = $v.careerType;
      _enteringDate = $v.enteringDate;
      _quittingDate = $v.quittingDate;
      _roleName = $v.roleName;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(EngWorkHistory other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$EngWorkHistory;
  }

  @override
  void update(void Function(EngWorkHistoryBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  EngWorkHistory build() => _build();

  _$EngWorkHistory _build() {
    final _$result = _$v ??
        new _$EngWorkHistory._(
          careerId: careerId,
          engineerId: BuiltValueNullFieldError.checkNotNull(
              engineerId, r'EngWorkHistory', 'engineerId'),
          companyName: companyName,
          jobDescription: jobDescription,
          careerType: careerType,
          enteringDate: enteringDate,
          quittingDate: quittingDate,
          roleName: roleName,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
