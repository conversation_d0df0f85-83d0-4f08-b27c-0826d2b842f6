// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'check_captcha_require_in_login.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$CheckCaptchaRequireInLogin extends CheckCaptchaRequireInLogin {
  @override
  final String email;

  factory _$CheckCaptchaRequireInLogin(
          [void Function(CheckCaptchaRequireInLoginBuilder)? updates]) =>
      (new CheckCaptchaRequireInLoginBuilder()..update(updates))._build();

  _$CheckCaptchaRequireInLogin._({required this.email}) : super._() {
    BuiltValueNullFieldError.checkNotNull(
        email, r'CheckCaptchaRequireInLogin', 'email');
  }

  @override
  CheckCaptchaRequireInLogin rebuild(
          void Function(CheckCaptchaRequireInLoginBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  CheckCaptchaRequireInLoginBuilder toBuilder() =>
      new CheckCaptchaRequireInLoginBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is CheckCaptchaRequireInLogin && email == other.email;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, email.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'CheckCaptchaRequireInLogin')
          ..add('email', email))
        .toString();
  }
}

class CheckCaptchaRequireInLoginBuilder
    implements
        Builder<CheckCaptchaRequireInLogin, CheckCaptchaRequireInLoginBuilder> {
  _$CheckCaptchaRequireInLogin? _$v;

  String? _email;
  String? get email => _$this._email;
  set email(String? email) => _$this._email = email;

  CheckCaptchaRequireInLoginBuilder() {
    CheckCaptchaRequireInLogin._defaults(this);
  }

  CheckCaptchaRequireInLoginBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _email = $v.email;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(CheckCaptchaRequireInLogin other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$CheckCaptchaRequireInLogin;
  }

  @override
  void update(void Function(CheckCaptchaRequireInLoginBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  CheckCaptchaRequireInLogin build() => _build();

  _$CheckCaptchaRequireInLogin _build() {
    final _$result = _$v ??
        new _$CheckCaptchaRequireInLogin._(
          email: BuiltValueNullFieldError.checkNotNull(
              email, r'CheckCaptchaRequireInLogin', 'email'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
