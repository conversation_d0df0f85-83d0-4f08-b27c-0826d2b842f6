// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recruit_company_information.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$RecruitCompanyInformation extends RecruitCompanyInformation {
  @override
  final int? companyId;
  @override
  final String? name;
  @override
  final String? logoImagePath;
  @override
  final String? prImagePath1;
  @override
  final String? prImagePath2;
  @override
  final String? prImagePath3;
  @override
  final String? introductionUrl;
  @override
  final String? address;
  @override
  final String? capitalStock;
  @override
  final int? employeesType;
  @override
  final String? tel;
  @override
  final String? internationalTel;
  @override
  final String? contactMail;
  @override
  final String? aboutUs;
  @override
  final String? businessDetails;
  @override
  final String? benefits;
  @override
  final String? capitalStockCurrCode;
  @override
  final String? countryCode;
  @override
  final String? addressCode;
  @override
  final String? webUrl;

  factory _$RecruitCompanyInformation(
          [void Function(RecruitCompanyInformationBuilder)? updates]) =>
      (new RecruitCompanyInformationBuilder()..update(updates))._build();

  _$RecruitCompanyInformation._(
      {this.companyId,
      this.name,
      this.logoImagePath,
      this.prImagePath1,
      this.prImagePath2,
      this.prImagePath3,
      this.introductionUrl,
      this.address,
      this.capitalStock,
      this.employeesType,
      this.tel,
      this.internationalTel,
      this.contactMail,
      this.aboutUs,
      this.businessDetails,
      this.benefits,
      this.capitalStockCurrCode,
      this.countryCode,
      this.addressCode,
      this.webUrl})
      : super._();

  @override
  RecruitCompanyInformation rebuild(
          void Function(RecruitCompanyInformationBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  RecruitCompanyInformationBuilder toBuilder() =>
      new RecruitCompanyInformationBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is RecruitCompanyInformation &&
        companyId == other.companyId &&
        name == other.name &&
        logoImagePath == other.logoImagePath &&
        prImagePath1 == other.prImagePath1 &&
        prImagePath2 == other.prImagePath2 &&
        prImagePath3 == other.prImagePath3 &&
        introductionUrl == other.introductionUrl &&
        address == other.address &&
        capitalStock == other.capitalStock &&
        employeesType == other.employeesType &&
        tel == other.tel &&
        internationalTel == other.internationalTel &&
        contactMail == other.contactMail &&
        aboutUs == other.aboutUs &&
        businessDetails == other.businessDetails &&
        benefits == other.benefits &&
        capitalStockCurrCode == other.capitalStockCurrCode &&
        countryCode == other.countryCode &&
        addressCode == other.addressCode &&
        webUrl == other.webUrl;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, companyId.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, logoImagePath.hashCode);
    _$hash = $jc(_$hash, prImagePath1.hashCode);
    _$hash = $jc(_$hash, prImagePath2.hashCode);
    _$hash = $jc(_$hash, prImagePath3.hashCode);
    _$hash = $jc(_$hash, introductionUrl.hashCode);
    _$hash = $jc(_$hash, address.hashCode);
    _$hash = $jc(_$hash, capitalStock.hashCode);
    _$hash = $jc(_$hash, employeesType.hashCode);
    _$hash = $jc(_$hash, tel.hashCode);
    _$hash = $jc(_$hash, internationalTel.hashCode);
    _$hash = $jc(_$hash, contactMail.hashCode);
    _$hash = $jc(_$hash, aboutUs.hashCode);
    _$hash = $jc(_$hash, businessDetails.hashCode);
    _$hash = $jc(_$hash, benefits.hashCode);
    _$hash = $jc(_$hash, capitalStockCurrCode.hashCode);
    _$hash = $jc(_$hash, countryCode.hashCode);
    _$hash = $jc(_$hash, addressCode.hashCode);
    _$hash = $jc(_$hash, webUrl.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'RecruitCompanyInformation')
          ..add('companyId', companyId)
          ..add('name', name)
          ..add('logoImagePath', logoImagePath)
          ..add('prImagePath1', prImagePath1)
          ..add('prImagePath2', prImagePath2)
          ..add('prImagePath3', prImagePath3)
          ..add('introductionUrl', introductionUrl)
          ..add('address', address)
          ..add('capitalStock', capitalStock)
          ..add('employeesType', employeesType)
          ..add('tel', tel)
          ..add('internationalTel', internationalTel)
          ..add('contactMail', contactMail)
          ..add('aboutUs', aboutUs)
          ..add('businessDetails', businessDetails)
          ..add('benefits', benefits)
          ..add('capitalStockCurrCode', capitalStockCurrCode)
          ..add('countryCode', countryCode)
          ..add('addressCode', addressCode)
          ..add('webUrl', webUrl))
        .toString();
  }
}

class RecruitCompanyInformationBuilder
    implements
        Builder<RecruitCompanyInformation, RecruitCompanyInformationBuilder> {
  _$RecruitCompanyInformation? _$v;

  int? _companyId;
  int? get companyId => _$this._companyId;
  set companyId(int? companyId) => _$this._companyId = companyId;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _logoImagePath;
  String? get logoImagePath => _$this._logoImagePath;
  set logoImagePath(String? logoImagePath) =>
      _$this._logoImagePath = logoImagePath;

  String? _prImagePath1;
  String? get prImagePath1 => _$this._prImagePath1;
  set prImagePath1(String? prImagePath1) => _$this._prImagePath1 = prImagePath1;

  String? _prImagePath2;
  String? get prImagePath2 => _$this._prImagePath2;
  set prImagePath2(String? prImagePath2) => _$this._prImagePath2 = prImagePath2;

  String? _prImagePath3;
  String? get prImagePath3 => _$this._prImagePath3;
  set prImagePath3(String? prImagePath3) => _$this._prImagePath3 = prImagePath3;

  String? _introductionUrl;
  String? get introductionUrl => _$this._introductionUrl;
  set introductionUrl(String? introductionUrl) =>
      _$this._introductionUrl = introductionUrl;

  String? _address;
  String? get address => _$this._address;
  set address(String? address) => _$this._address = address;

  String? _capitalStock;
  String? get capitalStock => _$this._capitalStock;
  set capitalStock(String? capitalStock) => _$this._capitalStock = capitalStock;

  int? _employeesType;
  int? get employeesType => _$this._employeesType;
  set employeesType(int? employeesType) =>
      _$this._employeesType = employeesType;

  String? _tel;
  String? get tel => _$this._tel;
  set tel(String? tel) => _$this._tel = tel;

  String? _internationalTel;
  String? get internationalTel => _$this._internationalTel;
  set internationalTel(String? internationalTel) =>
      _$this._internationalTel = internationalTel;

  String? _contactMail;
  String? get contactMail => _$this._contactMail;
  set contactMail(String? contactMail) => _$this._contactMail = contactMail;

  String? _aboutUs;
  String? get aboutUs => _$this._aboutUs;
  set aboutUs(String? aboutUs) => _$this._aboutUs = aboutUs;

  String? _businessDetails;
  String? get businessDetails => _$this._businessDetails;
  set businessDetails(String? businessDetails) =>
      _$this._businessDetails = businessDetails;

  String? _benefits;
  String? get benefits => _$this._benefits;
  set benefits(String? benefits) => _$this._benefits = benefits;

  String? _capitalStockCurrCode;
  String? get capitalStockCurrCode => _$this._capitalStockCurrCode;
  set capitalStockCurrCode(String? capitalStockCurrCode) =>
      _$this._capitalStockCurrCode = capitalStockCurrCode;

  String? _countryCode;
  String? get countryCode => _$this._countryCode;
  set countryCode(String? countryCode) => _$this._countryCode = countryCode;

  String? _addressCode;
  String? get addressCode => _$this._addressCode;
  set addressCode(String? addressCode) => _$this._addressCode = addressCode;

  String? _webUrl;
  String? get webUrl => _$this._webUrl;
  set webUrl(String? webUrl) => _$this._webUrl = webUrl;

  RecruitCompanyInformationBuilder() {
    RecruitCompanyInformation._defaults(this);
  }

  RecruitCompanyInformationBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _companyId = $v.companyId;
      _name = $v.name;
      _logoImagePath = $v.logoImagePath;
      _prImagePath1 = $v.prImagePath1;
      _prImagePath2 = $v.prImagePath2;
      _prImagePath3 = $v.prImagePath3;
      _introductionUrl = $v.introductionUrl;
      _address = $v.address;
      _capitalStock = $v.capitalStock;
      _employeesType = $v.employeesType;
      _tel = $v.tel;
      _internationalTel = $v.internationalTel;
      _contactMail = $v.contactMail;
      _aboutUs = $v.aboutUs;
      _businessDetails = $v.businessDetails;
      _benefits = $v.benefits;
      _capitalStockCurrCode = $v.capitalStockCurrCode;
      _countryCode = $v.countryCode;
      _addressCode = $v.addressCode;
      _webUrl = $v.webUrl;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(RecruitCompanyInformation other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$RecruitCompanyInformation;
  }

  @override
  void update(void Function(RecruitCompanyInformationBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  RecruitCompanyInformation build() => _build();

  _$RecruitCompanyInformation _build() {
    final _$result = _$v ??
        new _$RecruitCompanyInformation._(
          companyId: companyId,
          name: name,
          logoImagePath: logoImagePath,
          prImagePath1: prImagePath1,
          prImagePath2: prImagePath2,
          prImagePath3: prImagePath3,
          introductionUrl: introductionUrl,
          address: address,
          capitalStock: capitalStock,
          employeesType: employeesType,
          tel: tel,
          internationalTel: internationalTel,
          contactMail: contactMail,
          aboutUs: aboutUs,
          businessDetails: businessDetails,
          benefits: benefits,
          capitalStockCurrCode: capitalStockCurrCode,
          countryCode: countryCode,
          addressCode: addressCode,
          webUrl: webUrl,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
