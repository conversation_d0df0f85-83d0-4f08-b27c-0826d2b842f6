//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'self_assessment_sheet.g.dart';

/// SelfAssessmentSheet
///
/// Properties:
/// * [textEn] 
/// * [textVi] 
/// * [text] 
@BuiltValue()
abstract class SelfAssessmentSheet implements Built<SelfAssessmentSheet, SelfAssessmentSheetBuilder> {
  @BuiltValueField(wireName: r'text_en')
  String get textEn;

  @BuiltValueField(wireName: r'text_vi')
  String get textVi;

  @BuiltValueField(wireName: r'text')
  String get text;

  SelfAssessmentSheet._();

  factory SelfAssessmentSheet([void updates(SelfAssessmentSheetBuilder b)]) = _$SelfAssessmentSheet;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(SelfAssessmentSheetBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<SelfAssessmentSheet> get serializer => _$SelfAssessmentSheetSerializer();
}

class _$SelfAssessmentSheetSerializer implements PrimitiveSerializer<SelfAssessmentSheet> {
  @override
  final Iterable<Type> types = const [SelfAssessmentSheet, _$SelfAssessmentSheet];

  @override
  final String wireName = r'SelfAssessmentSheet';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    SelfAssessmentSheet object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'text_en';
    yield serializers.serialize(
      object.textEn,
      specifiedType: const FullType(String),
    );
    yield r'text_vi';
    yield serializers.serialize(
      object.textVi,
      specifiedType: const FullType(String),
    );
    yield r'text';
    yield serializers.serialize(
      object.text,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    SelfAssessmentSheet object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required SelfAssessmentSheetBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'text_en':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.textEn = valueDes;
          break;
        case r'text_vi':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.textVi = valueDes;
          break;
        case r'text':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.text = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  SelfAssessmentSheet deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = SelfAssessmentSheetBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

