// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'description.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$Description extends Description {
  @override
  final String text;
  @override
  final String textEn;
  @override
  final String textVi;

  factory _$Description([void Function(DescriptionBuilder)? updates]) =>
      (new DescriptionBuilder()..update(updates))._build();

  _$Description._(
      {required this.text, required this.textEn, required this.textVi})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(text, r'Description', 'text');
    BuiltValueNullFieldError.checkNotNull(textEn, r'Description', 'textEn');
    BuiltValueNullFieldError.checkNotNull(textVi, r'Description', 'textVi');
  }

  @override
  Description rebuild(void Function(DescriptionBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  DescriptionBuilder toBuilder() => new DescriptionBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is Description &&
        text == other.text &&
        textEn == other.textEn &&
        textVi == other.textVi;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, text.hashCode);
    _$hash = $jc(_$hash, textEn.hashCode);
    _$hash = $jc(_$hash, textVi.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'Description')
          ..add('text', text)
          ..add('textEn', textEn)
          ..add('textVi', textVi))
        .toString();
  }
}

class DescriptionBuilder implements Builder<Description, DescriptionBuilder> {
  _$Description? _$v;

  String? _text;
  String? get text => _$this._text;
  set text(String? text) => _$this._text = text;

  String? _textEn;
  String? get textEn => _$this._textEn;
  set textEn(String? textEn) => _$this._textEn = textEn;

  String? _textVi;
  String? get textVi => _$this._textVi;
  set textVi(String? textVi) => _$this._textVi = textVi;

  DescriptionBuilder() {
    Description._defaults(this);
  }

  DescriptionBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _text = $v.text;
      _textEn = $v.textEn;
      _textVi = $v.textVi;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(Description other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$Description;
  }

  @override
  void update(void Function(DescriptionBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  Description build() => _build();

  _$Description _build() {
    final _$result = _$v ??
        new _$Description._(
          text: BuiltValueNullFieldError.checkNotNull(
              text, r'Description', 'text'),
          textEn: BuiltValueNullFieldError.checkNotNull(
              textEn, r'Description', 'textEn'),
          textVi: BuiltValueNullFieldError.checkNotNull(
              textVi, r'Description', 'textVi'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
