// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'eng_career_job_skill.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$EngCareerJobSkill extends EngCareerJobSkill {
  @override
  final String? jobCode;
  @override
  final String? skillCode;
  @override
  final int? yearsOfExperience;
  @override
  final DateTime? created;
  @override
  final DateTime? updated;
  @override
  final String? skillCodeName;
  @override
  final String? jobName;
  @override
  final String? levelName;

  factory _$EngCareerJobSkill(
          [void Function(EngCareerJobSkillBuilder)? updates]) =>
      (new EngCareerJobSkillBuilder()..update(updates))._build();

  _$EngCareerJobSkill._(
      {this.jobCode,
      this.skillCode,
      this.yearsOfExperience,
      this.created,
      this.updated,
      this.skillCodeName,
      this.jobName,
      this.levelName})
      : super._();

  @override
  EngCareerJobSkill rebuild(void Function(EngCareerJobSkillBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  EngCareerJobSkillBuilder toBuilder() =>
      new EngCareerJobSkillBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is EngCareerJobSkill &&
        jobCode == other.jobCode &&
        skillCode == other.skillCode &&
        yearsOfExperience == other.yearsOfExperience &&
        created == other.created &&
        updated == other.updated &&
        skillCodeName == other.skillCodeName &&
        jobName == other.jobName &&
        levelName == other.levelName;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, jobCode.hashCode);
    _$hash = $jc(_$hash, skillCode.hashCode);
    _$hash = $jc(_$hash, yearsOfExperience.hashCode);
    _$hash = $jc(_$hash, created.hashCode);
    _$hash = $jc(_$hash, updated.hashCode);
    _$hash = $jc(_$hash, skillCodeName.hashCode);
    _$hash = $jc(_$hash, jobName.hashCode);
    _$hash = $jc(_$hash, levelName.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'EngCareerJobSkill')
          ..add('jobCode', jobCode)
          ..add('skillCode', skillCode)
          ..add('yearsOfExperience', yearsOfExperience)
          ..add('created', created)
          ..add('updated', updated)
          ..add('skillCodeName', skillCodeName)
          ..add('jobName', jobName)
          ..add('levelName', levelName))
        .toString();
  }
}

class EngCareerJobSkillBuilder
    implements Builder<EngCareerJobSkill, EngCareerJobSkillBuilder> {
  _$EngCareerJobSkill? _$v;

  String? _jobCode;
  String? get jobCode => _$this._jobCode;
  set jobCode(String? jobCode) => _$this._jobCode = jobCode;

  String? _skillCode;
  String? get skillCode => _$this._skillCode;
  set skillCode(String? skillCode) => _$this._skillCode = skillCode;

  int? _yearsOfExperience;
  int? get yearsOfExperience => _$this._yearsOfExperience;
  set yearsOfExperience(int? yearsOfExperience) =>
      _$this._yearsOfExperience = yearsOfExperience;

  DateTime? _created;
  DateTime? get created => _$this._created;
  set created(DateTime? created) => _$this._created = created;

  DateTime? _updated;
  DateTime? get updated => _$this._updated;
  set updated(DateTime? updated) => _$this._updated = updated;

  String? _skillCodeName;
  String? get skillCodeName => _$this._skillCodeName;
  set skillCodeName(String? skillCodeName) =>
      _$this._skillCodeName = skillCodeName;

  String? _jobName;
  String? get jobName => _$this._jobName;
  set jobName(String? jobName) => _$this._jobName = jobName;

  String? _levelName;
  String? get levelName => _$this._levelName;
  set levelName(String? levelName) => _$this._levelName = levelName;

  EngCareerJobSkillBuilder() {
    EngCareerJobSkill._defaults(this);
  }

  EngCareerJobSkillBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _jobCode = $v.jobCode;
      _skillCode = $v.skillCode;
      _yearsOfExperience = $v.yearsOfExperience;
      _created = $v.created;
      _updated = $v.updated;
      _skillCodeName = $v.skillCodeName;
      _jobName = $v.jobName;
      _levelName = $v.levelName;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(EngCareerJobSkill other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$EngCareerJobSkill;
  }

  @override
  void update(void Function(EngCareerJobSkillBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  EngCareerJobSkill build() => _build();

  _$EngCareerJobSkill _build() {
    final _$result = _$v ??
        new _$EngCareerJobSkill._(
          jobCode: jobCode,
          skillCode: skillCode,
          yearsOfExperience: yearsOfExperience,
          created: created,
          updated: updated,
          skillCodeName: skillCodeName,
          jobName: jobName,
          levelName: levelName,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
