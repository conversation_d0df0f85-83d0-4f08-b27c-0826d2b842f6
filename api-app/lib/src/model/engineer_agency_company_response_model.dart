//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/engineer_agency_company.dart';
import 'package:openapi/src/model/error_detail.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'engineer_agency_company_response_model.g.dart';

/// EngineerAgencyCompanyResponseModel
///
/// Properties:
/// * [message] 
/// * [data] 
/// * [errors] 
@BuiltValue()
abstract class EngineerAgencyCompanyResponseModel implements Built<EngineerAgencyCompanyResponseModel, EngineerAgencyCompanyResponseModelBuilder> {
  @BuiltValueField(wireName: r'message')
  String? get message;

  @BuiltValueField(wireName: r'data')
  BuiltList<EngineerAgencyCompany> get data;

  @BuiltValueField(wireName: r'errors')
  BuiltList<ErrorDetail>? get errors;

  EngineerAgencyCompanyResponseModel._();

  factory EngineerAgencyCompanyResponseModel([void updates(EngineerAgencyCompanyResponseModelBuilder b)]) = _$EngineerAgencyCompanyResponseModel;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(EngineerAgencyCompanyResponseModelBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<EngineerAgencyCompanyResponseModel> get serializer => _$EngineerAgencyCompanyResponseModelSerializer();
}

class _$EngineerAgencyCompanyResponseModelSerializer implements PrimitiveSerializer<EngineerAgencyCompanyResponseModel> {
  @override
  final Iterable<Type> types = const [EngineerAgencyCompanyResponseModel, _$EngineerAgencyCompanyResponseModel];

  @override
  final String wireName = r'EngineerAgencyCompanyResponseModel';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    EngineerAgencyCompanyResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'message';
    yield object.message == null ? null : serializers.serialize(
      object.message,
      specifiedType: const FullType.nullable(String),
    );
    yield r'data';
    yield serializers.serialize(
      object.data,
      specifiedType: const FullType(BuiltList, [FullType(EngineerAgencyCompany)]),
    );
    yield r'errors';
    yield object.errors == null ? null : serializers.serialize(
      object.errors,
      specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    EngineerAgencyCompanyResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required EngineerAgencyCompanyResponseModelBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'message':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.message = valueDes;
          break;
        case r'data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(EngineerAgencyCompany)]),
          ) as BuiltList<EngineerAgencyCompany>;
          result.data.replace(valueDes);
          break;
        case r'errors':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
          ) as BuiltList<ErrorDetail>?;
          if (valueDes == null) continue;
          result.errors.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  EngineerAgencyCompanyResponseModel deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = EngineerAgencyCompanyResponseModelBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

