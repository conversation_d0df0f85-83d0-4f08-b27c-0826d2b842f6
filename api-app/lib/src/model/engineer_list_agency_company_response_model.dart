//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/engineer_list_agency_company.dart';
import 'package:openapi/src/model/error_detail.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'engineer_list_agency_company_response_model.g.dart';

/// EngineerListAgencyCompanyResponseModel
///
/// Properties:
/// * [message] 
/// * [errors] 
/// * [data] 
@BuiltValue()
abstract class EngineerListAgencyCompanyResponseModel implements Built<EngineerListAgencyCompanyResponseModel, EngineerListAgencyCompanyResponseModelBuilder> {
  @BuiltValueField(wireName: r'message')
  String? get message;

  @BuiltValueField(wireName: r'errors')
  BuiltList<ErrorDetail>? get errors;

  @BuiltValueField(wireName: r'data')
  BuiltList<EngineerListAgencyCompany> get data;

  EngineerListAgencyCompanyResponseModel._();

  factory EngineerListAgencyCompanyResponseModel([void updates(EngineerListAgencyCompanyResponseModelBuilder b)]) = _$EngineerListAgencyCompanyResponseModel;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(EngineerListAgencyCompanyResponseModelBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<EngineerListAgencyCompanyResponseModel> get serializer => _$EngineerListAgencyCompanyResponseModelSerializer();
}

class _$EngineerListAgencyCompanyResponseModelSerializer implements PrimitiveSerializer<EngineerListAgencyCompanyResponseModel> {
  @override
  final Iterable<Type> types = const [EngineerListAgencyCompanyResponseModel, _$EngineerListAgencyCompanyResponseModel];

  @override
  final String wireName = r'EngineerListAgencyCompanyResponseModel';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    EngineerListAgencyCompanyResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'message';
    yield object.message == null ? null : serializers.serialize(
      object.message,
      specifiedType: const FullType.nullable(String),
    );
    yield r'errors';
    yield object.errors == null ? null : serializers.serialize(
      object.errors,
      specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
    );
    yield r'data';
    yield serializers.serialize(
      object.data,
      specifiedType: const FullType(BuiltList, [FullType(EngineerListAgencyCompany)]),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    EngineerListAgencyCompanyResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required EngineerListAgencyCompanyResponseModelBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'message':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.message = valueDes;
          break;
        case r'errors':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
          ) as BuiltList<ErrorDetail>?;
          if (valueDes == null) continue;
          result.errors.replace(valueDes);
          break;
        case r'data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(EngineerListAgencyCompany)]),
          ) as BuiltList<EngineerListAgencyCompany>;
          result.data.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  EngineerListAgencyCompanyResponseModel deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = EngineerListAgencyCompanyResponseModelBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

