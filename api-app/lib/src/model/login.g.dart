// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$Login extends Login {
  @override
  final String email;
  @override
  final String password;
  @override
  final String? captchaKey;
  @override
  final String? captchaValue;
  @override
  final int? userType;

  factory _$Login([void Function(LoginBuilder)? updates]) =>
      (new LoginBuilder()..update(updates))._build();

  _$Login._(
      {required this.email,
      required this.password,
      this.captcha<PERSON>ey,
      this.captchaValue,
      this.userType})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(email, r'Login', 'email');
    BuiltValueNullFieldError.checkNotNull(password, r'Login', 'password');
  }

  @override
  Login rebuild(void Function(LoginBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  LoginBuilder toBuilder() => new LoginBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is Login &&
        email == other.email &&
        password == other.password &&
        captchaKey == other.captchaKey &&
        captchaValue == other.captchaValue &&
        userType == other.userType;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, email.hashCode);
    _$hash = $jc(_$hash, password.hashCode);
    _$hash = $jc(_$hash, captchaKey.hashCode);
    _$hash = $jc(_$hash, captchaValue.hashCode);
    _$hash = $jc(_$hash, userType.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'Login')
          ..add('email', email)
          ..add('password', password)
          ..add('captchaKey', captchaKey)
          ..add('captchaValue', captchaValue)
          ..add('userType', userType))
        .toString();
  }
}

class LoginBuilder implements Builder<Login, LoginBuilder> {
  _$Login? _$v;

  String? _email;
  String? get email => _$this._email;
  set email(String? email) => _$this._email = email;

  String? _password;
  String? get password => _$this._password;
  set password(String? password) => _$this._password = password;

  String? _captchaKey;
  String? get captchaKey => _$this._captchaKey;
  set captchaKey(String? captchaKey) => _$this._captchaKey = captchaKey;

  String? _captchaValue;
  String? get captchaValue => _$this._captchaValue;
  set captchaValue(String? captchaValue) => _$this._captchaValue = captchaValue;

  int? _userType;
  int? get userType => _$this._userType;
  set userType(int? userType) => _$this._userType = userType;

  LoginBuilder() {
    Login._defaults(this);
  }

  LoginBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _email = $v.email;
      _password = $v.password;
      _captchaKey = $v.captchaKey;
      _captchaValue = $v.captchaValue;
      _userType = $v.userType;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(Login other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$Login;
  }

  @override
  void update(void Function(LoginBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  Login build() => _build();

  _$Login _build() {
    final _$result = _$v ??
        new _$Login._(
          email:
              BuiltValueNullFieldError.checkNotNull(email, r'Login', 'email'),
          password: BuiltValueNullFieldError.checkNotNull(
              password, r'Login', 'password'),
          captchaKey: captchaKey,
          captchaValue: captchaValue,
          userType: userType,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
