//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/eng_skill.dart';
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'category_skill.g.dart';

/// CategorySkill
///
/// Properties:
/// * [skills] 
/// * [categoryId] 
@BuiltValue()
abstract class CategorySkill implements Built<CategorySkill, CategorySkillBuilder> {
  @BuiltValueField(wireName: r'skills')
  BuiltList<EngSkill> get skills;

  @BuiltValueField(wireName: r'category_id')
  String get categoryId;

  CategorySkill._();

  factory CategorySkill([void updates(CategorySkillBuilder b)]) = _$CategorySkill;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(CategorySkillBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<CategorySkill> get serializer => _$CategorySkillSerializer();
}

class _$CategorySkillSerializer implements PrimitiveSerializer<CategorySkill> {
  @override
  final Iterable<Type> types = const [CategorySkill, _$CategorySkill];

  @override
  final String wireName = r'CategorySkill';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    CategorySkill object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'skills';
    yield serializers.serialize(
      object.skills,
      specifiedType: const FullType(BuiltList, [FullType(EngSkill)]),
    );
    yield r'category_id';
    yield serializers.serialize(
      object.categoryId,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    CategorySkill object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required CategorySkillBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'skills':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(EngSkill)]),
          ) as BuiltList<EngSkill>;
          result.skills.replace(valueDes);
          break;
        case r'category_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.categoryId = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  CategorySkill deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = CategorySkillBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

