// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'engineer_get_list_best_companies_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$EngineerGetListBestCompaniesResponseModel
    extends EngineerGetListBestCompaniesResponseModel {
  @override
  final String? message;
  @override
  final BuiltList<ErrorDetail>? errors;
  @override
  final EngineerListBestCompanyPagination data;

  factory _$EngineerGetListBestCompaniesResponseModel(
          [void Function(EngineerGetListBestCompaniesResponseModelBuilder)?
              updates]) =>
      (new EngineerGetListBestCompaniesResponseModelBuilder()..update(updates))
          ._build();

  _$EngineerGetListBestCompaniesResponseModel._(
      {this.message, this.errors, required this.data})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'EngineerGetListBestCompaniesResponseModel', 'data');
  }

  @override
  EngineerGetListBestCompaniesResponseModel rebuild(
          void Function(EngineerGetListBestCompaniesResponseModelBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  EngineerGetListBestCompaniesResponseModelBuilder toBuilder() =>
      new EngineerGetListBestCompaniesResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is EngineerGetListBestCompaniesResponseModel &&
        message == other.message &&
        errors == other.errors &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'EngineerGetListBestCompaniesResponseModel')
          ..add('message', message)
          ..add('errors', errors)
          ..add('data', data))
        .toString();
  }
}

class EngineerGetListBestCompaniesResponseModelBuilder
    implements
        Builder<EngineerGetListBestCompaniesResponseModel,
            EngineerGetListBestCompaniesResponseModelBuilder> {
  _$EngineerGetListBestCompaniesResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  EngineerListBestCompanyPaginationBuilder? _data;
  EngineerListBestCompanyPaginationBuilder get data =>
      _$this._data ??= new EngineerListBestCompanyPaginationBuilder();
  set data(EngineerListBestCompanyPaginationBuilder? data) =>
      _$this._data = data;

  EngineerGetListBestCompaniesResponseModelBuilder() {
    EngineerGetListBestCompaniesResponseModel._defaults(this);
  }

  EngineerGetListBestCompaniesResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _errors = $v.errors?.toBuilder();
      _data = $v.data.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(EngineerGetListBestCompaniesResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$EngineerGetListBestCompaniesResponseModel;
  }

  @override
  void update(
      void Function(EngineerGetListBestCompaniesResponseModelBuilder)?
          updates) {
    if (updates != null) updates(this);
  }

  @override
  EngineerGetListBestCompaniesResponseModel build() => _build();

  _$EngineerGetListBestCompaniesResponseModel _build() {
    _$EngineerGetListBestCompaniesResponseModel _$result;
    try {
      _$result = _$v ??
          new _$EngineerGetListBestCompaniesResponseModel._(
            message: message,
            errors: _errors?.build(),
            data: data.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'errors';
        _errors?.build();
        _$failedField = 'data';
        data.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'EngineerGetListBestCompaniesResponseModel',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
