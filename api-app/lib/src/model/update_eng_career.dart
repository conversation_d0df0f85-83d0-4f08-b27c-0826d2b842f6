//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/update_career_job_skill.dart';
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/date.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'update_eng_career.g.dart';

/// UpdateEngCareer
///
/// Properties:
/// * [companyName] 
/// * [careerType] 
/// * [enteringDate] 
/// * [careerJobSkills] 
/// * [jobDescription] 
/// * [quittingDate] 
/// * [roleName] 
/// * [jobCode] 
@BuiltValue()
abstract class UpdateEngCareer implements Built<UpdateEngCareer, UpdateEngCareerBuilder> {
  @BuiltValueField(wireName: r'company_name')
  String? get companyName;

  @BuiltValueField(wireName: r'career_type')
  int? get careerType;

  @BuiltValueField(wireName: r'entering_date')
  Date? get enteringDate;

  @BuiltValueField(wireName: r'career_job_skills')
  BuiltList<UpdateCareerJobSkill> get careerJobSkills;

  @BuiltValueField(wireName: r'job_description')
  String? get jobDescription;

  @BuiltValueField(wireName: r'quitting_date')
  Date? get quittingDate;

  @BuiltValueField(wireName: r'role_name')
  String? get roleName;

  @BuiltValueField(wireName: r'job_code')
  String? get jobCode;

  UpdateEngCareer._();

  factory UpdateEngCareer([void updates(UpdateEngCareerBuilder b)]) = _$UpdateEngCareer;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(UpdateEngCareerBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<UpdateEngCareer> get serializer => _$UpdateEngCareerSerializer();
}

class _$UpdateEngCareerSerializer implements PrimitiveSerializer<UpdateEngCareer> {
  @override
  final Iterable<Type> types = const [UpdateEngCareer, _$UpdateEngCareer];

  @override
  final String wireName = r'UpdateEngCareer';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    UpdateEngCareer object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.companyName != null) {
      yield r'company_name';
      yield serializers.serialize(
        object.companyName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.careerType != null) {
      yield r'career_type';
      yield serializers.serialize(
        object.careerType,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.enteringDate != null) {
      yield r'entering_date';
      yield serializers.serialize(
        object.enteringDate,
        specifiedType: const FullType.nullable(Date),
      );
    }
    yield r'career_job_skills';
    yield serializers.serialize(
      object.careerJobSkills,
      specifiedType: const FullType(BuiltList, [FullType(UpdateCareerJobSkill)]),
    );
    if (object.jobDescription != null) {
      yield r'job_description';
      yield serializers.serialize(
        object.jobDescription,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.quittingDate != null) {
      yield r'quitting_date';
      yield serializers.serialize(
        object.quittingDate,
        specifiedType: const FullType.nullable(Date),
      );
    }
    if (object.roleName != null) {
      yield r'role_name';
      yield serializers.serialize(
        object.roleName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.jobCode != null) {
      yield r'job_code';
      yield serializers.serialize(
        object.jobCode,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    UpdateEngCareer object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required UpdateEngCareerBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'company_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.companyName = valueDes;
          break;
        case r'career_type':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.careerType = valueDes;
          break;
        case r'entering_date':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(Date),
          ) as Date?;
          if (valueDes == null) continue;
          result.enteringDate = valueDes;
          break;
        case r'career_job_skills':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(UpdateCareerJobSkill)]),
          ) as BuiltList<UpdateCareerJobSkill>;
          result.careerJobSkills.replace(valueDes);
          break;
        case r'job_description':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.jobDescription = valueDes;
          break;
        case r'quitting_date':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(Date),
          ) as Date?;
          if (valueDes == null) continue;
          result.quittingDate = valueDes;
          break;
        case r'role_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.roleName = valueDes;
          break;
        case r'job_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.jobCode = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  UpdateEngCareer deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = UpdateEngCareerBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

