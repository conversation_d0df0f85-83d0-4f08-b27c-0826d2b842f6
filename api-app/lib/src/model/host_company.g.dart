// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'host_company.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$HostCompany extends HostCompany {
  @override
  final String? name;
  @override
  final String? webUrl;
  @override
  final int? employeesType;
  @override
  final String? countryCode;
  @override
  final String? addressCode;
  @override
  final String? address;
  @override
  final String? tel;
  @override
  final String contactMail;
  @override
  final String? internationalTel;
  @override
  final String? capitalStock;
  @override
  final String? capitalStockCurrCode;
  @override
  final String? logoImagePath;
  @override
  final String? acceptingFee;
  @override
  final String? acceptingFeeCurrCode;
  @override
  final String? supportOutsourcingFee;
  @override
  final String? supportOutsourcingFeeCurrCode;
  @override
  final String? support;

  factory _$HostCompany([void Function(HostCompanyBuilder)? updates]) =>
      (new HostCompanyBuilder()..update(updates))._build();

  _$HostCompany._(
      {this.name,
      this.webUrl,
      this.employeesType,
      this.countryCode,
      this.addressCode,
      this.address,
      this.tel,
      required this.contactMail,
      this.internationalTel,
      this.capitalStock,
      this.capitalStockCurrCode,
      this.logoImagePath,
      this.acceptingFee,
      this.acceptingFeeCurrCode,
      this.supportOutsourcingFee,
      this.supportOutsourcingFeeCurrCode,
      this.support})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        contactMail, r'HostCompany', 'contactMail');
  }

  @override
  HostCompany rebuild(void Function(HostCompanyBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  HostCompanyBuilder toBuilder() => new HostCompanyBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is HostCompany &&
        name == other.name &&
        webUrl == other.webUrl &&
        employeesType == other.employeesType &&
        countryCode == other.countryCode &&
        addressCode == other.addressCode &&
        address == other.address &&
        tel == other.tel &&
        contactMail == other.contactMail &&
        internationalTel == other.internationalTel &&
        capitalStock == other.capitalStock &&
        capitalStockCurrCode == other.capitalStockCurrCode &&
        logoImagePath == other.logoImagePath &&
        acceptingFee == other.acceptingFee &&
        acceptingFeeCurrCode == other.acceptingFeeCurrCode &&
        supportOutsourcingFee == other.supportOutsourcingFee &&
        supportOutsourcingFeeCurrCode == other.supportOutsourcingFeeCurrCode &&
        support == other.support;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, webUrl.hashCode);
    _$hash = $jc(_$hash, employeesType.hashCode);
    _$hash = $jc(_$hash, countryCode.hashCode);
    _$hash = $jc(_$hash, addressCode.hashCode);
    _$hash = $jc(_$hash, address.hashCode);
    _$hash = $jc(_$hash, tel.hashCode);
    _$hash = $jc(_$hash, contactMail.hashCode);
    _$hash = $jc(_$hash, internationalTel.hashCode);
    _$hash = $jc(_$hash, capitalStock.hashCode);
    _$hash = $jc(_$hash, capitalStockCurrCode.hashCode);
    _$hash = $jc(_$hash, logoImagePath.hashCode);
    _$hash = $jc(_$hash, acceptingFee.hashCode);
    _$hash = $jc(_$hash, acceptingFeeCurrCode.hashCode);
    _$hash = $jc(_$hash, supportOutsourcingFee.hashCode);
    _$hash = $jc(_$hash, supportOutsourcingFeeCurrCode.hashCode);
    _$hash = $jc(_$hash, support.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'HostCompany')
          ..add('name', name)
          ..add('webUrl', webUrl)
          ..add('employeesType', employeesType)
          ..add('countryCode', countryCode)
          ..add('addressCode', addressCode)
          ..add('address', address)
          ..add('tel', tel)
          ..add('contactMail', contactMail)
          ..add('internationalTel', internationalTel)
          ..add('capitalStock', capitalStock)
          ..add('capitalStockCurrCode', capitalStockCurrCode)
          ..add('logoImagePath', logoImagePath)
          ..add('acceptingFee', acceptingFee)
          ..add('acceptingFeeCurrCode', acceptingFeeCurrCode)
          ..add('supportOutsourcingFee', supportOutsourcingFee)
          ..add('supportOutsourcingFeeCurrCode', supportOutsourcingFeeCurrCode)
          ..add('support', support))
        .toString();
  }
}

class HostCompanyBuilder implements Builder<HostCompany, HostCompanyBuilder> {
  _$HostCompany? _$v;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _webUrl;
  String? get webUrl => _$this._webUrl;
  set webUrl(String? webUrl) => _$this._webUrl = webUrl;

  int? _employeesType;
  int? get employeesType => _$this._employeesType;
  set employeesType(int? employeesType) =>
      _$this._employeesType = employeesType;

  String? _countryCode;
  String? get countryCode => _$this._countryCode;
  set countryCode(String? countryCode) => _$this._countryCode = countryCode;

  String? _addressCode;
  String? get addressCode => _$this._addressCode;
  set addressCode(String? addressCode) => _$this._addressCode = addressCode;

  String? _address;
  String? get address => _$this._address;
  set address(String? address) => _$this._address = address;

  String? _tel;
  String? get tel => _$this._tel;
  set tel(String? tel) => _$this._tel = tel;

  String? _contactMail;
  String? get contactMail => _$this._contactMail;
  set contactMail(String? contactMail) => _$this._contactMail = contactMail;

  String? _internationalTel;
  String? get internationalTel => _$this._internationalTel;
  set internationalTel(String? internationalTel) =>
      _$this._internationalTel = internationalTel;

  String? _capitalStock;
  String? get capitalStock => _$this._capitalStock;
  set capitalStock(String? capitalStock) => _$this._capitalStock = capitalStock;

  String? _capitalStockCurrCode;
  String? get capitalStockCurrCode => _$this._capitalStockCurrCode;
  set capitalStockCurrCode(String? capitalStockCurrCode) =>
      _$this._capitalStockCurrCode = capitalStockCurrCode;

  String? _logoImagePath;
  String? get logoImagePath => _$this._logoImagePath;
  set logoImagePath(String? logoImagePath) =>
      _$this._logoImagePath = logoImagePath;

  String? _acceptingFee;
  String? get acceptingFee => _$this._acceptingFee;
  set acceptingFee(String? acceptingFee) => _$this._acceptingFee = acceptingFee;

  String? _acceptingFeeCurrCode;
  String? get acceptingFeeCurrCode => _$this._acceptingFeeCurrCode;
  set acceptingFeeCurrCode(String? acceptingFeeCurrCode) =>
      _$this._acceptingFeeCurrCode = acceptingFeeCurrCode;

  String? _supportOutsourcingFee;
  String? get supportOutsourcingFee => _$this._supportOutsourcingFee;
  set supportOutsourcingFee(String? supportOutsourcingFee) =>
      _$this._supportOutsourcingFee = supportOutsourcingFee;

  String? _supportOutsourcingFeeCurrCode;
  String? get supportOutsourcingFeeCurrCode =>
      _$this._supportOutsourcingFeeCurrCode;
  set supportOutsourcingFeeCurrCode(String? supportOutsourcingFeeCurrCode) =>
      _$this._supportOutsourcingFeeCurrCode = supportOutsourcingFeeCurrCode;

  String? _support;
  String? get support => _$this._support;
  set support(String? support) => _$this._support = support;

  HostCompanyBuilder() {
    HostCompany._defaults(this);
  }

  HostCompanyBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _name = $v.name;
      _webUrl = $v.webUrl;
      _employeesType = $v.employeesType;
      _countryCode = $v.countryCode;
      _addressCode = $v.addressCode;
      _address = $v.address;
      _tel = $v.tel;
      _contactMail = $v.contactMail;
      _internationalTel = $v.internationalTel;
      _capitalStock = $v.capitalStock;
      _capitalStockCurrCode = $v.capitalStockCurrCode;
      _logoImagePath = $v.logoImagePath;
      _acceptingFee = $v.acceptingFee;
      _acceptingFeeCurrCode = $v.acceptingFeeCurrCode;
      _supportOutsourcingFee = $v.supportOutsourcingFee;
      _supportOutsourcingFeeCurrCode = $v.supportOutsourcingFeeCurrCode;
      _support = $v.support;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(HostCompany other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$HostCompany;
  }

  @override
  void update(void Function(HostCompanyBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  HostCompany build() => _build();

  _$HostCompany _build() {
    final _$result = _$v ??
        new _$HostCompany._(
          name: name,
          webUrl: webUrl,
          employeesType: employeesType,
          countryCode: countryCode,
          addressCode: addressCode,
          address: address,
          tel: tel,
          contactMail: BuiltValueNullFieldError.checkNotNull(
              contactMail, r'HostCompany', 'contactMail'),
          internationalTel: internationalTel,
          capitalStock: capitalStock,
          capitalStockCurrCode: capitalStockCurrCode,
          logoImagePath: logoImagePath,
          acceptingFee: acceptingFee,
          acceptingFeeCurrCode: acceptingFeeCurrCode,
          supportOutsourcingFee: supportOutsourcingFee,
          supportOutsourcingFeeCurrCode: supportOutsourcingFeeCurrCode,
          support: support,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
