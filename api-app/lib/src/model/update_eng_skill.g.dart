// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_eng_skill.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UpdateEngSkill extends UpdateEngSkill {
  @override
  final String? skillCode;
  @override
  final String? jobCode;
  @override
  final int? levelType;
  @override
  final String? tempName;
  @override
  final int? tempCategoryId;

  factory _$UpdateEngSkill([void Function(UpdateEngSkillBuilder)? updates]) =>
      (new UpdateEngSkillBuilder()..update(updates))._build();

  _$UpdateEngSkill._(
      {this.skillCode,
      this.jobCode,
      this.levelType,
      this.tempName,
      this.tempCategoryId})
      : super._();

  @override
  UpdateEngSkill rebuild(void Function(UpdateEngSkillBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UpdateEngSkillBuilder toBuilder() =>
      new UpdateEngSkillBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UpdateEngSkill &&
        skillCode == other.skillCode &&
        jobCode == other.jobCode &&
        levelType == other.levelType &&
        tempName == other.tempName &&
        tempCategoryId == other.tempCategoryId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, skillCode.hashCode);
    _$hash = $jc(_$hash, jobCode.hashCode);
    _$hash = $jc(_$hash, levelType.hashCode);
    _$hash = $jc(_$hash, tempName.hashCode);
    _$hash = $jc(_$hash, tempCategoryId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UpdateEngSkill')
          ..add('skillCode', skillCode)
          ..add('jobCode', jobCode)
          ..add('levelType', levelType)
          ..add('tempName', tempName)
          ..add('tempCategoryId', tempCategoryId))
        .toString();
  }
}

class UpdateEngSkillBuilder
    implements Builder<UpdateEngSkill, UpdateEngSkillBuilder> {
  _$UpdateEngSkill? _$v;

  String? _skillCode;
  String? get skillCode => _$this._skillCode;
  set skillCode(String? skillCode) => _$this._skillCode = skillCode;

  String? _jobCode;
  String? get jobCode => _$this._jobCode;
  set jobCode(String? jobCode) => _$this._jobCode = jobCode;

  int? _levelType;
  int? get levelType => _$this._levelType;
  set levelType(int? levelType) => _$this._levelType = levelType;

  String? _tempName;
  String? get tempName => _$this._tempName;
  set tempName(String? tempName) => _$this._tempName = tempName;

  int? _tempCategoryId;
  int? get tempCategoryId => _$this._tempCategoryId;
  set tempCategoryId(int? tempCategoryId) =>
      _$this._tempCategoryId = tempCategoryId;

  UpdateEngSkillBuilder() {
    UpdateEngSkill._defaults(this);
  }

  UpdateEngSkillBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _skillCode = $v.skillCode;
      _jobCode = $v.jobCode;
      _levelType = $v.levelType;
      _tempName = $v.tempName;
      _tempCategoryId = $v.tempCategoryId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UpdateEngSkill other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$UpdateEngSkill;
  }

  @override
  void update(void Function(UpdateEngSkillBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UpdateEngSkill build() => _build();

  _$UpdateEngSkill _build() {
    final _$result = _$v ??
        new _$UpdateEngSkill._(
          skillCode: skillCode,
          jobCode: jobCode,
          levelType: levelType,
          tempName: tempName,
          tempCategoryId: tempCategoryId,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
