//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'delete_engineers.g.dart';

/// DeleteEngineers
///
/// Properties:
/// * [engineerIds] 
@BuiltValue()
abstract class DeleteEngineers implements Built<DeleteEngineers, DeleteEngineersBuilder> {
  @BuiltValueField(wireName: r'engineer_ids')
  BuiltList<int> get engineerIds;

  DeleteEngineers._();

  factory DeleteEngineers([void updates(DeleteEngineersBuilder b)]) = _$DeleteEngineers;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(DeleteEngineersBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<DeleteEngineers> get serializer => _$DeleteEngineersSerializer();
}

class _$DeleteEngineersSerializer implements PrimitiveSerializer<DeleteEngineers> {
  @override
  final Iterable<Type> types = const [DeleteEngineers, _$DeleteEngineers];

  @override
  final String wireName = r'DeleteEngineers';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    DeleteEngineers object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'engineer_ids';
    yield serializers.serialize(
      object.engineerIds,
      specifiedType: const FullType(BuiltList, [FullType(int)]),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    DeleteEngineers object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required DeleteEngineersBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'engineer_ids':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(int)]),
          ) as BuiltList<int>;
          result.engineerIds.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  DeleteEngineers deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = DeleteEngineersBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

