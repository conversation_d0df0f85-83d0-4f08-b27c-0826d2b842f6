// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'captcha_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$CaptchaResponseModel extends CaptchaResponseModel {
  @override
  final String? message;
  @override
  final Captcha data;
  @override
  final BuiltList<ErrorDetail>? errors;

  factory _$CaptchaResponseModel(
          [void Function(CaptchaResponseModelBuilder)? updates]) =>
      (new CaptchaResponseModelBuilder()..update(updates))._build();

  _$CaptchaResponseModel._({this.message, required this.data, this.errors})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'CaptchaResponseModel', 'data');
  }

  @override
  CaptchaResponseModel rebuild(
          void Function(CaptchaResponseModelBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  CaptchaResponseModelBuilder toBuilder() =>
      new CaptchaResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is CaptchaResponseModel &&
        message == other.message &&
        data == other.data &&
        errors == other.errors;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'CaptchaResponseModel')
          ..add('message', message)
          ..add('data', data)
          ..add('errors', errors))
        .toString();
  }
}

class CaptchaResponseModelBuilder
    implements Builder<CaptchaResponseModel, CaptchaResponseModelBuilder> {
  _$CaptchaResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  CaptchaBuilder? _data;
  CaptchaBuilder get data => _$this._data ??= new CaptchaBuilder();
  set data(CaptchaBuilder? data) => _$this._data = data;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  CaptchaResponseModelBuilder() {
    CaptchaResponseModel._defaults(this);
  }

  CaptchaResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _data = $v.data.toBuilder();
      _errors = $v.errors?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(CaptchaResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$CaptchaResponseModel;
  }

  @override
  void update(void Function(CaptchaResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  CaptchaResponseModel build() => _build();

  _$CaptchaResponseModel _build() {
    _$CaptchaResponseModel _$result;
    try {
      _$result = _$v ??
          new _$CaptchaResponseModel._(
            message: message,
            data: data.build(),
            errors: _errors?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        data.build();
        _$failedField = 'errors';
        _errors?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'CaptchaResponseModel', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
