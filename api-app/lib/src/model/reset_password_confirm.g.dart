// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'reset_password_confirm.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ResetPasswordConfirm extends ResetPasswordConfirm {
  @override
  final String token;

  factory _$ResetPasswordConfirm(
          [void Function(ResetPasswordConfirmBuilder)? updates]) =>
      (new ResetPasswordConfirmBuilder()..update(updates))._build();

  _$ResetPasswordConfirm._({required this.token}) : super._() {
    BuiltValueNullFieldError.checkNotNull(
        token, r'ResetPasswordConfirm', 'token');
  }

  @override
  ResetPasswordConfirm rebuild(
          void Function(ResetPasswordConfirmBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ResetPasswordConfirmBuilder toBuilder() =>
      new ResetPasswordConfirmBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ResetPasswordConfirm && token == other.token;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, token.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ResetPasswordConfirm')
          ..add('token', token))
        .toString();
  }
}

class ResetPasswordConfirmBuilder
    implements Builder<ResetPasswordConfirm, ResetPasswordConfirmBuilder> {
  _$ResetPasswordConfirm? _$v;

  String? _token;
  String? get token => _$this._token;
  set token(String? token) => _$this._token = token;

  ResetPasswordConfirmBuilder() {
    ResetPasswordConfirm._defaults(this);
  }

  ResetPasswordConfirmBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _token = $v.token;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ResetPasswordConfirm other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$ResetPasswordConfirm;
  }

  @override
  void update(void Function(ResetPasswordConfirmBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ResetPasswordConfirm build() => _build();

  _$ResetPasswordConfirm _build() {
    final _$result = _$v ??
        new _$ResetPasswordConfirm._(
          token: BuiltValueNullFieldError.checkNotNull(
              token, r'ResetPasswordConfirm', 'token'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
