// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'check_captcha_require_in_login_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$CheckCaptchaRequireInLoginResponseModel
    extends CheckCaptchaRequireInLoginResponseModel {
  @override
  final String? message;
  @override
  final CaptchaAndUserStatus data;
  @override
  final BuiltList<ErrorDetail>? errors;

  factory _$CheckCaptchaRequireInLoginResponseModel(
          [void Function(CheckCaptchaRequireInLoginResponseModelBuilder)?
              updates]) =>
      (new CheckCaptchaRequireInLoginResponseModelBuilder()..update(updates))
          ._build();

  _$CheckCaptchaRequireInLoginResponseModel._(
      {this.message, required this.data, this.errors})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'CheckCaptchaRequireInLoginResponseModel', 'data');
  }

  @override
  CheckCaptchaRequireInLoginResponseModel rebuild(
          void Function(CheckCaptchaRequireInLoginResponseModelBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  CheckCaptchaRequireInLoginResponseModelBuilder toBuilder() =>
      new CheckCaptchaRequireInLoginResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is CheckCaptchaRequireInLoginResponseModel &&
        message == other.message &&
        data == other.data &&
        errors == other.errors;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'CheckCaptchaRequireInLoginResponseModel')
          ..add('message', message)
          ..add('data', data)
          ..add('errors', errors))
        .toString();
  }
}

class CheckCaptchaRequireInLoginResponseModelBuilder
    implements
        Builder<CheckCaptchaRequireInLoginResponseModel,
            CheckCaptchaRequireInLoginResponseModelBuilder> {
  _$CheckCaptchaRequireInLoginResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  CaptchaAndUserStatusBuilder? _data;
  CaptchaAndUserStatusBuilder get data =>
      _$this._data ??= new CaptchaAndUserStatusBuilder();
  set data(CaptchaAndUserStatusBuilder? data) => _$this._data = data;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  CheckCaptchaRequireInLoginResponseModelBuilder() {
    CheckCaptchaRequireInLoginResponseModel._defaults(this);
  }

  CheckCaptchaRequireInLoginResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _data = $v.data.toBuilder();
      _errors = $v.errors?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(CheckCaptchaRequireInLoginResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$CheckCaptchaRequireInLoginResponseModel;
  }

  @override
  void update(
      void Function(CheckCaptchaRequireInLoginResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  CheckCaptchaRequireInLoginResponseModel build() => _build();

  _$CheckCaptchaRequireInLoginResponseModel _build() {
    _$CheckCaptchaRequireInLoginResponseModel _$result;
    try {
      _$result = _$v ??
          new _$CheckCaptchaRequireInLoginResponseModel._(
            message: message,
            data: data.build(),
            errors: _errors?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        data.build();
        _$failedField = 'errors';
        _errors?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'CheckCaptchaRequireInLoginResponseModel',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
