// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recruit_explore_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$RecruitExploreResponseModel extends RecruitExploreResponseModel {
  @override
  final String? message;
  @override
  final PagingResponseModel data;
  @override
  final BuiltList<ErrorDetail>? errors;

  factory _$RecruitExploreResponseModel(
          [void Function(RecruitExploreResponseModelBuilder)? updates]) =>
      (new RecruitExploreResponseModelBuilder()..update(updates))._build();

  _$RecruitExploreResponseModel._(
      {this.message, required this.data, this.errors})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'RecruitExploreResponseModel', 'data');
  }

  @override
  RecruitExploreResponseModel rebuild(
          void Function(RecruitExploreResponseModelBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  RecruitExploreResponseModelBuilder toBuilder() =>
      new RecruitExploreResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is RecruitExploreResponseModel &&
        message == other.message &&
        data == other.data &&
        errors == other.errors;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'RecruitExploreResponseModel')
          ..add('message', message)
          ..add('data', data)
          ..add('errors', errors))
        .toString();
  }
}

class RecruitExploreResponseModelBuilder
    implements
        Builder<RecruitExploreResponseModel,
            RecruitExploreResponseModelBuilder> {
  _$RecruitExploreResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  PagingResponseModelBuilder? _data;
  PagingResponseModelBuilder get data =>
      _$this._data ??= new PagingResponseModelBuilder();
  set data(PagingResponseModelBuilder? data) => _$this._data = data;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  RecruitExploreResponseModelBuilder() {
    RecruitExploreResponseModel._defaults(this);
  }

  RecruitExploreResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _data = $v.data.toBuilder();
      _errors = $v.errors?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(RecruitExploreResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$RecruitExploreResponseModel;
  }

  @override
  void update(void Function(RecruitExploreResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  RecruitExploreResponseModel build() => _build();

  _$RecruitExploreResponseModel _build() {
    _$RecruitExploreResponseModel _$result;
    try {
      _$result = _$v ??
          new _$RecruitExploreResponseModel._(
            message: message,
            data: data.build(),
            errors: _errors?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        data.build();
        _$failedField = 'errors';
        _errors?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'RecruitExploreResponseModel', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
