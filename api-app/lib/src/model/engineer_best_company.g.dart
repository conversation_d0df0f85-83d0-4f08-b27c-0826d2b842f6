// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'engineer_best_company.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$EngineerBestCompany extends EngineerBestCompany {
  @override
  final int? companyId;
  @override
  final String? name;
  @override
  final String? logoImagePath;

  factory _$EngineerBestCompany(
          [void Function(EngineerBestCompanyBuilder)? updates]) =>
      (new EngineerBestCompanyBuilder()..update(updates))._build();

  _$EngineerBestCompany._({this.companyId, this.name, this.logoImagePath})
      : super._();

  @override
  EngineerBestCompany rebuild(
          void Function(EngineerBestCompanyBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  EngineerBestCompanyBuilder toBuilder() =>
      new EngineerBestCompanyBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is EngineerBestCompany &&
        companyId == other.companyId &&
        name == other.name &&
        logoImagePath == other.logoImagePath;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, companyId.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, logoImagePath.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'EngineerBestCompany')
          ..add('companyId', companyId)
          ..add('name', name)
          ..add('logoImagePath', logoImagePath))
        .toString();
  }
}

class EngineerBestCompanyBuilder
    implements Builder<EngineerBestCompany, EngineerBestCompanyBuilder> {
  _$EngineerBestCompany? _$v;

  int? _companyId;
  int? get companyId => _$this._companyId;
  set companyId(int? companyId) => _$this._companyId = companyId;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _logoImagePath;
  String? get logoImagePath => _$this._logoImagePath;
  set logoImagePath(String? logoImagePath) =>
      _$this._logoImagePath = logoImagePath;

  EngineerBestCompanyBuilder() {
    EngineerBestCompany._defaults(this);
  }

  EngineerBestCompanyBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _companyId = $v.companyId;
      _name = $v.name;
      _logoImagePath = $v.logoImagePath;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(EngineerBestCompany other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$EngineerBestCompany;
  }

  @override
  void update(void Function(EngineerBestCompanyBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  EngineerBestCompany build() => _build();

  _$EngineerBestCompany _build() {
    final _$result = _$v ??
        new _$EngineerBestCompany._(
          companyId: companyId,
          name: name,
          logoImagePath: logoImagePath,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
