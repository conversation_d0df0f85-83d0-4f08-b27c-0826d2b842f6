//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/date.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'eng_work_history.g.dart';

/// EngWorkHistory
///
/// Properties:
/// * [careerId] 
/// * [engineerId] 
/// * [companyName] 
/// * [jobDescription] 
/// * [careerType] 
/// * [enteringDate] 
/// * [quittingDate] 
/// * [roleName] 
@BuiltValue()
abstract class EngWorkHistory implements Built<EngWorkHistory, EngWorkHistoryBuilder> {
  @BuiltValueField(wireName: r'career_id')
  int? get careerId;

  @BuiltValueField(wireName: r'engineer_id')
  int get engineerId;

  @BuiltValueField(wireName: r'company_name')
  String? get companyName;

  @BuiltValueField(wireName: r'job_description')
  String? get jobDescription;

  @BuiltValueField(wireName: r'career_type')
  int? get careerType;

  @BuiltValueField(wireName: r'entering_date')
  Date? get enteringDate;

  @BuiltValueField(wireName: r'quitting_date')
  Date? get quittingDate;

  @BuiltValueField(wireName: r'role_name')
  String? get roleName;

  EngWorkHistory._();

  factory EngWorkHistory([void updates(EngWorkHistoryBuilder b)]) = _$EngWorkHistory;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(EngWorkHistoryBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<EngWorkHistory> get serializer => _$EngWorkHistorySerializer();
}

class _$EngWorkHistorySerializer implements PrimitiveSerializer<EngWorkHistory> {
  @override
  final Iterable<Type> types = const [EngWorkHistory, _$EngWorkHistory];

  @override
  final String wireName = r'EngWorkHistory';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    EngWorkHistory object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.careerId != null) {
      yield r'career_id';
      yield serializers.serialize(
        object.careerId,
        specifiedType: const FullType(int),
      );
    }
    yield r'engineer_id';
    yield serializers.serialize(
      object.engineerId,
      specifiedType: const FullType(int),
    );
    if (object.companyName != null) {
      yield r'company_name';
      yield serializers.serialize(
        object.companyName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.jobDescription != null) {
      yield r'job_description';
      yield serializers.serialize(
        object.jobDescription,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.careerType != null) {
      yield r'career_type';
      yield serializers.serialize(
        object.careerType,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.enteringDate != null) {
      yield r'entering_date';
      yield serializers.serialize(
        object.enteringDate,
        specifiedType: const FullType.nullable(Date),
      );
    }
    if (object.quittingDate != null) {
      yield r'quitting_date';
      yield serializers.serialize(
        object.quittingDate,
        specifiedType: const FullType.nullable(Date),
      );
    }
    if (object.roleName != null) {
      yield r'role_name';
      yield serializers.serialize(
        object.roleName,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    EngWorkHistory object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required EngWorkHistoryBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'career_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.careerId = valueDes;
          break;
        case r'engineer_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.engineerId = valueDes;
          break;
        case r'company_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.companyName = valueDes;
          break;
        case r'job_description':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.jobDescription = valueDes;
          break;
        case r'career_type':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.careerType = valueDes;
          break;
        case r'entering_date':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(Date),
          ) as Date?;
          if (valueDes == null) continue;
          result.enteringDate = valueDes;
          break;
        case r'quitting_date':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(Date),
          ) as Date?;
          if (valueDes == null) continue;
          result.quittingDate = valueDes;
          break;
        case r'role_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.roleName = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  EngWorkHistory deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = EngWorkHistoryBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

