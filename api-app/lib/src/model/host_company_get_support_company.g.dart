// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'host_company_get_support_company.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$HostCompanyGetSupportCompany extends HostCompanyGetSupportCompany {
  @override
  final int? companyId;
  @override
  final String? name;
  @override
  final String? logoImagePath;
  @override
  final double? acceptingFee;
  @override
  final double? supportOutsourcingFee;
  @override
  final String? addressCode;
  @override
  final String? countryCode;
  @override
  final String? supportOutsourcingFeeCurrCode;
  @override
  final String? contactMail;
  @override
  final String? tel;
  @override
  final String? internationalTel;
  @override
  final String? acceptingFeeCurrCode;
  @override
  final String? introductionPr;

  factory _$HostCompanyGetSupportCompany(
          [void Function(HostCompanyGetSupportCompanyBuilder)? updates]) =>
      (new HostCompanyGetSupportCompanyBuilder()..update(updates))._build();

  _$HostCompanyGetSupportCompany._(
      {this.companyId,
      this.name,
      this.logoImagePath,
      this.acceptingFee,
      this.supportOutsourcingFee,
      this.addressCode,
      this.countryCode,
      this.supportOutsourcingFeeCurrCode,
      this.contactMail,
      this.tel,
      this.internationalTel,
      this.acceptingFeeCurrCode,
      this.introductionPr})
      : super._();

  @override
  HostCompanyGetSupportCompany rebuild(
          void Function(HostCompanyGetSupportCompanyBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  HostCompanyGetSupportCompanyBuilder toBuilder() =>
      new HostCompanyGetSupportCompanyBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is HostCompanyGetSupportCompany &&
        companyId == other.companyId &&
        name == other.name &&
        logoImagePath == other.logoImagePath &&
        acceptingFee == other.acceptingFee &&
        supportOutsourcingFee == other.supportOutsourcingFee &&
        addressCode == other.addressCode &&
        countryCode == other.countryCode &&
        supportOutsourcingFeeCurrCode == other.supportOutsourcingFeeCurrCode &&
        contactMail == other.contactMail &&
        tel == other.tel &&
        internationalTel == other.internationalTel &&
        acceptingFeeCurrCode == other.acceptingFeeCurrCode &&
        introductionPr == other.introductionPr;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, companyId.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, logoImagePath.hashCode);
    _$hash = $jc(_$hash, acceptingFee.hashCode);
    _$hash = $jc(_$hash, supportOutsourcingFee.hashCode);
    _$hash = $jc(_$hash, addressCode.hashCode);
    _$hash = $jc(_$hash, countryCode.hashCode);
    _$hash = $jc(_$hash, supportOutsourcingFeeCurrCode.hashCode);
    _$hash = $jc(_$hash, contactMail.hashCode);
    _$hash = $jc(_$hash, tel.hashCode);
    _$hash = $jc(_$hash, internationalTel.hashCode);
    _$hash = $jc(_$hash, acceptingFeeCurrCode.hashCode);
    _$hash = $jc(_$hash, introductionPr.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'HostCompanyGetSupportCompany')
          ..add('companyId', companyId)
          ..add('name', name)
          ..add('logoImagePath', logoImagePath)
          ..add('acceptingFee', acceptingFee)
          ..add('supportOutsourcingFee', supportOutsourcingFee)
          ..add('addressCode', addressCode)
          ..add('countryCode', countryCode)
          ..add('supportOutsourcingFeeCurrCode', supportOutsourcingFeeCurrCode)
          ..add('contactMail', contactMail)
          ..add('tel', tel)
          ..add('internationalTel', internationalTel)
          ..add('acceptingFeeCurrCode', acceptingFeeCurrCode)
          ..add('introductionPr', introductionPr))
        .toString();
  }
}

class HostCompanyGetSupportCompanyBuilder
    implements
        Builder<HostCompanyGetSupportCompany,
            HostCompanyGetSupportCompanyBuilder> {
  _$HostCompanyGetSupportCompany? _$v;

  int? _companyId;
  int? get companyId => _$this._companyId;
  set companyId(int? companyId) => _$this._companyId = companyId;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _logoImagePath;
  String? get logoImagePath => _$this._logoImagePath;
  set logoImagePath(String? logoImagePath) =>
      _$this._logoImagePath = logoImagePath;

  double? _acceptingFee;
  double? get acceptingFee => _$this._acceptingFee;
  set acceptingFee(double? acceptingFee) => _$this._acceptingFee = acceptingFee;

  double? _supportOutsourcingFee;
  double? get supportOutsourcingFee => _$this._supportOutsourcingFee;
  set supportOutsourcingFee(double? supportOutsourcingFee) =>
      _$this._supportOutsourcingFee = supportOutsourcingFee;

  String? _addressCode;
  String? get addressCode => _$this._addressCode;
  set addressCode(String? addressCode) => _$this._addressCode = addressCode;

  String? _countryCode;
  String? get countryCode => _$this._countryCode;
  set countryCode(String? countryCode) => _$this._countryCode = countryCode;

  String? _supportOutsourcingFeeCurrCode;
  String? get supportOutsourcingFeeCurrCode =>
      _$this._supportOutsourcingFeeCurrCode;
  set supportOutsourcingFeeCurrCode(String? supportOutsourcingFeeCurrCode) =>
      _$this._supportOutsourcingFeeCurrCode = supportOutsourcingFeeCurrCode;

  String? _contactMail;
  String? get contactMail => _$this._contactMail;
  set contactMail(String? contactMail) => _$this._contactMail = contactMail;

  String? _tel;
  String? get tel => _$this._tel;
  set tel(String? tel) => _$this._tel = tel;

  String? _internationalTel;
  String? get internationalTel => _$this._internationalTel;
  set internationalTel(String? internationalTel) =>
      _$this._internationalTel = internationalTel;

  String? _acceptingFeeCurrCode;
  String? get acceptingFeeCurrCode => _$this._acceptingFeeCurrCode;
  set acceptingFeeCurrCode(String? acceptingFeeCurrCode) =>
      _$this._acceptingFeeCurrCode = acceptingFeeCurrCode;

  String? _introductionPr;
  String? get introductionPr => _$this._introductionPr;
  set introductionPr(String? introductionPr) =>
      _$this._introductionPr = introductionPr;

  HostCompanyGetSupportCompanyBuilder() {
    HostCompanyGetSupportCompany._defaults(this);
  }

  HostCompanyGetSupportCompanyBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _companyId = $v.companyId;
      _name = $v.name;
      _logoImagePath = $v.logoImagePath;
      _acceptingFee = $v.acceptingFee;
      _supportOutsourcingFee = $v.supportOutsourcingFee;
      _addressCode = $v.addressCode;
      _countryCode = $v.countryCode;
      _supportOutsourcingFeeCurrCode = $v.supportOutsourcingFeeCurrCode;
      _contactMail = $v.contactMail;
      _tel = $v.tel;
      _internationalTel = $v.internationalTel;
      _acceptingFeeCurrCode = $v.acceptingFeeCurrCode;
      _introductionPr = $v.introductionPr;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(HostCompanyGetSupportCompany other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$HostCompanyGetSupportCompany;
  }

  @override
  void update(void Function(HostCompanyGetSupportCompanyBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  HostCompanyGetSupportCompany build() => _build();

  _$HostCompanyGetSupportCompany _build() {
    final _$result = _$v ??
        new _$HostCompanyGetSupportCompany._(
          companyId: companyId,
          name: name,
          logoImagePath: logoImagePath,
          acceptingFee: acceptingFee,
          supportOutsourcingFee: supportOutsourcingFee,
          addressCode: addressCode,
          countryCode: countryCode,
          supportOutsourcingFeeCurrCode: supportOutsourcingFeeCurrCode,
          contactMail: contactMail,
          tel: tel,
          internationalTel: internationalTel,
          acceptingFeeCurrCode: acceptingFeeCurrCode,
          introductionPr: introductionPr,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
