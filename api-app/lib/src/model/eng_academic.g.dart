// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'eng_academic.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$EngAcademic extends EngAcademic {
  @override
  final int engineerId;
  @override
  final String? school;
  @override
  final int? type;
  @override
  final Date? outDate;
  @override
  final String? faculty;
  @override
  final String? typeName;

  factory _$EngAcademic([void Function(EngAcademicBuilder)? updates]) =>
      (new EngAcademicBuilder()..update(updates))._build();

  _$EngAcademic._(
      {required this.engineerId,
      this.school,
      this.type,
      this.outDate,
      this.faculty,
      this.typeName})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        engineerId, r'EngAcademic', 'engineerId');
  }

  @override
  EngAcademic rebuild(void Function(EngAcademicBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  EngAcademicBuilder toBuilder() => new EngAcademicBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is EngAcademic &&
        engineerId == other.engineerId &&
        school == other.school &&
        type == other.type &&
        outDate == other.outDate &&
        faculty == other.faculty &&
        typeName == other.typeName;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, engineerId.hashCode);
    _$hash = $jc(_$hash, school.hashCode);
    _$hash = $jc(_$hash, type.hashCode);
    _$hash = $jc(_$hash, outDate.hashCode);
    _$hash = $jc(_$hash, faculty.hashCode);
    _$hash = $jc(_$hash, typeName.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'EngAcademic')
          ..add('engineerId', engineerId)
          ..add('school', school)
          ..add('type', type)
          ..add('outDate', outDate)
          ..add('faculty', faculty)
          ..add('typeName', typeName))
        .toString();
  }
}

class EngAcademicBuilder implements Builder<EngAcademic, EngAcademicBuilder> {
  _$EngAcademic? _$v;

  int? _engineerId;
  int? get engineerId => _$this._engineerId;
  set engineerId(int? engineerId) => _$this._engineerId = engineerId;

  String? _school;
  String? get school => _$this._school;
  set school(String? school) => _$this._school = school;

  int? _type;
  int? get type => _$this._type;
  set type(int? type) => _$this._type = type;

  Date? _outDate;
  Date? get outDate => _$this._outDate;
  set outDate(Date? outDate) => _$this._outDate = outDate;

  String? _faculty;
  String? get faculty => _$this._faculty;
  set faculty(String? faculty) => _$this._faculty = faculty;

  String? _typeName;
  String? get typeName => _$this._typeName;
  set typeName(String? typeName) => _$this._typeName = typeName;

  EngAcademicBuilder() {
    EngAcademic._defaults(this);
  }

  EngAcademicBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _engineerId = $v.engineerId;
      _school = $v.school;
      _type = $v.type;
      _outDate = $v.outDate;
      _faculty = $v.faculty;
      _typeName = $v.typeName;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(EngAcademic other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$EngAcademic;
  }

  @override
  void update(void Function(EngAcademicBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  EngAcademic build() => _build();

  _$EngAcademic _build() {
    final _$result = _$v ??
        new _$EngAcademic._(
          engineerId: BuiltValueNullFieldError.checkNotNull(
              engineerId, r'EngAcademic', 'engineerId'),
          school: school,
          type: type,
          outDate: outDate,
          faculty: faculty,
          typeName: typeName,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
