// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_company_list_user_applied_company_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GeneralCompanyListUserAppliedCompanyResponseModel
    extends GeneralCompanyListUserAppliedCompanyResponseModel {
  @override
  final String? message;
  @override
  final BuiltList<ErrorDetail>? errors;
  @override
  final PagingGeneralCompanyAppliedEngineersResponseModel data;

  factory _$GeneralCompanyListUserAppliedCompanyResponseModel(
          [void Function(
                  GeneralCompanyListUserAppliedCompanyResponseModelBuilder)?
              updates]) =>
      (new GeneralCompanyListUserAppliedCompanyResponseModelBuilder()
            ..update(updates))
          ._build();

  _$GeneralCompanyListUserAppliedCompanyResponseModel._(
      {this.message, this.errors, required this.data})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'GeneralCompanyListUserAppliedCompanyResponseModel', 'data');
  }

  @override
  GeneralCompanyListUserAppliedCompanyResponseModel rebuild(
          void Function(
                  GeneralCompanyListUserAppliedCompanyResponseModelBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GeneralCompanyListUserAppliedCompanyResponseModelBuilder toBuilder() =>
      new GeneralCompanyListUserAppliedCompanyResponseModelBuilder()
        ..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GeneralCompanyListUserAppliedCompanyResponseModel &&
        message == other.message &&
        errors == other.errors &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'GeneralCompanyListUserAppliedCompanyResponseModel')
          ..add('message', message)
          ..add('errors', errors)
          ..add('data', data))
        .toString();
  }
}

class GeneralCompanyListUserAppliedCompanyResponseModelBuilder
    implements
        Builder<GeneralCompanyListUserAppliedCompanyResponseModel,
            GeneralCompanyListUserAppliedCompanyResponseModelBuilder> {
  _$GeneralCompanyListUserAppliedCompanyResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  PagingGeneralCompanyAppliedEngineersResponseModelBuilder? _data;
  PagingGeneralCompanyAppliedEngineersResponseModelBuilder get data =>
      _$this._data ??=
          new PagingGeneralCompanyAppliedEngineersResponseModelBuilder();
  set data(PagingGeneralCompanyAppliedEngineersResponseModelBuilder? data) =>
      _$this._data = data;

  GeneralCompanyListUserAppliedCompanyResponseModelBuilder() {
    GeneralCompanyListUserAppliedCompanyResponseModel._defaults(this);
  }

  GeneralCompanyListUserAppliedCompanyResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _errors = $v.errors?.toBuilder();
      _data = $v.data.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GeneralCompanyListUserAppliedCompanyResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GeneralCompanyListUserAppliedCompanyResponseModel;
  }

  @override
  void update(
      void Function(GeneralCompanyListUserAppliedCompanyResponseModelBuilder)?
          updates) {
    if (updates != null) updates(this);
  }

  @override
  GeneralCompanyListUserAppliedCompanyResponseModel build() => _build();

  _$GeneralCompanyListUserAppliedCompanyResponseModel _build() {
    _$GeneralCompanyListUserAppliedCompanyResponseModel _$result;
    try {
      _$result = _$v ??
          new _$GeneralCompanyListUserAppliedCompanyResponseModel._(
            message: message,
            errors: _errors?.build(),
            data: data.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'errors';
        _errors?.build();
        _$failedField = 'data';
        data.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'GeneralCompanyListUserAppliedCompanyResponseModel',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
