//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/email_schedule.dart';
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/error_detail.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'get_email_detail_response_model.g.dart';

/// GetEmailDetailResponseModel
///
/// Properties:
/// * [message] 
/// * [data] 
/// * [errors] 
@BuiltValue()
abstract class GetEmailDetailResponseModel implements Built<GetEmailDetailResponseModel, GetEmailDetailResponseModelBuilder> {
  @BuiltValueField(wireName: r'message')
  String? get message;

  @BuiltValueField(wireName: r'data')
  EmailSchedule get data;

  @BuiltValueField(wireName: r'errors')
  BuiltList<ErrorDetail>? get errors;

  GetEmailDetailResponseModel._();

  factory GetEmailDetailResponseModel([void updates(GetEmailDetailResponseModelBuilder b)]) = _$GetEmailDetailResponseModel;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(GetEmailDetailResponseModelBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<GetEmailDetailResponseModel> get serializer => _$GetEmailDetailResponseModelSerializer();
}

class _$GetEmailDetailResponseModelSerializer implements PrimitiveSerializer<GetEmailDetailResponseModel> {
  @override
  final Iterable<Type> types = const [GetEmailDetailResponseModel, _$GetEmailDetailResponseModel];

  @override
  final String wireName = r'GetEmailDetailResponseModel';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    GetEmailDetailResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'message';
    yield object.message == null ? null : serializers.serialize(
      object.message,
      specifiedType: const FullType.nullable(String),
    );
    yield r'data';
    yield serializers.serialize(
      object.data,
      specifiedType: const FullType(EmailSchedule),
    );
    yield r'errors';
    yield object.errors == null ? null : serializers.serialize(
      object.errors,
      specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    GetEmailDetailResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required GetEmailDetailResponseModelBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'message':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.message = valueDes;
          break;
        case r'data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(EmailSchedule),
          ) as EmailSchedule;
          result.data.replace(valueDes);
          break;
        case r'errors':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
          ) as BuiltList<ErrorDetail>?;
          if (valueDes == null) continue;
          result.errors.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  GetEmailDetailResponseModel deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = GetEmailDetailResponseModelBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

