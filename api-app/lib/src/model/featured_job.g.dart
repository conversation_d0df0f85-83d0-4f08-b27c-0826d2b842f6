// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'featured_job.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$FeaturedJob extends FeaturedJob {
  @override
  final String id;
  @override
  final String nameJp;
  @override
  final String nameEn;
  @override
  final String image;
  @override
  final int totalJobs;

  factory _$FeaturedJob([void Function(FeaturedJobBuilder)? updates]) =>
      (new FeaturedJobBuilder()..update(updates))._build();

  _$FeaturedJob._(
      {required this.id,
      required this.nameJp,
      required this.nameEn,
      required this.image,
      required this.totalJobs})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(id, r'FeaturedJob', 'id');
    BuiltValueNullFieldError.checkNotNull(nameJp, r'FeaturedJob', 'nameJp');
    BuiltValueNullFieldError.checkNotNull(nameEn, r'FeaturedJob', 'nameEn');
    BuiltValueNullFieldError.checkNotNull(image, r'FeaturedJob', 'image');
    BuiltValueNullFieldError.checkNotNull(
        totalJobs, r'FeaturedJob', 'totalJobs');
  }

  @override
  FeaturedJob rebuild(void Function(FeaturedJobBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  FeaturedJobBuilder toBuilder() => new FeaturedJobBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is FeaturedJob &&
        id == other.id &&
        nameJp == other.nameJp &&
        nameEn == other.nameEn &&
        image == other.image &&
        totalJobs == other.totalJobs;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, nameJp.hashCode);
    _$hash = $jc(_$hash, nameEn.hashCode);
    _$hash = $jc(_$hash, image.hashCode);
    _$hash = $jc(_$hash, totalJobs.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'FeaturedJob')
          ..add('id', id)
          ..add('nameJp', nameJp)
          ..add('nameEn', nameEn)
          ..add('image', image)
          ..add('totalJobs', totalJobs))
        .toString();
  }
}

class FeaturedJobBuilder implements Builder<FeaturedJob, FeaturedJobBuilder> {
  _$FeaturedJob? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _nameJp;
  String? get nameJp => _$this._nameJp;
  set nameJp(String? nameJp) => _$this._nameJp = nameJp;

  String? _nameEn;
  String? get nameEn => _$this._nameEn;
  set nameEn(String? nameEn) => _$this._nameEn = nameEn;

  String? _image;
  String? get image => _$this._image;
  set image(String? image) => _$this._image = image;

  int? _totalJobs;
  int? get totalJobs => _$this._totalJobs;
  set totalJobs(int? totalJobs) => _$this._totalJobs = totalJobs;

  FeaturedJobBuilder() {
    FeaturedJob._defaults(this);
  }

  FeaturedJobBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _nameJp = $v.nameJp;
      _nameEn = $v.nameEn;
      _image = $v.image;
      _totalJobs = $v.totalJobs;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(FeaturedJob other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$FeaturedJob;
  }

  @override
  void update(void Function(FeaturedJobBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  FeaturedJob build() => _build();

  _$FeaturedJob _build() {
    final _$result = _$v ??
        new _$FeaturedJob._(
          id: BuiltValueNullFieldError.checkNotNull(id, r'FeaturedJob', 'id'),
          nameJp: BuiltValueNullFieldError.checkNotNull(
              nameJp, r'FeaturedJob', 'nameJp'),
          nameEn: BuiltValueNullFieldError.checkNotNull(
              nameEn, r'FeaturedJob', 'nameEn'),
          image: BuiltValueNullFieldError.checkNotNull(
              image, r'FeaturedJob', 'image'),
          totalJobs: BuiltValueNullFieldError.checkNotNull(
              totalJobs, r'FeaturedJob', 'totalJobs'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
