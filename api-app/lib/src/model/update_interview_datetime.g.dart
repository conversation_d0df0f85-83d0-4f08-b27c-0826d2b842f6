// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_interview_datetime.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UpdateInterviewDatetime extends UpdateInterviewDatetime {
  @override
  final DateTime? interviewDatetime;

  factory _$UpdateInterviewDatetime(
          [void Function(UpdateInterviewDatetimeBuilder)? updates]) =>
      (new UpdateInterviewDatetimeBuilder()..update(updates))._build();

  _$UpdateInterviewDatetime._({this.interviewDatetime}) : super._();

  @override
  UpdateInterviewDatetime rebuild(
          void Function(UpdateInterviewDatetimeBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UpdateInterviewDatetimeBuilder toBuilder() =>
      new UpdateInterviewDatetimeBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UpdateInterviewDatetime &&
        interviewDatetime == other.interviewDatetime;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, interviewDatetime.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UpdateInterviewDatetime')
          ..add('interviewDatetime', interviewDatetime))
        .toString();
  }
}

class UpdateInterviewDatetimeBuilder
    implements
        Builder<UpdateInterviewDatetime, UpdateInterviewDatetimeBuilder> {
  _$UpdateInterviewDatetime? _$v;

  DateTime? _interviewDatetime;
  DateTime? get interviewDatetime => _$this._interviewDatetime;
  set interviewDatetime(DateTime? interviewDatetime) =>
      _$this._interviewDatetime = interviewDatetime;

  UpdateInterviewDatetimeBuilder() {
    UpdateInterviewDatetime._defaults(this);
  }

  UpdateInterviewDatetimeBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _interviewDatetime = $v.interviewDatetime;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UpdateInterviewDatetime other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$UpdateInterviewDatetime;
  }

  @override
  void update(void Function(UpdateInterviewDatetimeBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UpdateInterviewDatetime build() => _build();

  _$UpdateInterviewDatetime _build() {
    final _$result = _$v ??
        new _$UpdateInterviewDatetime._(
          interviewDatetime: interviewDatetime,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
