// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'eng_hope.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$EngHope extends EngHope {
  @override
  final String? employCode;
  @override
  final String? placeCode1;
  @override
  final String? placeCode2;
  @override
  final String? placeCode3;
  @override
  final String? payrollPrice;
  @override
  final String? payrollCode;
  @override
  final BuiltList<HopeJobSkill>? jobSkills;
  @override
  final String? placeCode1Name;
  @override
  final String? placeCode2Name;
  @override
  final String? placeCode3Name;
  @override
  final String? countryPlaceCode1Name;
  @override
  final String? countryPlaceCode2Name;
  @override
  final String? countryPlaceCode3Name;
  @override
  final String? remoteCode;
  @override
  final BuiltList<HopeCategorySkill> categorySkills;
  @override
  final double? payrollPriceUsd;

  factory _$EngHope([void Function(EngHopeBuilder)? updates]) =>
      (new EngHopeBuilder()..update(updates))._build();

  _$EngHope._(
      {this.employCode,
      this.placeCode1,
      this.placeCode2,
      this.placeCode3,
      this.payrollPrice,
      this.payrollCode,
      this.jobSkills,
      this.placeCode1Name,
      this.placeCode2Name,
      this.placeCode3Name,
      this.countryPlaceCode1Name,
      this.countryPlaceCode2Name,
      this.countryPlaceCode3Name,
      this.remoteCode,
      required this.categorySkills,
      this.payrollPriceUsd})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        categorySkills, r'EngHope', 'categorySkills');
  }

  @override
  EngHope rebuild(void Function(EngHopeBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  EngHopeBuilder toBuilder() => new EngHopeBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is EngHope &&
        employCode == other.employCode &&
        placeCode1 == other.placeCode1 &&
        placeCode2 == other.placeCode2 &&
        placeCode3 == other.placeCode3 &&
        payrollPrice == other.payrollPrice &&
        payrollCode == other.payrollCode &&
        jobSkills == other.jobSkills &&
        placeCode1Name == other.placeCode1Name &&
        placeCode2Name == other.placeCode2Name &&
        placeCode3Name == other.placeCode3Name &&
        countryPlaceCode1Name == other.countryPlaceCode1Name &&
        countryPlaceCode2Name == other.countryPlaceCode2Name &&
        countryPlaceCode3Name == other.countryPlaceCode3Name &&
        remoteCode == other.remoteCode &&
        categorySkills == other.categorySkills &&
        payrollPriceUsd == other.payrollPriceUsd;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, employCode.hashCode);
    _$hash = $jc(_$hash, placeCode1.hashCode);
    _$hash = $jc(_$hash, placeCode2.hashCode);
    _$hash = $jc(_$hash, placeCode3.hashCode);
    _$hash = $jc(_$hash, payrollPrice.hashCode);
    _$hash = $jc(_$hash, payrollCode.hashCode);
    _$hash = $jc(_$hash, jobSkills.hashCode);
    _$hash = $jc(_$hash, placeCode1Name.hashCode);
    _$hash = $jc(_$hash, placeCode2Name.hashCode);
    _$hash = $jc(_$hash, placeCode3Name.hashCode);
    _$hash = $jc(_$hash, countryPlaceCode1Name.hashCode);
    _$hash = $jc(_$hash, countryPlaceCode2Name.hashCode);
    _$hash = $jc(_$hash, countryPlaceCode3Name.hashCode);
    _$hash = $jc(_$hash, remoteCode.hashCode);
    _$hash = $jc(_$hash, categorySkills.hashCode);
    _$hash = $jc(_$hash, payrollPriceUsd.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'EngHope')
          ..add('employCode', employCode)
          ..add('placeCode1', placeCode1)
          ..add('placeCode2', placeCode2)
          ..add('placeCode3', placeCode3)
          ..add('payrollPrice', payrollPrice)
          ..add('payrollCode', payrollCode)
          ..add('jobSkills', jobSkills)
          ..add('placeCode1Name', placeCode1Name)
          ..add('placeCode2Name', placeCode2Name)
          ..add('placeCode3Name', placeCode3Name)
          ..add('countryPlaceCode1Name', countryPlaceCode1Name)
          ..add('countryPlaceCode2Name', countryPlaceCode2Name)
          ..add('countryPlaceCode3Name', countryPlaceCode3Name)
          ..add('remoteCode', remoteCode)
          ..add('categorySkills', categorySkills)
          ..add('payrollPriceUsd', payrollPriceUsd))
        .toString();
  }
}

class EngHopeBuilder implements Builder<EngHope, EngHopeBuilder> {
  _$EngHope? _$v;

  String? _employCode;
  String? get employCode => _$this._employCode;
  set employCode(String? employCode) => _$this._employCode = employCode;

  String? _placeCode1;
  String? get placeCode1 => _$this._placeCode1;
  set placeCode1(String? placeCode1) => _$this._placeCode1 = placeCode1;

  String? _placeCode2;
  String? get placeCode2 => _$this._placeCode2;
  set placeCode2(String? placeCode2) => _$this._placeCode2 = placeCode2;

  String? _placeCode3;
  String? get placeCode3 => _$this._placeCode3;
  set placeCode3(String? placeCode3) => _$this._placeCode3 = placeCode3;

  String? _payrollPrice;
  String? get payrollPrice => _$this._payrollPrice;
  set payrollPrice(String? payrollPrice) => _$this._payrollPrice = payrollPrice;

  String? _payrollCode;
  String? get payrollCode => _$this._payrollCode;
  set payrollCode(String? payrollCode) => _$this._payrollCode = payrollCode;

  ListBuilder<HopeJobSkill>? _jobSkills;
  ListBuilder<HopeJobSkill> get jobSkills =>
      _$this._jobSkills ??= new ListBuilder<HopeJobSkill>();
  set jobSkills(ListBuilder<HopeJobSkill>? jobSkills) =>
      _$this._jobSkills = jobSkills;

  String? _placeCode1Name;
  String? get placeCode1Name => _$this._placeCode1Name;
  set placeCode1Name(String? placeCode1Name) =>
      _$this._placeCode1Name = placeCode1Name;

  String? _placeCode2Name;
  String? get placeCode2Name => _$this._placeCode2Name;
  set placeCode2Name(String? placeCode2Name) =>
      _$this._placeCode2Name = placeCode2Name;

  String? _placeCode3Name;
  String? get placeCode3Name => _$this._placeCode3Name;
  set placeCode3Name(String? placeCode3Name) =>
      _$this._placeCode3Name = placeCode3Name;

  String? _countryPlaceCode1Name;
  String? get countryPlaceCode1Name => _$this._countryPlaceCode1Name;
  set countryPlaceCode1Name(String? countryPlaceCode1Name) =>
      _$this._countryPlaceCode1Name = countryPlaceCode1Name;

  String? _countryPlaceCode2Name;
  String? get countryPlaceCode2Name => _$this._countryPlaceCode2Name;
  set countryPlaceCode2Name(String? countryPlaceCode2Name) =>
      _$this._countryPlaceCode2Name = countryPlaceCode2Name;

  String? _countryPlaceCode3Name;
  String? get countryPlaceCode3Name => _$this._countryPlaceCode3Name;
  set countryPlaceCode3Name(String? countryPlaceCode3Name) =>
      _$this._countryPlaceCode3Name = countryPlaceCode3Name;

  String? _remoteCode;
  String? get remoteCode => _$this._remoteCode;
  set remoteCode(String? remoteCode) => _$this._remoteCode = remoteCode;

  ListBuilder<HopeCategorySkill>? _categorySkills;
  ListBuilder<HopeCategorySkill> get categorySkills =>
      _$this._categorySkills ??= new ListBuilder<HopeCategorySkill>();
  set categorySkills(ListBuilder<HopeCategorySkill>? categorySkills) =>
      _$this._categorySkills = categorySkills;

  double? _payrollPriceUsd;
  double? get payrollPriceUsd => _$this._payrollPriceUsd;
  set payrollPriceUsd(double? payrollPriceUsd) =>
      _$this._payrollPriceUsd = payrollPriceUsd;

  EngHopeBuilder() {
    EngHope._defaults(this);
  }

  EngHopeBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _employCode = $v.employCode;
      _placeCode1 = $v.placeCode1;
      _placeCode2 = $v.placeCode2;
      _placeCode3 = $v.placeCode3;
      _payrollPrice = $v.payrollPrice;
      _payrollCode = $v.payrollCode;
      _jobSkills = $v.jobSkills?.toBuilder();
      _placeCode1Name = $v.placeCode1Name;
      _placeCode2Name = $v.placeCode2Name;
      _placeCode3Name = $v.placeCode3Name;
      _countryPlaceCode1Name = $v.countryPlaceCode1Name;
      _countryPlaceCode2Name = $v.countryPlaceCode2Name;
      _countryPlaceCode3Name = $v.countryPlaceCode3Name;
      _remoteCode = $v.remoteCode;
      _categorySkills = $v.categorySkills.toBuilder();
      _payrollPriceUsd = $v.payrollPriceUsd;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(EngHope other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$EngHope;
  }

  @override
  void update(void Function(EngHopeBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  EngHope build() => _build();

  _$EngHope _build() {
    _$EngHope _$result;
    try {
      _$result = _$v ??
          new _$EngHope._(
            employCode: employCode,
            placeCode1: placeCode1,
            placeCode2: placeCode2,
            placeCode3: placeCode3,
            payrollPrice: payrollPrice,
            payrollCode: payrollCode,
            jobSkills: _jobSkills?.build(),
            placeCode1Name: placeCode1Name,
            placeCode2Name: placeCode2Name,
            placeCode3Name: placeCode3Name,
            countryPlaceCode1Name: countryPlaceCode1Name,
            countryPlaceCode2Name: countryPlaceCode2Name,
            countryPlaceCode3Name: countryPlaceCode3Name,
            remoteCode: remoteCode,
            categorySkills: categorySkills.build(),
            payrollPriceUsd: payrollPriceUsd,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'jobSkills';
        _jobSkills?.build();

        _$failedField = 'categorySkills';
        categorySkills.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'EngHope', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
