// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'send_code_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$SendCodeRequest extends SendCodeRequest {
  @override
  final String email;
  @override
  final String captchaKey;
  @override
  final String captchaValue;
  @override
  final String? snsEmail;

  factory _$SendCodeRequest([void Function(SendCodeRequestBuilder)? updates]) =>
      (new SendCodeRequestBuilder()..update(updates))._build();

  _$SendCodeRequest._(
      {required this.email,
      required this.captcha<PERSON>ey,
      required this.captchaValue,
      this.snsEmail})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(email, r'SendCodeRequest', 'email');
    BuiltValueNullFieldError.checkNotNull(
        captchaKey, r'SendCodeRequest', 'captchaKey');
    BuiltValueNullFieldError.checkNotNull(
        captchaValue, r'SendCodeRequest', 'captchaValue');
  }

  @override
  SendCodeRequest rebuild(void Function(SendCodeRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SendCodeRequestBuilder toBuilder() =>
      new SendCodeRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SendCodeRequest &&
        email == other.email &&
        captchaKey == other.captchaKey &&
        captchaValue == other.captchaValue &&
        snsEmail == other.snsEmail;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, email.hashCode);
    _$hash = $jc(_$hash, captchaKey.hashCode);
    _$hash = $jc(_$hash, captchaValue.hashCode);
    _$hash = $jc(_$hash, snsEmail.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SendCodeRequest')
          ..add('email', email)
          ..add('captchaKey', captchaKey)
          ..add('captchaValue', captchaValue)
          ..add('snsEmail', snsEmail))
        .toString();
  }
}

class SendCodeRequestBuilder
    implements Builder<SendCodeRequest, SendCodeRequestBuilder> {
  _$SendCodeRequest? _$v;

  String? _email;
  String? get email => _$this._email;
  set email(String? email) => _$this._email = email;

  String? _captchaKey;
  String? get captchaKey => _$this._captchaKey;
  set captchaKey(String? captchaKey) => _$this._captchaKey = captchaKey;

  String? _captchaValue;
  String? get captchaValue => _$this._captchaValue;
  set captchaValue(String? captchaValue) => _$this._captchaValue = captchaValue;

  String? _snsEmail;
  String? get snsEmail => _$this._snsEmail;
  set snsEmail(String? snsEmail) => _$this._snsEmail = snsEmail;

  SendCodeRequestBuilder() {
    SendCodeRequest._defaults(this);
  }

  SendCodeRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _email = $v.email;
      _captchaKey = $v.captchaKey;
      _captchaValue = $v.captchaValue;
      _snsEmail = $v.snsEmail;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SendCodeRequest other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$SendCodeRequest;
  }

  @override
  void update(void Function(SendCodeRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SendCodeRequest build() => _build();

  _$SendCodeRequest _build() {
    final _$result = _$v ??
        new _$SendCodeRequest._(
          email: BuiltValueNullFieldError.checkNotNull(
              email, r'SendCodeRequest', 'email'),
          captchaKey: BuiltValueNullFieldError.checkNotNull(
              captchaKey, r'SendCodeRequest', 'captchaKey'),
          captchaValue: BuiltValueNullFieldError.checkNotNull(
              captchaValue, r'SendCodeRequest', 'captchaValue'),
          snsEmail: snsEmail,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
