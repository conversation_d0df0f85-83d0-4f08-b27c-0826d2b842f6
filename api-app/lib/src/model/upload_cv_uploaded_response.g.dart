// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'upload_cv_uploaded_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UploadCVUploadedResponse extends UploadCVUploadedResponse {
  @override
  final String? message;
  @override
  final BuiltList<ErrorDetail>? errors;
  @override
  final UploadCVUploaded? data;

  factory _$UploadCVUploadedResponse(
          [void Function(UploadCVUploadedResponseBuilder)? updates]) =>
      (new UploadCVUploadedResponseBuilder()..update(updates))._build();

  _$UploadCVUploadedResponse._({this.message, this.errors, this.data})
      : super._();

  @override
  UploadCVUploadedResponse rebuild(
          void Function(UploadCVUploadedResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UploadCVUploadedResponseBuilder toBuilder() =>
      new UploadCVUploadedResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UploadCVUploadedResponse &&
        message == other.message &&
        errors == other.errors &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UploadCVUploadedResponse')
          ..add('message', message)
          ..add('errors', errors)
          ..add('data', data))
        .toString();
  }
}

class UploadCVUploadedResponseBuilder
    implements
        Builder<UploadCVUploadedResponse, UploadCVUploadedResponseBuilder> {
  _$UploadCVUploadedResponse? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  UploadCVUploadedBuilder? _data;
  UploadCVUploadedBuilder get data =>
      _$this._data ??= new UploadCVUploadedBuilder();
  set data(UploadCVUploadedBuilder? data) => _$this._data = data;

  UploadCVUploadedResponseBuilder() {
    UploadCVUploadedResponse._defaults(this);
  }

  UploadCVUploadedResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _errors = $v.errors?.toBuilder();
      _data = $v.data?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UploadCVUploadedResponse other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$UploadCVUploadedResponse;
  }

  @override
  void update(void Function(UploadCVUploadedResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UploadCVUploadedResponse build() => _build();

  _$UploadCVUploadedResponse _build() {
    _$UploadCVUploadedResponse _$result;
    try {
      _$result = _$v ??
          new _$UploadCVUploadedResponse._(
            message: message,
            errors: _errors?.build(),
            data: _data?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'errors';
        _errors?.build();
        _$failedField = 'data';
        _data?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'UploadCVUploadedResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
