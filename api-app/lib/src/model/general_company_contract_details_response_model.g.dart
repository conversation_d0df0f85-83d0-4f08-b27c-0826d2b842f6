// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_company_contract_details_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GeneralCompanyContractDetailsResponseModel
    extends GeneralCompanyContractDetailsResponseModel {
  @override
  final String? message;
  @override
  final BuiltList<ErrorDetail>? errors;
  @override
  final GeneralCompanyGetContractDetails data;

  factory _$GeneralCompanyContractDetailsResponseModel(
          [void Function(GeneralCompanyContractDetailsResponseModelBuilder)?
              updates]) =>
      (new GeneralCompanyContractDetailsResponseModelBuilder()..update(updates))
          ._build();

  _$GeneralCompanyContractDetailsResponseModel._(
      {this.message, this.errors, required this.data})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'GeneralCompanyContractDetailsResponseModel', 'data');
  }

  @override
  GeneralCompanyContractDetailsResponseModel rebuild(
          void Function(GeneralCompanyContractDetailsResponseModelBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GeneralCompanyContractDetailsResponseModelBuilder toBuilder() =>
      new GeneralCompanyContractDetailsResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GeneralCompanyContractDetailsResponseModel &&
        message == other.message &&
        errors == other.errors &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'GeneralCompanyContractDetailsResponseModel')
          ..add('message', message)
          ..add('errors', errors)
          ..add('data', data))
        .toString();
  }
}

class GeneralCompanyContractDetailsResponseModelBuilder
    implements
        Builder<GeneralCompanyContractDetailsResponseModel,
            GeneralCompanyContractDetailsResponseModelBuilder> {
  _$GeneralCompanyContractDetailsResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  GeneralCompanyGetContractDetailsBuilder? _data;
  GeneralCompanyGetContractDetailsBuilder get data =>
      _$this._data ??= new GeneralCompanyGetContractDetailsBuilder();
  set data(GeneralCompanyGetContractDetailsBuilder? data) =>
      _$this._data = data;

  GeneralCompanyContractDetailsResponseModelBuilder() {
    GeneralCompanyContractDetailsResponseModel._defaults(this);
  }

  GeneralCompanyContractDetailsResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _errors = $v.errors?.toBuilder();
      _data = $v.data.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GeneralCompanyContractDetailsResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GeneralCompanyContractDetailsResponseModel;
  }

  @override
  void update(
      void Function(GeneralCompanyContractDetailsResponseModelBuilder)?
          updates) {
    if (updates != null) updates(this);
  }

  @override
  GeneralCompanyContractDetailsResponseModel build() => _build();

  _$GeneralCompanyContractDetailsResponseModel _build() {
    _$GeneralCompanyContractDetailsResponseModel _$result;
    try {
      _$result = _$v ??
          new _$GeneralCompanyContractDetailsResponseModel._(
            message: message,
            errors: _errors?.build(),
            data: data.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'errors';
        _errors?.build();
        _$failedField = 'data';
        data.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'GeneralCompanyContractDetailsResponseModel',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
