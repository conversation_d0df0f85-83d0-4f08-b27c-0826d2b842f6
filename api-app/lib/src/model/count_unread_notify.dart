//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'count_unread_notify.g.dart';

/// CountUnreadNotify
///
/// Properties:
/// * [totalNotify] 
/// * [totalUnreadNotify] 
@BuiltValue()
abstract class CountUnreadNotify implements Built<CountUnreadNotify, CountUnreadNotifyBuilder> {
  @BuiltValueField(wireName: r'total_notify')
  int get totalNotify;

  @BuiltValueField(wireName: r'total_unread_notify')
  int get totalUnreadNotify;

  CountUnreadNotify._();

  factory CountUnreadNotify([void updates(CountUnreadNotifyBuilder b)]) = _$CountUnreadNotify;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(CountUnreadNotifyBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<CountUnreadNotify> get serializer => _$CountUnreadNotifySerializer();
}

class _$CountUnreadNotifySerializer implements PrimitiveSerializer<CountUnreadNotify> {
  @override
  final Iterable<Type> types = const [CountUnreadNotify, _$CountUnreadNotify];

  @override
  final String wireName = r'CountUnreadNotify';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    CountUnreadNotify object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'total_notify';
    yield serializers.serialize(
      object.totalNotify,
      specifiedType: const FullType(int),
    );
    yield r'total_unread_notify';
    yield serializers.serialize(
      object.totalUnreadNotify,
      specifiedType: const FullType(int),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    CountUnreadNotify object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required CountUnreadNotifyBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'total_notify':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.totalNotify = valueDes;
          break;
        case r'total_unread_notify':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.totalUnreadNotify = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  CountUnreadNotify deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = CountUnreadNotifyBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

