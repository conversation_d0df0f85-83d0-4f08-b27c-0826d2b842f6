//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/recruit_explore.dart';
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'paging_response_model.g.dart';

/// PagingResponseModel
///
/// Properties:
/// * [next] 
/// * [previous] 
/// * [results] 
@BuiltValue()
abstract class PagingResponseModel implements Built<PagingResponseModel, PagingResponseModelBuilder> {
  @BuiltValueField(wireName: r'next')
  String? get next;

  @BuiltValueField(wireName: r'previous')
  String? get previous;

  @BuiltValueField(wireName: r'results')
  BuiltList<RecruitExplore> get results;

  PagingResponseModel._();

  factory PagingResponseModel([void updates(PagingResponseModelBuilder b)]) = _$PagingResponseModel;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(PagingResponseModelBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<PagingResponseModel> get serializer => _$PagingResponseModelSerializer();
}

class _$PagingResponseModelSerializer implements PrimitiveSerializer<PagingResponseModel> {
  @override
  final Iterable<Type> types = const [PagingResponseModel, _$PagingResponseModel];

  @override
  final String wireName = r'PagingResponseModel';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    PagingResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'next';
    yield object.next == null ? null : serializers.serialize(
      object.next,
      specifiedType: const FullType.nullable(String),
    );
    yield r'previous';
    yield object.previous == null ? null : serializers.serialize(
      object.previous,
      specifiedType: const FullType.nullable(String),
    );
    yield r'results';
    yield serializers.serialize(
      object.results,
      specifiedType: const FullType(BuiltList, [FullType(RecruitExplore)]),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    PagingResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required PagingResponseModelBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'next':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.next = valueDes;
          break;
        case r'previous':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.previous = valueDes;
          break;
        case r'results':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(RecruitExplore)]),
          ) as BuiltList<RecruitExplore>;
          result.results.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  PagingResponseModel deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = PagingResponseModelBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

