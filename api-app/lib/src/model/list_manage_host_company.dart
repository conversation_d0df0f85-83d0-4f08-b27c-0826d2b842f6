//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/manage_host_company.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'list_manage_host_company.g.dart';

/// ListManageHostCompany
///
/// Properties:
/// * [next] 
/// * [previous] 
/// * [results] 
@BuiltValue()
abstract class ListManageHostCompany implements Built<ListManageHostCompany, ListManageHostCompanyBuilder> {
  @BuiltValueField(wireName: r'next')
  String? get next;

  @BuiltValueField(wireName: r'previous')
  String? get previous;

  @BuiltValueField(wireName: r'results')
  BuiltList<ManageHostCompany> get results;

  ListManageHostCompany._();

  factory ListManageHostCompany([void updates(ListManageHostCompanyBuilder b)]) = _$ListManageHostCompany;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(ListManageHostCompanyBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<ListManageHostCompany> get serializer => _$ListManageHostCompanySerializer();
}

class _$ListManageHostCompanySerializer implements PrimitiveSerializer<ListManageHostCompany> {
  @override
  final Iterable<Type> types = const [ListManageHostCompany, _$ListManageHostCompany];

  @override
  final String wireName = r'ListManageHostCompany';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    ListManageHostCompany object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'next';
    yield object.next == null ? null : serializers.serialize(
      object.next,
      specifiedType: const FullType.nullable(String),
    );
    yield r'previous';
    yield object.previous == null ? null : serializers.serialize(
      object.previous,
      specifiedType: const FullType.nullable(String),
    );
    yield r'results';
    yield serializers.serialize(
      object.results,
      specifiedType: const FullType(BuiltList, [FullType(ManageHostCompany)]),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    ListManageHostCompany object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required ListManageHostCompanyBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'next':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.next = valueDes;
          break;
        case r'previous':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.previous = valueDes;
          break;
        case r'results':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(ManageHostCompany)]),
          ) as BuiltList<ManageHostCompany>;
          result.results.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  ListManageHostCompany deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = ListManageHostCompanyBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

