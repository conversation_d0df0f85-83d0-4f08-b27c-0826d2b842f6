// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bool_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BoolResponseModel extends BoolResponseModel {
  @override
  final String? message;
  @override
  final bool data;
  @override
  final BuiltList<ErrorDetail>? errors;

  factory _$BoolResponseModel(
          [void Function(BoolResponseModelBuilder)? updates]) =>
      (new BoolResponseModelBuilder()..update(updates))._build();

  _$Bool<PERSON>esponseModel._({this.message, required this.data, this.errors})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(data, r'BoolResponseModel', 'data');
  }

  @override
  BoolResponseModel rebuild(void Function(BoolResponseModelBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BoolResponseModelBuilder toBuilder() =>
      new BoolResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BoolResponseModel &&
        message == other.message &&
        data == other.data &&
        errors == other.errors;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'BoolResponseModel')
          ..add('message', message)
          ..add('data', data)
          ..add('errors', errors))
        .toString();
  }
}

class BoolResponseModelBuilder
    implements Builder<BoolResponseModel, BoolResponseModelBuilder> {
  _$BoolResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  bool? _data;
  bool? get data => _$this._data;
  set data(bool? data) => _$this._data = data;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  BoolResponseModelBuilder() {
    BoolResponseModel._defaults(this);
  }

  BoolResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _data = $v.data;
      _errors = $v.errors?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BoolResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$BoolResponseModel;
  }

  @override
  void update(void Function(BoolResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  BoolResponseModel build() => _build();

  _$BoolResponseModel _build() {
    _$BoolResponseModel _$result;
    try {
      _$result = _$v ??
          new _$BoolResponseModel._(
            message: message,
            data: BuiltValueNullFieldError.checkNotNull(
                data, r'BoolResponseModel', 'data'),
            errors: _errors?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'errors';
        _errors?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'BoolResponseModel', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
