// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_company_pr_comment.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GeneralCompanyPRComment extends GeneralCompanyPRComment {
  @override
  final String? introductionPr;
  @override
  final DateTime? createDatetime;
  @override
  final int? mapId;
  @override
  final int agencyCompany;
  @override
  final int? agencyAgent;
  @override
  final DateTime? updateDatetime;

  factory _$GeneralCompanyPRComment(
          [void Function(GeneralCompanyPRCommentBuilder)? updates]) =>
      (new GeneralCompanyPRCommentBuilder()..update(updates))._build();

  _$GeneralCompanyPRComment._(
      {this.introductionPr,
      this.createDatetime,
      this.mapId,
      required this.agencyCompany,
      this.agencyAgent,
      this.updateDatetime})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        agencyCompany, r'GeneralCompanyPRComment', 'agencyCompany');
  }

  @override
  GeneralCompanyPRComment rebuild(
          void Function(GeneralCompanyPRCommentBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GeneralCompanyPRCommentBuilder toBuilder() =>
      new GeneralCompanyPRCommentBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GeneralCompanyPRComment &&
        introductionPr == other.introductionPr &&
        createDatetime == other.createDatetime &&
        mapId == other.mapId &&
        agencyCompany == other.agencyCompany &&
        agencyAgent == other.agencyAgent &&
        updateDatetime == other.updateDatetime;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, introductionPr.hashCode);
    _$hash = $jc(_$hash, createDatetime.hashCode);
    _$hash = $jc(_$hash, mapId.hashCode);
    _$hash = $jc(_$hash, agencyCompany.hashCode);
    _$hash = $jc(_$hash, agencyAgent.hashCode);
    _$hash = $jc(_$hash, updateDatetime.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GeneralCompanyPRComment')
          ..add('introductionPr', introductionPr)
          ..add('createDatetime', createDatetime)
          ..add('mapId', mapId)
          ..add('agencyCompany', agencyCompany)
          ..add('agencyAgent', agencyAgent)
          ..add('updateDatetime', updateDatetime))
        .toString();
  }
}

class GeneralCompanyPRCommentBuilder
    implements
        Builder<GeneralCompanyPRComment, GeneralCompanyPRCommentBuilder> {
  _$GeneralCompanyPRComment? _$v;

  String? _introductionPr;
  String? get introductionPr => _$this._introductionPr;
  set introductionPr(String? introductionPr) =>
      _$this._introductionPr = introductionPr;

  DateTime? _createDatetime;
  DateTime? get createDatetime => _$this._createDatetime;
  set createDatetime(DateTime? createDatetime) =>
      _$this._createDatetime = createDatetime;

  int? _mapId;
  int? get mapId => _$this._mapId;
  set mapId(int? mapId) => _$this._mapId = mapId;

  int? _agencyCompany;
  int? get agencyCompany => _$this._agencyCompany;
  set agencyCompany(int? agencyCompany) =>
      _$this._agencyCompany = agencyCompany;

  int? _agencyAgent;
  int? get agencyAgent => _$this._agencyAgent;
  set agencyAgent(int? agencyAgent) => _$this._agencyAgent = agencyAgent;

  DateTime? _updateDatetime;
  DateTime? get updateDatetime => _$this._updateDatetime;
  set updateDatetime(DateTime? updateDatetime) =>
      _$this._updateDatetime = updateDatetime;

  GeneralCompanyPRCommentBuilder() {
    GeneralCompanyPRComment._defaults(this);
  }

  GeneralCompanyPRCommentBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _introductionPr = $v.introductionPr;
      _createDatetime = $v.createDatetime;
      _mapId = $v.mapId;
      _agencyCompany = $v.agencyCompany;
      _agencyAgent = $v.agencyAgent;
      _updateDatetime = $v.updateDatetime;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GeneralCompanyPRComment other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GeneralCompanyPRComment;
  }

  @override
  void update(void Function(GeneralCompanyPRCommentBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GeneralCompanyPRComment build() => _build();

  _$GeneralCompanyPRComment _build() {
    final _$result = _$v ??
        new _$GeneralCompanyPRComment._(
          introductionPr: introductionPr,
          createDatetime: createDatetime,
          mapId: mapId,
          agencyCompany: BuiltValueNullFieldError.checkNotNull(
              agencyCompany, r'GeneralCompanyPRComment', 'agencyCompany'),
          agencyAgent: agencyAgent,
          updateDatetime: updateDatetime,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
