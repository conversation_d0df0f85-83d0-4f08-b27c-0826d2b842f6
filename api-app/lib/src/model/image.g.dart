// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'image.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$Image extends Image {
  @override
  final String? title;
  @override
  final String? image;
  @override
  final DateTime? uploadedAt;
  @override
  final int? user;
  @override
  final int? type;
  @override
  final String? mainFolderPath;

  factory _$Image([void Function(ImageBuilder)? updates]) =>
      (new ImageBuilder()..update(updates))._build();

  _$Image._(
      {this.title,
      this.image,
      this.uploadedAt,
      this.user,
      this.type,
      this.mainFolderPath})
      : super._();

  @override
  Image rebuild(void Function(ImageBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ImageBuilder toBuilder() => new ImageBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is Image &&
        title == other.title &&
        image == other.image &&
        uploadedAt == other.uploadedAt &&
        user == other.user &&
        type == other.type &&
        mainFolderPath == other.mainFolderPath;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, title.hashCode);
    _$hash = $jc(_$hash, image.hashCode);
    _$hash = $jc(_$hash, uploadedAt.hashCode);
    _$hash = $jc(_$hash, user.hashCode);
    _$hash = $jc(_$hash, type.hashCode);
    _$hash = $jc(_$hash, mainFolderPath.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'Image')
          ..add('title', title)
          ..add('image', image)
          ..add('uploadedAt', uploadedAt)
          ..add('user', user)
          ..add('type', type)
          ..add('mainFolderPath', mainFolderPath))
        .toString();
  }
}

class ImageBuilder implements Builder<Image, ImageBuilder> {
  _$Image? _$v;

  String? _title;
  String? get title => _$this._title;
  set title(String? title) => _$this._title = title;

  String? _image;
  String? get image => _$this._image;
  set image(String? image) => _$this._image = image;

  DateTime? _uploadedAt;
  DateTime? get uploadedAt => _$this._uploadedAt;
  set uploadedAt(DateTime? uploadedAt) => _$this._uploadedAt = uploadedAt;

  int? _user;
  int? get user => _$this._user;
  set user(int? user) => _$this._user = user;

  int? _type;
  int? get type => _$this._type;
  set type(int? type) => _$this._type = type;

  String? _mainFolderPath;
  String? get mainFolderPath => _$this._mainFolderPath;
  set mainFolderPath(String? mainFolderPath) =>
      _$this._mainFolderPath = mainFolderPath;

  ImageBuilder() {
    Image._defaults(this);
  }

  ImageBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _title = $v.title;
      _image = $v.image;
      _uploadedAt = $v.uploadedAt;
      _user = $v.user;
      _type = $v.type;
      _mainFolderPath = $v.mainFolderPath;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(Image other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$Image;
  }

  @override
  void update(void Function(ImageBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  Image build() => _build();

  _$Image _build() {
    final _$result = _$v ??
        new _$Image._(
          title: title,
          image: image,
          uploadedAt: uploadedAt,
          user: user,
          type: type,
          mainFolderPath: mainFolderPath,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
