// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'assessment_answer.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AssessmentAnswer extends AssessmentAnswer {
  @override
  final String uuid;
  @override
  final String? text;
  @override
  final String textEn;
  @override
  final String textVi;
  @override
  final bool? isSelected;
  @override
  final String? description;

  factory _$AssessmentAnswer(
          [void Function(AssessmentAnswerBuilder)? updates]) =>
      (new AssessmentAnswerBuilder()..update(updates))._build();

  _$AssessmentAnswer._(
      {required this.uuid,
      this.text,
      required this.textEn,
      required this.textVi,
      this.isSelected,
      this.description})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(uuid, r'AssessmentAnswer', 'uuid');
    BuiltValueNullFieldError.checkNotNull(
        textEn, r'AssessmentAnswer', 'textEn');
    BuiltValueNullFieldError.checkNotNull(
        textVi, r'AssessmentAnswer', 'textVi');
  }

  @override
  AssessmentAnswer rebuild(void Function(AssessmentAnswerBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AssessmentAnswerBuilder toBuilder() =>
      new AssessmentAnswerBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AssessmentAnswer &&
        uuid == other.uuid &&
        text == other.text &&
        textEn == other.textEn &&
        textVi == other.textVi &&
        isSelected == other.isSelected &&
        description == other.description;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, uuid.hashCode);
    _$hash = $jc(_$hash, text.hashCode);
    _$hash = $jc(_$hash, textEn.hashCode);
    _$hash = $jc(_$hash, textVi.hashCode);
    _$hash = $jc(_$hash, isSelected.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AssessmentAnswer')
          ..add('uuid', uuid)
          ..add('text', text)
          ..add('textEn', textEn)
          ..add('textVi', textVi)
          ..add('isSelected', isSelected)
          ..add('description', description))
        .toString();
  }
}

class AssessmentAnswerBuilder
    implements Builder<AssessmentAnswer, AssessmentAnswerBuilder> {
  _$AssessmentAnswer? _$v;

  String? _uuid;
  String? get uuid => _$this._uuid;
  set uuid(String? uuid) => _$this._uuid = uuid;

  String? _text;
  String? get text => _$this._text;
  set text(String? text) => _$this._text = text;

  String? _textEn;
  String? get textEn => _$this._textEn;
  set textEn(String? textEn) => _$this._textEn = textEn;

  String? _textVi;
  String? get textVi => _$this._textVi;
  set textVi(String? textVi) => _$this._textVi = textVi;

  bool? _isSelected;
  bool? get isSelected => _$this._isSelected;
  set isSelected(bool? isSelected) => _$this._isSelected = isSelected;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  AssessmentAnswerBuilder() {
    AssessmentAnswer._defaults(this);
  }

  AssessmentAnswerBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _uuid = $v.uuid;
      _text = $v.text;
      _textEn = $v.textEn;
      _textVi = $v.textVi;
      _isSelected = $v.isSelected;
      _description = $v.description;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AssessmentAnswer other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$AssessmentAnswer;
  }

  @override
  void update(void Function(AssessmentAnswerBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AssessmentAnswer build() => _build();

  _$AssessmentAnswer _build() {
    final _$result = _$v ??
        new _$AssessmentAnswer._(
          uuid: BuiltValueNullFieldError.checkNotNull(
              uuid, r'AssessmentAnswer', 'uuid'),
          text: text,
          textEn: BuiltValueNullFieldError.checkNotNull(
              textEn, r'AssessmentAnswer', 'textEn'),
          textVi: BuiltValueNullFieldError.checkNotNull(
              textVi, r'AssessmentAnswer', 'textVi'),
          isSelected: isSelected,
          description: description,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
