// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_company_details.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GeneralCompanyDetails extends GeneralCompanyDetails {
  @override
  final int? companyId;
  @override
  final String? capitalStock;
  @override
  final String? acceptingFee;
  @override
  final String? supportOutsourcingFee;
  @override
  final int userType;
  @override
  final String? name;
  @override
  final String? aboutUs;
  @override
  final String? businessDetails;
  @override
  final int? employeesType;
  @override
  final String? countryCode;
  @override
  final String? addressCode;
  @override
  final String? address;
  @override
  final String? tel;
  @override
  final String? logoImagePath;
  @override
  final String? prImagePath1;
  @override
  final String? prImagePath2;
  @override
  final String? prImagePath3;
  @override
  final String? contactMail;
  @override
  final String? webUrl;
  @override
  final String? introductionUrl;
  @override
  final String? memo;
  @override
  final DateTime? created;
  @override
  final DateTime? updated;
  @override
  final String? benefits;
  @override
  final String? capitalStockCurrCode;
  @override
  final String? internationalTel;
  @override
  final double? agentFee;
  @override
  final String? agentFeeCurrCode;
  @override
  final String? acceptingFeeCurrCode;
  @override
  final String? supportOutsourcingFeeCurrCode;
  @override
  final String? support;
  @override
  final int? status;
  @override
  final String? workingHoursFrom;
  @override
  final String? workingHoursTo;

  factory _$GeneralCompanyDetails(
          [void Function(GeneralCompanyDetailsBuilder)? updates]) =>
      (new GeneralCompanyDetailsBuilder()..update(updates))._build();

  _$GeneralCompanyDetails._(
      {this.companyId,
      this.capitalStock,
      this.acceptingFee,
      this.supportOutsourcingFee,
      required this.userType,
      this.name,
      this.aboutUs,
      this.businessDetails,
      this.employeesType,
      this.countryCode,
      this.addressCode,
      this.address,
      this.tel,
      this.logoImagePath,
      this.prImagePath1,
      this.prImagePath2,
      this.prImagePath3,
      this.contactMail,
      this.webUrl,
      this.introductionUrl,
      this.memo,
      this.created,
      this.updated,
      this.benefits,
      this.capitalStockCurrCode,
      this.internationalTel,
      this.agentFee,
      this.agentFeeCurrCode,
      this.acceptingFeeCurrCode,
      this.supportOutsourcingFeeCurrCode,
      this.support,
      this.status,
      this.workingHoursFrom,
      this.workingHoursTo})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        userType, r'GeneralCompanyDetails', 'userType');
  }

  @override
  GeneralCompanyDetails rebuild(
          void Function(GeneralCompanyDetailsBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GeneralCompanyDetailsBuilder toBuilder() =>
      new GeneralCompanyDetailsBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GeneralCompanyDetails &&
        companyId == other.companyId &&
        capitalStock == other.capitalStock &&
        acceptingFee == other.acceptingFee &&
        supportOutsourcingFee == other.supportOutsourcingFee &&
        userType == other.userType &&
        name == other.name &&
        aboutUs == other.aboutUs &&
        businessDetails == other.businessDetails &&
        employeesType == other.employeesType &&
        countryCode == other.countryCode &&
        addressCode == other.addressCode &&
        address == other.address &&
        tel == other.tel &&
        logoImagePath == other.logoImagePath &&
        prImagePath1 == other.prImagePath1 &&
        prImagePath2 == other.prImagePath2 &&
        prImagePath3 == other.prImagePath3 &&
        contactMail == other.contactMail &&
        webUrl == other.webUrl &&
        introductionUrl == other.introductionUrl &&
        memo == other.memo &&
        created == other.created &&
        updated == other.updated &&
        benefits == other.benefits &&
        capitalStockCurrCode == other.capitalStockCurrCode &&
        internationalTel == other.internationalTel &&
        agentFee == other.agentFee &&
        agentFeeCurrCode == other.agentFeeCurrCode &&
        acceptingFeeCurrCode == other.acceptingFeeCurrCode &&
        supportOutsourcingFeeCurrCode == other.supportOutsourcingFeeCurrCode &&
        support == other.support &&
        status == other.status &&
        workingHoursFrom == other.workingHoursFrom &&
        workingHoursTo == other.workingHoursTo;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, companyId.hashCode);
    _$hash = $jc(_$hash, capitalStock.hashCode);
    _$hash = $jc(_$hash, acceptingFee.hashCode);
    _$hash = $jc(_$hash, supportOutsourcingFee.hashCode);
    _$hash = $jc(_$hash, userType.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, aboutUs.hashCode);
    _$hash = $jc(_$hash, businessDetails.hashCode);
    _$hash = $jc(_$hash, employeesType.hashCode);
    _$hash = $jc(_$hash, countryCode.hashCode);
    _$hash = $jc(_$hash, addressCode.hashCode);
    _$hash = $jc(_$hash, address.hashCode);
    _$hash = $jc(_$hash, tel.hashCode);
    _$hash = $jc(_$hash, logoImagePath.hashCode);
    _$hash = $jc(_$hash, prImagePath1.hashCode);
    _$hash = $jc(_$hash, prImagePath2.hashCode);
    _$hash = $jc(_$hash, prImagePath3.hashCode);
    _$hash = $jc(_$hash, contactMail.hashCode);
    _$hash = $jc(_$hash, webUrl.hashCode);
    _$hash = $jc(_$hash, introductionUrl.hashCode);
    _$hash = $jc(_$hash, memo.hashCode);
    _$hash = $jc(_$hash, created.hashCode);
    _$hash = $jc(_$hash, updated.hashCode);
    _$hash = $jc(_$hash, benefits.hashCode);
    _$hash = $jc(_$hash, capitalStockCurrCode.hashCode);
    _$hash = $jc(_$hash, internationalTel.hashCode);
    _$hash = $jc(_$hash, agentFee.hashCode);
    _$hash = $jc(_$hash, agentFeeCurrCode.hashCode);
    _$hash = $jc(_$hash, acceptingFeeCurrCode.hashCode);
    _$hash = $jc(_$hash, supportOutsourcingFeeCurrCode.hashCode);
    _$hash = $jc(_$hash, support.hashCode);
    _$hash = $jc(_$hash, status.hashCode);
    _$hash = $jc(_$hash, workingHoursFrom.hashCode);
    _$hash = $jc(_$hash, workingHoursTo.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GeneralCompanyDetails')
          ..add('companyId', companyId)
          ..add('capitalStock', capitalStock)
          ..add('acceptingFee', acceptingFee)
          ..add('supportOutsourcingFee', supportOutsourcingFee)
          ..add('userType', userType)
          ..add('name', name)
          ..add('aboutUs', aboutUs)
          ..add('businessDetails', businessDetails)
          ..add('employeesType', employeesType)
          ..add('countryCode', countryCode)
          ..add('addressCode', addressCode)
          ..add('address', address)
          ..add('tel', tel)
          ..add('logoImagePath', logoImagePath)
          ..add('prImagePath1', prImagePath1)
          ..add('prImagePath2', prImagePath2)
          ..add('prImagePath3', prImagePath3)
          ..add('contactMail', contactMail)
          ..add('webUrl', webUrl)
          ..add('introductionUrl', introductionUrl)
          ..add('memo', memo)
          ..add('created', created)
          ..add('updated', updated)
          ..add('benefits', benefits)
          ..add('capitalStockCurrCode', capitalStockCurrCode)
          ..add('internationalTel', internationalTel)
          ..add('agentFee', agentFee)
          ..add('agentFeeCurrCode', agentFeeCurrCode)
          ..add('acceptingFeeCurrCode', acceptingFeeCurrCode)
          ..add('supportOutsourcingFeeCurrCode', supportOutsourcingFeeCurrCode)
          ..add('support', support)
          ..add('status', status)
          ..add('workingHoursFrom', workingHoursFrom)
          ..add('workingHoursTo', workingHoursTo))
        .toString();
  }
}

class GeneralCompanyDetailsBuilder
    implements Builder<GeneralCompanyDetails, GeneralCompanyDetailsBuilder> {
  _$GeneralCompanyDetails? _$v;

  int? _companyId;
  int? get companyId => _$this._companyId;
  set companyId(int? companyId) => _$this._companyId = companyId;

  String? _capitalStock;
  String? get capitalStock => _$this._capitalStock;
  set capitalStock(String? capitalStock) => _$this._capitalStock = capitalStock;

  String? _acceptingFee;
  String? get acceptingFee => _$this._acceptingFee;
  set acceptingFee(String? acceptingFee) => _$this._acceptingFee = acceptingFee;

  String? _supportOutsourcingFee;
  String? get supportOutsourcingFee => _$this._supportOutsourcingFee;
  set supportOutsourcingFee(String? supportOutsourcingFee) =>
      _$this._supportOutsourcingFee = supportOutsourcingFee;

  int? _userType;
  int? get userType => _$this._userType;
  set userType(int? userType) => _$this._userType = userType;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _aboutUs;
  String? get aboutUs => _$this._aboutUs;
  set aboutUs(String? aboutUs) => _$this._aboutUs = aboutUs;

  String? _businessDetails;
  String? get businessDetails => _$this._businessDetails;
  set businessDetails(String? businessDetails) =>
      _$this._businessDetails = businessDetails;

  int? _employeesType;
  int? get employeesType => _$this._employeesType;
  set employeesType(int? employeesType) =>
      _$this._employeesType = employeesType;

  String? _countryCode;
  String? get countryCode => _$this._countryCode;
  set countryCode(String? countryCode) => _$this._countryCode = countryCode;

  String? _addressCode;
  String? get addressCode => _$this._addressCode;
  set addressCode(String? addressCode) => _$this._addressCode = addressCode;

  String? _address;
  String? get address => _$this._address;
  set address(String? address) => _$this._address = address;

  String? _tel;
  String? get tel => _$this._tel;
  set tel(String? tel) => _$this._tel = tel;

  String? _logoImagePath;
  String? get logoImagePath => _$this._logoImagePath;
  set logoImagePath(String? logoImagePath) =>
      _$this._logoImagePath = logoImagePath;

  String? _prImagePath1;
  String? get prImagePath1 => _$this._prImagePath1;
  set prImagePath1(String? prImagePath1) => _$this._prImagePath1 = prImagePath1;

  String? _prImagePath2;
  String? get prImagePath2 => _$this._prImagePath2;
  set prImagePath2(String? prImagePath2) => _$this._prImagePath2 = prImagePath2;

  String? _prImagePath3;
  String? get prImagePath3 => _$this._prImagePath3;
  set prImagePath3(String? prImagePath3) => _$this._prImagePath3 = prImagePath3;

  String? _contactMail;
  String? get contactMail => _$this._contactMail;
  set contactMail(String? contactMail) => _$this._contactMail = contactMail;

  String? _webUrl;
  String? get webUrl => _$this._webUrl;
  set webUrl(String? webUrl) => _$this._webUrl = webUrl;

  String? _introductionUrl;
  String? get introductionUrl => _$this._introductionUrl;
  set introductionUrl(String? introductionUrl) =>
      _$this._introductionUrl = introductionUrl;

  String? _memo;
  String? get memo => _$this._memo;
  set memo(String? memo) => _$this._memo = memo;

  DateTime? _created;
  DateTime? get created => _$this._created;
  set created(DateTime? created) => _$this._created = created;

  DateTime? _updated;
  DateTime? get updated => _$this._updated;
  set updated(DateTime? updated) => _$this._updated = updated;

  String? _benefits;
  String? get benefits => _$this._benefits;
  set benefits(String? benefits) => _$this._benefits = benefits;

  String? _capitalStockCurrCode;
  String? get capitalStockCurrCode => _$this._capitalStockCurrCode;
  set capitalStockCurrCode(String? capitalStockCurrCode) =>
      _$this._capitalStockCurrCode = capitalStockCurrCode;

  String? _internationalTel;
  String? get internationalTel => _$this._internationalTel;
  set internationalTel(String? internationalTel) =>
      _$this._internationalTel = internationalTel;

  double? _agentFee;
  double? get agentFee => _$this._agentFee;
  set agentFee(double? agentFee) => _$this._agentFee = agentFee;

  String? _agentFeeCurrCode;
  String? get agentFeeCurrCode => _$this._agentFeeCurrCode;
  set agentFeeCurrCode(String? agentFeeCurrCode) =>
      _$this._agentFeeCurrCode = agentFeeCurrCode;

  String? _acceptingFeeCurrCode;
  String? get acceptingFeeCurrCode => _$this._acceptingFeeCurrCode;
  set acceptingFeeCurrCode(String? acceptingFeeCurrCode) =>
      _$this._acceptingFeeCurrCode = acceptingFeeCurrCode;

  String? _supportOutsourcingFeeCurrCode;
  String? get supportOutsourcingFeeCurrCode =>
      _$this._supportOutsourcingFeeCurrCode;
  set supportOutsourcingFeeCurrCode(String? supportOutsourcingFeeCurrCode) =>
      _$this._supportOutsourcingFeeCurrCode = supportOutsourcingFeeCurrCode;

  String? _support;
  String? get support => _$this._support;
  set support(String? support) => _$this._support = support;

  int? _status;
  int? get status => _$this._status;
  set status(int? status) => _$this._status = status;

  String? _workingHoursFrom;
  String? get workingHoursFrom => _$this._workingHoursFrom;
  set workingHoursFrom(String? workingHoursFrom) =>
      _$this._workingHoursFrom = workingHoursFrom;

  String? _workingHoursTo;
  String? get workingHoursTo => _$this._workingHoursTo;
  set workingHoursTo(String? workingHoursTo) =>
      _$this._workingHoursTo = workingHoursTo;

  GeneralCompanyDetailsBuilder() {
    GeneralCompanyDetails._defaults(this);
  }

  GeneralCompanyDetailsBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _companyId = $v.companyId;
      _capitalStock = $v.capitalStock;
      _acceptingFee = $v.acceptingFee;
      _supportOutsourcingFee = $v.supportOutsourcingFee;
      _userType = $v.userType;
      _name = $v.name;
      _aboutUs = $v.aboutUs;
      _businessDetails = $v.businessDetails;
      _employeesType = $v.employeesType;
      _countryCode = $v.countryCode;
      _addressCode = $v.addressCode;
      _address = $v.address;
      _tel = $v.tel;
      _logoImagePath = $v.logoImagePath;
      _prImagePath1 = $v.prImagePath1;
      _prImagePath2 = $v.prImagePath2;
      _prImagePath3 = $v.prImagePath3;
      _contactMail = $v.contactMail;
      _webUrl = $v.webUrl;
      _introductionUrl = $v.introductionUrl;
      _memo = $v.memo;
      _created = $v.created;
      _updated = $v.updated;
      _benefits = $v.benefits;
      _capitalStockCurrCode = $v.capitalStockCurrCode;
      _internationalTel = $v.internationalTel;
      _agentFee = $v.agentFee;
      _agentFeeCurrCode = $v.agentFeeCurrCode;
      _acceptingFeeCurrCode = $v.acceptingFeeCurrCode;
      _supportOutsourcingFeeCurrCode = $v.supportOutsourcingFeeCurrCode;
      _support = $v.support;
      _status = $v.status;
      _workingHoursFrom = $v.workingHoursFrom;
      _workingHoursTo = $v.workingHoursTo;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GeneralCompanyDetails other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GeneralCompanyDetails;
  }

  @override
  void update(void Function(GeneralCompanyDetailsBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GeneralCompanyDetails build() => _build();

  _$GeneralCompanyDetails _build() {
    final _$result = _$v ??
        new _$GeneralCompanyDetails._(
          companyId: companyId,
          capitalStock: capitalStock,
          acceptingFee: acceptingFee,
          supportOutsourcingFee: supportOutsourcingFee,
          userType: BuiltValueNullFieldError.checkNotNull(
              userType, r'GeneralCompanyDetails', 'userType'),
          name: name,
          aboutUs: aboutUs,
          businessDetails: businessDetails,
          employeesType: employeesType,
          countryCode: countryCode,
          addressCode: addressCode,
          address: address,
          tel: tel,
          logoImagePath: logoImagePath,
          prImagePath1: prImagePath1,
          prImagePath2: prImagePath2,
          prImagePath3: prImagePath3,
          contactMail: contactMail,
          webUrl: webUrl,
          introductionUrl: introductionUrl,
          memo: memo,
          created: created,
          updated: updated,
          benefits: benefits,
          capitalStockCurrCode: capitalStockCurrCode,
          internationalTel: internationalTel,
          agentFee: agentFee,
          agentFeeCurrCode: agentFeeCurrCode,
          acceptingFeeCurrCode: acceptingFeeCurrCode,
          supportOutsourcingFeeCurrCode: supportOutsourcingFeeCurrCode,
          support: support,
          status: status,
          workingHoursFrom: workingHoursFrom,
          workingHoursTo: workingHoursTo,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
