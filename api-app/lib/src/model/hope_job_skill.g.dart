// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hope_job_skill.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$HopeJobSkill extends HopeJobSkill {
  @override
  final String? jobCode;
  @override
  final String? skillCode;

  factory _$HopeJobSkill([void Function(HopeJobSkillBuilder)? updates]) =>
      (new HopeJobSkillBuilder()..update(updates))._build();

  _$HopeJobSkill._({this.jobCode, this.skillCode}) : super._();

  @override
  HopeJobSkill rebuild(void Function(HopeJobSkillBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  HopeJobSkillBuilder toBuilder() => new HopeJobSkillBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is HopeJobSkill &&
        jobCode == other.jobCode &&
        skillCode == other.skillCode;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, jobCode.hashCode);
    _$hash = $jc(_$hash, skillCode.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'HopeJobSkill')
          ..add('jobCode', jobCode)
          ..add('skillCode', skillCode))
        .toString();
  }
}

class HopeJobSkillBuilder
    implements Builder<HopeJobSkill, HopeJobSkillBuilder> {
  _$HopeJobSkill? _$v;

  String? _jobCode;
  String? get jobCode => _$this._jobCode;
  set jobCode(String? jobCode) => _$this._jobCode = jobCode;

  String? _skillCode;
  String? get skillCode => _$this._skillCode;
  set skillCode(String? skillCode) => _$this._skillCode = skillCode;

  HopeJobSkillBuilder() {
    HopeJobSkill._defaults(this);
  }

  HopeJobSkillBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _jobCode = $v.jobCode;
      _skillCode = $v.skillCode;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(HopeJobSkill other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$HopeJobSkill;
  }

  @override
  void update(void Function(HopeJobSkillBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  HopeJobSkill build() => _build();

  _$HopeJobSkill _build() {
    final _$result = _$v ??
        new _$HopeJobSkill._(
          jobCode: jobCode,
          skillCode: skillCode,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
