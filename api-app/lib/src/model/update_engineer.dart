//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'update_engineer.g.dart';

/// UpdateEngineer
///
/// Properties:
/// * [employmentStatus] 
/// * [salesMemo] 
@BuiltValue()
abstract class UpdateEngineer implements Built<UpdateEngineer, UpdateEngineerBuilder> {
  @BuiltValueField(wireName: r'employment_status')
  int? get employmentStatus;

  @BuiltValueField(wireName: r'sales_memo')
  String? get salesMemo;

  UpdateEngineer._();

  factory UpdateEngineer([void updates(UpdateEngineerBuilder b)]) = _$UpdateEngineer;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(UpdateEngineerBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<UpdateEngineer> get serializer => _$UpdateEngineerSerializer();
}

class _$UpdateEngineerSerializer implements PrimitiveSerializer<UpdateEngineer> {
  @override
  final Iterable<Type> types = const [UpdateEngineer, _$UpdateEngineer];

  @override
  final String wireName = r'UpdateEngineer';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    UpdateEngineer object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.employmentStatus != null) {
      yield r'employment_status';
      yield serializers.serialize(
        object.employmentStatus,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.salesMemo != null) {
      yield r'sales_memo';
      yield serializers.serialize(
        object.salesMemo,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    UpdateEngineer object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required UpdateEngineerBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'employment_status':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.employmentStatus = valueDes;
          break;
        case r'sales_memo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.salesMemo = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  UpdateEngineer deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = UpdateEngineerBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

