//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'general_company_update_interview_date_time.g.dart';

/// GeneralCompanyUpdateInterviewDateTime
///
/// Properties:
/// * [applyId] 
/// * [interviewDatetime] 
/// * [hostCompanyId] 
@BuiltValue()
abstract class GeneralCompanyUpdateInterviewDateTime implements Built<GeneralCompanyUpdateInterviewDateTime, GeneralCompanyUpdateInterviewDateTimeBuilder> {
  @BuiltValueField(wireName: r'apply_id')
  int get applyId;

  @BuiltValueField(wireName: r'interview_datetime')
  DateTime get interviewDatetime;

  @BuiltValueField(wireName: r'host_company_id')
  int? get hostCompanyId;

  GeneralCompanyUpdateInterviewDateTime._();

  factory GeneralCompanyUpdateInterviewDateTime([void updates(GeneralCompanyUpdateInterviewDateTimeBuilder b)]) = _$GeneralCompanyUpdateInterviewDateTime;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(GeneralCompanyUpdateInterviewDateTimeBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<GeneralCompanyUpdateInterviewDateTime> get serializer => _$GeneralCompanyUpdateInterviewDateTimeSerializer();
}

class _$GeneralCompanyUpdateInterviewDateTimeSerializer implements PrimitiveSerializer<GeneralCompanyUpdateInterviewDateTime> {
  @override
  final Iterable<Type> types = const [GeneralCompanyUpdateInterviewDateTime, _$GeneralCompanyUpdateInterviewDateTime];

  @override
  final String wireName = r'GeneralCompanyUpdateInterviewDateTime';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    GeneralCompanyUpdateInterviewDateTime object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'apply_id';
    yield serializers.serialize(
      object.applyId,
      specifiedType: const FullType(int),
    );
    yield r'interview_datetime';
    yield serializers.serialize(
      object.interviewDatetime,
      specifiedType: const FullType(DateTime),
    );
    if (object.hostCompanyId != null) {
      yield r'host_company_id';
      yield serializers.serialize(
        object.hostCompanyId,
        specifiedType: const FullType.nullable(int),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    GeneralCompanyUpdateInterviewDateTime object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required GeneralCompanyUpdateInterviewDateTimeBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'apply_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.applyId = valueDes;
          break;
        case r'interview_datetime':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(DateTime),
          ) as DateTime;
          result.interviewDatetime = valueDes;
          break;
        case r'host_company_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.hostCompanyId = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  GeneralCompanyUpdateInterviewDateTime deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = GeneralCompanyUpdateInterviewDateTimeBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

