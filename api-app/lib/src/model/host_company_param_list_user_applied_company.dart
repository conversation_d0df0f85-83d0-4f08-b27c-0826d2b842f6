//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'host_company_param_list_user_applied_company.g.dart';

/// HostCompanyParamListUserAppliedCompany
///
/// Properties:
/// * [recruitIds] 
/// * [ordering] 
/// * [applyStatusFilterCodes] 
/// * [search] 
@BuiltValue()
abstract class HostCompanyParamListUserAppliedCompany implements Built<HostCompanyParamListUserAppliedCompany, HostCompanyParamListUserAppliedCompanyBuilder> {
  @BuiltValueField(wireName: r'recruit_ids')
  BuiltList<int>? get recruitIds;

  @BuiltValueField(wireName: r'ordering')
  String? get ordering;

  @BuiltValueField(wireName: r'apply_status_filter_codes')
  BuiltList<int>? get applyStatusFilterCodes;

  @BuiltValueField(wireName: r'search')
  String? get search;

  HostCompanyParamListUserAppliedCompany._();

  factory HostCompanyParamListUserAppliedCompany([void updates(HostCompanyParamListUserAppliedCompanyBuilder b)]) = _$HostCompanyParamListUserAppliedCompany;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(HostCompanyParamListUserAppliedCompanyBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<HostCompanyParamListUserAppliedCompany> get serializer => _$HostCompanyParamListUserAppliedCompanySerializer();
}

class _$HostCompanyParamListUserAppliedCompanySerializer implements PrimitiveSerializer<HostCompanyParamListUserAppliedCompany> {
  @override
  final Iterable<Type> types = const [HostCompanyParamListUserAppliedCompany, _$HostCompanyParamListUserAppliedCompany];

  @override
  final String wireName = r'HostCompanyParamListUserAppliedCompany';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    HostCompanyParamListUserAppliedCompany object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.recruitIds != null) {
      yield r'recruit_ids';
      yield serializers.serialize(
        object.recruitIds,
        specifiedType: const FullType.nullable(BuiltList, [FullType(int)]),
      );
    }
    if (object.ordering != null) {
      yield r'ordering';
      yield serializers.serialize(
        object.ordering,
        specifiedType: const FullType(String),
      );
    }
    if (object.applyStatusFilterCodes != null) {
      yield r'apply_status_filter_codes';
      yield serializers.serialize(
        object.applyStatusFilterCodes,
        specifiedType: const FullType.nullable(BuiltList, [FullType(int)]),
      );
    }
    if (object.search != null) {
      yield r'search';
      yield serializers.serialize(
        object.search,
        specifiedType: const FullType(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    HostCompanyParamListUserAppliedCompany object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required HostCompanyParamListUserAppliedCompanyBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'recruit_ids':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(BuiltList, [FullType(int)]),
          ) as BuiltList<int>?;
          if (valueDes == null) continue;
          result.recruitIds.replace(valueDes);
          break;
        case r'ordering':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.ordering = valueDes;
          break;
        case r'apply_status_filter_codes':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(BuiltList, [FullType(int)]),
          ) as BuiltList<int>?;
          if (valueDes == null) continue;
          result.applyStatusFilterCodes.replace(valueDes);
          break;
        case r'search':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.search = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  HostCompanyParamListUserAppliedCompany deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = HostCompanyParamListUserAppliedCompanyBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

