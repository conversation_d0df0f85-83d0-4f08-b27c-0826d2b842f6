//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/host_company_param_list_user_applied_company.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'host_company_list_applied_engineer.g.dart';

/// HostCompanyListAppliedEngineer
///
/// Properties:
/// * [data] 
/// * [totalEngineers] 
@BuiltValue()
abstract class HostCompanyListAppliedEngineer implements Built<HostCompanyListAppliedEngineer, HostCompanyListAppliedEngineerBuilder> {
  @BuiltValueField(wireName: r'data')
  BuiltList<HostCompanyParamListUserAppliedCompany> get data;

  @BuiltValueField(wireName: r'total_engineers')
  int get totalEngineers;

  HostCompanyListAppliedEngineer._();

  factory HostCompanyListAppliedEngineer([void updates(HostCompanyListAppliedEngineerBuilder b)]) = _$HostCompanyListAppliedEngineer;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(HostCompanyListAppliedEngineerBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<HostCompanyListAppliedEngineer> get serializer => _$HostCompanyListAppliedEngineerSerializer();
}

class _$HostCompanyListAppliedEngineerSerializer implements PrimitiveSerializer<HostCompanyListAppliedEngineer> {
  @override
  final Iterable<Type> types = const [HostCompanyListAppliedEngineer, _$HostCompanyListAppliedEngineer];

  @override
  final String wireName = r'HostCompanyListAppliedEngineer';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    HostCompanyListAppliedEngineer object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'data';
    yield serializers.serialize(
      object.data,
      specifiedType: const FullType(BuiltList, [FullType(HostCompanyParamListUserAppliedCompany)]),
    );
    yield r'total_engineers';
    yield serializers.serialize(
      object.totalEngineers,
      specifiedType: const FullType(int),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    HostCompanyListAppliedEngineer object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required HostCompanyListAppliedEngineerBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(HostCompanyParamListUserAppliedCompany)]),
          ) as BuiltList<HostCompanyParamListUserAppliedCompany>;
          result.data.replace(valueDes);
          break;
        case r'total_engineers':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.totalEngineers = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  HostCompanyListAppliedEngineer deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = HostCompanyListAppliedEngineerBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

