//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/hope_job_skill.dart';
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/hope_category_skill.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'eng_hope.g.dart';

/// EngHope
///
/// Properties:
/// * [employCode] 
/// * [placeCode1] 
/// * [placeCode2] 
/// * [placeCode3] 
/// * [payrollPrice] 
/// * [payrollCode] 
/// * [jobSkills] 
/// * [placeCode1Name] 
/// * [placeCode2Name] 
/// * [placeCode3Name] 
/// * [countryPlaceCode1Name] 
/// * [countryPlaceCode2Name] 
/// * [countryPlaceCode3Name] 
/// * [remoteCode] 
/// * [categorySkills] 
/// * [payrollPriceUsd] 
@BuiltValue()
abstract class EngHope implements Built<EngHope, EngHopeBuilder> {
  @BuiltValueField(wireName: r'employ_code')
  String? get employCode;

  @BuiltValueField(wireName: r'place_code1')
  String? get placeCode1;

  @BuiltValueField(wireName: r'place_code2')
  String? get placeCode2;

  @BuiltValueField(wireName: r'place_code3')
  String? get placeCode3;

  @BuiltValueField(wireName: r'payroll_price')
  String? get payrollPrice;

  @BuiltValueField(wireName: r'payroll_code')
  String? get payrollCode;

  @BuiltValueField(wireName: r'job_skills')
  BuiltList<HopeJobSkill>? get jobSkills;

  @BuiltValueField(wireName: r'place_code1_name')
  String? get placeCode1Name;

  @BuiltValueField(wireName: r'place_code2_name')
  String? get placeCode2Name;

  @BuiltValueField(wireName: r'place_code3_name')
  String? get placeCode3Name;

  @BuiltValueField(wireName: r'country_place_code1_name')
  String? get countryPlaceCode1Name;

  @BuiltValueField(wireName: r'country_place_code2_name')
  String? get countryPlaceCode2Name;

  @BuiltValueField(wireName: r'country_place_code3_name')
  String? get countryPlaceCode3Name;

  @BuiltValueField(wireName: r'remote_code')
  String? get remoteCode;

  @BuiltValueField(wireName: r'category_skills')
  BuiltList<HopeCategorySkill> get categorySkills;

  @BuiltValueField(wireName: r'payroll_price_usd')
  double? get payrollPriceUsd;

  EngHope._();

  factory EngHope([void updates(EngHopeBuilder b)]) = _$EngHope;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(EngHopeBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<EngHope> get serializer => _$EngHopeSerializer();
}

class _$EngHopeSerializer implements PrimitiveSerializer<EngHope> {
  @override
  final Iterable<Type> types = const [EngHope, _$EngHope];

  @override
  final String wireName = r'EngHope';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    EngHope object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.employCode != null) {
      yield r'employ_code';
      yield serializers.serialize(
        object.employCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.placeCode1 != null) {
      yield r'place_code1';
      yield serializers.serialize(
        object.placeCode1,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.placeCode2 != null) {
      yield r'place_code2';
      yield serializers.serialize(
        object.placeCode2,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.placeCode3 != null) {
      yield r'place_code3';
      yield serializers.serialize(
        object.placeCode3,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.payrollPrice != null) {
      yield r'payroll_price';
      yield serializers.serialize(
        object.payrollPrice,
        specifiedType: const FullType(String),
      );
    }
    if (object.payrollCode != null) {
      yield r'payroll_code';
      yield serializers.serialize(
        object.payrollCode,
        specifiedType: const FullType(String),
      );
    }
    if (object.jobSkills != null) {
      yield r'job_skills';
      yield serializers.serialize(
        object.jobSkills,
        specifiedType: const FullType(BuiltList, [FullType(HopeJobSkill)]),
      );
    }
    if (object.placeCode1Name != null) {
      yield r'place_code1_name';
      yield serializers.serialize(
        object.placeCode1Name,
        specifiedType: const FullType(String),
      );
    }
    if (object.placeCode2Name != null) {
      yield r'place_code2_name';
      yield serializers.serialize(
        object.placeCode2Name,
        specifiedType: const FullType(String),
      );
    }
    if (object.placeCode3Name != null) {
      yield r'place_code3_name';
      yield serializers.serialize(
        object.placeCode3Name,
        specifiedType: const FullType(String),
      );
    }
    if (object.countryPlaceCode1Name != null) {
      yield r'country_place_code1_name';
      yield serializers.serialize(
        object.countryPlaceCode1Name,
        specifiedType: const FullType(String),
      );
    }
    if (object.countryPlaceCode2Name != null) {
      yield r'country_place_code2_name';
      yield serializers.serialize(
        object.countryPlaceCode2Name,
        specifiedType: const FullType(String),
      );
    }
    if (object.countryPlaceCode3Name != null) {
      yield r'country_place_code3_name';
      yield serializers.serialize(
        object.countryPlaceCode3Name,
        specifiedType: const FullType(String),
      );
    }
    if (object.remoteCode != null) {
      yield r'remote_code';
      yield serializers.serialize(
        object.remoteCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    yield r'category_skills';
    yield serializers.serialize(
      object.categorySkills,
      specifiedType: const FullType(BuiltList, [FullType(HopeCategorySkill)]),
    );
    if (object.payrollPriceUsd != null) {
      yield r'payroll_price_usd';
      yield serializers.serialize(
        object.payrollPriceUsd,
        specifiedType: const FullType.nullable(double),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    EngHope object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required EngHopeBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'employ_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.employCode = valueDes;
          break;
        case r'place_code1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.placeCode1 = valueDes;
          break;
        case r'place_code2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.placeCode2 = valueDes;
          break;
        case r'place_code3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.placeCode3 = valueDes;
          break;
        case r'payroll_price':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.payrollPrice = valueDes;
          break;
        case r'payroll_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.payrollCode = valueDes;
          break;
        case r'job_skills':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(HopeJobSkill)]),
          ) as BuiltList<HopeJobSkill>;
          result.jobSkills.replace(valueDes);
          break;
        case r'place_code1_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.placeCode1Name = valueDes;
          break;
        case r'place_code2_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.placeCode2Name = valueDes;
          break;
        case r'place_code3_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.placeCode3Name = valueDes;
          break;
        case r'country_place_code1_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.countryPlaceCode1Name = valueDes;
          break;
        case r'country_place_code2_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.countryPlaceCode2Name = valueDes;
          break;
        case r'country_place_code3_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.countryPlaceCode3Name = valueDes;
          break;
        case r'remote_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.remoteCode = valueDes;
          break;
        case r'category_skills':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(HopeCategorySkill)]),
          ) as BuiltList<HopeCategorySkill>;
          result.categorySkills.replace(valueDes);
          break;
        case r'payroll_price_usd':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(double),
          ) as double?;
          if (valueDes == null) continue;
          result.payrollPriceUsd = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  EngHope deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = EngHopeBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

