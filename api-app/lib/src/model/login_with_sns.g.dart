// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login_with_sns.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$LoginWithSNS extends LoginWithSNS {
  @override
  final String snsType;
  @override
  final String? code;
  @override
  final String? redirectUri;
  @override
  final String? codeVerifier;
  @override
  final String? whatsappNumber;

  factory _$LoginWithSNS([void Function(LoginWithSNSBuilder)? updates]) =>
      (new LoginWithSNSBuilder()..update(updates))._build();

  _$LoginWithSNS._(
      {required this.snsType,
      this.code,
      this.redirectUri,
      this.codeVerifier,
      this.whatsappNumber})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(snsType, r'LoginWithSNS', 'snsType');
  }

  @override
  LoginWithSNS rebuild(void Function(LoginWithSNSBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  LoginWithSNSBuilder toBuilder() => new LoginWithSNSBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is LoginWithSNS &&
        snsType == other.snsType &&
        code == other.code &&
        redirectUri == other.redirectUri &&
        codeVerifier == other.codeVerifier &&
        whatsappNumber == other.whatsappNumber;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, snsType.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, redirectUri.hashCode);
    _$hash = $jc(_$hash, codeVerifier.hashCode);
    _$hash = $jc(_$hash, whatsappNumber.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'LoginWithSNS')
          ..add('snsType', snsType)
          ..add('code', code)
          ..add('redirectUri', redirectUri)
          ..add('codeVerifier', codeVerifier)
          ..add('whatsappNumber', whatsappNumber))
        .toString();
  }
}

class LoginWithSNSBuilder
    implements Builder<LoginWithSNS, LoginWithSNSBuilder> {
  _$LoginWithSNS? _$v;

  String? _snsType;
  String? get snsType => _$this._snsType;
  set snsType(String? snsType) => _$this._snsType = snsType;

  String? _code;
  String? get code => _$this._code;
  set code(String? code) => _$this._code = code;

  String? _redirectUri;
  String? get redirectUri => _$this._redirectUri;
  set redirectUri(String? redirectUri) => _$this._redirectUri = redirectUri;

  String? _codeVerifier;
  String? get codeVerifier => _$this._codeVerifier;
  set codeVerifier(String? codeVerifier) => _$this._codeVerifier = codeVerifier;

  String? _whatsappNumber;
  String? get whatsappNumber => _$this._whatsappNumber;
  set whatsappNumber(String? whatsappNumber) =>
      _$this._whatsappNumber = whatsappNumber;

  LoginWithSNSBuilder() {
    LoginWithSNS._defaults(this);
  }

  LoginWithSNSBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _snsType = $v.snsType;
      _code = $v.code;
      _redirectUri = $v.redirectUri;
      _codeVerifier = $v.codeVerifier;
      _whatsappNumber = $v.whatsappNumber;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(LoginWithSNS other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$LoginWithSNS;
  }

  @override
  void update(void Function(LoginWithSNSBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  LoginWithSNS build() => _build();

  _$LoginWithSNS _build() {
    final _$result = _$v ??
        new _$LoginWithSNS._(
          snsType: BuiltValueNullFieldError.checkNotNull(
              snsType, r'LoginWithSNS', 'snsType'),
          code: code,
          redirectUri: redirectUri,
          codeVerifier: codeVerifier,
          whatsappNumber: whatsappNumber,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
