// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_company_user.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GeneralCompanyUser extends GeneralCompanyUser {
  @override
  final String email;
  @override
  final String password;
  @override
  final String? firstName;
  @override
  final String? lastName;

  factory _$GeneralCompanyUser(
          [void Function(GeneralCompanyUserBuilder)? updates]) =>
      (new GeneralCompanyUserBuilder()..update(updates))._build();

  _$GeneralCompanyUser._(
      {required this.email,
      required this.password,
      this.firstName,
      this.lastName})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        email, r'GeneralCompanyUser', 'email');
    BuiltValueNullFieldError.checkNotNull(
        password, r'GeneralCompanyUser', 'password');
  }

  @override
  GeneralCompanyUser rebuild(
          void Function(GeneralCompanyUserBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GeneralCompanyUserBuilder toBuilder() =>
      new GeneralCompanyUserBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GeneralCompanyUser &&
        email == other.email &&
        password == other.password &&
        firstName == other.firstName &&
        lastName == other.lastName;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, email.hashCode);
    _$hash = $jc(_$hash, password.hashCode);
    _$hash = $jc(_$hash, firstName.hashCode);
    _$hash = $jc(_$hash, lastName.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GeneralCompanyUser')
          ..add('email', email)
          ..add('password', password)
          ..add('firstName', firstName)
          ..add('lastName', lastName))
        .toString();
  }
}

class GeneralCompanyUserBuilder
    implements Builder<GeneralCompanyUser, GeneralCompanyUserBuilder> {
  _$GeneralCompanyUser? _$v;

  String? _email;
  String? get email => _$this._email;
  set email(String? email) => _$this._email = email;

  String? _password;
  String? get password => _$this._password;
  set password(String? password) => _$this._password = password;

  String? _firstName;
  String? get firstName => _$this._firstName;
  set firstName(String? firstName) => _$this._firstName = firstName;

  String? _lastName;
  String? get lastName => _$this._lastName;
  set lastName(String? lastName) => _$this._lastName = lastName;

  GeneralCompanyUserBuilder() {
    GeneralCompanyUser._defaults(this);
  }

  GeneralCompanyUserBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _email = $v.email;
      _password = $v.password;
      _firstName = $v.firstName;
      _lastName = $v.lastName;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GeneralCompanyUser other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GeneralCompanyUser;
  }

  @override
  void update(void Function(GeneralCompanyUserBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GeneralCompanyUser build() => _build();

  _$GeneralCompanyUser _build() {
    final _$result = _$v ??
        new _$GeneralCompanyUser._(
          email: BuiltValueNullFieldError.checkNotNull(
              email, r'GeneralCompanyUser', 'email'),
          password: BuiltValueNullFieldError.checkNotNull(
              password, r'GeneralCompanyUser', 'password'),
          firstName: firstName,
          lastName: lastName,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
