// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notify.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$Notify extends Notify {
  @override
  final int? recId;
  @override
  final int? type;
  @override
  final String? message;
  @override
  final int? userId;
  @override
  final DateTime? readAt;
  @override
  final String? payload;
  @override
  final DateTime? created;
  @override
  final DateTime? updated;

  factory _$Notify([void Function(NotifyBuilder)? updates]) =>
      (new NotifyBuilder()..update(updates))._build();

  _$Notify._(
      {this.recId,
      this.type,
      this.message,
      this.userId,
      this.readAt,
      this.payload,
      this.created,
      this.updated})
      : super._();

  @override
  Notify rebuild(void Function(NotifyBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  NotifyBuilder toBuilder() => new NotifyBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is Notify &&
        recId == other.recId &&
        type == other.type &&
        message == other.message &&
        userId == other.userId &&
        readAt == other.readAt &&
        payload == other.payload &&
        created == other.created &&
        updated == other.updated;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, recId.hashCode);
    _$hash = $jc(_$hash, type.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, userId.hashCode);
    _$hash = $jc(_$hash, readAt.hashCode);
    _$hash = $jc(_$hash, payload.hashCode);
    _$hash = $jc(_$hash, created.hashCode);
    _$hash = $jc(_$hash, updated.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'Notify')
          ..add('recId', recId)
          ..add('type', type)
          ..add('message', message)
          ..add('userId', userId)
          ..add('readAt', readAt)
          ..add('payload', payload)
          ..add('created', created)
          ..add('updated', updated))
        .toString();
  }
}

class NotifyBuilder implements Builder<Notify, NotifyBuilder> {
  _$Notify? _$v;

  int? _recId;
  int? get recId => _$this._recId;
  set recId(int? recId) => _$this._recId = recId;

  int? _type;
  int? get type => _$this._type;
  set type(int? type) => _$this._type = type;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  int? _userId;
  int? get userId => _$this._userId;
  set userId(int? userId) => _$this._userId = userId;

  DateTime? _readAt;
  DateTime? get readAt => _$this._readAt;
  set readAt(DateTime? readAt) => _$this._readAt = readAt;

  String? _payload;
  String? get payload => _$this._payload;
  set payload(String? payload) => _$this._payload = payload;

  DateTime? _created;
  DateTime? get created => _$this._created;
  set created(DateTime? created) => _$this._created = created;

  DateTime? _updated;
  DateTime? get updated => _$this._updated;
  set updated(DateTime? updated) => _$this._updated = updated;

  NotifyBuilder() {
    Notify._defaults(this);
  }

  NotifyBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _recId = $v.recId;
      _type = $v.type;
      _message = $v.message;
      _userId = $v.userId;
      _readAt = $v.readAt;
      _payload = $v.payload;
      _created = $v.created;
      _updated = $v.updated;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(Notify other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$Notify;
  }

  @override
  void update(void Function(NotifyBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  Notify build() => _build();

  _$Notify _build() {
    final _$result = _$v ??
        new _$Notify._(
          recId: recId,
          type: type,
          message: message,
          userId: userId,
          readAt: readAt,
          payload: payload,
          created: created,
          updated: updated,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
