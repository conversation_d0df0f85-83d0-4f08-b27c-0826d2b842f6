// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'mark_read_all_messages_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$MarkReadAllMessagesResponseModel
    extends MarkReadAllMessagesResponseModel {
  @override
  final String? message;
  @override
  final bool? data;
  @override
  final BuiltList<ErrorDetail>? errors;

  factory _$MarkReadAllMessagesResponseModel(
          [void Function(MarkReadAllMessagesResponseModelBuilder)? updates]) =>
      (new MarkReadAllMessagesResponseModelBuilder()..update(updates))._build();

  _$MarkReadAllMessagesResponseModel._({this.message, this.data, this.errors})
      : super._();

  @override
  MarkReadAllMessagesResponseModel rebuild(
          void Function(MarkReadAllMessagesResponseModelBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  MarkReadAllMessagesResponseModelBuilder toBuilder() =>
      new MarkReadAllMessagesResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is MarkReadAllMessagesResponseModel &&
        message == other.message &&
        data == other.data &&
        errors == other.errors;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'MarkReadAllMessagesResponseModel')
          ..add('message', message)
          ..add('data', data)
          ..add('errors', errors))
        .toString();
  }
}

class MarkReadAllMessagesResponseModelBuilder
    implements
        Builder<MarkReadAllMessagesResponseModel,
            MarkReadAllMessagesResponseModelBuilder> {
  _$MarkReadAllMessagesResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  bool? _data;
  bool? get data => _$this._data;
  set data(bool? data) => _$this._data = data;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  MarkReadAllMessagesResponseModelBuilder() {
    MarkReadAllMessagesResponseModel._defaults(this);
  }

  MarkReadAllMessagesResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _data = $v.data;
      _errors = $v.errors?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(MarkReadAllMessagesResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$MarkReadAllMessagesResponseModel;
  }

  @override
  void update(void Function(MarkReadAllMessagesResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  MarkReadAllMessagesResponseModel build() => _build();

  _$MarkReadAllMessagesResponseModel _build() {
    _$MarkReadAllMessagesResponseModel _$result;
    try {
      _$result = _$v ??
          new _$MarkReadAllMessagesResponseModel._(
            message: message,
            data: data,
            errors: _errors?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'errors';
        _errors?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'MarkReadAllMessagesResponseModel', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
