//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'eng_language.g.dart';

/// EngLanguage
///
/// Properties:
/// * [engineerId] 
/// * [languageLevelType] 
/// * [languageId] 
/// * [languageCode] 
/// * [languageName] 
/// * [languageLevelName] 
@BuiltValue()
abstract class EngLanguage implements Built<EngLanguage, EngLanguageBuilder> {
  @BuiltValueField(wireName: r'engineer_id')
  int get engineerId;

  @BuiltValueField(wireName: r'language_level_type')
  int? get languageLevelType;

  @BuiltValueField(wireName: r'language_id')
  int? get languageId;

  @BuiltValueField(wireName: r'language_code')
  String? get languageCode;

  @BuiltValueField(wireName: r'language_name')
  String? get languageName;

  @BuiltValueField(wireName: r'language_level_name')
  String? get languageLevelName;

  EngLanguage._();

  factory EngLanguage([void updates(EngLanguageBuilder b)]) = _$EngLanguage;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(EngLanguageBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<EngLanguage> get serializer => _$EngLanguageSerializer();
}

class _$EngLanguageSerializer implements PrimitiveSerializer<EngLanguage> {
  @override
  final Iterable<Type> types = const [EngLanguage, _$EngLanguage];

  @override
  final String wireName = r'EngLanguage';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    EngLanguage object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'engineer_id';
    yield serializers.serialize(
      object.engineerId,
      specifiedType: const FullType(int),
    );
    if (object.languageLevelType != null) {
      yield r'language_level_type';
      yield serializers.serialize(
        object.languageLevelType,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.languageId != null) {
      yield r'language_id';
      yield serializers.serialize(
        object.languageId,
        specifiedType: const FullType(int),
      );
    }
    if (object.languageCode != null) {
      yield r'language_code';
      yield serializers.serialize(
        object.languageCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.languageName != null) {
      yield r'language_name';
      yield serializers.serialize(
        object.languageName,
        specifiedType: const FullType(String),
      );
    }
    if (object.languageLevelName != null) {
      yield r'language_level_name';
      yield serializers.serialize(
        object.languageLevelName,
        specifiedType: const FullType(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    EngLanguage object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required EngLanguageBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'engineer_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.engineerId = valueDes;
          break;
        case r'language_level_type':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.languageLevelType = valueDes;
          break;
        case r'language_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.languageId = valueDes;
          break;
        case r'language_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.languageCode = valueDes;
          break;
        case r'language_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.languageName = valueDes;
          break;
        case r'language_level_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.languageLevelName = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  EngLanguage deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = EngLanguageBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

