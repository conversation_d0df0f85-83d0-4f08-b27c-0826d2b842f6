// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_company_update_favorite_user.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GeneralCompanyUpdateFavoriteUser
    extends GeneralCompanyUpdateFavoriteUser {
  @override
  final int userId;
  @override
  final bool isFavorite;

  factory _$GeneralCompanyUpdateFavoriteUser(
          [void Function(GeneralCompanyUpdateFavoriteUserBuilder)? updates]) =>
      (new GeneralCompanyUpdateFavoriteUserBuilder()..update(updates))._build();

  _$GeneralCompanyUpdateFavoriteUser._(
      {required this.userId, required this.isFavorite})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        userId, r'GeneralCompanyUpdateFavoriteUser', 'userId');
    BuiltValueNullFieldError.checkNotNull(
        isFavorite, r'GeneralCompanyUpdateFavoriteUser', 'isFavorite');
  }

  @override
  GeneralCompanyUpdateFavoriteUser rebuild(
          void Function(GeneralCompanyUpdateFavoriteUserBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GeneralCompanyUpdateFavoriteUserBuilder toBuilder() =>
      new GeneralCompanyUpdateFavoriteUserBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GeneralCompanyUpdateFavoriteUser &&
        userId == other.userId &&
        isFavorite == other.isFavorite;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, userId.hashCode);
    _$hash = $jc(_$hash, isFavorite.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GeneralCompanyUpdateFavoriteUser')
          ..add('userId', userId)
          ..add('isFavorite', isFavorite))
        .toString();
  }
}

class GeneralCompanyUpdateFavoriteUserBuilder
    implements
        Builder<GeneralCompanyUpdateFavoriteUser,
            GeneralCompanyUpdateFavoriteUserBuilder> {
  _$GeneralCompanyUpdateFavoriteUser? _$v;

  int? _userId;
  int? get userId => _$this._userId;
  set userId(int? userId) => _$this._userId = userId;

  bool? _isFavorite;
  bool? get isFavorite => _$this._isFavorite;
  set isFavorite(bool? isFavorite) => _$this._isFavorite = isFavorite;

  GeneralCompanyUpdateFavoriteUserBuilder() {
    GeneralCompanyUpdateFavoriteUser._defaults(this);
  }

  GeneralCompanyUpdateFavoriteUserBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _userId = $v.userId;
      _isFavorite = $v.isFavorite;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GeneralCompanyUpdateFavoriteUser other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GeneralCompanyUpdateFavoriteUser;
  }

  @override
  void update(void Function(GeneralCompanyUpdateFavoriteUserBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GeneralCompanyUpdateFavoriteUser build() => _build();

  _$GeneralCompanyUpdateFavoriteUser _build() {
    final _$result = _$v ??
        new _$GeneralCompanyUpdateFavoriteUser._(
          userId: BuiltValueNullFieldError.checkNotNull(
              userId, r'GeneralCompanyUpdateFavoriteUser', 'userId'),
          isFavorite: BuiltValueNullFieldError.checkNotNull(
              isFavorite, r'GeneralCompanyUpdateFavoriteUser', 'isFavorite'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
