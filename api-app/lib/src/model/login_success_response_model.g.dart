// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login_success_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$LoginSuccessResponseModel extends LoginSuccessResponseModel {
  @override
  final String? message;
  @override
  final LoginSuccess data;
  @override
  final BuiltList<ErrorDetail>? errors;

  factory _$LoginSuccessResponseModel(
          [void Function(LoginSuccessResponseModelBuilder)? updates]) =>
      (new LoginSuccessResponseModelBuilder()..update(updates))._build();

  _$LoginSuccessResponseModel._({this.message, required this.data, this.errors})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'LoginSuccessResponseModel', 'data');
  }

  @override
  LoginSuccessResponseModel rebuild(
          void Function(LoginSuccessResponseModelBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  LoginSuccessResponseModelBuilder toBuilder() =>
      new LoginSuccessResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is LoginSuccessResponseModel &&
        message == other.message &&
        data == other.data &&
        errors == other.errors;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'LoginSuccessResponseModel')
          ..add('message', message)
          ..add('data', data)
          ..add('errors', errors))
        .toString();
  }
}

class LoginSuccessResponseModelBuilder
    implements
        Builder<LoginSuccessResponseModel, LoginSuccessResponseModelBuilder> {
  _$LoginSuccessResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  LoginSuccessBuilder? _data;
  LoginSuccessBuilder get data => _$this._data ??= new LoginSuccessBuilder();
  set data(LoginSuccessBuilder? data) => _$this._data = data;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  LoginSuccessResponseModelBuilder() {
    LoginSuccessResponseModel._defaults(this);
  }

  LoginSuccessResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _data = $v.data.toBuilder();
      _errors = $v.errors?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(LoginSuccessResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$LoginSuccessResponseModel;
  }

  @override
  void update(void Function(LoginSuccessResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  LoginSuccessResponseModel build() => _build();

  _$LoginSuccessResponseModel _build() {
    _$LoginSuccessResponseModel _$result;
    try {
      _$result = _$v ??
          new _$LoginSuccessResponseModel._(
            message: message,
            data: data.build(),
            errors: _errors?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        data.build();
        _$failedField = 'errors';
        _errors?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'LoginSuccessResponseModel', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
