// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_company_compare_details.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GeneralCompanyCompareDetails extends GeneralCompanyCompareDetails {
  @override
  final String? description;
  @override
  final int? point;
  @override
  final bool? salaryMatched;
  @override
  final bool? workLocationMatched;
  @override
  final bool? employCodeMatched;
  @override
  final bool? jobSkillsMatched;

  factory _$GeneralCompanyCompareDetails(
          [void Function(GeneralCompanyCompareDetailsBuilder)? updates]) =>
      (new GeneralCompanyCompareDetailsBuilder()..update(updates))._build();

  _$GeneralCompanyCompareDetails._(
      {this.description,
      this.point,
      this.salaryMatched,
      this.workLocationMatched,
      this.employCodeMatched,
      this.jobSkillsMatched})
      : super._();

  @override
  GeneralCompanyCompareDetails rebuild(
          void Function(GeneralCompanyCompareDetailsBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GeneralCompanyCompareDetailsBuilder toBuilder() =>
      new GeneralCompanyCompareDetailsBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GeneralCompanyCompareDetails &&
        description == other.description &&
        point == other.point &&
        salaryMatched == other.salaryMatched &&
        workLocationMatched == other.workLocationMatched &&
        employCodeMatched == other.employCodeMatched &&
        jobSkillsMatched == other.jobSkillsMatched;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, point.hashCode);
    _$hash = $jc(_$hash, salaryMatched.hashCode);
    _$hash = $jc(_$hash, workLocationMatched.hashCode);
    _$hash = $jc(_$hash, employCodeMatched.hashCode);
    _$hash = $jc(_$hash, jobSkillsMatched.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GeneralCompanyCompareDetails')
          ..add('description', description)
          ..add('point', point)
          ..add('salaryMatched', salaryMatched)
          ..add('workLocationMatched', workLocationMatched)
          ..add('employCodeMatched', employCodeMatched)
          ..add('jobSkillsMatched', jobSkillsMatched))
        .toString();
  }
}

class GeneralCompanyCompareDetailsBuilder
    implements
        Builder<GeneralCompanyCompareDetails,
            GeneralCompanyCompareDetailsBuilder> {
  _$GeneralCompanyCompareDetails? _$v;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  int? _point;
  int? get point => _$this._point;
  set point(int? point) => _$this._point = point;

  bool? _salaryMatched;
  bool? get salaryMatched => _$this._salaryMatched;
  set salaryMatched(bool? salaryMatched) =>
      _$this._salaryMatched = salaryMatched;

  bool? _workLocationMatched;
  bool? get workLocationMatched => _$this._workLocationMatched;
  set workLocationMatched(bool? workLocationMatched) =>
      _$this._workLocationMatched = workLocationMatched;

  bool? _employCodeMatched;
  bool? get employCodeMatched => _$this._employCodeMatched;
  set employCodeMatched(bool? employCodeMatched) =>
      _$this._employCodeMatched = employCodeMatched;

  bool? _jobSkillsMatched;
  bool? get jobSkillsMatched => _$this._jobSkillsMatched;
  set jobSkillsMatched(bool? jobSkillsMatched) =>
      _$this._jobSkillsMatched = jobSkillsMatched;

  GeneralCompanyCompareDetailsBuilder() {
    GeneralCompanyCompareDetails._defaults(this);
  }

  GeneralCompanyCompareDetailsBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _description = $v.description;
      _point = $v.point;
      _salaryMatched = $v.salaryMatched;
      _workLocationMatched = $v.workLocationMatched;
      _employCodeMatched = $v.employCodeMatched;
      _jobSkillsMatched = $v.jobSkillsMatched;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GeneralCompanyCompareDetails other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GeneralCompanyCompareDetails;
  }

  @override
  void update(void Function(GeneralCompanyCompareDetailsBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GeneralCompanyCompareDetails build() => _build();

  _$GeneralCompanyCompareDetails _build() {
    final _$result = _$v ??
        new _$GeneralCompanyCompareDetails._(
          description: description,
          point: point,
          salaryMatched: salaryMatched,
          workLocationMatched: workLocationMatched,
          employCodeMatched: employCodeMatched,
          jobSkillsMatched: jobSkillsMatched,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
