//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'update_eng_language.g.dart';

/// UpdateEngLanguage
///
/// Properties:
/// * [languageLevelType] 
/// * [languageCode] 
@BuiltValue()
abstract class UpdateEngLanguage implements Built<UpdateEngLanguage, UpdateEngLanguageBuilder> {
  @BuiltValueField(wireName: r'language_level_type')
  int? get languageLevelType;

  @BuiltValueField(wireName: r'language_code')
  String? get languageCode;

  UpdateEngLanguage._();

  factory UpdateEngLanguage([void updates(UpdateEngLanguageBuilder b)]) = _$UpdateEngLanguage;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(UpdateEngLanguageBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<UpdateEngLanguage> get serializer => _$UpdateEngLanguageSerializer();
}

class _$UpdateEngLanguageSerializer implements PrimitiveSerializer<UpdateEngLanguage> {
  @override
  final Iterable<Type> types = const [UpdateEngLanguage, _$UpdateEngLanguage];

  @override
  final String wireName = r'UpdateEngLanguage';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    UpdateEngLanguage object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.languageLevelType != null) {
      yield r'language_level_type';
      yield serializers.serialize(
        object.languageLevelType,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.languageCode != null) {
      yield r'language_code';
      yield serializers.serialize(
        object.languageCode,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    UpdateEngLanguage object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required UpdateEngLanguageBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'language_level_type':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.languageLevelType = valueDes;
          break;
        case r'language_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.languageCode = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  UpdateEngLanguage deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = UpdateEngLanguageBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

