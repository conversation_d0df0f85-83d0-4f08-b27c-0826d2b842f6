//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'check_captcha_require_in_login.g.dart';

/// CheckCaptchaRequireInLogin
///
/// Properties:
/// * [email] 
@BuiltValue()
abstract class CheckCaptchaRequireInLogin implements Built<CheckCaptchaRequireInLogin, CheckCaptchaRequireInLoginBuilder> {
  @BuiltValueField(wireName: r'email')
  String get email;

  CheckCaptchaRequireInLogin._();

  factory CheckCaptchaRequireInLogin([void updates(CheckCaptchaRequireInLoginBuilder b)]) = _$CheckCaptchaRequireInLogin;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(CheckCaptchaRequireInLoginBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<CheckCaptchaRequireInLogin> get serializer => _$CheckCaptchaRequireInLoginSerializer();
}

class _$CheckCaptchaRequireInLoginSerializer implements PrimitiveSerializer<CheckCaptchaRequireInLogin> {
  @override
  final Iterable<Type> types = const [CheckCaptchaRequireInLogin, _$CheckCaptchaRequireInLogin];

  @override
  final String wireName = r'CheckCaptchaRequireInLogin';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    CheckCaptchaRequireInLogin object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'email';
    yield serializers.serialize(
      object.email,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    CheckCaptchaRequireInLogin object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required CheckCaptchaRequireInLoginBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'email':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.email = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  CheckCaptchaRequireInLogin deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = CheckCaptchaRequireInLoginBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

