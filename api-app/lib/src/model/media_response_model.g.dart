// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'media_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$MediaResponseModel extends MediaResponseModel {
  @override
  final String message;
  @override
  final Image data;
  @override
  final BuiltList<ErrorDetail> errors;

  factory _$MediaResponseModel(
          [void Function(MediaResponseModelBuilder)? updates]) =>
      (new MediaResponseModelBuilder()..update(updates))._build();

  _$MediaResponseModel._(
      {required this.message, required this.data, required this.errors})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        message, r'MediaResponseModel', 'message');
    BuiltValueNullFieldError.checkNotNull(data, r'MediaResponseModel', 'data');
    BuiltValueNullFieldError.checkNotNull(
        errors, r'MediaResponseModel', 'errors');
  }

  @override
  MediaResponseModel rebuild(
          void Function(MediaResponseModelBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  MediaResponseModelBuilder toBuilder() =>
      new MediaResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is MediaResponseModel &&
        message == other.message &&
        data == other.data &&
        errors == other.errors;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'MediaResponseModel')
          ..add('message', message)
          ..add('data', data)
          ..add('errors', errors))
        .toString();
  }
}

class MediaResponseModelBuilder
    implements Builder<MediaResponseModel, MediaResponseModelBuilder> {
  _$MediaResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  ImageBuilder? _data;
  ImageBuilder get data => _$this._data ??= new ImageBuilder();
  set data(ImageBuilder? data) => _$this._data = data;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  MediaResponseModelBuilder() {
    MediaResponseModel._defaults(this);
  }

  MediaResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _data = $v.data.toBuilder();
      _errors = $v.errors.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(MediaResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$MediaResponseModel;
  }

  @override
  void update(void Function(MediaResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  MediaResponseModel build() => _build();

  _$MediaResponseModel _build() {
    _$MediaResponseModel _$result;
    try {
      _$result = _$v ??
          new _$MediaResponseModel._(
            message: BuiltValueNullFieldError.checkNotNull(
                message, r'MediaResponseModel', 'message'),
            data: data.build(),
            errors: errors.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        data.build();
        _$failedField = 'errors';
        errors.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'MediaResponseModel', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
