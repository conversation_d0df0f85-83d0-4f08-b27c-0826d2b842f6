// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_company_apply_details_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GeneralCompanyApplyDetailsResponseModel
    extends GeneralCompanyApplyDetailsResponseModel {
  @override
  final String? message;
  @override
  final BuiltList<ErrorDetail>? errors;
  @override
  final GeneralCompanyGetApplyDetails data;

  factory _$GeneralCompanyApplyDetailsResponseModel(
          [void Function(GeneralCompanyApplyDetailsResponseModelBuilder)?
              updates]) =>
      (new GeneralCompanyApplyDetailsResponseModelBuilder()..update(updates))
          ._build();

  _$GeneralCompanyApplyDetailsResponseModel._(
      {this.message, this.errors, required this.data})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'GeneralCompanyApplyDetailsResponseModel', 'data');
  }

  @override
  GeneralCompanyApplyDetailsResponseModel rebuild(
          void Function(GeneralCompanyApplyDetailsResponseModelBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GeneralCompanyApplyDetailsResponseModelBuilder toBuilder() =>
      new GeneralCompanyApplyDetailsResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GeneralCompanyApplyDetailsResponseModel &&
        message == other.message &&
        errors == other.errors &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'GeneralCompanyApplyDetailsResponseModel')
          ..add('message', message)
          ..add('errors', errors)
          ..add('data', data))
        .toString();
  }
}

class GeneralCompanyApplyDetailsResponseModelBuilder
    implements
        Builder<GeneralCompanyApplyDetailsResponseModel,
            GeneralCompanyApplyDetailsResponseModelBuilder> {
  _$GeneralCompanyApplyDetailsResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  GeneralCompanyGetApplyDetailsBuilder? _data;
  GeneralCompanyGetApplyDetailsBuilder get data =>
      _$this._data ??= new GeneralCompanyGetApplyDetailsBuilder();
  set data(GeneralCompanyGetApplyDetailsBuilder? data) => _$this._data = data;

  GeneralCompanyApplyDetailsResponseModelBuilder() {
    GeneralCompanyApplyDetailsResponseModel._defaults(this);
  }

  GeneralCompanyApplyDetailsResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _errors = $v.errors?.toBuilder();
      _data = $v.data.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GeneralCompanyApplyDetailsResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GeneralCompanyApplyDetailsResponseModel;
  }

  @override
  void update(
      void Function(GeneralCompanyApplyDetailsResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GeneralCompanyApplyDetailsResponseModel build() => _build();

  _$GeneralCompanyApplyDetailsResponseModel _build() {
    _$GeneralCompanyApplyDetailsResponseModel _$result;
    try {
      _$result = _$v ??
          new _$GeneralCompanyApplyDetailsResponseModel._(
            message: message,
            errors: _errors?.build(),
            data: data.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'errors';
        _errors?.build();
        _$failedField = 'data';
        data.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'GeneralCompanyApplyDetailsResponseModel',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
