// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'self_assessment_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$SelfAssessmentResponseModel extends SelfAssessmentResponseModel {
  @override
  final String? message;
  @override
  final SelfAssessmentData data;
  @override
  final BuiltList<String>? errors;

  factory _$SelfAssessmentResponseModel(
          [void Function(SelfAssessmentResponseModelBuilder)? updates]) =>
      (new SelfAssessmentResponseModelBuilder()..update(updates))._build();

  _$SelfAssessmentResponseModel._(
      {this.message, required this.data, this.errors})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'SelfAssessmentResponseModel', 'data');
  }

  @override
  SelfAssessmentResponseModel rebuild(
          void Function(SelfAssessmentResponseModelBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SelfAssessmentResponseModelBuilder toBuilder() =>
      new SelfAssessmentResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SelfAssessmentResponseModel &&
        message == other.message &&
        data == other.data &&
        errors == other.errors;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SelfAssessmentResponseModel')
          ..add('message', message)
          ..add('data', data)
          ..add('errors', errors))
        .toString();
  }
}

class SelfAssessmentResponseModelBuilder
    implements
        Builder<SelfAssessmentResponseModel,
            SelfAssessmentResponseModelBuilder> {
  _$SelfAssessmentResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  SelfAssessmentDataBuilder? _data;
  SelfAssessmentDataBuilder get data =>
      _$this._data ??= new SelfAssessmentDataBuilder();
  set data(SelfAssessmentDataBuilder? data) => _$this._data = data;

  ListBuilder<String>? _errors;
  ListBuilder<String> get errors =>
      _$this._errors ??= new ListBuilder<String>();
  set errors(ListBuilder<String>? errors) => _$this._errors = errors;

  SelfAssessmentResponseModelBuilder() {
    SelfAssessmentResponseModel._defaults(this);
  }

  SelfAssessmentResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _data = $v.data.toBuilder();
      _errors = $v.errors?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SelfAssessmentResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$SelfAssessmentResponseModel;
  }

  @override
  void update(void Function(SelfAssessmentResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SelfAssessmentResponseModel build() => _build();

  _$SelfAssessmentResponseModel _build() {
    _$SelfAssessmentResponseModel _$result;
    try {
      _$result = _$v ??
          new _$SelfAssessmentResponseModel._(
            message: message,
            data: data.build(),
            errors: _errors?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        data.build();
        _$failedField = 'errors';
        _errors?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'SelfAssessmentResponseModel', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
