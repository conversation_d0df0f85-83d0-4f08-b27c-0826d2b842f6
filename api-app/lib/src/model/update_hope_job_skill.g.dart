// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_hope_job_skill.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UpdateHopeJobSkill extends UpdateHopeJobSkill {
  @override
  final String? jobCode;
  @override
  final String? skillCode;

  factory _$UpdateHopeJobSkill(
          [void Function(UpdateHopeJobSkillBuilder)? updates]) =>
      (new UpdateHopeJobSkillBuilder()..update(updates))._build();

  _$UpdateHopeJobSkill._({this.jobCode, this.skillCode}) : super._();

  @override
  UpdateHopeJobSkill rebuild(
          void Function(UpdateHopeJobSkillBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UpdateHopeJobSkillBuilder toBuilder() =>
      new UpdateHopeJobSkillBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UpdateHopeJobSkill &&
        jobCode == other.jobCode &&
        skillCode == other.skillCode;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, jobCode.hashCode);
    _$hash = $jc(_$hash, skillCode.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UpdateHopeJobSkill')
          ..add('jobCode', jobCode)
          ..add('skillCode', skillCode))
        .toString();
  }
}

class UpdateHopeJobSkillBuilder
    implements Builder<UpdateHopeJobSkill, UpdateHopeJobSkillBuilder> {
  _$UpdateHopeJobSkill? _$v;

  String? _jobCode;
  String? get jobCode => _$this._jobCode;
  set jobCode(String? jobCode) => _$this._jobCode = jobCode;

  String? _skillCode;
  String? get skillCode => _$this._skillCode;
  set skillCode(String? skillCode) => _$this._skillCode = skillCode;

  UpdateHopeJobSkillBuilder() {
    UpdateHopeJobSkill._defaults(this);
  }

  UpdateHopeJobSkillBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _jobCode = $v.jobCode;
      _skillCode = $v.skillCode;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UpdateHopeJobSkill other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$UpdateHopeJobSkill;
  }

  @override
  void update(void Function(UpdateHopeJobSkillBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UpdateHopeJobSkill build() => _build();

  _$UpdateHopeJobSkill _build() {
    final _$result = _$v ??
        new _$UpdateHopeJobSkill._(
          jobCode: jobCode,
          skillCode: skillCode,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
