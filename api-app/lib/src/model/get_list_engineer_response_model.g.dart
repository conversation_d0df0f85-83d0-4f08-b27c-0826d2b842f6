// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_list_engineer_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GetListEngineerResponseModel extends GetListEngineerResponseModel {
  @override
  final String? message;
  @override
  final PagingGetListEngineer data;
  @override
  final BuiltList<ErrorDetail>? errors;

  factory _$GetListEngineerResponseModel(
          [void Function(GetListEngineerResponseModelBuilder)? updates]) =>
      (new GetListEngineerResponseModelBuilder()..update(updates))._build();

  _$GetListEngineerResponseModel._(
      {this.message, required this.data, this.errors})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'GetListEngineerResponseModel', 'data');
  }

  @override
  GetListEngineerResponseModel rebuild(
          void Function(GetListEngineerResponseModelBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GetListEngineerResponseModelBuilder toBuilder() =>
      new GetListEngineerResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GetListEngineerResponseModel &&
        message == other.message &&
        data == other.data &&
        errors == other.errors;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GetListEngineerResponseModel')
          ..add('message', message)
          ..add('data', data)
          ..add('errors', errors))
        .toString();
  }
}

class GetListEngineerResponseModelBuilder
    implements
        Builder<GetListEngineerResponseModel,
            GetListEngineerResponseModelBuilder> {
  _$GetListEngineerResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  PagingGetListEngineerBuilder? _data;
  PagingGetListEngineerBuilder get data =>
      _$this._data ??= new PagingGetListEngineerBuilder();
  set data(PagingGetListEngineerBuilder? data) => _$this._data = data;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  GetListEngineerResponseModelBuilder() {
    GetListEngineerResponseModel._defaults(this);
  }

  GetListEngineerResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _data = $v.data.toBuilder();
      _errors = $v.errors?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GetListEngineerResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GetListEngineerResponseModel;
  }

  @override
  void update(void Function(GetListEngineerResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GetListEngineerResponseModel build() => _build();

  _$GetListEngineerResponseModel _build() {
    _$GetListEngineerResponseModel _$result;
    try {
      _$result = _$v ??
          new _$GetListEngineerResponseModel._(
            message: message,
            data: data.build(),
            errors: _errors?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        data.build();
        _$failedField = 'errors';
        _errors?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'GetListEngineerResponseModel', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
