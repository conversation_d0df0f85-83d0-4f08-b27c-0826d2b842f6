//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/group_chat.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'list_group_chat.g.dart';

/// ListGroupChat
///
/// Properties:
/// * [next] 
/// * [previous] 
/// * [results] 
/// * [totalCount] 
@BuiltValue()
abstract class ListGroupChat implements Built<ListGroupChat, ListGroupChatBuilder> {
  @BuiltValueField(wireName: r'next')
  String? get next;

  @BuiltValueField(wireName: r'previous')
  String? get previous;

  @BuiltValueField(wireName: r'results')
  BuiltList<GroupChat> get results;

  @BuiltValueField(wireName: r'total_count')
  int get totalCount;

  ListGroupChat._();

  factory ListGroupChat([void updates(ListGroupChatBuilder b)]) = _$ListGroupChat;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(ListGroupChatBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<ListGroupChat> get serializer => _$ListGroupChatSerializer();
}

class _$ListGroupChatSerializer implements PrimitiveSerializer<ListGroupChat> {
  @override
  final Iterable<Type> types = const [ListGroupChat, _$ListGroupChat];

  @override
  final String wireName = r'ListGroupChat';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    ListGroupChat object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'next';
    yield object.next == null ? null : serializers.serialize(
      object.next,
      specifiedType: const FullType.nullable(String),
    );
    yield r'previous';
    yield object.previous == null ? null : serializers.serialize(
      object.previous,
      specifiedType: const FullType.nullable(String),
    );
    yield r'results';
    yield serializers.serialize(
      object.results,
      specifiedType: const FullType(BuiltList, [FullType(GroupChat)]),
    );
    yield r'total_count';
    yield serializers.serialize(
      object.totalCount,
      specifiedType: const FullType(int),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    ListGroupChat object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required ListGroupChatBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'next':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.next = valueDes;
          break;
        case r'previous':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.previous = valueDes;
          break;
        case r'results':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(GroupChat)]),
          ) as BuiltList<GroupChat>;
          result.results.replace(valueDes);
          break;
        case r'total_count':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.totalCount = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  ListGroupChat deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = ListGroupChatBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

