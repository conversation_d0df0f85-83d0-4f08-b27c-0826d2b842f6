// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'list_notify_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ListNotifyResponseModel extends ListNotifyResponseModel {
  @override
  final String? message;
  @override
  final BuiltList<Notify> data;
  @override
  final BuiltList<String>? errors;

  factory _$ListNotifyResponseModel(
          [void Function(ListNotifyResponseModelBuilder)? updates]) =>
      (new ListNotifyResponseModelBuilder()..update(updates))._build();

  _$ListNotifyResponseModel._({this.message, required this.data, this.errors})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'ListNotifyResponseModel', 'data');
  }

  @override
  ListNotifyResponseModel rebuild(
          void Function(ListNotifyResponseModelBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ListNotifyResponseModelBuilder toBuilder() =>
      new ListNotifyResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ListNotifyResponseModel &&
        message == other.message &&
        data == other.data &&
        errors == other.errors;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ListNotifyResponseModel')
          ..add('message', message)
          ..add('data', data)
          ..add('errors', errors))
        .toString();
  }
}

class ListNotifyResponseModelBuilder
    implements
        Builder<ListNotifyResponseModel, ListNotifyResponseModelBuilder> {
  _$ListNotifyResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  ListBuilder<Notify>? _data;
  ListBuilder<Notify> get data => _$this._data ??= new ListBuilder<Notify>();
  set data(ListBuilder<Notify>? data) => _$this._data = data;

  ListBuilder<String>? _errors;
  ListBuilder<String> get errors =>
      _$this._errors ??= new ListBuilder<String>();
  set errors(ListBuilder<String>? errors) => _$this._errors = errors;

  ListNotifyResponseModelBuilder() {
    ListNotifyResponseModel._defaults(this);
  }

  ListNotifyResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _data = $v.data.toBuilder();
      _errors = $v.errors?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ListNotifyResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$ListNotifyResponseModel;
  }

  @override
  void update(void Function(ListNotifyResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ListNotifyResponseModel build() => _build();

  _$ListNotifyResponseModel _build() {
    _$ListNotifyResponseModel _$result;
    try {
      _$result = _$v ??
          new _$ListNotifyResponseModel._(
            message: message,
            data: data.build(),
            errors: _errors?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        data.build();
        _$failedField = 'errors';
        _errors?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'ListNotifyResponseModel', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
