//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'captcha_and_user_status.g.dart';

/// CaptchaAndUserStatus
///
/// Properties:
/// * [requireCaptcha] 
/// * [isUserVerified] 
@BuiltValue()
abstract class CaptchaAndUserStatus implements Built<CaptchaAndUserStatus, CaptchaAndUserStatusBuilder> {
  @BuiltValueField(wireName: r'require_captcha')
  bool get requireCaptcha;

  @BuiltValueField(wireName: r'is_user_verified')
  bool get isUserVerified;

  CaptchaAndUserStatus._();

  factory CaptchaAndUserStatus([void updates(CaptchaAndUserStatusBuilder b)]) = _$CaptchaAndUserStatus;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(CaptchaAndUserStatusBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<CaptchaAndUserStatus> get serializer => _$CaptchaAndUserStatusSerializer();
}

class _$CaptchaAndUserStatusSerializer implements PrimitiveSerializer<CaptchaAndUserStatus> {
  @override
  final Iterable<Type> types = const [CaptchaAndUserStatus, _$CaptchaAndUserStatus];

  @override
  final String wireName = r'CaptchaAndUserStatus';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    CaptchaAndUserStatus object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'require_captcha';
    yield serializers.serialize(
      object.requireCaptcha,
      specifiedType: const FullType(bool),
    );
    yield r'is_user_verified';
    yield serializers.serialize(
      object.isUserVerified,
      specifiedType: const FullType(bool),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    CaptchaAndUserStatus object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required CaptchaAndUserStatusBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'require_captcha':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(bool),
          ) as bool;
          result.requireCaptcha = valueDes;
          break;
        case r'is_user_verified':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(bool),
          ) as bool;
          result.isUserVerified = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  CaptchaAndUserStatus deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = CaptchaAndUserStatusBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

