// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_engineer.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UpdateEngineer extends UpdateEngineer {
  @override
  final int? employmentStatus;
  @override
  final String? salesMemo;

  factory _$UpdateEngineer([void Function(UpdateEngineerBuilder)? updates]) =>
      (new UpdateEngineerBuilder()..update(updates))._build();

  _$UpdateEngineer._({this.employmentStatus, this.salesMemo}) : super._();

  @override
  UpdateEngineer rebuild(void Function(UpdateEngineerBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UpdateEngineerBuilder toBuilder() =>
      new UpdateEngineerBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UpdateEngineer &&
        employmentStatus == other.employmentStatus &&
        salesMemo == other.salesMemo;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, employmentStatus.hashCode);
    _$hash = $jc(_$hash, salesMemo.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UpdateEngineer')
          ..add('employmentStatus', employmentStatus)
          ..add('salesMemo', salesMemo))
        .toString();
  }
}

class UpdateEngineerBuilder
    implements Builder<UpdateEngineer, UpdateEngineerBuilder> {
  _$UpdateEngineer? _$v;

  int? _employmentStatus;
  int? get employmentStatus => _$this._employmentStatus;
  set employmentStatus(int? employmentStatus) =>
      _$this._employmentStatus = employmentStatus;

  String? _salesMemo;
  String? get salesMemo => _$this._salesMemo;
  set salesMemo(String? salesMemo) => _$this._salesMemo = salesMemo;

  UpdateEngineerBuilder() {
    UpdateEngineer._defaults(this);
  }

  UpdateEngineerBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _employmentStatus = $v.employmentStatus;
      _salesMemo = $v.salesMemo;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UpdateEngineer other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$UpdateEngineer;
  }

  @override
  void update(void Function(UpdateEngineerBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UpdateEngineer build() => _build();

  _$UpdateEngineer _build() {
    final _$result = _$v ??
        new _$UpdateEngineer._(
          employmentStatus: employmentStatus,
          salesMemo: salesMemo,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
