// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'request_interview_success.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$RequestInterviewSuccess extends RequestInterviewSuccess {
  @override
  final int applyId;

  factory _$RequestInterviewSuccess(
          [void Function(RequestInterviewSuccessBuilder)? updates]) =>
      (new RequestInterviewSuccessBuilder()..update(updates))._build();

  _$RequestInterviewSuccess._({required this.applyId}) : super._() {
    BuiltValueNullFieldError.checkNotNull(
        applyId, r'RequestInterviewSuccess', 'applyId');
  }

  @override
  RequestInterviewSuccess rebuild(
          void Function(RequestInterviewSuccessBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  RequestInterviewSuccessBuilder toBuilder() =>
      new RequestInterviewSuccessBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is RequestInterviewSuccess && applyId == other.applyId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, applyId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'RequestInterviewSuccess')
          ..add('applyId', applyId))
        .toString();
  }
}

class RequestInterviewSuccessBuilder
    implements
        Builder<RequestInterviewSuccess, RequestInterviewSuccessBuilder> {
  _$RequestInterviewSuccess? _$v;

  int? _applyId;
  int? get applyId => _$this._applyId;
  set applyId(int? applyId) => _$this._applyId = applyId;

  RequestInterviewSuccessBuilder() {
    RequestInterviewSuccess._defaults(this);
  }

  RequestInterviewSuccessBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _applyId = $v.applyId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(RequestInterviewSuccess other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$RequestInterviewSuccess;
  }

  @override
  void update(void Function(RequestInterviewSuccessBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  RequestInterviewSuccess build() => _build();

  _$RequestInterviewSuccess _build() {
    final _$result = _$v ??
        new _$RequestInterviewSuccess._(
          applyId: BuiltValueNullFieldError.checkNotNull(
              applyId, r'RequestInterviewSuccess', 'applyId'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
