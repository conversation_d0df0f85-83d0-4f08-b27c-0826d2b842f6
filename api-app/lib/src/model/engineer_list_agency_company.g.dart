// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'engineer_list_agency_company.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$EngineerListAgencyCompany extends EngineerListAgencyCompany {
  @override
  final int? companyId;
  @override
  final int userType;
  @override
  final String? name;
  @override
  final String? tel;
  @override
  final String? logoImagePath;
  @override
  final String? contactMail;
  @override
  final double? agentFee;
  @override
  final String? agentFeeCurrCode;

  factory _$EngineerListAgencyCompany(
          [void Function(EngineerListAgencyCompanyBuilder)? updates]) =>
      (new EngineerListAgencyCompanyBuilder()..update(updates))._build();

  _$EngineerListAgencyCompany._(
      {this.companyId,
      required this.userType,
      this.name,
      this.tel,
      this.logoImagePath,
      this.contactMail,
      this.agentFee,
      this.agentFeeCurrCode})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        userType, r'EngineerListAgencyCompany', 'userType');
  }

  @override
  EngineerListAgencyCompany rebuild(
          void Function(EngineerListAgencyCompanyBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  EngineerListAgencyCompanyBuilder toBuilder() =>
      new EngineerListAgencyCompanyBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is EngineerListAgencyCompany &&
        companyId == other.companyId &&
        userType == other.userType &&
        name == other.name &&
        tel == other.tel &&
        logoImagePath == other.logoImagePath &&
        contactMail == other.contactMail &&
        agentFee == other.agentFee &&
        agentFeeCurrCode == other.agentFeeCurrCode;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, companyId.hashCode);
    _$hash = $jc(_$hash, userType.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, tel.hashCode);
    _$hash = $jc(_$hash, logoImagePath.hashCode);
    _$hash = $jc(_$hash, contactMail.hashCode);
    _$hash = $jc(_$hash, agentFee.hashCode);
    _$hash = $jc(_$hash, agentFeeCurrCode.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'EngineerListAgencyCompany')
          ..add('companyId', companyId)
          ..add('userType', userType)
          ..add('name', name)
          ..add('tel', tel)
          ..add('logoImagePath', logoImagePath)
          ..add('contactMail', contactMail)
          ..add('agentFee', agentFee)
          ..add('agentFeeCurrCode', agentFeeCurrCode))
        .toString();
  }
}

class EngineerListAgencyCompanyBuilder
    implements
        Builder<EngineerListAgencyCompany, EngineerListAgencyCompanyBuilder> {
  _$EngineerListAgencyCompany? _$v;

  int? _companyId;
  int? get companyId => _$this._companyId;
  set companyId(int? companyId) => _$this._companyId = companyId;

  int? _userType;
  int? get userType => _$this._userType;
  set userType(int? userType) => _$this._userType = userType;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _tel;
  String? get tel => _$this._tel;
  set tel(String? tel) => _$this._tel = tel;

  String? _logoImagePath;
  String? get logoImagePath => _$this._logoImagePath;
  set logoImagePath(String? logoImagePath) =>
      _$this._logoImagePath = logoImagePath;

  String? _contactMail;
  String? get contactMail => _$this._contactMail;
  set contactMail(String? contactMail) => _$this._contactMail = contactMail;

  double? _agentFee;
  double? get agentFee => _$this._agentFee;
  set agentFee(double? agentFee) => _$this._agentFee = agentFee;

  String? _agentFeeCurrCode;
  String? get agentFeeCurrCode => _$this._agentFeeCurrCode;
  set agentFeeCurrCode(String? agentFeeCurrCode) =>
      _$this._agentFeeCurrCode = agentFeeCurrCode;

  EngineerListAgencyCompanyBuilder() {
    EngineerListAgencyCompany._defaults(this);
  }

  EngineerListAgencyCompanyBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _companyId = $v.companyId;
      _userType = $v.userType;
      _name = $v.name;
      _tel = $v.tel;
      _logoImagePath = $v.logoImagePath;
      _contactMail = $v.contactMail;
      _agentFee = $v.agentFee;
      _agentFeeCurrCode = $v.agentFeeCurrCode;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(EngineerListAgencyCompany other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$EngineerListAgencyCompany;
  }

  @override
  void update(void Function(EngineerListAgencyCompanyBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  EngineerListAgencyCompany build() => _build();

  _$EngineerListAgencyCompany _build() {
    final _$result = _$v ??
        new _$EngineerListAgencyCompany._(
          companyId: companyId,
          userType: BuiltValueNullFieldError.checkNotNull(
              userType, r'EngineerListAgencyCompany', 'userType'),
          name: name,
          tel: tel,
          logoImagePath: logoImagePath,
          contactMail: contactMail,
          agentFee: agentFee,
          agentFeeCurrCode: agentFeeCurrCode,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
