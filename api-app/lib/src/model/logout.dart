//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'logout.g.dart';

/// Logout
///
/// Properties:
/// * [refresh] 
@BuiltValue()
abstract class Logout implements Built<Logout, LogoutBuilder> {
  @BuiltValueField(wireName: r'refresh')
  String get refresh;

  Logout._();

  factory Logout([void updates(LogoutBuilder b)]) = _$Logout;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(LogoutBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<Logout> get serializer => _$LogoutSerializer();
}

class _$LogoutSerializer implements PrimitiveSerializer<Logout> {
  @override
  final Iterable<Type> types = const [Logout, _$Logout];

  @override
  final String wireName = r'Logout';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    Logout object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'refresh';
    yield serializers.serialize(
      object.refresh,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    Logout object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required LogoutBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'refresh':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.refresh = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  Logout deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = LogoutBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

