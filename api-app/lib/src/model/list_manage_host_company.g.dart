// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'list_manage_host_company.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ListManageHostCompany extends ListManageHostCompany {
  @override
  final String? next;
  @override
  final String? previous;
  @override
  final BuiltList<ManageHostCompany> results;

  factory _$ListManageHostCompany(
          [void Function(ListManageHostCompanyBuilder)? updates]) =>
      (new ListManageHostCompanyBuilder()..update(updates))._build();

  _$ListManageHostCompany._({this.next, this.previous, required this.results})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        results, r'ListManageHostCompany', 'results');
  }

  @override
  ListManageHostCompany rebuild(
          void Function(ListManageHostCompanyBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ListManageHostCompanyBuilder toBuilder() =>
      new ListManageHostCompanyBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ListManageHostCompany &&
        next == other.next &&
        previous == other.previous &&
        results == other.results;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, next.hashCode);
    _$hash = $jc(_$hash, previous.hashCode);
    _$hash = $jc(_$hash, results.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ListManageHostCompany')
          ..add('next', next)
          ..add('previous', previous)
          ..add('results', results))
        .toString();
  }
}

class ListManageHostCompanyBuilder
    implements Builder<ListManageHostCompany, ListManageHostCompanyBuilder> {
  _$ListManageHostCompany? _$v;

  String? _next;
  String? get next => _$this._next;
  set next(String? next) => _$this._next = next;

  String? _previous;
  String? get previous => _$this._previous;
  set previous(String? previous) => _$this._previous = previous;

  ListBuilder<ManageHostCompany>? _results;
  ListBuilder<ManageHostCompany> get results =>
      _$this._results ??= new ListBuilder<ManageHostCompany>();
  set results(ListBuilder<ManageHostCompany>? results) =>
      _$this._results = results;

  ListManageHostCompanyBuilder() {
    ListManageHostCompany._defaults(this);
  }

  ListManageHostCompanyBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _next = $v.next;
      _previous = $v.previous;
      _results = $v.results.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ListManageHostCompany other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$ListManageHostCompany;
  }

  @override
  void update(void Function(ListManageHostCompanyBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ListManageHostCompany build() => _build();

  _$ListManageHostCompany _build() {
    _$ListManageHostCompany _$result;
    try {
      _$result = _$v ??
          new _$ListManageHostCompany._(
            next: next,
            previous: previous,
            results: results.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'results';
        results.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'ListManageHostCompany', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
