// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_list_engineer.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GetListEngineer extends GetListEngineer {
  @override
  final String? profileImagePath;
  @override
  final String email;
  @override
  final String? firstName;
  @override
  final String? lastName;
  @override
  final String? countryCode;
  @override
  final DateTime? updated;
  @override
  final DateTime? created;
  @override
  final String? createdUser;
  @override
  final BuiltList<EngSkill>? skills;
  @override
  final int? userId;
  @override
  final String? lastAcademicName;
  @override
  final String? facebookUrl;
  @override
  final String? linkedinUrl;
  @override
  final String? whatsappUrl;
  @override
  final String? zaloId;
  @override
  final EngHope? requirements;
  @override
  final BuiltList<EngLanguage>? languages;
  @override
  final int? employmentStatus;
  @override
  final String? salesMemo;
  @override
  final int? isDataPolicyAccept;
  @override
  final String? tel;
  @override
  final String? internationalTel;

  factory _$GetListEngineer([void Function(GetListEngineerBuilder)? updates]) =>
      (new GetListEngineerBuilder()..update(updates))._build();

  _$GetListEngineer._(
      {this.profileImagePath,
      required this.email,
      this.firstName,
      this.lastName,
      this.countryCode,
      this.updated,
      this.created,
      this.createdUser,
      this.skills,
      this.userId,
      this.lastAcademicName,
      this.facebookUrl,
      this.linkedinUrl,
      this.whatsappUrl,
      this.zaloId,
      this.requirements,
      this.languages,
      this.employmentStatus,
      this.salesMemo,
      this.isDataPolicyAccept,
      this.tel,
      this.internationalTel})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(email, r'GetListEngineer', 'email');
  }

  @override
  GetListEngineer rebuild(void Function(GetListEngineerBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GetListEngineerBuilder toBuilder() =>
      new GetListEngineerBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GetListEngineer &&
        profileImagePath == other.profileImagePath &&
        email == other.email &&
        firstName == other.firstName &&
        lastName == other.lastName &&
        countryCode == other.countryCode &&
        updated == other.updated &&
        created == other.created &&
        createdUser == other.createdUser &&
        skills == other.skills &&
        userId == other.userId &&
        lastAcademicName == other.lastAcademicName &&
        facebookUrl == other.facebookUrl &&
        linkedinUrl == other.linkedinUrl &&
        whatsappUrl == other.whatsappUrl &&
        zaloId == other.zaloId &&
        requirements == other.requirements &&
        languages == other.languages &&
        employmentStatus == other.employmentStatus &&
        salesMemo == other.salesMemo &&
        isDataPolicyAccept == other.isDataPolicyAccept &&
        tel == other.tel &&
        internationalTel == other.internationalTel;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, profileImagePath.hashCode);
    _$hash = $jc(_$hash, email.hashCode);
    _$hash = $jc(_$hash, firstName.hashCode);
    _$hash = $jc(_$hash, lastName.hashCode);
    _$hash = $jc(_$hash, countryCode.hashCode);
    _$hash = $jc(_$hash, updated.hashCode);
    _$hash = $jc(_$hash, created.hashCode);
    _$hash = $jc(_$hash, createdUser.hashCode);
    _$hash = $jc(_$hash, skills.hashCode);
    _$hash = $jc(_$hash, userId.hashCode);
    _$hash = $jc(_$hash, lastAcademicName.hashCode);
    _$hash = $jc(_$hash, facebookUrl.hashCode);
    _$hash = $jc(_$hash, linkedinUrl.hashCode);
    _$hash = $jc(_$hash, whatsappUrl.hashCode);
    _$hash = $jc(_$hash, zaloId.hashCode);
    _$hash = $jc(_$hash, requirements.hashCode);
    _$hash = $jc(_$hash, languages.hashCode);
    _$hash = $jc(_$hash, employmentStatus.hashCode);
    _$hash = $jc(_$hash, salesMemo.hashCode);
    _$hash = $jc(_$hash, isDataPolicyAccept.hashCode);
    _$hash = $jc(_$hash, tel.hashCode);
    _$hash = $jc(_$hash, internationalTel.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GetListEngineer')
          ..add('profileImagePath', profileImagePath)
          ..add('email', email)
          ..add('firstName', firstName)
          ..add('lastName', lastName)
          ..add('countryCode', countryCode)
          ..add('updated', updated)
          ..add('created', created)
          ..add('createdUser', createdUser)
          ..add('skills', skills)
          ..add('userId', userId)
          ..add('lastAcademicName', lastAcademicName)
          ..add('facebookUrl', facebookUrl)
          ..add('linkedinUrl', linkedinUrl)
          ..add('whatsappUrl', whatsappUrl)
          ..add('zaloId', zaloId)
          ..add('requirements', requirements)
          ..add('languages', languages)
          ..add('employmentStatus', employmentStatus)
          ..add('salesMemo', salesMemo)
          ..add('isDataPolicyAccept', isDataPolicyAccept)
          ..add('tel', tel)
          ..add('internationalTel', internationalTel))
        .toString();
  }
}

class GetListEngineerBuilder
    implements Builder<GetListEngineer, GetListEngineerBuilder> {
  _$GetListEngineer? _$v;

  String? _profileImagePath;
  String? get profileImagePath => _$this._profileImagePath;
  set profileImagePath(String? profileImagePath) =>
      _$this._profileImagePath = profileImagePath;

  String? _email;
  String? get email => _$this._email;
  set email(String? email) => _$this._email = email;

  String? _firstName;
  String? get firstName => _$this._firstName;
  set firstName(String? firstName) => _$this._firstName = firstName;

  String? _lastName;
  String? get lastName => _$this._lastName;
  set lastName(String? lastName) => _$this._lastName = lastName;

  String? _countryCode;
  String? get countryCode => _$this._countryCode;
  set countryCode(String? countryCode) => _$this._countryCode = countryCode;

  DateTime? _updated;
  DateTime? get updated => _$this._updated;
  set updated(DateTime? updated) => _$this._updated = updated;

  DateTime? _created;
  DateTime? get created => _$this._created;
  set created(DateTime? created) => _$this._created = created;

  String? _createdUser;
  String? get createdUser => _$this._createdUser;
  set createdUser(String? createdUser) => _$this._createdUser = createdUser;

  ListBuilder<EngSkill>? _skills;
  ListBuilder<EngSkill> get skills =>
      _$this._skills ??= new ListBuilder<EngSkill>();
  set skills(ListBuilder<EngSkill>? skills) => _$this._skills = skills;

  int? _userId;
  int? get userId => _$this._userId;
  set userId(int? userId) => _$this._userId = userId;

  String? _lastAcademicName;
  String? get lastAcademicName => _$this._lastAcademicName;
  set lastAcademicName(String? lastAcademicName) =>
      _$this._lastAcademicName = lastAcademicName;

  String? _facebookUrl;
  String? get facebookUrl => _$this._facebookUrl;
  set facebookUrl(String? facebookUrl) => _$this._facebookUrl = facebookUrl;

  String? _linkedinUrl;
  String? get linkedinUrl => _$this._linkedinUrl;
  set linkedinUrl(String? linkedinUrl) => _$this._linkedinUrl = linkedinUrl;

  String? _whatsappUrl;
  String? get whatsappUrl => _$this._whatsappUrl;
  set whatsappUrl(String? whatsappUrl) => _$this._whatsappUrl = whatsappUrl;

  String? _zaloId;
  String? get zaloId => _$this._zaloId;
  set zaloId(String? zaloId) => _$this._zaloId = zaloId;

  EngHopeBuilder? _requirements;
  EngHopeBuilder get requirements =>
      _$this._requirements ??= new EngHopeBuilder();
  set requirements(EngHopeBuilder? requirements) =>
      _$this._requirements = requirements;

  ListBuilder<EngLanguage>? _languages;
  ListBuilder<EngLanguage> get languages =>
      _$this._languages ??= new ListBuilder<EngLanguage>();
  set languages(ListBuilder<EngLanguage>? languages) =>
      _$this._languages = languages;

  int? _employmentStatus;
  int? get employmentStatus => _$this._employmentStatus;
  set employmentStatus(int? employmentStatus) =>
      _$this._employmentStatus = employmentStatus;

  String? _salesMemo;
  String? get salesMemo => _$this._salesMemo;
  set salesMemo(String? salesMemo) => _$this._salesMemo = salesMemo;

  int? _isDataPolicyAccept;
  int? get isDataPolicyAccept => _$this._isDataPolicyAccept;
  set isDataPolicyAccept(int? isDataPolicyAccept) =>
      _$this._isDataPolicyAccept = isDataPolicyAccept;

  String? _tel;
  String? get tel => _$this._tel;
  set tel(String? tel) => _$this._tel = tel;

  String? _internationalTel;
  String? get internationalTel => _$this._internationalTel;
  set internationalTel(String? internationalTel) =>
      _$this._internationalTel = internationalTel;

  GetListEngineerBuilder() {
    GetListEngineer._defaults(this);
  }

  GetListEngineerBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _profileImagePath = $v.profileImagePath;
      _email = $v.email;
      _firstName = $v.firstName;
      _lastName = $v.lastName;
      _countryCode = $v.countryCode;
      _updated = $v.updated;
      _created = $v.created;
      _createdUser = $v.createdUser;
      _skills = $v.skills?.toBuilder();
      _userId = $v.userId;
      _lastAcademicName = $v.lastAcademicName;
      _facebookUrl = $v.facebookUrl;
      _linkedinUrl = $v.linkedinUrl;
      _whatsappUrl = $v.whatsappUrl;
      _zaloId = $v.zaloId;
      _requirements = $v.requirements?.toBuilder();
      _languages = $v.languages?.toBuilder();
      _employmentStatus = $v.employmentStatus;
      _salesMemo = $v.salesMemo;
      _isDataPolicyAccept = $v.isDataPolicyAccept;
      _tel = $v.tel;
      _internationalTel = $v.internationalTel;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GetListEngineer other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GetListEngineer;
  }

  @override
  void update(void Function(GetListEngineerBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GetListEngineer build() => _build();

  _$GetListEngineer _build() {
    _$GetListEngineer _$result;
    try {
      _$result = _$v ??
          new _$GetListEngineer._(
            profileImagePath: profileImagePath,
            email: BuiltValueNullFieldError.checkNotNull(
                email, r'GetListEngineer', 'email'),
            firstName: firstName,
            lastName: lastName,
            countryCode: countryCode,
            updated: updated,
            created: created,
            createdUser: createdUser,
            skills: _skills?.build(),
            userId: userId,
            lastAcademicName: lastAcademicName,
            facebookUrl: facebookUrl,
            linkedinUrl: linkedinUrl,
            whatsappUrl: whatsappUrl,
            zaloId: zaloId,
            requirements: _requirements?.build(),
            languages: _languages?.build(),
            employmentStatus: employmentStatus,
            salesMemo: salesMemo,
            isDataPolicyAccept: isDataPolicyAccept,
            tel: tel,
            internationalTel: internationalTel,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'skills';
        _skills?.build();

        _$failedField = 'requirements';
        _requirements?.build();
        _$failedField = 'languages';
        _languages?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'GetListEngineer', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
