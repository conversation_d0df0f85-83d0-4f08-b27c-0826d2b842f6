//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/engineer_best_company.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'engineer_list_best_company_pagination.g.dart';

/// EngineerListBestCompanyPagination
///
/// Properties:
/// * [next] 
/// * [previous] 
/// * [results] 
@BuiltValue()
abstract class EngineerListBestCompanyPagination implements Built<EngineerListBestCompanyPagination, EngineerListBestCompanyPaginationBuilder> {
  @BuiltValueField(wireName: r'next')
  String? get next;

  @BuiltValueField(wireName: r'previous')
  String? get previous;

  @BuiltValueField(wireName: r'results')
  BuiltList<EngineerBestCompany> get results;

  EngineerListBestCompanyPagination._();

  factory EngineerListBestCompanyPagination([void updates(EngineerListBestCompanyPaginationBuilder b)]) = _$EngineerListBestCompanyPagination;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(EngineerListBestCompanyPaginationBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<EngineerListBestCompanyPagination> get serializer => _$EngineerListBestCompanyPaginationSerializer();
}

class _$EngineerListBestCompanyPaginationSerializer implements PrimitiveSerializer<EngineerListBestCompanyPagination> {
  @override
  final Iterable<Type> types = const [EngineerListBestCompanyPagination, _$EngineerListBestCompanyPagination];

  @override
  final String wireName = r'EngineerListBestCompanyPagination';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    EngineerListBestCompanyPagination object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'next';
    yield object.next == null ? null : serializers.serialize(
      object.next,
      specifiedType: const FullType.nullable(String),
    );
    yield r'previous';
    yield object.previous == null ? null : serializers.serialize(
      object.previous,
      specifiedType: const FullType.nullable(String),
    );
    yield r'results';
    yield serializers.serialize(
      object.results,
      specifiedType: const FullType(BuiltList, [FullType(EngineerBestCompany)]),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    EngineerListBestCompanyPagination object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required EngineerListBestCompanyPaginationBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'next':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.next = valueDes;
          break;
        case r'previous':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.previous = valueDes;
          break;
        case r'results':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(EngineerBestCompany)]),
          ) as BuiltList<EngineerBestCompany>;
          result.results.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  EngineerListBestCompanyPagination deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = EngineerListBestCompanyPaginationBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

