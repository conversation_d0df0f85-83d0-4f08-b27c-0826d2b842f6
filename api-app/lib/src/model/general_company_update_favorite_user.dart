//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'general_company_update_favorite_user.g.dart';

/// GeneralCompanyUpdateFavoriteUser
///
/// Properties:
/// * [userId] 
/// * [isFavorite] 
@BuiltValue()
abstract class GeneralCompanyUpdateFavoriteUser implements Built<GeneralCompanyUpdateFavoriteUser, GeneralCompanyUpdateFavoriteUserBuilder> {
  @BuiltValueField(wireName: r'user_id')
  int get userId;

  @BuiltValueField(wireName: r'is_favorite')
  bool get isFavorite;

  GeneralCompanyUpdateFavoriteUser._();

  factory GeneralCompanyUpdateFavoriteUser([void updates(GeneralCompanyUpdateFavoriteUserBuilder b)]) = _$GeneralCompanyUpdateFavoriteUser;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(GeneralCompanyUpdateFavoriteUserBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<GeneralCompanyUpdateFavoriteUser> get serializer => _$GeneralCompanyUpdateFavoriteUserSerializer();
}

class _$GeneralCompanyUpdateFavoriteUserSerializer implements PrimitiveSerializer<GeneralCompanyUpdateFavoriteUser> {
  @override
  final Iterable<Type> types = const [GeneralCompanyUpdateFavoriteUser, _$GeneralCompanyUpdateFavoriteUser];

  @override
  final String wireName = r'GeneralCompanyUpdateFavoriteUser';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    GeneralCompanyUpdateFavoriteUser object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'user_id';
    yield serializers.serialize(
      object.userId,
      specifiedType: const FullType(int),
    );
    yield r'is_favorite';
    yield serializers.serialize(
      object.isFavorite,
      specifiedType: const FullType(bool),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    GeneralCompanyUpdateFavoriteUser object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required GeneralCompanyUpdateFavoriteUserBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'user_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.userId = valueDes;
          break;
        case r'is_favorite':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(bool),
          ) as bool;
          result.isFavorite = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  GeneralCompanyUpdateFavoriteUser deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = GeneralCompanyUpdateFavoriteUserBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

