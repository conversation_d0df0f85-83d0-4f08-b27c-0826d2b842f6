// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_video_social_source_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GetVideoSocialSourceResponseModel
    extends GetVideoSocialSourceResponseModel {
  @override
  final String data;

  factory _$GetVideoSocialSourceResponseModel(
          [void Function(GetVideoSocialSourceResponseModelBuilder)? updates]) =>
      (new GetVideoSocialSourceResponseModelBuilder()..update(updates))
          ._build();

  _$GetVideoSocialSourceResponseModel._({required this.data}) : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'GetVideoSocialSourceResponseModel', 'data');
  }

  @override
  GetVideoSocialSourceResponseModel rebuild(
          void Function(GetVideoSocialSourceResponseModelBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GetVideoSocialSourceResponseModelBuilder toBuilder() =>
      new GetVideoSocialSourceResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GetVideoSocialSourceResponseModel && data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GetVideoSocialSourceResponseModel')
          ..add('data', data))
        .toString();
  }
}

class GetVideoSocialSourceResponseModelBuilder
    implements
        Builder<GetVideoSocialSourceResponseModel,
            GetVideoSocialSourceResponseModelBuilder> {
  _$GetVideoSocialSourceResponseModel? _$v;

  String? _data;
  String? get data => _$this._data;
  set data(String? data) => _$this._data = data;

  GetVideoSocialSourceResponseModelBuilder() {
    GetVideoSocialSourceResponseModel._defaults(this);
  }

  GetVideoSocialSourceResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _data = $v.data;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GetVideoSocialSourceResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GetVideoSocialSourceResponseModel;
  }

  @override
  void update(
      void Function(GetVideoSocialSourceResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GetVideoSocialSourceResponseModel build() => _build();

  _$GetVideoSocialSourceResponseModel _build() {
    final _$result = _$v ??
        new _$GetVideoSocialSourceResponseModel._(
          data: BuiltValueNullFieldError.checkNotNull(
              data, r'GetVideoSocialSourceResponseModel', 'data'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
