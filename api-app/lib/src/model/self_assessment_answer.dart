//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'self_assessment_answer.g.dart';

/// SelfAssessmentAnswer
///
/// Properties:
/// * [listAnswer] 
@BuiltValue()
abstract class SelfAssessmentAnswer implements Built<SelfAssessmentAnswer, SelfAssessmentAnswerBuilder> {
  @BuiltValueField(wireName: r'list_answer')
  BuiltList<String?> get listAnswer;

  SelfAssessmentAnswer._();

  factory SelfAssessmentAnswer([void updates(SelfAssessmentAnswerBuilder b)]) = _$SelfAssessmentAnswer;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(SelfAssessmentAnswerBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<SelfAssessmentAnswer> get serializer => _$SelfAssessmentAnswerSerializer();
}

class _$SelfAssessmentAnswerSerializer implements PrimitiveSerializer<SelfAssessmentAnswer> {
  @override
  final Iterable<Type> types = const [SelfAssessmentAnswer, _$SelfAssessmentAnswer];

  @override
  final String wireName = r'SelfAssessmentAnswer';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    SelfAssessmentAnswer object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'list_answer';
    yield serializers.serialize(
      object.listAnswer,
      specifiedType: const FullType(BuiltList, [FullType.nullable(String)]),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    SelfAssessmentAnswer object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required SelfAssessmentAnswerBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'list_answer':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType.nullable(String)]),
          ) as BuiltList<String?>;
          result.listAnswer.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  SelfAssessmentAnswer deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = SelfAssessmentAnswerBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

