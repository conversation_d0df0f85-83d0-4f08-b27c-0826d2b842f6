// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_company_list_filter_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GeneralCompanyListFilterResponseModel
    extends GeneralCompanyListFilterResponseModel {
  @override
  final String? message;
  @override
  final BuiltList<GeneralCompanyFilter> data;
  @override
  final BuiltList<ErrorDetail>? errors;

  factory _$GeneralCompanyListFilterResponseModel(
          [void Function(GeneralCompanyListFilterResponseModelBuilder)?
              updates]) =>
      (new GeneralCompanyListFilterResponseModelBuilder()..update(updates))
          ._build();

  _$GeneralCompanyListFilterResponseModel._(
      {this.message, required this.data, this.errors})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'GeneralCompanyListFilterResponseModel', 'data');
  }

  @override
  GeneralCompanyListFilterResponseModel rebuild(
          void Function(GeneralCompanyListFilterResponseModelBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GeneralCompanyListFilterResponseModelBuilder toBuilder() =>
      new GeneralCompanyListFilterResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GeneralCompanyListFilterResponseModel &&
        message == other.message &&
        data == other.data &&
        errors == other.errors;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'GeneralCompanyListFilterResponseModel')
          ..add('message', message)
          ..add('data', data)
          ..add('errors', errors))
        .toString();
  }
}

class GeneralCompanyListFilterResponseModelBuilder
    implements
        Builder<GeneralCompanyListFilterResponseModel,
            GeneralCompanyListFilterResponseModelBuilder> {
  _$GeneralCompanyListFilterResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  ListBuilder<GeneralCompanyFilter>? _data;
  ListBuilder<GeneralCompanyFilter> get data =>
      _$this._data ??= new ListBuilder<GeneralCompanyFilter>();
  set data(ListBuilder<GeneralCompanyFilter>? data) => _$this._data = data;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  GeneralCompanyListFilterResponseModelBuilder() {
    GeneralCompanyListFilterResponseModel._defaults(this);
  }

  GeneralCompanyListFilterResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _data = $v.data.toBuilder();
      _errors = $v.errors?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GeneralCompanyListFilterResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GeneralCompanyListFilterResponseModel;
  }

  @override
  void update(
      void Function(GeneralCompanyListFilterResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GeneralCompanyListFilterResponseModel build() => _build();

  _$GeneralCompanyListFilterResponseModel _build() {
    _$GeneralCompanyListFilterResponseModel _$result;
    try {
      _$result = _$v ??
          new _$GeneralCompanyListFilterResponseModel._(
            message: message,
            data: data.build(),
            errors: _errors?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        data.build();
        _$failedField = 'errors';
        _errors?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'GeneralCompanyListFilterResponseModel',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
