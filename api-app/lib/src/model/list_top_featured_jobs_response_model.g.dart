// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'list_top_featured_jobs_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ListTopFeaturedJobsResponseModel
    extends ListTopFeaturedJobsResponseModel {
  @override
  final String? message;
  @override
  final BuiltList<FeaturedJob> data;
  @override
  final BuiltList<ErrorDetail>? errors;

  factory _$ListTopFeaturedJobsResponseModel(
          [void Function(ListTopFeaturedJobsResponseModelBuilder)? updates]) =>
      (new ListTopFeaturedJobsResponseModelBuilder()..update(updates))._build();

  _$ListTopFeaturedJobsResponseModel._(
      {this.message, required this.data, this.errors})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'ListTopFeaturedJobsResponseModel', 'data');
  }

  @override
  ListTopFeaturedJobsResponseModel rebuild(
          void Function(ListTopFeaturedJobsResponseModelBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ListTopFeaturedJobsResponseModelBuilder toBuilder() =>
      new ListTopFeaturedJobsResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ListTopFeaturedJobsResponseModel &&
        message == other.message &&
        data == other.data &&
        errors == other.errors;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ListTopFeaturedJobsResponseModel')
          ..add('message', message)
          ..add('data', data)
          ..add('errors', errors))
        .toString();
  }
}

class ListTopFeaturedJobsResponseModelBuilder
    implements
        Builder<ListTopFeaturedJobsResponseModel,
            ListTopFeaturedJobsResponseModelBuilder> {
  _$ListTopFeaturedJobsResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  ListBuilder<FeaturedJob>? _data;
  ListBuilder<FeaturedJob> get data =>
      _$this._data ??= new ListBuilder<FeaturedJob>();
  set data(ListBuilder<FeaturedJob>? data) => _$this._data = data;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  ListTopFeaturedJobsResponseModelBuilder() {
    ListTopFeaturedJobsResponseModel._defaults(this);
  }

  ListTopFeaturedJobsResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _data = $v.data.toBuilder();
      _errors = $v.errors?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ListTopFeaturedJobsResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$ListTopFeaturedJobsResponseModel;
  }

  @override
  void update(void Function(ListTopFeaturedJobsResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ListTopFeaturedJobsResponseModel build() => _build();

  _$ListTopFeaturedJobsResponseModel _build() {
    _$ListTopFeaturedJobsResponseModel _$result;
    try {
      _$result = _$v ??
          new _$ListTopFeaturedJobsResponseModel._(
            message: message,
            data: data.build(),
            errors: _errors?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        data.build();
        _$failedField = 'errors';
        _errors?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'ListTopFeaturedJobsResponseModel', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
