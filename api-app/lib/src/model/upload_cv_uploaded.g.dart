// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'upload_cv_uploaded.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UploadCVUploaded extends UploadCVUploaded {
  @override
  final String cvId;
  @override
  final JsonObject cvData;

  factory _$UploadCVUploaded(
          [void Function(UploadCVUploadedBuilder)? updates]) =>
      (new UploadCVUploadedBuilder()..update(updates))._build();

  _$UploadCVUploaded._({required this.cvId, required this.cvData}) : super._() {
    BuiltValueNullFieldError.checkNotNull(cvId, r'UploadCVUploaded', 'cvId');
    BuiltValueNullFieldError.checkNotNull(
        cvData, r'UploadCVUploaded', 'cvData');
  }

  @override
  UploadCVUploaded rebuild(void Function(UploadCVUploadedBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UploadCVUploadedBuilder toBuilder() =>
      new UploadCVUploadedBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UploadCVUploaded &&
        cvId == other.cvId &&
        cvData == other.cvData;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, cvId.hashCode);
    _$hash = $jc(_$hash, cvData.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UploadCVUploaded')
          ..add('cvId', cvId)
          ..add('cvData', cvData))
        .toString();
  }
}

class UploadCVUploadedBuilder
    implements Builder<UploadCVUploaded, UploadCVUploadedBuilder> {
  _$UploadCVUploaded? _$v;

  String? _cvId;
  String? get cvId => _$this._cvId;
  set cvId(String? cvId) => _$this._cvId = cvId;

  JsonObject? _cvData;
  JsonObject? get cvData => _$this._cvData;
  set cvData(JsonObject? cvData) => _$this._cvData = cvData;

  UploadCVUploadedBuilder() {
    UploadCVUploaded._defaults(this);
  }

  UploadCVUploadedBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _cvId = $v.cvId;
      _cvData = $v.cvData;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UploadCVUploaded other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$UploadCVUploaded;
  }

  @override
  void update(void Function(UploadCVUploadedBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UploadCVUploaded build() => _build();

  _$UploadCVUploaded _build() {
    final _$result = _$v ??
        new _$UploadCVUploaded._(
          cvId: BuiltValueNullFieldError.checkNotNull(
              cvId, r'UploadCVUploaded', 'cvId'),
          cvData: BuiltValueNullFieldError.checkNotNull(
              cvData, r'UploadCVUploaded', 'cvData'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
