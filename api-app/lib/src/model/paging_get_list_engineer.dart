//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/get_list_engineer.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'paging_get_list_engineer.g.dart';

/// PagingGetListEngineer
///
/// Properties:
/// * [next] 
/// * [previous] 
/// * [results] 
/// * [totalCount] 
@BuiltValue()
abstract class PagingGetListEngineer implements Built<PagingGetListEngineer, PagingGetListEngineerBuilder> {
  @BuiltValueField(wireName: r'next')
  String? get next;

  @BuiltValueField(wireName: r'previous')
  String? get previous;

  @BuiltValueField(wireName: r'results')
  BuiltList<GetListEngineer> get results;

  @BuiltValueField(wireName: r'total_count')
  int get totalCount;

  PagingGetListEngineer._();

  factory PagingGetListEngineer([void updates(PagingGetListEngineerBuilder b)]) = _$PagingGetListEngineer;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(PagingGetListEngineerBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<PagingGetListEngineer> get serializer => _$PagingGetListEngineerSerializer();
}

class _$PagingGetListEngineerSerializer implements PrimitiveSerializer<PagingGetListEngineer> {
  @override
  final Iterable<Type> types = const [PagingGetListEngineer, _$PagingGetListEngineer];

  @override
  final String wireName = r'PagingGetListEngineer';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    PagingGetListEngineer object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'next';
    yield object.next == null ? null : serializers.serialize(
      object.next,
      specifiedType: const FullType.nullable(String),
    );
    yield r'previous';
    yield object.previous == null ? null : serializers.serialize(
      object.previous,
      specifiedType: const FullType.nullable(String),
    );
    yield r'results';
    yield serializers.serialize(
      object.results,
      specifiedType: const FullType(BuiltList, [FullType(GetListEngineer)]),
    );
    yield r'total_count';
    yield serializers.serialize(
      object.totalCount,
      specifiedType: const FullType(int),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    PagingGetListEngineer object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required PagingGetListEngineerBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'next':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.next = valueDes;
          break;
        case r'previous':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.previous = valueDes;
          break;
        case r'results':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(GetListEngineer)]),
          ) as BuiltList<GetListEngineer>;
          result.results.replace(valueDes);
          break;
        case r'total_count':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.totalCount = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  PagingGetListEngineer deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = PagingGetListEngineerBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

