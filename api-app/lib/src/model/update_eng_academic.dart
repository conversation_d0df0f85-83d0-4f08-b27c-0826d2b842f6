//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/date.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'update_eng_academic.g.dart';

/// UpdateEngAcademic
///
/// Properties:
/// * [school] 
/// * [type] 
/// * [outDate] 
/// * [faculty] 
@BuiltValue()
abstract class UpdateEngAcademic implements Built<UpdateEngAcademic, UpdateEngAcademicBuilder> {
  @BuiltValueField(wireName: r'school')
  String? get school;

  @BuiltValueField(wireName: r'type')
  int? get type;

  @BuiltValueField(wireName: r'out_date')
  Date? get outDate;

  @BuiltValueField(wireName: r'faculty')
  String? get faculty;

  UpdateEngAcademic._();

  factory UpdateEngAcademic([void updates(UpdateEngAcademicBuilder b)]) = _$UpdateEngAcademic;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(UpdateEngAcademicBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<UpdateEngAcademic> get serializer => _$UpdateEngAcademicSerializer();
}

class _$UpdateEngAcademicSerializer implements PrimitiveSerializer<UpdateEngAcademic> {
  @override
  final Iterable<Type> types = const [UpdateEngAcademic, _$UpdateEngAcademic];

  @override
  final String wireName = r'UpdateEngAcademic';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    UpdateEngAcademic object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.school != null) {
      yield r'school';
      yield serializers.serialize(
        object.school,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.type != null) {
      yield r'type';
      yield serializers.serialize(
        object.type,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.outDate != null) {
      yield r'out_date';
      yield serializers.serialize(
        object.outDate,
        specifiedType: const FullType.nullable(Date),
      );
    }
    if (object.faculty != null) {
      yield r'faculty';
      yield serializers.serialize(
        object.faculty,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    UpdateEngAcademic object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required UpdateEngAcademicBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'school':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.school = valueDes;
          break;
        case r'type':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.type = valueDes;
          break;
        case r'out_date':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(Date),
          ) as Date?;
          if (valueDes == null) continue;
          result.outDate = valueDes;
          break;
        case r'faculty':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.faculty = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  UpdateEngAcademic deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = UpdateEngAcademicBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

