// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_company_get_contract_details.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GeneralCompanyGetContractDetails
    extends GeneralCompanyGetContractDetails {
  @override
  final int? applyId;
  @override
  final int? hostCompanyId;
  @override
  final String? hostCompanyName;
  @override
  final String? hostAgentFirstName;
  @override
  final String? hostAgentLastName;
  @override
  final String? hostAgentAcceptSignPath;
  @override
  final int? supportCompanyId;
  @override
  final String? supportCompanyName;
  @override
  final String? supportAgentFirstName;
  @override
  final String? supportAgentLastName;
  @override
  final String? supportAgentAcceptSignPath;
  @override
  final int? agencyCompanyId;
  @override
  final String? agencyCompanyName;
  @override
  final String? agencyAgentFirstName;
  @override
  final String? agencyAgentLastName;
  @override
  final String? agencyAgentAcceptSignPath;
  @override
  final int? engineerId;
  @override
  final String? engineerFirstName;
  @override
  final String? engineerLastName;
  @override
  final String? engineerAcceptSignPath;

  factory _$GeneralCompanyGetContractDetails(
          [void Function(GeneralCompanyGetContractDetailsBuilder)? updates]) =>
      (new GeneralCompanyGetContractDetailsBuilder()..update(updates))._build();

  _$GeneralCompanyGetContractDetails._(
      {this.applyId,
      this.hostCompanyId,
      this.hostCompanyName,
      this.hostAgentFirstName,
      this.hostAgentLastName,
      this.hostAgentAcceptSignPath,
      this.supportCompanyId,
      this.supportCompanyName,
      this.supportAgentFirstName,
      this.supportAgentLastName,
      this.supportAgentAcceptSignPath,
      this.agencyCompanyId,
      this.agencyCompanyName,
      this.agencyAgentFirstName,
      this.agencyAgentLastName,
      this.agencyAgentAcceptSignPath,
      this.engineerId,
      this.engineerFirstName,
      this.engineerLastName,
      this.engineerAcceptSignPath})
      : super._();

  @override
  GeneralCompanyGetContractDetails rebuild(
          void Function(GeneralCompanyGetContractDetailsBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GeneralCompanyGetContractDetailsBuilder toBuilder() =>
      new GeneralCompanyGetContractDetailsBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GeneralCompanyGetContractDetails &&
        applyId == other.applyId &&
        hostCompanyId == other.hostCompanyId &&
        hostCompanyName == other.hostCompanyName &&
        hostAgentFirstName == other.hostAgentFirstName &&
        hostAgentLastName == other.hostAgentLastName &&
        hostAgentAcceptSignPath == other.hostAgentAcceptSignPath &&
        supportCompanyId == other.supportCompanyId &&
        supportCompanyName == other.supportCompanyName &&
        supportAgentFirstName == other.supportAgentFirstName &&
        supportAgentLastName == other.supportAgentLastName &&
        supportAgentAcceptSignPath == other.supportAgentAcceptSignPath &&
        agencyCompanyId == other.agencyCompanyId &&
        agencyCompanyName == other.agencyCompanyName &&
        agencyAgentFirstName == other.agencyAgentFirstName &&
        agencyAgentLastName == other.agencyAgentLastName &&
        agencyAgentAcceptSignPath == other.agencyAgentAcceptSignPath &&
        engineerId == other.engineerId &&
        engineerFirstName == other.engineerFirstName &&
        engineerLastName == other.engineerLastName &&
        engineerAcceptSignPath == other.engineerAcceptSignPath;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, applyId.hashCode);
    _$hash = $jc(_$hash, hostCompanyId.hashCode);
    _$hash = $jc(_$hash, hostCompanyName.hashCode);
    _$hash = $jc(_$hash, hostAgentFirstName.hashCode);
    _$hash = $jc(_$hash, hostAgentLastName.hashCode);
    _$hash = $jc(_$hash, hostAgentAcceptSignPath.hashCode);
    _$hash = $jc(_$hash, supportCompanyId.hashCode);
    _$hash = $jc(_$hash, supportCompanyName.hashCode);
    _$hash = $jc(_$hash, supportAgentFirstName.hashCode);
    _$hash = $jc(_$hash, supportAgentLastName.hashCode);
    _$hash = $jc(_$hash, supportAgentAcceptSignPath.hashCode);
    _$hash = $jc(_$hash, agencyCompanyId.hashCode);
    _$hash = $jc(_$hash, agencyCompanyName.hashCode);
    _$hash = $jc(_$hash, agencyAgentFirstName.hashCode);
    _$hash = $jc(_$hash, agencyAgentLastName.hashCode);
    _$hash = $jc(_$hash, agencyAgentAcceptSignPath.hashCode);
    _$hash = $jc(_$hash, engineerId.hashCode);
    _$hash = $jc(_$hash, engineerFirstName.hashCode);
    _$hash = $jc(_$hash, engineerLastName.hashCode);
    _$hash = $jc(_$hash, engineerAcceptSignPath.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GeneralCompanyGetContractDetails')
          ..add('applyId', applyId)
          ..add('hostCompanyId', hostCompanyId)
          ..add('hostCompanyName', hostCompanyName)
          ..add('hostAgentFirstName', hostAgentFirstName)
          ..add('hostAgentLastName', hostAgentLastName)
          ..add('hostAgentAcceptSignPath', hostAgentAcceptSignPath)
          ..add('supportCompanyId', supportCompanyId)
          ..add('supportCompanyName', supportCompanyName)
          ..add('supportAgentFirstName', supportAgentFirstName)
          ..add('supportAgentLastName', supportAgentLastName)
          ..add('supportAgentAcceptSignPath', supportAgentAcceptSignPath)
          ..add('agencyCompanyId', agencyCompanyId)
          ..add('agencyCompanyName', agencyCompanyName)
          ..add('agencyAgentFirstName', agencyAgentFirstName)
          ..add('agencyAgentLastName', agencyAgentLastName)
          ..add('agencyAgentAcceptSignPath', agencyAgentAcceptSignPath)
          ..add('engineerId', engineerId)
          ..add('engineerFirstName', engineerFirstName)
          ..add('engineerLastName', engineerLastName)
          ..add('engineerAcceptSignPath', engineerAcceptSignPath))
        .toString();
  }
}

class GeneralCompanyGetContractDetailsBuilder
    implements
        Builder<GeneralCompanyGetContractDetails,
            GeneralCompanyGetContractDetailsBuilder> {
  _$GeneralCompanyGetContractDetails? _$v;

  int? _applyId;
  int? get applyId => _$this._applyId;
  set applyId(int? applyId) => _$this._applyId = applyId;

  int? _hostCompanyId;
  int? get hostCompanyId => _$this._hostCompanyId;
  set hostCompanyId(int? hostCompanyId) =>
      _$this._hostCompanyId = hostCompanyId;

  String? _hostCompanyName;
  String? get hostCompanyName => _$this._hostCompanyName;
  set hostCompanyName(String? hostCompanyName) =>
      _$this._hostCompanyName = hostCompanyName;

  String? _hostAgentFirstName;
  String? get hostAgentFirstName => _$this._hostAgentFirstName;
  set hostAgentFirstName(String? hostAgentFirstName) =>
      _$this._hostAgentFirstName = hostAgentFirstName;

  String? _hostAgentLastName;
  String? get hostAgentLastName => _$this._hostAgentLastName;
  set hostAgentLastName(String? hostAgentLastName) =>
      _$this._hostAgentLastName = hostAgentLastName;

  String? _hostAgentAcceptSignPath;
  String? get hostAgentAcceptSignPath => _$this._hostAgentAcceptSignPath;
  set hostAgentAcceptSignPath(String? hostAgentAcceptSignPath) =>
      _$this._hostAgentAcceptSignPath = hostAgentAcceptSignPath;

  int? _supportCompanyId;
  int? get supportCompanyId => _$this._supportCompanyId;
  set supportCompanyId(int? supportCompanyId) =>
      _$this._supportCompanyId = supportCompanyId;

  String? _supportCompanyName;
  String? get supportCompanyName => _$this._supportCompanyName;
  set supportCompanyName(String? supportCompanyName) =>
      _$this._supportCompanyName = supportCompanyName;

  String? _supportAgentFirstName;
  String? get supportAgentFirstName => _$this._supportAgentFirstName;
  set supportAgentFirstName(String? supportAgentFirstName) =>
      _$this._supportAgentFirstName = supportAgentFirstName;

  String? _supportAgentLastName;
  String? get supportAgentLastName => _$this._supportAgentLastName;
  set supportAgentLastName(String? supportAgentLastName) =>
      _$this._supportAgentLastName = supportAgentLastName;

  String? _supportAgentAcceptSignPath;
  String? get supportAgentAcceptSignPath => _$this._supportAgentAcceptSignPath;
  set supportAgentAcceptSignPath(String? supportAgentAcceptSignPath) =>
      _$this._supportAgentAcceptSignPath = supportAgentAcceptSignPath;

  int? _agencyCompanyId;
  int? get agencyCompanyId => _$this._agencyCompanyId;
  set agencyCompanyId(int? agencyCompanyId) =>
      _$this._agencyCompanyId = agencyCompanyId;

  String? _agencyCompanyName;
  String? get agencyCompanyName => _$this._agencyCompanyName;
  set agencyCompanyName(String? agencyCompanyName) =>
      _$this._agencyCompanyName = agencyCompanyName;

  String? _agencyAgentFirstName;
  String? get agencyAgentFirstName => _$this._agencyAgentFirstName;
  set agencyAgentFirstName(String? agencyAgentFirstName) =>
      _$this._agencyAgentFirstName = agencyAgentFirstName;

  String? _agencyAgentLastName;
  String? get agencyAgentLastName => _$this._agencyAgentLastName;
  set agencyAgentLastName(String? agencyAgentLastName) =>
      _$this._agencyAgentLastName = agencyAgentLastName;

  String? _agencyAgentAcceptSignPath;
  String? get agencyAgentAcceptSignPath => _$this._agencyAgentAcceptSignPath;
  set agencyAgentAcceptSignPath(String? agencyAgentAcceptSignPath) =>
      _$this._agencyAgentAcceptSignPath = agencyAgentAcceptSignPath;

  int? _engineerId;
  int? get engineerId => _$this._engineerId;
  set engineerId(int? engineerId) => _$this._engineerId = engineerId;

  String? _engineerFirstName;
  String? get engineerFirstName => _$this._engineerFirstName;
  set engineerFirstName(String? engineerFirstName) =>
      _$this._engineerFirstName = engineerFirstName;

  String? _engineerLastName;
  String? get engineerLastName => _$this._engineerLastName;
  set engineerLastName(String? engineerLastName) =>
      _$this._engineerLastName = engineerLastName;

  String? _engineerAcceptSignPath;
  String? get engineerAcceptSignPath => _$this._engineerAcceptSignPath;
  set engineerAcceptSignPath(String? engineerAcceptSignPath) =>
      _$this._engineerAcceptSignPath = engineerAcceptSignPath;

  GeneralCompanyGetContractDetailsBuilder() {
    GeneralCompanyGetContractDetails._defaults(this);
  }

  GeneralCompanyGetContractDetailsBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _applyId = $v.applyId;
      _hostCompanyId = $v.hostCompanyId;
      _hostCompanyName = $v.hostCompanyName;
      _hostAgentFirstName = $v.hostAgentFirstName;
      _hostAgentLastName = $v.hostAgentLastName;
      _hostAgentAcceptSignPath = $v.hostAgentAcceptSignPath;
      _supportCompanyId = $v.supportCompanyId;
      _supportCompanyName = $v.supportCompanyName;
      _supportAgentFirstName = $v.supportAgentFirstName;
      _supportAgentLastName = $v.supportAgentLastName;
      _supportAgentAcceptSignPath = $v.supportAgentAcceptSignPath;
      _agencyCompanyId = $v.agencyCompanyId;
      _agencyCompanyName = $v.agencyCompanyName;
      _agencyAgentFirstName = $v.agencyAgentFirstName;
      _agencyAgentLastName = $v.agencyAgentLastName;
      _agencyAgentAcceptSignPath = $v.agencyAgentAcceptSignPath;
      _engineerId = $v.engineerId;
      _engineerFirstName = $v.engineerFirstName;
      _engineerLastName = $v.engineerLastName;
      _engineerAcceptSignPath = $v.engineerAcceptSignPath;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GeneralCompanyGetContractDetails other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GeneralCompanyGetContractDetails;
  }

  @override
  void update(void Function(GeneralCompanyGetContractDetailsBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GeneralCompanyGetContractDetails build() => _build();

  _$GeneralCompanyGetContractDetails _build() {
    final _$result = _$v ??
        new _$GeneralCompanyGetContractDetails._(
          applyId: applyId,
          hostCompanyId: hostCompanyId,
          hostCompanyName: hostCompanyName,
          hostAgentFirstName: hostAgentFirstName,
          hostAgentLastName: hostAgentLastName,
          hostAgentAcceptSignPath: hostAgentAcceptSignPath,
          supportCompanyId: supportCompanyId,
          supportCompanyName: supportCompanyName,
          supportAgentFirstName: supportAgentFirstName,
          supportAgentLastName: supportAgentLastName,
          supportAgentAcceptSignPath: supportAgentAcceptSignPath,
          agencyCompanyId: agencyCompanyId,
          agencyCompanyName: agencyCompanyName,
          agencyAgentFirstName: agencyAgentFirstName,
          agencyAgentLastName: agencyAgentLastName,
          agencyAgentAcceptSignPath: agencyAgentAcceptSignPath,
          engineerId: engineerId,
          engineerFirstName: engineerFirstName,
          engineerLastName: engineerLastName,
          engineerAcceptSignPath: engineerAcceptSignPath,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
