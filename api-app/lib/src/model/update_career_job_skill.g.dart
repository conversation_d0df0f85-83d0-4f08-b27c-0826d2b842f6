// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_career_job_skill.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UpdateCareerJobSkill extends UpdateCareerJobSkill {
  @override
  final String? jobCode;
  @override
  final String? skillCode;
  @override
  final int? yearsOfExperience;

  factory _$UpdateCareerJobSkill(
          [void Function(UpdateCareerJobSkillBuilder)? updates]) =>
      (new UpdateCareerJobSkillBuilder()..update(updates))._build();

  _$UpdateCareerJobSkill._(
      {this.jobCode, this.skillCode, this.yearsOfExperience})
      : super._();

  @override
  UpdateCareerJobSkill rebuild(
          void Function(UpdateCareerJobSkillBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UpdateCareerJobSkillBuilder toBuilder() =>
      new UpdateCareerJobSkillBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UpdateCareerJobSkill &&
        jobCode == other.jobCode &&
        skillCode == other.skillCode &&
        yearsOfExperience == other.yearsOfExperience;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, jobCode.hashCode);
    _$hash = $jc(_$hash, skillCode.hashCode);
    _$hash = $jc(_$hash, yearsOfExperience.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UpdateCareerJobSkill')
          ..add('jobCode', jobCode)
          ..add('skillCode', skillCode)
          ..add('yearsOfExperience', yearsOfExperience))
        .toString();
  }
}

class UpdateCareerJobSkillBuilder
    implements Builder<UpdateCareerJobSkill, UpdateCareerJobSkillBuilder> {
  _$UpdateCareerJobSkill? _$v;

  String? _jobCode;
  String? get jobCode => _$this._jobCode;
  set jobCode(String? jobCode) => _$this._jobCode = jobCode;

  String? _skillCode;
  String? get skillCode => _$this._skillCode;
  set skillCode(String? skillCode) => _$this._skillCode = skillCode;

  int? _yearsOfExperience;
  int? get yearsOfExperience => _$this._yearsOfExperience;
  set yearsOfExperience(int? yearsOfExperience) =>
      _$this._yearsOfExperience = yearsOfExperience;

  UpdateCareerJobSkillBuilder() {
    UpdateCareerJobSkill._defaults(this);
  }

  UpdateCareerJobSkillBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _jobCode = $v.jobCode;
      _skillCode = $v.skillCode;
      _yearsOfExperience = $v.yearsOfExperience;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UpdateCareerJobSkill other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$UpdateCareerJobSkill;
  }

  @override
  void update(void Function(UpdateCareerJobSkillBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UpdateCareerJobSkill build() => _build();

  _$UpdateCareerJobSkill _build() {
    final _$result = _$v ??
        new _$UpdateCareerJobSkill._(
          jobCode: jobCode,
          skillCode: skillCode,
          yearsOfExperience: yearsOfExperience,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
