// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'eng_skill.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$EngSkill extends EngSkill {
  @override
  final int engineerId;
  @override
  final String? skillCode;
  @override
  final int? levelType;
  @override
  final int? skillId;
  @override
  final String? skillName;
  @override
  final String? levelTypeName;
  @override
  final String? jobCode;
  @override
  final String? tempName;
  @override
  final int? tempCategoryId;

  factory _$EngSkill([void Function(EngSkillBuilder)? updates]) =>
      (new EngSkillBuilder()..update(updates))._build();

  _$EngSkill._(
      {required this.engineerId,
      this.skillCode,
      this.levelType,
      this.skillId,
      this.skillName,
      this.levelTypeName,
      this.jobCode,
      this.tempName,
      this.tempCategoryId})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        engineerId, r'EngSkill', 'engineerId');
  }

  @override
  EngSkill rebuild(void Function(EngSkillBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  EngSkillBuilder toBuilder() => new EngSkillBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is EngSkill &&
        engineerId == other.engineerId &&
        skillCode == other.skillCode &&
        levelType == other.levelType &&
        skillId == other.skillId &&
        skillName == other.skillName &&
        levelTypeName == other.levelTypeName &&
        jobCode == other.jobCode &&
        tempName == other.tempName &&
        tempCategoryId == other.tempCategoryId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, engineerId.hashCode);
    _$hash = $jc(_$hash, skillCode.hashCode);
    _$hash = $jc(_$hash, levelType.hashCode);
    _$hash = $jc(_$hash, skillId.hashCode);
    _$hash = $jc(_$hash, skillName.hashCode);
    _$hash = $jc(_$hash, levelTypeName.hashCode);
    _$hash = $jc(_$hash, jobCode.hashCode);
    _$hash = $jc(_$hash, tempName.hashCode);
    _$hash = $jc(_$hash, tempCategoryId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'EngSkill')
          ..add('engineerId', engineerId)
          ..add('skillCode', skillCode)
          ..add('levelType', levelType)
          ..add('skillId', skillId)
          ..add('skillName', skillName)
          ..add('levelTypeName', levelTypeName)
          ..add('jobCode', jobCode)
          ..add('tempName', tempName)
          ..add('tempCategoryId', tempCategoryId))
        .toString();
  }
}

class EngSkillBuilder implements Builder<EngSkill, EngSkillBuilder> {
  _$EngSkill? _$v;

  int? _engineerId;
  int? get engineerId => _$this._engineerId;
  set engineerId(int? engineerId) => _$this._engineerId = engineerId;

  String? _skillCode;
  String? get skillCode => _$this._skillCode;
  set skillCode(String? skillCode) => _$this._skillCode = skillCode;

  int? _levelType;
  int? get levelType => _$this._levelType;
  set levelType(int? levelType) => _$this._levelType = levelType;

  int? _skillId;
  int? get skillId => _$this._skillId;
  set skillId(int? skillId) => _$this._skillId = skillId;

  String? _skillName;
  String? get skillName => _$this._skillName;
  set skillName(String? skillName) => _$this._skillName = skillName;

  String? _levelTypeName;
  String? get levelTypeName => _$this._levelTypeName;
  set levelTypeName(String? levelTypeName) =>
      _$this._levelTypeName = levelTypeName;

  String? _jobCode;
  String? get jobCode => _$this._jobCode;
  set jobCode(String? jobCode) => _$this._jobCode = jobCode;

  String? _tempName;
  String? get tempName => _$this._tempName;
  set tempName(String? tempName) => _$this._tempName = tempName;

  int? _tempCategoryId;
  int? get tempCategoryId => _$this._tempCategoryId;
  set tempCategoryId(int? tempCategoryId) =>
      _$this._tempCategoryId = tempCategoryId;

  EngSkillBuilder() {
    EngSkill._defaults(this);
  }

  EngSkillBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _engineerId = $v.engineerId;
      _skillCode = $v.skillCode;
      _levelType = $v.levelType;
      _skillId = $v.skillId;
      _skillName = $v.skillName;
      _levelTypeName = $v.levelTypeName;
      _jobCode = $v.jobCode;
      _tempName = $v.tempName;
      _tempCategoryId = $v.tempCategoryId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(EngSkill other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$EngSkill;
  }

  @override
  void update(void Function(EngSkillBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  EngSkill build() => _build();

  _$EngSkill _build() {
    final _$result = _$v ??
        new _$EngSkill._(
          engineerId: BuiltValueNullFieldError.checkNotNull(
              engineerId, r'EngSkill', 'engineerId'),
          skillCode: skillCode,
          levelType: levelType,
          skillId: skillId,
          skillName: skillName,
          levelTypeName: levelTypeName,
          jobCode: jobCode,
          tempName: tempName,
          tempCategoryId: tempCategoryId,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
