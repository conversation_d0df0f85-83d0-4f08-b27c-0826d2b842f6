// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'engineer_list_apply_company.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$EngineerListApplyCompany extends EngineerListApplyCompany {
  @override
  final int? applyId;
  @override
  final int? engineerId;
  @override
  final int? recruitId;
  @override
  final String? payrollPriceFrom;
  @override
  final String? payrollPriceTo;
  @override
  final String? payrollCode;
  @override
  final int? hostCompanyId;
  @override
  final int? recruitProgressCode;
  @override
  final DateTime? updated;
  @override
  final DateTime? progressUpdateDatetime;
  @override
  final bool? isClosed;
  @override
  final bool? isRead;
  @override
  final EngineerListApplyCompanyInformation? hostCompany;

  factory _$EngineerListApplyCompany(
          [void Function(EngineerListApplyCompanyBuilder)? updates]) =>
      (new EngineerListApplyCompanyBuilder()..update(updates))._build();

  _$EngineerListApplyCompany._(
      {this.applyId,
      this.engineerId,
      this.recruitId,
      this.payrollPriceFrom,
      this.payrollPriceTo,
      this.payrollCode,
      this.hostCompanyId,
      this.recruitProgressCode,
      this.updated,
      this.progressUpdateDatetime,
      this.isClosed,
      this.isRead,
      this.hostCompany})
      : super._();

  @override
  EngineerListApplyCompany rebuild(
          void Function(EngineerListApplyCompanyBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  EngineerListApplyCompanyBuilder toBuilder() =>
      new EngineerListApplyCompanyBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is EngineerListApplyCompany &&
        applyId == other.applyId &&
        engineerId == other.engineerId &&
        recruitId == other.recruitId &&
        payrollPriceFrom == other.payrollPriceFrom &&
        payrollPriceTo == other.payrollPriceTo &&
        payrollCode == other.payrollCode &&
        hostCompanyId == other.hostCompanyId &&
        recruitProgressCode == other.recruitProgressCode &&
        updated == other.updated &&
        progressUpdateDatetime == other.progressUpdateDatetime &&
        isClosed == other.isClosed &&
        isRead == other.isRead &&
        hostCompany == other.hostCompany;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, applyId.hashCode);
    _$hash = $jc(_$hash, engineerId.hashCode);
    _$hash = $jc(_$hash, recruitId.hashCode);
    _$hash = $jc(_$hash, payrollPriceFrom.hashCode);
    _$hash = $jc(_$hash, payrollPriceTo.hashCode);
    _$hash = $jc(_$hash, payrollCode.hashCode);
    _$hash = $jc(_$hash, hostCompanyId.hashCode);
    _$hash = $jc(_$hash, recruitProgressCode.hashCode);
    _$hash = $jc(_$hash, updated.hashCode);
    _$hash = $jc(_$hash, progressUpdateDatetime.hashCode);
    _$hash = $jc(_$hash, isClosed.hashCode);
    _$hash = $jc(_$hash, isRead.hashCode);
    _$hash = $jc(_$hash, hostCompany.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'EngineerListApplyCompany')
          ..add('applyId', applyId)
          ..add('engineerId', engineerId)
          ..add('recruitId', recruitId)
          ..add('payrollPriceFrom', payrollPriceFrom)
          ..add('payrollPriceTo', payrollPriceTo)
          ..add('payrollCode', payrollCode)
          ..add('hostCompanyId', hostCompanyId)
          ..add('recruitProgressCode', recruitProgressCode)
          ..add('updated', updated)
          ..add('progressUpdateDatetime', progressUpdateDatetime)
          ..add('isClosed', isClosed)
          ..add('isRead', isRead)
          ..add('hostCompany', hostCompany))
        .toString();
  }
}

class EngineerListApplyCompanyBuilder
    implements
        Builder<EngineerListApplyCompany, EngineerListApplyCompanyBuilder> {
  _$EngineerListApplyCompany? _$v;

  int? _applyId;
  int? get applyId => _$this._applyId;
  set applyId(int? applyId) => _$this._applyId = applyId;

  int? _engineerId;
  int? get engineerId => _$this._engineerId;
  set engineerId(int? engineerId) => _$this._engineerId = engineerId;

  int? _recruitId;
  int? get recruitId => _$this._recruitId;
  set recruitId(int? recruitId) => _$this._recruitId = recruitId;

  String? _payrollPriceFrom;
  String? get payrollPriceFrom => _$this._payrollPriceFrom;
  set payrollPriceFrom(String? payrollPriceFrom) =>
      _$this._payrollPriceFrom = payrollPriceFrom;

  String? _payrollPriceTo;
  String? get payrollPriceTo => _$this._payrollPriceTo;
  set payrollPriceTo(String? payrollPriceTo) =>
      _$this._payrollPriceTo = payrollPriceTo;

  String? _payrollCode;
  String? get payrollCode => _$this._payrollCode;
  set payrollCode(String? payrollCode) => _$this._payrollCode = payrollCode;

  int? _hostCompanyId;
  int? get hostCompanyId => _$this._hostCompanyId;
  set hostCompanyId(int? hostCompanyId) =>
      _$this._hostCompanyId = hostCompanyId;

  int? _recruitProgressCode;
  int? get recruitProgressCode => _$this._recruitProgressCode;
  set recruitProgressCode(int? recruitProgressCode) =>
      _$this._recruitProgressCode = recruitProgressCode;

  DateTime? _updated;
  DateTime? get updated => _$this._updated;
  set updated(DateTime? updated) => _$this._updated = updated;

  DateTime? _progressUpdateDatetime;
  DateTime? get progressUpdateDatetime => _$this._progressUpdateDatetime;
  set progressUpdateDatetime(DateTime? progressUpdateDatetime) =>
      _$this._progressUpdateDatetime = progressUpdateDatetime;

  bool? _isClosed;
  bool? get isClosed => _$this._isClosed;
  set isClosed(bool? isClosed) => _$this._isClosed = isClosed;

  bool? _isRead;
  bool? get isRead => _$this._isRead;
  set isRead(bool? isRead) => _$this._isRead = isRead;

  EngineerListApplyCompanyInformationBuilder? _hostCompany;
  EngineerListApplyCompanyInformationBuilder get hostCompany =>
      _$this._hostCompany ??= new EngineerListApplyCompanyInformationBuilder();
  set hostCompany(EngineerListApplyCompanyInformationBuilder? hostCompany) =>
      _$this._hostCompany = hostCompany;

  EngineerListApplyCompanyBuilder() {
    EngineerListApplyCompany._defaults(this);
  }

  EngineerListApplyCompanyBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _applyId = $v.applyId;
      _engineerId = $v.engineerId;
      _recruitId = $v.recruitId;
      _payrollPriceFrom = $v.payrollPriceFrom;
      _payrollPriceTo = $v.payrollPriceTo;
      _payrollCode = $v.payrollCode;
      _hostCompanyId = $v.hostCompanyId;
      _recruitProgressCode = $v.recruitProgressCode;
      _updated = $v.updated;
      _progressUpdateDatetime = $v.progressUpdateDatetime;
      _isClosed = $v.isClosed;
      _isRead = $v.isRead;
      _hostCompany = $v.hostCompany?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(EngineerListApplyCompany other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$EngineerListApplyCompany;
  }

  @override
  void update(void Function(EngineerListApplyCompanyBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  EngineerListApplyCompany build() => _build();

  _$EngineerListApplyCompany _build() {
    _$EngineerListApplyCompany _$result;
    try {
      _$result = _$v ??
          new _$EngineerListApplyCompany._(
            applyId: applyId,
            engineerId: engineerId,
            recruitId: recruitId,
            payrollPriceFrom: payrollPriceFrom,
            payrollPriceTo: payrollPriceTo,
            payrollCode: payrollCode,
            hostCompanyId: hostCompanyId,
            recruitProgressCode: recruitProgressCode,
            updated: updated,
            progressUpdateDatetime: progressUpdateDatetime,
            isClosed: isClosed,
            isRead: isRead,
            hostCompany: _hostCompany?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'hostCompany';
        _hostCompany?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'EngineerListApplyCompany', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
