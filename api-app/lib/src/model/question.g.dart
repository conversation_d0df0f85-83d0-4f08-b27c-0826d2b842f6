// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'question.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$Question extends Question {
  @override
  final String textEn;
  @override
  final String textVi;
  @override
  final String text;
  @override
  final bool? isExpanded;

  factory _$Question([void Function(QuestionBuilder)? updates]) =>
      (new QuestionBuilder()..update(updates))._build();

  _$Question._(
      {required this.textEn,
      required this.textVi,
      required this.text,
      this.isExpanded})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(textEn, r'Question', 'textEn');
    BuiltValueNullFieldError.checkNotNull(textVi, r'Question', 'textVi');
    BuiltValueNullFieldError.checkNotNull(text, r'Question', 'text');
  }

  @override
  Question rebuild(void Function(QuestionBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  QuestionBuilder toBuilder() => new QuestionBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is Question &&
        textEn == other.textEn &&
        textVi == other.textVi &&
        text == other.text &&
        isExpanded == other.isExpanded;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, textEn.hashCode);
    _$hash = $jc(_$hash, textVi.hashCode);
    _$hash = $jc(_$hash, text.hashCode);
    _$hash = $jc(_$hash, isExpanded.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'Question')
          ..add('textEn', textEn)
          ..add('textVi', textVi)
          ..add('text', text)
          ..add('isExpanded', isExpanded))
        .toString();
  }
}

class QuestionBuilder implements Builder<Question, QuestionBuilder> {
  _$Question? _$v;

  String? _textEn;
  String? get textEn => _$this._textEn;
  set textEn(String? textEn) => _$this._textEn = textEn;

  String? _textVi;
  String? get textVi => _$this._textVi;
  set textVi(String? textVi) => _$this._textVi = textVi;

  String? _text;
  String? get text => _$this._text;
  set text(String? text) => _$this._text = text;

  bool? _isExpanded;
  bool? get isExpanded => _$this._isExpanded;
  set isExpanded(bool? isExpanded) => _$this._isExpanded = isExpanded;

  QuestionBuilder() {
    Question._defaults(this);
  }

  QuestionBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _textEn = $v.textEn;
      _textVi = $v.textVi;
      _text = $v.text;
      _isExpanded = $v.isExpanded;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(Question other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$Question;
  }

  @override
  void update(void Function(QuestionBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  Question build() => _build();

  _$Question _build() {
    final _$result = _$v ??
        new _$Question._(
          textEn: BuiltValueNullFieldError.checkNotNull(
              textEn, r'Question', 'textEn'),
          textVi: BuiltValueNullFieldError.checkNotNull(
              textVi, r'Question', 'textVi'),
          text:
              BuiltValueNullFieldError.checkNotNull(text, r'Question', 'text'),
          isExpanded: isExpanded,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
