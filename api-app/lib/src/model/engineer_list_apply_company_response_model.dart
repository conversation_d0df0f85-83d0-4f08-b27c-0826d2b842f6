//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/engineer_list_apply_company_pagination.dart';
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/error_detail.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'engineer_list_apply_company_response_model.g.dart';

/// EngineerListApplyCompanyResponseModel
///
/// Properties:
/// * [message] 
/// * [errors] 
/// * [data] 
@BuiltValue()
abstract class EngineerListApplyCompanyResponseModel implements Built<EngineerListApplyCompanyResponseModel, EngineerListApplyCompanyResponseModelBuilder> {
  @BuiltValueField(wireName: r'message')
  String? get message;

  @BuiltValueField(wireName: r'errors')
  BuiltList<ErrorDetail>? get errors;

  @BuiltValueField(wireName: r'data')
  EngineerListApplyCompanyPagination get data;

  EngineerListApplyCompanyResponseModel._();

  factory EngineerListApplyCompanyResponseModel([void updates(EngineerListApplyCompanyResponseModelBuilder b)]) = _$EngineerListApplyCompanyResponseModel;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(EngineerListApplyCompanyResponseModelBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<EngineerListApplyCompanyResponseModel> get serializer => _$EngineerListApplyCompanyResponseModelSerializer();
}

class _$EngineerListApplyCompanyResponseModelSerializer implements PrimitiveSerializer<EngineerListApplyCompanyResponseModel> {
  @override
  final Iterable<Type> types = const [EngineerListApplyCompanyResponseModel, _$EngineerListApplyCompanyResponseModel];

  @override
  final String wireName = r'EngineerListApplyCompanyResponseModel';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    EngineerListApplyCompanyResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'message';
    yield object.message == null ? null : serializers.serialize(
      object.message,
      specifiedType: const FullType.nullable(String),
    );
    yield r'errors';
    yield object.errors == null ? null : serializers.serialize(
      object.errors,
      specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
    );
    yield r'data';
    yield serializers.serialize(
      object.data,
      specifiedType: const FullType(EngineerListApplyCompanyPagination),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    EngineerListApplyCompanyResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required EngineerListApplyCompanyResponseModelBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'message':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.message = valueDes;
          break;
        case r'errors':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
          ) as BuiltList<ErrorDetail>?;
          if (valueDes == null) continue;
          result.errors.replace(valueDes);
          break;
        case r'data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(EngineerListApplyCompanyPagination),
          ) as EngineerListApplyCompanyPagination;
          result.data.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  EngineerListApplyCompanyResponseModel deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = EngineerListApplyCompanyResponseModelBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

