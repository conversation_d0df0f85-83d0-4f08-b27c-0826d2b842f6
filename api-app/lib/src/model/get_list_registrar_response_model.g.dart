// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_list_registrar_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GetListRegistrarResponseModel extends GetListRegistrarResponseModel {
  @override
  final String? message;
  @override
  final BuiltList<String> data;
  @override
  final BuiltList<ErrorDetail>? errors;

  factory _$GetListRegistrarResponseModel(
          [void Function(GetListRegistrarResponseModelBuilder)? updates]) =>
      (new GetListRegistrarResponseModelBuilder()..update(updates))._build();

  _$GetListRegistrarResponseModel._(
      {this.message, required this.data, this.errors})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'GetListRegistrarResponseModel', 'data');
  }

  @override
  GetListRegistrarResponseModel rebuild(
          void Function(GetListRegistrarResponseModelBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GetListRegistrarResponseModelBuilder toBuilder() =>
      new GetListRegistrarResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GetListRegistrarResponseModel &&
        message == other.message &&
        data == other.data &&
        errors == other.errors;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GetListRegistrarResponseModel')
          ..add('message', message)
          ..add('data', data)
          ..add('errors', errors))
        .toString();
  }
}

class GetListRegistrarResponseModelBuilder
    implements
        Builder<GetListRegistrarResponseModel,
            GetListRegistrarResponseModelBuilder> {
  _$GetListRegistrarResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  ListBuilder<String>? _data;
  ListBuilder<String> get data => _$this._data ??= new ListBuilder<String>();
  set data(ListBuilder<String>? data) => _$this._data = data;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  GetListRegistrarResponseModelBuilder() {
    GetListRegistrarResponseModel._defaults(this);
  }

  GetListRegistrarResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _data = $v.data.toBuilder();
      _errors = $v.errors?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GetListRegistrarResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GetListRegistrarResponseModel;
  }

  @override
  void update(void Function(GetListRegistrarResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GetListRegistrarResponseModel build() => _build();

  _$GetListRegistrarResponseModel _build() {
    _$GetListRegistrarResponseModel _$result;
    try {
      _$result = _$v ??
          new _$GetListRegistrarResponseModel._(
            message: message,
            data: data.build(),
            errors: _errors?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        data.build();
        _$failedField = 'errors';
        _errors?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'GetListRegistrarResponseModel', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
