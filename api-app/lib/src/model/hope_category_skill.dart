//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/hope_job_skill.dart';
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'hope_category_skill.g.dart';

/// HopeCategorySkill
///
/// Properties:
/// * [categoryId] 
/// * [skills] 
@BuiltValue()
abstract class HopeCategorySkill implements Built<HopeCategorySkill, HopeCategorySkillBuilder> {
  @BuiltValueField(wireName: r'category_id')
  String get categoryId;

  @BuiltValueField(wireName: r'skills')
  BuiltList<HopeJobSkill> get skills;

  HopeCategorySkill._();

  factory HopeCategorySkill([void updates(HopeCategorySkillBuilder b)]) = _$HopeCategorySkill;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(HopeCategorySkillBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<HopeCategorySkill> get serializer => _$HopeCategorySkillSerializer();
}

class _$HopeCategorySkillSerializer implements PrimitiveSerializer<HopeCategorySkill> {
  @override
  final Iterable<Type> types = const [HopeCategorySkill, _$HopeCategorySkill];

  @override
  final String wireName = r'HopeCategorySkill';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    HopeCategorySkill object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'category_id';
    yield serializers.serialize(
      object.categoryId,
      specifiedType: const FullType(String),
    );
    yield r'skills';
    yield serializers.serialize(
      object.skills,
      specifiedType: const FullType(BuiltList, [FullType(HopeJobSkill)]),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    HopeCategorySkill object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required HopeCategorySkillBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'category_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.categoryId = valueDes;
          break;
        case r'skills':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(HopeJobSkill)]),
          ) as BuiltList<HopeJobSkill>;
          result.skills.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  HopeCategorySkill deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = HopeCategorySkillBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

