// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'host_company_update_interview_admission.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$HostCompanyUpdateInterviewAdmission
    extends HostCompanyUpdateInterviewAdmission {
  @override
  final int applyId;
  @override
  final String jobCode;
  @override
  final String employCode;
  @override
  final String placeCode;
  @override
  final String payrollCode;
  @override
  final double payrollPrice;
  @override
  final Date joiningDate;

  factory _$HostCompanyUpdateInterviewAdmission(
          [void Function(HostCompanyUpdateInterviewAdmissionBuilder)?
              updates]) =>
      (new HostCompanyUpdateInterviewAdmissionBuilder()..update(updates))
          ._build();

  _$HostCompanyUpdateInterviewAdmission._(
      {required this.applyId,
      required this.jobCode,
      required this.employCode,
      required this.placeCode,
      required this.payrollCode,
      required this.payrollPrice,
      required this.joiningDate})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        applyId, r'HostCompanyUpdateInterviewAdmission', 'applyId');
    BuiltValueNullFieldError.checkNotNull(
        jobCode, r'HostCompanyUpdateInterviewAdmission', 'jobCode');
    BuiltValueNullFieldError.checkNotNull(
        employCode, r'HostCompanyUpdateInterviewAdmission', 'employCode');
    BuiltValueNullFieldError.checkNotNull(
        placeCode, r'HostCompanyUpdateInterviewAdmission', 'placeCode');
    BuiltValueNullFieldError.checkNotNull(
        payrollCode, r'HostCompanyUpdateInterviewAdmission', 'payrollCode');
    BuiltValueNullFieldError.checkNotNull(
        payrollPrice, r'HostCompanyUpdateInterviewAdmission', 'payrollPrice');
    BuiltValueNullFieldError.checkNotNull(
        joiningDate, r'HostCompanyUpdateInterviewAdmission', 'joiningDate');
  }

  @override
  HostCompanyUpdateInterviewAdmission rebuild(
          void Function(HostCompanyUpdateInterviewAdmissionBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  HostCompanyUpdateInterviewAdmissionBuilder toBuilder() =>
      new HostCompanyUpdateInterviewAdmissionBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is HostCompanyUpdateInterviewAdmission &&
        applyId == other.applyId &&
        jobCode == other.jobCode &&
        employCode == other.employCode &&
        placeCode == other.placeCode &&
        payrollCode == other.payrollCode &&
        payrollPrice == other.payrollPrice &&
        joiningDate == other.joiningDate;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, applyId.hashCode);
    _$hash = $jc(_$hash, jobCode.hashCode);
    _$hash = $jc(_$hash, employCode.hashCode);
    _$hash = $jc(_$hash, placeCode.hashCode);
    _$hash = $jc(_$hash, payrollCode.hashCode);
    _$hash = $jc(_$hash, payrollPrice.hashCode);
    _$hash = $jc(_$hash, joiningDate.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'HostCompanyUpdateInterviewAdmission')
          ..add('applyId', applyId)
          ..add('jobCode', jobCode)
          ..add('employCode', employCode)
          ..add('placeCode', placeCode)
          ..add('payrollCode', payrollCode)
          ..add('payrollPrice', payrollPrice)
          ..add('joiningDate', joiningDate))
        .toString();
  }
}

class HostCompanyUpdateInterviewAdmissionBuilder
    implements
        Builder<HostCompanyUpdateInterviewAdmission,
            HostCompanyUpdateInterviewAdmissionBuilder> {
  _$HostCompanyUpdateInterviewAdmission? _$v;

  int? _applyId;
  int? get applyId => _$this._applyId;
  set applyId(int? applyId) => _$this._applyId = applyId;

  String? _jobCode;
  String? get jobCode => _$this._jobCode;
  set jobCode(String? jobCode) => _$this._jobCode = jobCode;

  String? _employCode;
  String? get employCode => _$this._employCode;
  set employCode(String? employCode) => _$this._employCode = employCode;

  String? _placeCode;
  String? get placeCode => _$this._placeCode;
  set placeCode(String? placeCode) => _$this._placeCode = placeCode;

  String? _payrollCode;
  String? get payrollCode => _$this._payrollCode;
  set payrollCode(String? payrollCode) => _$this._payrollCode = payrollCode;

  double? _payrollPrice;
  double? get payrollPrice => _$this._payrollPrice;
  set payrollPrice(double? payrollPrice) => _$this._payrollPrice = payrollPrice;

  Date? _joiningDate;
  Date? get joiningDate => _$this._joiningDate;
  set joiningDate(Date? joiningDate) => _$this._joiningDate = joiningDate;

  HostCompanyUpdateInterviewAdmissionBuilder() {
    HostCompanyUpdateInterviewAdmission._defaults(this);
  }

  HostCompanyUpdateInterviewAdmissionBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _applyId = $v.applyId;
      _jobCode = $v.jobCode;
      _employCode = $v.employCode;
      _placeCode = $v.placeCode;
      _payrollCode = $v.payrollCode;
      _payrollPrice = $v.payrollPrice;
      _joiningDate = $v.joiningDate;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(HostCompanyUpdateInterviewAdmission other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$HostCompanyUpdateInterviewAdmission;
  }

  @override
  void update(
      void Function(HostCompanyUpdateInterviewAdmissionBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  HostCompanyUpdateInterviewAdmission build() => _build();

  _$HostCompanyUpdateInterviewAdmission _build() {
    final _$result = _$v ??
        new _$HostCompanyUpdateInterviewAdmission._(
          applyId: BuiltValueNullFieldError.checkNotNull(
              applyId, r'HostCompanyUpdateInterviewAdmission', 'applyId'),
          jobCode: BuiltValueNullFieldError.checkNotNull(
              jobCode, r'HostCompanyUpdateInterviewAdmission', 'jobCode'),
          employCode: BuiltValueNullFieldError.checkNotNull(
              employCode, r'HostCompanyUpdateInterviewAdmission', 'employCode'),
          placeCode: BuiltValueNullFieldError.checkNotNull(
              placeCode, r'HostCompanyUpdateInterviewAdmission', 'placeCode'),
          payrollCode: BuiltValueNullFieldError.checkNotNull(payrollCode,
              r'HostCompanyUpdateInterviewAdmission', 'payrollCode'),
          payrollPrice: BuiltValueNullFieldError.checkNotNull(payrollPrice,
              r'HostCompanyUpdateInterviewAdmission', 'payrollPrice'),
          joiningDate: BuiltValueNullFieldError.checkNotNull(joiningDate,
              r'HostCompanyUpdateInterviewAdmission', 'joiningDate'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
