// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'list_recruit_uploaded_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ListRecruitUploadedResponseModel
    extends ListRecruitUploadedResponseModel {
  @override
  final String? message;
  @override
  final ListRecruitSerializers data;
  @override
  final BuiltList<ErrorDetail>? errors;

  factory _$ListRecruitUploadedResponseModel(
          [void Function(ListRecruitUploadedResponseModelBuilder)? updates]) =>
      (new ListRecruitUploadedResponseModelBuilder()..update(updates))._build();

  _$ListRecruitUploadedResponseModel._(
      {this.message, required this.data, this.errors})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'ListRecruitUploadedResponseModel', 'data');
  }

  @override
  ListRecruitUploadedResponseModel rebuild(
          void Function(ListRecruitUploadedResponseModelBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ListRecruitUploadedResponseModelBuilder toBuilder() =>
      new ListRecruitUploadedResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ListRecruitUploadedResponseModel &&
        message == other.message &&
        data == other.data &&
        errors == other.errors;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ListRecruitUploadedResponseModel')
          ..add('message', message)
          ..add('data', data)
          ..add('errors', errors))
        .toString();
  }
}

class ListRecruitUploadedResponseModelBuilder
    implements
        Builder<ListRecruitUploadedResponseModel,
            ListRecruitUploadedResponseModelBuilder> {
  _$ListRecruitUploadedResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  ListRecruitSerializersBuilder? _data;
  ListRecruitSerializersBuilder get data =>
      _$this._data ??= new ListRecruitSerializersBuilder();
  set data(ListRecruitSerializersBuilder? data) => _$this._data = data;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  ListRecruitUploadedResponseModelBuilder() {
    ListRecruitUploadedResponseModel._defaults(this);
  }

  ListRecruitUploadedResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _data = $v.data.toBuilder();
      _errors = $v.errors?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ListRecruitUploadedResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$ListRecruitUploadedResponseModel;
  }

  @override
  void update(void Function(ListRecruitUploadedResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ListRecruitUploadedResponseModel build() => _build();

  _$ListRecruitUploadedResponseModel _build() {
    _$ListRecruitUploadedResponseModel _$result;
    try {
      _$result = _$v ??
          new _$ListRecruitUploadedResponseModel._(
            message: message,
            data: data.build(),
            errors: _errors?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        data.build();
        _$failedField = 'errors';
        _errors?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'ListRecruitUploadedResponseModel', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
