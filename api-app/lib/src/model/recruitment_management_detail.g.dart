// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recruitment_management_detail.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$RecruitmentManagementDetail extends RecruitmentManagementDetail {
  @override
  final int? recruitId;
  @override
  final String? title;
  @override
  final int? totalRecruit;
  @override
  final String? catchCopy;
  @override
  final String? payrollPriceFrom;
  @override
  final String? payrollPriceTo;
  @override
  final String? payrollCode;
  @override
  final DateTime? startDate;
  @override
  final DateTime? endDate;
  @override
  final String? jobCode;
  @override
  final String? employCode;
  @override
  final String? placeCode1;
  @override
  final String? placeCode2;
  @override
  final String? placeCode3;
  @override
  final String? prefCode1;
  @override
  final String? prefCode2;
  @override
  final String? prefCode3;
  @override
  final String? countryCode;
  @override
  final int? ageFrom;
  @override
  final int? ageTo;
  @override
  final String? lastAcademicCode;
  @override
  final String? languageCode1;
  @override
  final int? languageLevelType1;
  @override
  final String? languageCode2;
  @override
  final int? languageLevelType2;
  @override
  final String? experiencedJobCode;
  @override
  final int? yearsOfExperience;
  @override
  final String? skillJobCode1;
  @override
  final String? skillCode1;
  @override
  final int? skillLevelType1;
  @override
  final String? skillJobCode2;
  @override
  final String? skillCode2;
  @override
  final int? skillLevelType2;
  @override
  final String? skillJobCode3;
  @override
  final String? skillCode3;
  @override
  final int? skillLevelType3;
  @override
  final String? content;
  @override
  final int? sexType;
  @override
  final String? licenceCode1;
  @override
  final int? licencePoint1;
  @override
  final String? licenceCode2;
  @override
  final String? licenceName1;
  @override
  final String? licenceName2;
  @override
  final String? licenceName3;
  @override
  final int? licencePoint2;
  @override
  final String? licenceCode3;
  @override
  final int? licencePoint3;
  @override
  final int? recruitProgressCode;
  @override
  final int? waitingFlag;
  @override
  final RecruitCompanyInformation? hostCompany;
  @override
  final BuiltList<RecRecruit>? similarRecruits;
  @override
  final DateTime? interviewDatetime;
  @override
  final double? payrollPrice;
  @override
  final String? placeCode;
  @override
  final Date? joingDate;
  @override
  final int? groupId;
  @override
  final int? applyId;

  factory _$RecruitmentManagementDetail(
          [void Function(RecruitmentManagementDetailBuilder)? updates]) =>
      (new RecruitmentManagementDetailBuilder()..update(updates))._build();

  _$RecruitmentManagementDetail._(
      {this.recruitId,
      this.title,
      this.totalRecruit,
      this.catchCopy,
      this.payrollPriceFrom,
      this.payrollPriceTo,
      this.payrollCode,
      this.startDate,
      this.endDate,
      this.jobCode,
      this.employCode,
      this.placeCode1,
      this.placeCode2,
      this.placeCode3,
      this.prefCode1,
      this.prefCode2,
      this.prefCode3,
      this.countryCode,
      this.ageFrom,
      this.ageTo,
      this.lastAcademicCode,
      this.languageCode1,
      this.languageLevelType1,
      this.languageCode2,
      this.languageLevelType2,
      this.experiencedJobCode,
      this.yearsOfExperience,
      this.skillJobCode1,
      this.skillCode1,
      this.skillLevelType1,
      this.skillJobCode2,
      this.skillCode2,
      this.skillLevelType2,
      this.skillJobCode3,
      this.skillCode3,
      this.skillLevelType3,
      this.content,
      this.sexType,
      this.licenceCode1,
      this.licencePoint1,
      this.licenceCode2,
      this.licenceName1,
      this.licenceName2,
      this.licenceName3,
      this.licencePoint2,
      this.licenceCode3,
      this.licencePoint3,
      this.recruitProgressCode,
      this.waitingFlag,
      this.hostCompany,
      this.similarRecruits,
      this.interviewDatetime,
      this.payrollPrice,
      this.placeCode,
      this.joingDate,
      this.groupId,
      this.applyId})
      : super._();

  @override
  RecruitmentManagementDetail rebuild(
          void Function(RecruitmentManagementDetailBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  RecruitmentManagementDetailBuilder toBuilder() =>
      new RecruitmentManagementDetailBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is RecruitmentManagementDetail &&
        recruitId == other.recruitId &&
        title == other.title &&
        totalRecruit == other.totalRecruit &&
        catchCopy == other.catchCopy &&
        payrollPriceFrom == other.payrollPriceFrom &&
        payrollPriceTo == other.payrollPriceTo &&
        payrollCode == other.payrollCode &&
        startDate == other.startDate &&
        endDate == other.endDate &&
        jobCode == other.jobCode &&
        employCode == other.employCode &&
        placeCode1 == other.placeCode1 &&
        placeCode2 == other.placeCode2 &&
        placeCode3 == other.placeCode3 &&
        prefCode1 == other.prefCode1 &&
        prefCode2 == other.prefCode2 &&
        prefCode3 == other.prefCode3 &&
        countryCode == other.countryCode &&
        ageFrom == other.ageFrom &&
        ageTo == other.ageTo &&
        lastAcademicCode == other.lastAcademicCode &&
        languageCode1 == other.languageCode1 &&
        languageLevelType1 == other.languageLevelType1 &&
        languageCode2 == other.languageCode2 &&
        languageLevelType2 == other.languageLevelType2 &&
        experiencedJobCode == other.experiencedJobCode &&
        yearsOfExperience == other.yearsOfExperience &&
        skillJobCode1 == other.skillJobCode1 &&
        skillCode1 == other.skillCode1 &&
        skillLevelType1 == other.skillLevelType1 &&
        skillJobCode2 == other.skillJobCode2 &&
        skillCode2 == other.skillCode2 &&
        skillLevelType2 == other.skillLevelType2 &&
        skillJobCode3 == other.skillJobCode3 &&
        skillCode3 == other.skillCode3 &&
        skillLevelType3 == other.skillLevelType3 &&
        content == other.content &&
        sexType == other.sexType &&
        licenceCode1 == other.licenceCode1 &&
        licencePoint1 == other.licencePoint1 &&
        licenceCode2 == other.licenceCode2 &&
        licenceName1 == other.licenceName1 &&
        licenceName2 == other.licenceName2 &&
        licenceName3 == other.licenceName3 &&
        licencePoint2 == other.licencePoint2 &&
        licenceCode3 == other.licenceCode3 &&
        licencePoint3 == other.licencePoint3 &&
        recruitProgressCode == other.recruitProgressCode &&
        waitingFlag == other.waitingFlag &&
        hostCompany == other.hostCompany &&
        similarRecruits == other.similarRecruits &&
        interviewDatetime == other.interviewDatetime &&
        payrollPrice == other.payrollPrice &&
        placeCode == other.placeCode &&
        joingDate == other.joingDate &&
        groupId == other.groupId &&
        applyId == other.applyId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, recruitId.hashCode);
    _$hash = $jc(_$hash, title.hashCode);
    _$hash = $jc(_$hash, totalRecruit.hashCode);
    _$hash = $jc(_$hash, catchCopy.hashCode);
    _$hash = $jc(_$hash, payrollPriceFrom.hashCode);
    _$hash = $jc(_$hash, payrollPriceTo.hashCode);
    _$hash = $jc(_$hash, payrollCode.hashCode);
    _$hash = $jc(_$hash, startDate.hashCode);
    _$hash = $jc(_$hash, endDate.hashCode);
    _$hash = $jc(_$hash, jobCode.hashCode);
    _$hash = $jc(_$hash, employCode.hashCode);
    _$hash = $jc(_$hash, placeCode1.hashCode);
    _$hash = $jc(_$hash, placeCode2.hashCode);
    _$hash = $jc(_$hash, placeCode3.hashCode);
    _$hash = $jc(_$hash, prefCode1.hashCode);
    _$hash = $jc(_$hash, prefCode2.hashCode);
    _$hash = $jc(_$hash, prefCode3.hashCode);
    _$hash = $jc(_$hash, countryCode.hashCode);
    _$hash = $jc(_$hash, ageFrom.hashCode);
    _$hash = $jc(_$hash, ageTo.hashCode);
    _$hash = $jc(_$hash, lastAcademicCode.hashCode);
    _$hash = $jc(_$hash, languageCode1.hashCode);
    _$hash = $jc(_$hash, languageLevelType1.hashCode);
    _$hash = $jc(_$hash, languageCode2.hashCode);
    _$hash = $jc(_$hash, languageLevelType2.hashCode);
    _$hash = $jc(_$hash, experiencedJobCode.hashCode);
    _$hash = $jc(_$hash, yearsOfExperience.hashCode);
    _$hash = $jc(_$hash, skillJobCode1.hashCode);
    _$hash = $jc(_$hash, skillCode1.hashCode);
    _$hash = $jc(_$hash, skillLevelType1.hashCode);
    _$hash = $jc(_$hash, skillJobCode2.hashCode);
    _$hash = $jc(_$hash, skillCode2.hashCode);
    _$hash = $jc(_$hash, skillLevelType2.hashCode);
    _$hash = $jc(_$hash, skillJobCode3.hashCode);
    _$hash = $jc(_$hash, skillCode3.hashCode);
    _$hash = $jc(_$hash, skillLevelType3.hashCode);
    _$hash = $jc(_$hash, content.hashCode);
    _$hash = $jc(_$hash, sexType.hashCode);
    _$hash = $jc(_$hash, licenceCode1.hashCode);
    _$hash = $jc(_$hash, licencePoint1.hashCode);
    _$hash = $jc(_$hash, licenceCode2.hashCode);
    _$hash = $jc(_$hash, licenceName1.hashCode);
    _$hash = $jc(_$hash, licenceName2.hashCode);
    _$hash = $jc(_$hash, licenceName3.hashCode);
    _$hash = $jc(_$hash, licencePoint2.hashCode);
    _$hash = $jc(_$hash, licenceCode3.hashCode);
    _$hash = $jc(_$hash, licencePoint3.hashCode);
    _$hash = $jc(_$hash, recruitProgressCode.hashCode);
    _$hash = $jc(_$hash, waitingFlag.hashCode);
    _$hash = $jc(_$hash, hostCompany.hashCode);
    _$hash = $jc(_$hash, similarRecruits.hashCode);
    _$hash = $jc(_$hash, interviewDatetime.hashCode);
    _$hash = $jc(_$hash, payrollPrice.hashCode);
    _$hash = $jc(_$hash, placeCode.hashCode);
    _$hash = $jc(_$hash, joingDate.hashCode);
    _$hash = $jc(_$hash, groupId.hashCode);
    _$hash = $jc(_$hash, applyId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'RecruitmentManagementDetail')
          ..add('recruitId', recruitId)
          ..add('title', title)
          ..add('totalRecruit', totalRecruit)
          ..add('catchCopy', catchCopy)
          ..add('payrollPriceFrom', payrollPriceFrom)
          ..add('payrollPriceTo', payrollPriceTo)
          ..add('payrollCode', payrollCode)
          ..add('startDate', startDate)
          ..add('endDate', endDate)
          ..add('jobCode', jobCode)
          ..add('employCode', employCode)
          ..add('placeCode1', placeCode1)
          ..add('placeCode2', placeCode2)
          ..add('placeCode3', placeCode3)
          ..add('prefCode1', prefCode1)
          ..add('prefCode2', prefCode2)
          ..add('prefCode3', prefCode3)
          ..add('countryCode', countryCode)
          ..add('ageFrom', ageFrom)
          ..add('ageTo', ageTo)
          ..add('lastAcademicCode', lastAcademicCode)
          ..add('languageCode1', languageCode1)
          ..add('languageLevelType1', languageLevelType1)
          ..add('languageCode2', languageCode2)
          ..add('languageLevelType2', languageLevelType2)
          ..add('experiencedJobCode', experiencedJobCode)
          ..add('yearsOfExperience', yearsOfExperience)
          ..add('skillJobCode1', skillJobCode1)
          ..add('skillCode1', skillCode1)
          ..add('skillLevelType1', skillLevelType1)
          ..add('skillJobCode2', skillJobCode2)
          ..add('skillCode2', skillCode2)
          ..add('skillLevelType2', skillLevelType2)
          ..add('skillJobCode3', skillJobCode3)
          ..add('skillCode3', skillCode3)
          ..add('skillLevelType3', skillLevelType3)
          ..add('content', content)
          ..add('sexType', sexType)
          ..add('licenceCode1', licenceCode1)
          ..add('licencePoint1', licencePoint1)
          ..add('licenceCode2', licenceCode2)
          ..add('licenceName1', licenceName1)
          ..add('licenceName2', licenceName2)
          ..add('licenceName3', licenceName3)
          ..add('licencePoint2', licencePoint2)
          ..add('licenceCode3', licenceCode3)
          ..add('licencePoint3', licencePoint3)
          ..add('recruitProgressCode', recruitProgressCode)
          ..add('waitingFlag', waitingFlag)
          ..add('hostCompany', hostCompany)
          ..add('similarRecruits', similarRecruits)
          ..add('interviewDatetime', interviewDatetime)
          ..add('payrollPrice', payrollPrice)
          ..add('placeCode', placeCode)
          ..add('joingDate', joingDate)
          ..add('groupId', groupId)
          ..add('applyId', applyId))
        .toString();
  }
}

class RecruitmentManagementDetailBuilder
    implements
        Builder<RecruitmentManagementDetail,
            RecruitmentManagementDetailBuilder> {
  _$RecruitmentManagementDetail? _$v;

  int? _recruitId;
  int? get recruitId => _$this._recruitId;
  set recruitId(int? recruitId) => _$this._recruitId = recruitId;

  String? _title;
  String? get title => _$this._title;
  set title(String? title) => _$this._title = title;

  int? _totalRecruit;
  int? get totalRecruit => _$this._totalRecruit;
  set totalRecruit(int? totalRecruit) => _$this._totalRecruit = totalRecruit;

  String? _catchCopy;
  String? get catchCopy => _$this._catchCopy;
  set catchCopy(String? catchCopy) => _$this._catchCopy = catchCopy;

  String? _payrollPriceFrom;
  String? get payrollPriceFrom => _$this._payrollPriceFrom;
  set payrollPriceFrom(String? payrollPriceFrom) =>
      _$this._payrollPriceFrom = payrollPriceFrom;

  String? _payrollPriceTo;
  String? get payrollPriceTo => _$this._payrollPriceTo;
  set payrollPriceTo(String? payrollPriceTo) =>
      _$this._payrollPriceTo = payrollPriceTo;

  String? _payrollCode;
  String? get payrollCode => _$this._payrollCode;
  set payrollCode(String? payrollCode) => _$this._payrollCode = payrollCode;

  DateTime? _startDate;
  DateTime? get startDate => _$this._startDate;
  set startDate(DateTime? startDate) => _$this._startDate = startDate;

  DateTime? _endDate;
  DateTime? get endDate => _$this._endDate;
  set endDate(DateTime? endDate) => _$this._endDate = endDate;

  String? _jobCode;
  String? get jobCode => _$this._jobCode;
  set jobCode(String? jobCode) => _$this._jobCode = jobCode;

  String? _employCode;
  String? get employCode => _$this._employCode;
  set employCode(String? employCode) => _$this._employCode = employCode;

  String? _placeCode1;
  String? get placeCode1 => _$this._placeCode1;
  set placeCode1(String? placeCode1) => _$this._placeCode1 = placeCode1;

  String? _placeCode2;
  String? get placeCode2 => _$this._placeCode2;
  set placeCode2(String? placeCode2) => _$this._placeCode2 = placeCode2;

  String? _placeCode3;
  String? get placeCode3 => _$this._placeCode3;
  set placeCode3(String? placeCode3) => _$this._placeCode3 = placeCode3;

  String? _prefCode1;
  String? get prefCode1 => _$this._prefCode1;
  set prefCode1(String? prefCode1) => _$this._prefCode1 = prefCode1;

  String? _prefCode2;
  String? get prefCode2 => _$this._prefCode2;
  set prefCode2(String? prefCode2) => _$this._prefCode2 = prefCode2;

  String? _prefCode3;
  String? get prefCode3 => _$this._prefCode3;
  set prefCode3(String? prefCode3) => _$this._prefCode3 = prefCode3;

  String? _countryCode;
  String? get countryCode => _$this._countryCode;
  set countryCode(String? countryCode) => _$this._countryCode = countryCode;

  int? _ageFrom;
  int? get ageFrom => _$this._ageFrom;
  set ageFrom(int? ageFrom) => _$this._ageFrom = ageFrom;

  int? _ageTo;
  int? get ageTo => _$this._ageTo;
  set ageTo(int? ageTo) => _$this._ageTo = ageTo;

  String? _lastAcademicCode;
  String? get lastAcademicCode => _$this._lastAcademicCode;
  set lastAcademicCode(String? lastAcademicCode) =>
      _$this._lastAcademicCode = lastAcademicCode;

  String? _languageCode1;
  String? get languageCode1 => _$this._languageCode1;
  set languageCode1(String? languageCode1) =>
      _$this._languageCode1 = languageCode1;

  int? _languageLevelType1;
  int? get languageLevelType1 => _$this._languageLevelType1;
  set languageLevelType1(int? languageLevelType1) =>
      _$this._languageLevelType1 = languageLevelType1;

  String? _languageCode2;
  String? get languageCode2 => _$this._languageCode2;
  set languageCode2(String? languageCode2) =>
      _$this._languageCode2 = languageCode2;

  int? _languageLevelType2;
  int? get languageLevelType2 => _$this._languageLevelType2;
  set languageLevelType2(int? languageLevelType2) =>
      _$this._languageLevelType2 = languageLevelType2;

  String? _experiencedJobCode;
  String? get experiencedJobCode => _$this._experiencedJobCode;
  set experiencedJobCode(String? experiencedJobCode) =>
      _$this._experiencedJobCode = experiencedJobCode;

  int? _yearsOfExperience;
  int? get yearsOfExperience => _$this._yearsOfExperience;
  set yearsOfExperience(int? yearsOfExperience) =>
      _$this._yearsOfExperience = yearsOfExperience;

  String? _skillJobCode1;
  String? get skillJobCode1 => _$this._skillJobCode1;
  set skillJobCode1(String? skillJobCode1) =>
      _$this._skillJobCode1 = skillJobCode1;

  String? _skillCode1;
  String? get skillCode1 => _$this._skillCode1;
  set skillCode1(String? skillCode1) => _$this._skillCode1 = skillCode1;

  int? _skillLevelType1;
  int? get skillLevelType1 => _$this._skillLevelType1;
  set skillLevelType1(int? skillLevelType1) =>
      _$this._skillLevelType1 = skillLevelType1;

  String? _skillJobCode2;
  String? get skillJobCode2 => _$this._skillJobCode2;
  set skillJobCode2(String? skillJobCode2) =>
      _$this._skillJobCode2 = skillJobCode2;

  String? _skillCode2;
  String? get skillCode2 => _$this._skillCode2;
  set skillCode2(String? skillCode2) => _$this._skillCode2 = skillCode2;

  int? _skillLevelType2;
  int? get skillLevelType2 => _$this._skillLevelType2;
  set skillLevelType2(int? skillLevelType2) =>
      _$this._skillLevelType2 = skillLevelType2;

  String? _skillJobCode3;
  String? get skillJobCode3 => _$this._skillJobCode3;
  set skillJobCode3(String? skillJobCode3) =>
      _$this._skillJobCode3 = skillJobCode3;

  String? _skillCode3;
  String? get skillCode3 => _$this._skillCode3;
  set skillCode3(String? skillCode3) => _$this._skillCode3 = skillCode3;

  int? _skillLevelType3;
  int? get skillLevelType3 => _$this._skillLevelType3;
  set skillLevelType3(int? skillLevelType3) =>
      _$this._skillLevelType3 = skillLevelType3;

  String? _content;
  String? get content => _$this._content;
  set content(String? content) => _$this._content = content;

  int? _sexType;
  int? get sexType => _$this._sexType;
  set sexType(int? sexType) => _$this._sexType = sexType;

  String? _licenceCode1;
  String? get licenceCode1 => _$this._licenceCode1;
  set licenceCode1(String? licenceCode1) => _$this._licenceCode1 = licenceCode1;

  int? _licencePoint1;
  int? get licencePoint1 => _$this._licencePoint1;
  set licencePoint1(int? licencePoint1) =>
      _$this._licencePoint1 = licencePoint1;

  String? _licenceCode2;
  String? get licenceCode2 => _$this._licenceCode2;
  set licenceCode2(String? licenceCode2) => _$this._licenceCode2 = licenceCode2;

  String? _licenceName1;
  String? get licenceName1 => _$this._licenceName1;
  set licenceName1(String? licenceName1) => _$this._licenceName1 = licenceName1;

  String? _licenceName2;
  String? get licenceName2 => _$this._licenceName2;
  set licenceName2(String? licenceName2) => _$this._licenceName2 = licenceName2;

  String? _licenceName3;
  String? get licenceName3 => _$this._licenceName3;
  set licenceName3(String? licenceName3) => _$this._licenceName3 = licenceName3;

  int? _licencePoint2;
  int? get licencePoint2 => _$this._licencePoint2;
  set licencePoint2(int? licencePoint2) =>
      _$this._licencePoint2 = licencePoint2;

  String? _licenceCode3;
  String? get licenceCode3 => _$this._licenceCode3;
  set licenceCode3(String? licenceCode3) => _$this._licenceCode3 = licenceCode3;

  int? _licencePoint3;
  int? get licencePoint3 => _$this._licencePoint3;
  set licencePoint3(int? licencePoint3) =>
      _$this._licencePoint3 = licencePoint3;

  int? _recruitProgressCode;
  int? get recruitProgressCode => _$this._recruitProgressCode;
  set recruitProgressCode(int? recruitProgressCode) =>
      _$this._recruitProgressCode = recruitProgressCode;

  int? _waitingFlag;
  int? get waitingFlag => _$this._waitingFlag;
  set waitingFlag(int? waitingFlag) => _$this._waitingFlag = waitingFlag;

  RecruitCompanyInformationBuilder? _hostCompany;
  RecruitCompanyInformationBuilder get hostCompany =>
      _$this._hostCompany ??= new RecruitCompanyInformationBuilder();
  set hostCompany(RecruitCompanyInformationBuilder? hostCompany) =>
      _$this._hostCompany = hostCompany;

  ListBuilder<RecRecruit>? _similarRecruits;
  ListBuilder<RecRecruit> get similarRecruits =>
      _$this._similarRecruits ??= new ListBuilder<RecRecruit>();
  set similarRecruits(ListBuilder<RecRecruit>? similarRecruits) =>
      _$this._similarRecruits = similarRecruits;

  DateTime? _interviewDatetime;
  DateTime? get interviewDatetime => _$this._interviewDatetime;
  set interviewDatetime(DateTime? interviewDatetime) =>
      _$this._interviewDatetime = interviewDatetime;

  double? _payrollPrice;
  double? get payrollPrice => _$this._payrollPrice;
  set payrollPrice(double? payrollPrice) => _$this._payrollPrice = payrollPrice;

  String? _placeCode;
  String? get placeCode => _$this._placeCode;
  set placeCode(String? placeCode) => _$this._placeCode = placeCode;

  Date? _joingDate;
  Date? get joingDate => _$this._joingDate;
  set joingDate(Date? joingDate) => _$this._joingDate = joingDate;

  int? _groupId;
  int? get groupId => _$this._groupId;
  set groupId(int? groupId) => _$this._groupId = groupId;

  int? _applyId;
  int? get applyId => _$this._applyId;
  set applyId(int? applyId) => _$this._applyId = applyId;

  RecruitmentManagementDetailBuilder() {
    RecruitmentManagementDetail._defaults(this);
  }

  RecruitmentManagementDetailBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _recruitId = $v.recruitId;
      _title = $v.title;
      _totalRecruit = $v.totalRecruit;
      _catchCopy = $v.catchCopy;
      _payrollPriceFrom = $v.payrollPriceFrom;
      _payrollPriceTo = $v.payrollPriceTo;
      _payrollCode = $v.payrollCode;
      _startDate = $v.startDate;
      _endDate = $v.endDate;
      _jobCode = $v.jobCode;
      _employCode = $v.employCode;
      _placeCode1 = $v.placeCode1;
      _placeCode2 = $v.placeCode2;
      _placeCode3 = $v.placeCode3;
      _prefCode1 = $v.prefCode1;
      _prefCode2 = $v.prefCode2;
      _prefCode3 = $v.prefCode3;
      _countryCode = $v.countryCode;
      _ageFrom = $v.ageFrom;
      _ageTo = $v.ageTo;
      _lastAcademicCode = $v.lastAcademicCode;
      _languageCode1 = $v.languageCode1;
      _languageLevelType1 = $v.languageLevelType1;
      _languageCode2 = $v.languageCode2;
      _languageLevelType2 = $v.languageLevelType2;
      _experiencedJobCode = $v.experiencedJobCode;
      _yearsOfExperience = $v.yearsOfExperience;
      _skillJobCode1 = $v.skillJobCode1;
      _skillCode1 = $v.skillCode1;
      _skillLevelType1 = $v.skillLevelType1;
      _skillJobCode2 = $v.skillJobCode2;
      _skillCode2 = $v.skillCode2;
      _skillLevelType2 = $v.skillLevelType2;
      _skillJobCode3 = $v.skillJobCode3;
      _skillCode3 = $v.skillCode3;
      _skillLevelType3 = $v.skillLevelType3;
      _content = $v.content;
      _sexType = $v.sexType;
      _licenceCode1 = $v.licenceCode1;
      _licencePoint1 = $v.licencePoint1;
      _licenceCode2 = $v.licenceCode2;
      _licenceName1 = $v.licenceName1;
      _licenceName2 = $v.licenceName2;
      _licenceName3 = $v.licenceName3;
      _licencePoint2 = $v.licencePoint2;
      _licenceCode3 = $v.licenceCode3;
      _licencePoint3 = $v.licencePoint3;
      _recruitProgressCode = $v.recruitProgressCode;
      _waitingFlag = $v.waitingFlag;
      _hostCompany = $v.hostCompany?.toBuilder();
      _similarRecruits = $v.similarRecruits?.toBuilder();
      _interviewDatetime = $v.interviewDatetime;
      _payrollPrice = $v.payrollPrice;
      _placeCode = $v.placeCode;
      _joingDate = $v.joingDate;
      _groupId = $v.groupId;
      _applyId = $v.applyId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(RecruitmentManagementDetail other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$RecruitmentManagementDetail;
  }

  @override
  void update(void Function(RecruitmentManagementDetailBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  RecruitmentManagementDetail build() => _build();

  _$RecruitmentManagementDetail _build() {
    _$RecruitmentManagementDetail _$result;
    try {
      _$result = _$v ??
          new _$RecruitmentManagementDetail._(
            recruitId: recruitId,
            title: title,
            totalRecruit: totalRecruit,
            catchCopy: catchCopy,
            payrollPriceFrom: payrollPriceFrom,
            payrollPriceTo: payrollPriceTo,
            payrollCode: payrollCode,
            startDate: startDate,
            endDate: endDate,
            jobCode: jobCode,
            employCode: employCode,
            placeCode1: placeCode1,
            placeCode2: placeCode2,
            placeCode3: placeCode3,
            prefCode1: prefCode1,
            prefCode2: prefCode2,
            prefCode3: prefCode3,
            countryCode: countryCode,
            ageFrom: ageFrom,
            ageTo: ageTo,
            lastAcademicCode: lastAcademicCode,
            languageCode1: languageCode1,
            languageLevelType1: languageLevelType1,
            languageCode2: languageCode2,
            languageLevelType2: languageLevelType2,
            experiencedJobCode: experiencedJobCode,
            yearsOfExperience: yearsOfExperience,
            skillJobCode1: skillJobCode1,
            skillCode1: skillCode1,
            skillLevelType1: skillLevelType1,
            skillJobCode2: skillJobCode2,
            skillCode2: skillCode2,
            skillLevelType2: skillLevelType2,
            skillJobCode3: skillJobCode3,
            skillCode3: skillCode3,
            skillLevelType3: skillLevelType3,
            content: content,
            sexType: sexType,
            licenceCode1: licenceCode1,
            licencePoint1: licencePoint1,
            licenceCode2: licenceCode2,
            licenceName1: licenceName1,
            licenceName2: licenceName2,
            licenceName3: licenceName3,
            licencePoint2: licencePoint2,
            licenceCode3: licenceCode3,
            licencePoint3: licencePoint3,
            recruitProgressCode: recruitProgressCode,
            waitingFlag: waitingFlag,
            hostCompany: _hostCompany?.build(),
            similarRecruits: _similarRecruits?.build(),
            interviewDatetime: interviewDatetime,
            payrollPrice: payrollPrice,
            placeCode: placeCode,
            joingDate: joingDate,
            groupId: groupId,
            applyId: applyId,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'hostCompany';
        _hostCompany?.build();
        _$failedField = 'similarRecruits';
        _similarRecruits?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'RecruitmentManagementDetail', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
