//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'evaluation_method.g.dart';

/// EvaluationMethod
///
/// Properties:
/// * [textEn] 
/// * [textVi] 
/// * [text] 
@BuiltValue()
abstract class EvaluationMethod implements Built<EvaluationMethod, EvaluationMethodBuilder> {
  @BuiltValueField(wireName: r'text_en')
  String get textEn;

  @BuiltValueField(wireName: r'text_vi')
  String get textVi;

  @BuiltValueField(wireName: r'text')
  String get text;

  EvaluationMethod._();

  factory EvaluationMethod([void updates(EvaluationMethodBuilder b)]) = _$EvaluationMethod;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(EvaluationMethodBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<EvaluationMethod> get serializer => _$EvaluationMethodSerializer();
}

class _$EvaluationMethodSerializer implements PrimitiveSerializer<EvaluationMethod> {
  @override
  final Iterable<Type> types = const [EvaluationMethod, _$EvaluationMethod];

  @override
  final String wireName = r'EvaluationMethod';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    EvaluationMethod object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'text_en';
    yield serializers.serialize(
      object.textEn,
      specifiedType: const FullType(String),
    );
    yield r'text_vi';
    yield serializers.serialize(
      object.textVi,
      specifiedType: const FullType(String),
    );
    yield r'text';
    yield serializers.serialize(
      object.text,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    EvaluationMethod object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required EvaluationMethodBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'text_en':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.textEn = valueDes;
          break;
        case r'text_vi':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.textVi = valueDes;
          break;
        case r'text':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.text = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  EvaluationMethod deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = EvaluationMethodBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

