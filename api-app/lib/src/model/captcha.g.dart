// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'captcha.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$Captcha extends Captcha {
  @override
  final String key;
  @override
  final String imageUrl;

  factory _$Captcha([void Function(CaptchaBuilder)? updates]) =>
      (new CaptchaBuilder()..update(updates))._build();

  _$Captcha._({required this.key, required this.imageUrl}) : super._() {
    BuiltValueNullFieldError.checkNotNull(key, r'Captcha', 'key');
    BuiltValueNullFieldError.checkNotNull(imageUrl, r'Captcha', 'imageUrl');
  }

  @override
  Captcha rebuild(void Function(CaptchaBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  CaptchaBuilder toBuilder() => new CaptchaBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is Captcha && key == other.key && imageUrl == other.imageUrl;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, key.hashCode);
    _$hash = $jc(_$hash, imageUrl.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'Captcha')
          ..add('key', key)
          ..add('imageUrl', imageUrl))
        .toString();
  }
}

class CaptchaBuilder implements Builder<Captcha, CaptchaBuilder> {
  _$Captcha? _$v;

  String? _key;
  String? get key => _$this._key;
  set key(String? key) => _$this._key = key;

  String? _imageUrl;
  String? get imageUrl => _$this._imageUrl;
  set imageUrl(String? imageUrl) => _$this._imageUrl = imageUrl;

  CaptchaBuilder() {
    Captcha._defaults(this);
  }

  CaptchaBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _key = $v.key;
      _imageUrl = $v.imageUrl;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(Captcha other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$Captcha;
  }

  @override
  void update(void Function(CaptchaBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  Captcha build() => _build();

  _$Captcha _build() {
    final _$result = _$v ??
        new _$Captcha._(
          key: BuiltValueNullFieldError.checkNotNull(key, r'Captcha', 'key'),
          imageUrl: BuiltValueNullFieldError.checkNotNull(
              imageUrl, r'Captcha', 'imageUrl'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
