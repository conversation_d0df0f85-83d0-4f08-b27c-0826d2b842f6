// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'self_assessment_answer.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$SelfAssessmentAnswer extends SelfAssessmentAnswer {
  @override
  final BuiltList<String?> listAnswer;

  factory _$SelfAssessmentAnswer(
          [void Function(SelfAssessmentAnswerBuilder)? updates]) =>
      (new SelfAssessmentAnswerBuilder()..update(updates))._build();

  _$SelfAssessmentAnswer._({required this.listAnswer}) : super._() {
    BuiltValueNullFieldError.checkNotNull(
        listAnswer, r'SelfAssessmentAnswer', 'listAnswer');
  }

  @override
  SelfAssessmentAnswer rebuild(
          void Function(SelfAssessmentAnswerBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SelfAssessmentAnswerBuilder toBuilder() =>
      new SelfAssessmentAnswerBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SelfAssessmentAnswer && listAnswer == other.listAnswer;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, listAnswer.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SelfAssessmentAnswer')
          ..add('listAnswer', listAnswer))
        .toString();
  }
}

class SelfAssessmentAnswerBuilder
    implements Builder<SelfAssessmentAnswer, SelfAssessmentAnswerBuilder> {
  _$SelfAssessmentAnswer? _$v;

  ListBuilder<String?>? _listAnswer;
  ListBuilder<String?> get listAnswer =>
      _$this._listAnswer ??= new ListBuilder<String?>();
  set listAnswer(ListBuilder<String?>? listAnswer) =>
      _$this._listAnswer = listAnswer;

  SelfAssessmentAnswerBuilder() {
    SelfAssessmentAnswer._defaults(this);
  }

  SelfAssessmentAnswerBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _listAnswer = $v.listAnswer.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SelfAssessmentAnswer other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$SelfAssessmentAnswer;
  }

  @override
  void update(void Function(SelfAssessmentAnswerBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SelfAssessmentAnswer build() => _build();

  _$SelfAssessmentAnswer _build() {
    _$SelfAssessmentAnswer _$result;
    try {
      _$result = _$v ??
          new _$SelfAssessmentAnswer._(
            listAnswer: listAnswer.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'listAnswer';
        listAnswer.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'SelfAssessmentAnswer', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
