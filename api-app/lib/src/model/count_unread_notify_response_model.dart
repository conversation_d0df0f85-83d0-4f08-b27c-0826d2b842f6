//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/count_unread_notify.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'count_unread_notify_response_model.g.dart';

/// CountUnreadNotifyResponseModel
///
/// Properties:
/// * [message] 
/// * [data] 
/// * [errors] 
@BuiltValue()
abstract class CountUnreadNotifyResponseModel implements Built<CountUnreadNotifyResponseModel, CountUnreadNotifyResponseModelBuilder> {
  @BuiltValueField(wireName: r'message')
  String? get message;

  @BuiltValueField(wireName: r'data')
  CountUnreadNotify get data;

  @BuiltValueField(wireName: r'errors')
  BuiltList<String>? get errors;

  CountUnreadNotifyResponseModel._();

  factory CountUnreadNotifyResponseModel([void updates(CountUnreadNotifyResponseModelBuilder b)]) = _$CountUnreadNotifyResponseModel;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(CountUnreadNotifyResponseModelBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<CountUnreadNotifyResponseModel> get serializer => _$CountUnreadNotifyResponseModelSerializer();
}

class _$CountUnreadNotifyResponseModelSerializer implements PrimitiveSerializer<CountUnreadNotifyResponseModel> {
  @override
  final Iterable<Type> types = const [CountUnreadNotifyResponseModel, _$CountUnreadNotifyResponseModel];

  @override
  final String wireName = r'CountUnreadNotifyResponseModel';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    CountUnreadNotifyResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'message';
    yield object.message == null ? null : serializers.serialize(
      object.message,
      specifiedType: const FullType.nullable(String),
    );
    yield r'data';
    yield serializers.serialize(
      object.data,
      specifiedType: const FullType(CountUnreadNotify),
    );
    yield r'errors';
    yield object.errors == null ? null : serializers.serialize(
      object.errors,
      specifiedType: const FullType.nullable(BuiltList, [FullType(String)]),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    CountUnreadNotifyResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required CountUnreadNotifyResponseModelBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'message':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.message = valueDes;
          break;
        case r'data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(CountUnreadNotify),
          ) as CountUnreadNotify;
          result.data.replace(valueDes);
          break;
        case r'errors':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(BuiltList, [FullType(String)]),
          ) as BuiltList<String>?;
          if (valueDes == null) continue;
          result.errors.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  CountUnreadNotifyResponseModel deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = CountUnreadNotifyResponseModelBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

