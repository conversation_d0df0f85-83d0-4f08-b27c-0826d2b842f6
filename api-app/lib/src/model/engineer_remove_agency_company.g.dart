// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'engineer_remove_agency_company.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$EngineerRemoveAgencyCompany extends EngineerRemoveAgencyCompany {
  @override
  final int? optionalEngineerId;
  @override
  final int companyId;

  factory _$EngineerRemoveAgencyCompany(
          [void Function(EngineerRemoveAgencyCompanyBuilder)? updates]) =>
      (new EngineerRemoveAgencyCompanyBuilder()..update(updates))._build();

  _$EngineerRemoveAgencyCompany._(
      {this.optionalEngineerId, required this.companyId})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        companyId, r'EngineerRemoveAgencyCompany', 'companyId');
  }

  @override
  EngineerRemoveAgencyCompany rebuild(
          void Function(EngineerRemoveAgencyCompanyBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  EngineerRemoveAgencyCompanyBuilder toBuilder() =>
      new EngineerRemoveAgencyCompanyBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is EngineerRemoveAgencyCompany &&
        optionalEngineerId == other.optionalEngineerId &&
        companyId == other.companyId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, optionalEngineerId.hashCode);
    _$hash = $jc(_$hash, companyId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'EngineerRemoveAgencyCompany')
          ..add('optionalEngineerId', optionalEngineerId)
          ..add('companyId', companyId))
        .toString();
  }
}

class EngineerRemoveAgencyCompanyBuilder
    implements
        Builder<EngineerRemoveAgencyCompany,
            EngineerRemoveAgencyCompanyBuilder> {
  _$EngineerRemoveAgencyCompany? _$v;

  int? _optionalEngineerId;
  int? get optionalEngineerId => _$this._optionalEngineerId;
  set optionalEngineerId(int? optionalEngineerId) =>
      _$this._optionalEngineerId = optionalEngineerId;

  int? _companyId;
  int? get companyId => _$this._companyId;
  set companyId(int? companyId) => _$this._companyId = companyId;

  EngineerRemoveAgencyCompanyBuilder() {
    EngineerRemoveAgencyCompany._defaults(this);
  }

  EngineerRemoveAgencyCompanyBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _optionalEngineerId = $v.optionalEngineerId;
      _companyId = $v.companyId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(EngineerRemoveAgencyCompany other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$EngineerRemoveAgencyCompany;
  }

  @override
  void update(void Function(EngineerRemoveAgencyCompanyBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  EngineerRemoveAgencyCompany build() => _build();

  _$EngineerRemoveAgencyCompany _build() {
    final _$result = _$v ??
        new _$EngineerRemoveAgencyCompany._(
          optionalEngineerId: optionalEngineerId,
          companyId: BuiltValueNullFieldError.checkNotNull(
              companyId, r'EngineerRemoveAgencyCompany', 'companyId'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
