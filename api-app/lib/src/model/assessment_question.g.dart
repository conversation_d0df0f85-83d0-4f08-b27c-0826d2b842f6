// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'assessment_question.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AssessmentQuestion extends AssessmentQuestion {
  @override
  final String? text;
  @override
  final String textEn;
  @override
  final String textVi;

  factory _$AssessmentQuestion(
          [void Function(AssessmentQuestionBuilder)? updates]) =>
      (new AssessmentQuestionBuilder()..update(updates))._build();

  _$AssessmentQuestion._(
      {this.text, required this.textEn, required this.textVi})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        textEn, r'AssessmentQuestion', 'textEn');
    BuiltValueNullFieldError.checkNotNull(
        textVi, r'AssessmentQuestion', 'textVi');
  }

  @override
  AssessmentQuestion rebuild(
          void Function(AssessmentQuestionBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AssessmentQuestionBuilder toBuilder() =>
      new AssessmentQuestionBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AssessmentQuestion &&
        text == other.text &&
        textEn == other.textEn &&
        textVi == other.textVi;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, text.hashCode);
    _$hash = $jc(_$hash, textEn.hashCode);
    _$hash = $jc(_$hash, textVi.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AssessmentQuestion')
          ..add('text', text)
          ..add('textEn', textEn)
          ..add('textVi', textVi))
        .toString();
  }
}

class AssessmentQuestionBuilder
    implements Builder<AssessmentQuestion, AssessmentQuestionBuilder> {
  _$AssessmentQuestion? _$v;

  String? _text;
  String? get text => _$this._text;
  set text(String? text) => _$this._text = text;

  String? _textEn;
  String? get textEn => _$this._textEn;
  set textEn(String? textEn) => _$this._textEn = textEn;

  String? _textVi;
  String? get textVi => _$this._textVi;
  set textVi(String? textVi) => _$this._textVi = textVi;

  AssessmentQuestionBuilder() {
    AssessmentQuestion._defaults(this);
  }

  AssessmentQuestionBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _text = $v.text;
      _textEn = $v.textEn;
      _textVi = $v.textVi;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AssessmentQuestion other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$AssessmentQuestion;
  }

  @override
  void update(void Function(AssessmentQuestionBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AssessmentQuestion build() => _build();

  _$AssessmentQuestion _build() {
    final _$result = _$v ??
        new _$AssessmentQuestion._(
          text: text,
          textEn: BuiltValueNullFieldError.checkNotNull(
              textEn, r'AssessmentQuestion', 'textEn'),
          textVi: BuiltValueNullFieldError.checkNotNull(
              textVi, r'AssessmentQuestion', 'textVi'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
