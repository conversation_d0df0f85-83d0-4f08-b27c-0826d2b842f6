// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recruitment_details_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$RecruitmentDetailsResponseModel
    extends RecruitmentDetailsResponseModel {
  @override
  final String? message;
  @override
  final RecruitDetail data;
  @override
  final BuiltList<ErrorDetail>? errors;

  factory _$RecruitmentDetailsResponseModel(
          [void Function(RecruitmentDetailsResponseModelBuilder)? updates]) =>
      (new RecruitmentDetailsResponseModelBuilder()..update(updates))._build();

  _$RecruitmentDetailsResponseModel._(
      {this.message, required this.data, this.errors})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'RecruitmentDetailsResponseModel', 'data');
  }

  @override
  RecruitmentDetailsResponseModel rebuild(
          void Function(RecruitmentDetailsResponseModelBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  RecruitmentDetailsResponseModelBuilder toBuilder() =>
      new RecruitmentDetailsResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is RecruitmentDetailsResponseModel &&
        message == other.message &&
        data == other.data &&
        errors == other.errors;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'RecruitmentDetailsResponseModel')
          ..add('message', message)
          ..add('data', data)
          ..add('errors', errors))
        .toString();
  }
}

class RecruitmentDetailsResponseModelBuilder
    implements
        Builder<RecruitmentDetailsResponseModel,
            RecruitmentDetailsResponseModelBuilder> {
  _$RecruitmentDetailsResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  RecruitDetailBuilder? _data;
  RecruitDetailBuilder get data => _$this._data ??= new RecruitDetailBuilder();
  set data(RecruitDetailBuilder? data) => _$this._data = data;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  RecruitmentDetailsResponseModelBuilder() {
    RecruitmentDetailsResponseModel._defaults(this);
  }

  RecruitmentDetailsResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _data = $v.data.toBuilder();
      _errors = $v.errors?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(RecruitmentDetailsResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$RecruitmentDetailsResponseModel;
  }

  @override
  void update(void Function(RecruitmentDetailsResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  RecruitmentDetailsResponseModel build() => _build();

  _$RecruitmentDetailsResponseModel _build() {
    _$RecruitmentDetailsResponseModel _$result;
    try {
      _$result = _$v ??
          new _$RecruitmentDetailsResponseModel._(
            message: message,
            data: data.build(),
            errors: _errors?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        data.build();
        _$failedField = 'errors';
        _errors?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'RecruitmentDetailsResponseModel', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
