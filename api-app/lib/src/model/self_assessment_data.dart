//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/self_assessment_sheet.dart';
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/evaluation_method.dart';
import 'package:openapi/src/model/questions.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'self_assessment_data.g.dart';

/// SelfAssessmentData
///
/// Properties:
/// * [remoteWorkSkills] 
/// * [communicationSkillsSelfAssessmentSheet] 
/// * [globalResponsivenessSkills] 
/// * [reportingConsultationSkillsSelfEvaluation] 
/// * [projectManagementSkillsSelfEvaluation] 
/// * [evaluationMethods] 
/// * [questions] 
@BuiltValue()
abstract class SelfAssessmentData implements Built<SelfAssessmentData, SelfAssessmentDataBuilder> {
  @BuiltValueField(wireName: r'remote_work_skills')
  SelfAssessmentSheet? get remoteWorkSkills;

  @BuiltValueField(wireName: r'communication_skills_self_assessment_sheet')
  SelfAssessmentSheet? get communicationSkillsSelfAssessmentSheet;

  @BuiltValueField(wireName: r'global_responsiveness_skills')
  SelfAssessmentSheet? get globalResponsivenessSkills;

  @BuiltValueField(wireName: r'reporting_consultation_skills_self_evaluation')
  SelfAssessmentSheet? get reportingConsultationSkillsSelfEvaluation;

  @BuiltValueField(wireName: r'project_management_skills_self_evaluation')
  SelfAssessmentSheet? get projectManagementSkillsSelfEvaluation;

  @BuiltValueField(wireName: r'evaluation_methods')
  BuiltList<EvaluationMethod> get evaluationMethods;

  @BuiltValueField(wireName: r'questions')
  BuiltList<Questions> get questions;

  SelfAssessmentData._();

  factory SelfAssessmentData([void updates(SelfAssessmentDataBuilder b)]) = _$SelfAssessmentData;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(SelfAssessmentDataBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<SelfAssessmentData> get serializer => _$SelfAssessmentDataSerializer();
}

class _$SelfAssessmentDataSerializer implements PrimitiveSerializer<SelfAssessmentData> {
  @override
  final Iterable<Type> types = const [SelfAssessmentData, _$SelfAssessmentData];

  @override
  final String wireName = r'SelfAssessmentData';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    SelfAssessmentData object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'remote_work_skills';
    yield object.remoteWorkSkills == null ? null : serializers.serialize(
      object.remoteWorkSkills,
      specifiedType: const FullType.nullable(SelfAssessmentSheet),
    );
    yield r'communication_skills_self_assessment_sheet';
    yield object.communicationSkillsSelfAssessmentSheet == null ? null : serializers.serialize(
      object.communicationSkillsSelfAssessmentSheet,
      specifiedType: const FullType.nullable(SelfAssessmentSheet),
    );
    yield r'global_responsiveness_skills';
    yield object.globalResponsivenessSkills == null ? null : serializers.serialize(
      object.globalResponsivenessSkills,
      specifiedType: const FullType.nullable(SelfAssessmentSheet),
    );
    yield r'reporting_consultation_skills_self_evaluation';
    yield object.reportingConsultationSkillsSelfEvaluation == null ? null : serializers.serialize(
      object.reportingConsultationSkillsSelfEvaluation,
      specifiedType: const FullType.nullable(SelfAssessmentSheet),
    );
    yield r'project_management_skills_self_evaluation';
    yield object.projectManagementSkillsSelfEvaluation == null ? null : serializers.serialize(
      object.projectManagementSkillsSelfEvaluation,
      specifiedType: const FullType.nullable(SelfAssessmentSheet),
    );
    yield r'evaluation_methods';
    yield serializers.serialize(
      object.evaluationMethods,
      specifiedType: const FullType(BuiltList, [FullType(EvaluationMethod)]),
    );
    yield r'questions';
    yield serializers.serialize(
      object.questions,
      specifiedType: const FullType(BuiltList, [FullType(Questions)]),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    SelfAssessmentData object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required SelfAssessmentDataBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'remote_work_skills':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(SelfAssessmentSheet),
          ) as SelfAssessmentSheet?;
          if (valueDes == null) continue;
          result.remoteWorkSkills.replace(valueDes);
          break;
        case r'communication_skills_self_assessment_sheet':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(SelfAssessmentSheet),
          ) as SelfAssessmentSheet?;
          if (valueDes == null) continue;
          result.communicationSkillsSelfAssessmentSheet.replace(valueDes);
          break;
        case r'global_responsiveness_skills':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(SelfAssessmentSheet),
          ) as SelfAssessmentSheet?;
          if (valueDes == null) continue;
          result.globalResponsivenessSkills.replace(valueDes);
          break;
        case r'reporting_consultation_skills_self_evaluation':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(SelfAssessmentSheet),
          ) as SelfAssessmentSheet?;
          if (valueDes == null) continue;
          result.reportingConsultationSkillsSelfEvaluation.replace(valueDes);
          break;
        case r'project_management_skills_self_evaluation':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(SelfAssessmentSheet),
          ) as SelfAssessmentSheet?;
          if (valueDes == null) continue;
          result.projectManagementSkillsSelfEvaluation.replace(valueDes);
          break;
        case r'evaluation_methods':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(EvaluationMethod)]),
          ) as BuiltList<EvaluationMethod>;
          result.evaluationMethods.replace(valueDes);
          break;
        case r'questions':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(Questions)]),
          ) as BuiltList<Questions>;
          result.questions.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  SelfAssessmentData deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = SelfAssessmentDataBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

