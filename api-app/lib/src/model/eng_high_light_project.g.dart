// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'eng_high_light_project.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$EngHighLightProject extends EngHighLightProject {
  @override
  final String? name;
  @override
  final String? description;
  @override
  final String? size;
  @override
  final String? roleName;
  @override
  final String? responsibilities;
  @override
  final String? technologyUsed;
  @override
  final Date? fromDate;
  @override
  final Date? toDate;

  factory _$EngHighLightProject(
          [void Function(EngHighLightProjectBuilder)? updates]) =>
      (new EngHighLightProjectBuilder()..update(updates))._build();

  _$EngHighLightProject._(
      {this.name,
      this.description,
      this.size,
      this.roleName,
      this.responsibilities,
      this.technologyUsed,
      this.fromDate,
      this.toDate})
      : super._();

  @override
  EngHighLightProject rebuild(
          void Function(EngHighLightProjectBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  EngHighLightProjectBuilder toBuilder() =>
      new EngHighLightProjectBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is EngHighLightProject &&
        name == other.name &&
        description == other.description &&
        size == other.size &&
        roleName == other.roleName &&
        responsibilities == other.responsibilities &&
        technologyUsed == other.technologyUsed &&
        fromDate == other.fromDate &&
        toDate == other.toDate;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, size.hashCode);
    _$hash = $jc(_$hash, roleName.hashCode);
    _$hash = $jc(_$hash, responsibilities.hashCode);
    _$hash = $jc(_$hash, technologyUsed.hashCode);
    _$hash = $jc(_$hash, fromDate.hashCode);
    _$hash = $jc(_$hash, toDate.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'EngHighLightProject')
          ..add('name', name)
          ..add('description', description)
          ..add('size', size)
          ..add('roleName', roleName)
          ..add('responsibilities', responsibilities)
          ..add('technologyUsed', technologyUsed)
          ..add('fromDate', fromDate)
          ..add('toDate', toDate))
        .toString();
  }
}

class EngHighLightProjectBuilder
    implements Builder<EngHighLightProject, EngHighLightProjectBuilder> {
  _$EngHighLightProject? _$v;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  String? _size;
  String? get size => _$this._size;
  set size(String? size) => _$this._size = size;

  String? _roleName;
  String? get roleName => _$this._roleName;
  set roleName(String? roleName) => _$this._roleName = roleName;

  String? _responsibilities;
  String? get responsibilities => _$this._responsibilities;
  set responsibilities(String? responsibilities) =>
      _$this._responsibilities = responsibilities;

  String? _technologyUsed;
  String? get technologyUsed => _$this._technologyUsed;
  set technologyUsed(String? technologyUsed) =>
      _$this._technologyUsed = technologyUsed;

  Date? _fromDate;
  Date? get fromDate => _$this._fromDate;
  set fromDate(Date? fromDate) => _$this._fromDate = fromDate;

  Date? _toDate;
  Date? get toDate => _$this._toDate;
  set toDate(Date? toDate) => _$this._toDate = toDate;

  EngHighLightProjectBuilder() {
    EngHighLightProject._defaults(this);
  }

  EngHighLightProjectBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _name = $v.name;
      _description = $v.description;
      _size = $v.size;
      _roleName = $v.roleName;
      _responsibilities = $v.responsibilities;
      _technologyUsed = $v.technologyUsed;
      _fromDate = $v.fromDate;
      _toDate = $v.toDate;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(EngHighLightProject other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$EngHighLightProject;
  }

  @override
  void update(void Function(EngHighLightProjectBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  EngHighLightProject build() => _build();

  _$EngHighLightProject _build() {
    final _$result = _$v ??
        new _$EngHighLightProject._(
          name: name,
          description: description,
          size: size,
          roleName: roleName,
          responsibilities: responsibilities,
          technologyUsed: technologyUsed,
          fromDate: fromDate,
          toDate: toDate,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
