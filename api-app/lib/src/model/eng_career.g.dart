// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'eng_career.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$EngCareer extends EngCareer {
  @override
  final int? careerId;
  @override
  final int engineerId;
  @override
  final String? companyName;
  @override
  final String? jobDescription;
  @override
  final int? careerType;
  @override
  final Date? enteringDate;
  @override
  final Date? quittingDate;
  @override
  final BuiltList<EngCareerJobSkill>? careerJobSkills;
  @override
  final String? roleName;
  @override
  final String? jobCode;

  factory _$EngCareer([void Function(EngCareerBuilder)? updates]) =>
      (new EngCareerBuilder()..update(updates))._build();

  _$EngCareer._(
      {this.careerId,
      required this.engineerId,
      this.companyName,
      this.jobDescription,
      this.careerType,
      this.enteringDate,
      this.quittingDate,
      this.careerJobSkills,
      this.roleName,
      this.jobCode})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        engineerId, r'EngCareer', 'engineerId');
  }

  @override
  EngCareer rebuild(void Function(EngCareerBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  EngCareerBuilder toBuilder() => new EngCareerBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is EngCareer &&
        careerId == other.careerId &&
        engineerId == other.engineerId &&
        companyName == other.companyName &&
        jobDescription == other.jobDescription &&
        careerType == other.careerType &&
        enteringDate == other.enteringDate &&
        quittingDate == other.quittingDate &&
        careerJobSkills == other.careerJobSkills &&
        roleName == other.roleName &&
        jobCode == other.jobCode;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, careerId.hashCode);
    _$hash = $jc(_$hash, engineerId.hashCode);
    _$hash = $jc(_$hash, companyName.hashCode);
    _$hash = $jc(_$hash, jobDescription.hashCode);
    _$hash = $jc(_$hash, careerType.hashCode);
    _$hash = $jc(_$hash, enteringDate.hashCode);
    _$hash = $jc(_$hash, quittingDate.hashCode);
    _$hash = $jc(_$hash, careerJobSkills.hashCode);
    _$hash = $jc(_$hash, roleName.hashCode);
    _$hash = $jc(_$hash, jobCode.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'EngCareer')
          ..add('careerId', careerId)
          ..add('engineerId', engineerId)
          ..add('companyName', companyName)
          ..add('jobDescription', jobDescription)
          ..add('careerType', careerType)
          ..add('enteringDate', enteringDate)
          ..add('quittingDate', quittingDate)
          ..add('careerJobSkills', careerJobSkills)
          ..add('roleName', roleName)
          ..add('jobCode', jobCode))
        .toString();
  }
}

class EngCareerBuilder implements Builder<EngCareer, EngCareerBuilder> {
  _$EngCareer? _$v;

  int? _careerId;
  int? get careerId => _$this._careerId;
  set careerId(int? careerId) => _$this._careerId = careerId;

  int? _engineerId;
  int? get engineerId => _$this._engineerId;
  set engineerId(int? engineerId) => _$this._engineerId = engineerId;

  String? _companyName;
  String? get companyName => _$this._companyName;
  set companyName(String? companyName) => _$this._companyName = companyName;

  String? _jobDescription;
  String? get jobDescription => _$this._jobDescription;
  set jobDescription(String? jobDescription) =>
      _$this._jobDescription = jobDescription;

  int? _careerType;
  int? get careerType => _$this._careerType;
  set careerType(int? careerType) => _$this._careerType = careerType;

  Date? _enteringDate;
  Date? get enteringDate => _$this._enteringDate;
  set enteringDate(Date? enteringDate) => _$this._enteringDate = enteringDate;

  Date? _quittingDate;
  Date? get quittingDate => _$this._quittingDate;
  set quittingDate(Date? quittingDate) => _$this._quittingDate = quittingDate;

  ListBuilder<EngCareerJobSkill>? _careerJobSkills;
  ListBuilder<EngCareerJobSkill> get careerJobSkills =>
      _$this._careerJobSkills ??= new ListBuilder<EngCareerJobSkill>();
  set careerJobSkills(ListBuilder<EngCareerJobSkill>? careerJobSkills) =>
      _$this._careerJobSkills = careerJobSkills;

  String? _roleName;
  String? get roleName => _$this._roleName;
  set roleName(String? roleName) => _$this._roleName = roleName;

  String? _jobCode;
  String? get jobCode => _$this._jobCode;
  set jobCode(String? jobCode) => _$this._jobCode = jobCode;

  EngCareerBuilder() {
    EngCareer._defaults(this);
  }

  EngCareerBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _careerId = $v.careerId;
      _engineerId = $v.engineerId;
      _companyName = $v.companyName;
      _jobDescription = $v.jobDescription;
      _careerType = $v.careerType;
      _enteringDate = $v.enteringDate;
      _quittingDate = $v.quittingDate;
      _careerJobSkills = $v.careerJobSkills?.toBuilder();
      _roleName = $v.roleName;
      _jobCode = $v.jobCode;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(EngCareer other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$EngCareer;
  }

  @override
  void update(void Function(EngCareerBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  EngCareer build() => _build();

  _$EngCareer _build() {
    _$EngCareer _$result;
    try {
      _$result = _$v ??
          new _$EngCareer._(
            careerId: careerId,
            engineerId: BuiltValueNullFieldError.checkNotNull(
                engineerId, r'EngCareer', 'engineerId'),
            companyName: companyName,
            jobDescription: jobDescription,
            careerType: careerType,
            enteringDate: enteringDate,
            quittingDate: quittingDate,
            careerJobSkills: _careerJobSkills?.build(),
            roleName: roleName,
            jobCode: jobCode,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'careerJobSkills';
        _careerJobSkills?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'EngCareer', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
