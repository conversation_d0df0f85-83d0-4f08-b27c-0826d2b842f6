//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'reset_password.g.dart';

/// ResetPassword
///
/// Properties:
/// * [email] 
@BuiltValue()
abstract class ResetPassword implements Built<ResetPassword, ResetPasswordBuilder> {
  @BuiltValueField(wireName: r'email')
  String get email;

  ResetPassword._();

  factory ResetPassword([void updates(ResetPasswordBuilder b)]) = _$ResetPassword;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(ResetPasswordBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<ResetPassword> get serializer => _$ResetPasswordSerializer();
}

class _$ResetPasswordSerializer implements PrimitiveSerializer<ResetPassword> {
  @override
  final Iterable<Type> types = const [ResetPassword, _$ResetPassword];

  @override
  final String wireName = r'ResetPassword';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    ResetPassword object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'email';
    yield serializers.serialize(
      object.email,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    ResetPassword object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required ResetPasswordBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'email':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.email = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  ResetPassword deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = ResetPasswordBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

