// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_eng_license.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UpdateEngLicense extends UpdateEngLicense {
  @override
  final String? licenceCode;
  @override
  final String? licenceName;
  @override
  final String? getDate;
  @override
  final double? licencePoint;

  factory _$UpdateEngLicense(
          [void Function(UpdateEngLicenseBuilder)? updates]) =>
      (new UpdateEngLicenseBuilder()..update(updates))._build();

  _$UpdateEngLicense._(
      {this.licenceCode, this.licenceName, this.getDate, this.licencePoint})
      : super._();

  @override
  UpdateEngLicense rebuild(void Function(UpdateEngLicenseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UpdateEngLicenseBuilder toBuilder() =>
      new UpdateEngLicenseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UpdateEngLicense &&
        licenceCode == other.licenceCode &&
        licenceName == other.licenceName &&
        getDate == other.getDate &&
        licencePoint == other.licencePoint;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, licenceCode.hashCode);
    _$hash = $jc(_$hash, licenceName.hashCode);
    _$hash = $jc(_$hash, getDate.hashCode);
    _$hash = $jc(_$hash, licencePoint.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UpdateEngLicense')
          ..add('licenceCode', licenceCode)
          ..add('licenceName', licenceName)
          ..add('getDate', getDate)
          ..add('licencePoint', licencePoint))
        .toString();
  }
}

class UpdateEngLicenseBuilder
    implements Builder<UpdateEngLicense, UpdateEngLicenseBuilder> {
  _$UpdateEngLicense? _$v;

  String? _licenceCode;
  String? get licenceCode => _$this._licenceCode;
  set licenceCode(String? licenceCode) => _$this._licenceCode = licenceCode;

  String? _licenceName;
  String? get licenceName => _$this._licenceName;
  set licenceName(String? licenceName) => _$this._licenceName = licenceName;

  String? _getDate;
  String? get getDate => _$this._getDate;
  set getDate(String? getDate) => _$this._getDate = getDate;

  double? _licencePoint;
  double? get licencePoint => _$this._licencePoint;
  set licencePoint(double? licencePoint) => _$this._licencePoint = licencePoint;

  UpdateEngLicenseBuilder() {
    UpdateEngLicense._defaults(this);
  }

  UpdateEngLicenseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _licenceCode = $v.licenceCode;
      _licenceName = $v.licenceName;
      _getDate = $v.getDate;
      _licencePoint = $v.licencePoint;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UpdateEngLicense other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$UpdateEngLicense;
  }

  @override
  void update(void Function(UpdateEngLicenseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UpdateEngLicense build() => _build();

  _$UpdateEngLicense _build() {
    final _$result = _$v ??
        new _$UpdateEngLicense._(
          licenceCode: licenceCode,
          licenceName: licenceName,
          getDate: getDate,
          licencePoint: licencePoint,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
