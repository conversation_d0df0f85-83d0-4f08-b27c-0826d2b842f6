// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'eng_language.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$EngLanguage extends EngLanguage {
  @override
  final int engineerId;
  @override
  final int? languageLevelType;
  @override
  final int? languageId;
  @override
  final String? languageCode;
  @override
  final String? languageName;
  @override
  final String? languageLevelName;

  factory _$EngLanguage([void Function(EngLanguageBuilder)? updates]) =>
      (new EngLanguageBuilder()..update(updates))._build();

  _$EngLanguage._(
      {required this.engineerId,
      this.languageLevelType,
      this.languageId,
      this.languageCode,
      this.languageName,
      this.languageLevelName})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        engineerId, r'EngLanguage', 'engineerId');
  }

  @override
  EngLanguage rebuild(void Function(EngLanguageBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  EngLanguageBuilder toBuilder() => new EngLanguageBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is EngLanguage &&
        engineerId == other.engineerId &&
        languageLevelType == other.languageLevelType &&
        languageId == other.languageId &&
        languageCode == other.languageCode &&
        languageName == other.languageName &&
        languageLevelName == other.languageLevelName;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, engineerId.hashCode);
    _$hash = $jc(_$hash, languageLevelType.hashCode);
    _$hash = $jc(_$hash, languageId.hashCode);
    _$hash = $jc(_$hash, languageCode.hashCode);
    _$hash = $jc(_$hash, languageName.hashCode);
    _$hash = $jc(_$hash, languageLevelName.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'EngLanguage')
          ..add('engineerId', engineerId)
          ..add('languageLevelType', languageLevelType)
          ..add('languageId', languageId)
          ..add('languageCode', languageCode)
          ..add('languageName', languageName)
          ..add('languageLevelName', languageLevelName))
        .toString();
  }
}

class EngLanguageBuilder implements Builder<EngLanguage, EngLanguageBuilder> {
  _$EngLanguage? _$v;

  int? _engineerId;
  int? get engineerId => _$this._engineerId;
  set engineerId(int? engineerId) => _$this._engineerId = engineerId;

  int? _languageLevelType;
  int? get languageLevelType => _$this._languageLevelType;
  set languageLevelType(int? languageLevelType) =>
      _$this._languageLevelType = languageLevelType;

  int? _languageId;
  int? get languageId => _$this._languageId;
  set languageId(int? languageId) => _$this._languageId = languageId;

  String? _languageCode;
  String? get languageCode => _$this._languageCode;
  set languageCode(String? languageCode) => _$this._languageCode = languageCode;

  String? _languageName;
  String? get languageName => _$this._languageName;
  set languageName(String? languageName) => _$this._languageName = languageName;

  String? _languageLevelName;
  String? get languageLevelName => _$this._languageLevelName;
  set languageLevelName(String? languageLevelName) =>
      _$this._languageLevelName = languageLevelName;

  EngLanguageBuilder() {
    EngLanguage._defaults(this);
  }

  EngLanguageBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _engineerId = $v.engineerId;
      _languageLevelType = $v.languageLevelType;
      _languageId = $v.languageId;
      _languageCode = $v.languageCode;
      _languageName = $v.languageName;
      _languageLevelName = $v.languageLevelName;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(EngLanguage other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$EngLanguage;
  }

  @override
  void update(void Function(EngLanguageBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  EngLanguage build() => _build();

  _$EngLanguage _build() {
    final _$result = _$v ??
        new _$EngLanguage._(
          engineerId: BuiltValueNullFieldError.checkNotNull(
              engineerId, r'EngLanguage', 'engineerId'),
          languageLevelType: languageLevelType,
          languageId: languageId,
          languageCode: languageCode,
          languageName: languageName,
          languageLevelName: languageLevelName,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
