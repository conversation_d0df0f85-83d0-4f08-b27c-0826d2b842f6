// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_company_reject_apply.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GeneralCompanyRejectApply extends GeneralCompanyRejectApply {
  @override
  final int applyId;
  @override
  final int? hostCompanyId;

  factory _$GeneralCompanyRejectApply(
          [void Function(GeneralCompanyRejectApplyBuilder)? updates]) =>
      (new GeneralCompanyRejectApplyBuilder()..update(updates))._build();

  _$GeneralCompanyRejectApply._({required this.applyId, this.hostCompanyId})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        applyId, r'GeneralCompanyRejectApply', 'applyId');
  }

  @override
  GeneralCompanyRejectApply rebuild(
          void Function(GeneralCompanyRejectApplyBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GeneralCompanyRejectApplyBuilder toBuilder() =>
      new GeneralCompanyRejectApplyBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GeneralCompanyRejectApply &&
        applyId == other.applyId &&
        hostCompanyId == other.hostCompanyId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, applyId.hashCode);
    _$hash = $jc(_$hash, hostCompanyId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GeneralCompanyRejectApply')
          ..add('applyId', applyId)
          ..add('hostCompanyId', hostCompanyId))
        .toString();
  }
}

class GeneralCompanyRejectApplyBuilder
    implements
        Builder<GeneralCompanyRejectApply, GeneralCompanyRejectApplyBuilder> {
  _$GeneralCompanyRejectApply? _$v;

  int? _applyId;
  int? get applyId => _$this._applyId;
  set applyId(int? applyId) => _$this._applyId = applyId;

  int? _hostCompanyId;
  int? get hostCompanyId => _$this._hostCompanyId;
  set hostCompanyId(int? hostCompanyId) =>
      _$this._hostCompanyId = hostCompanyId;

  GeneralCompanyRejectApplyBuilder() {
    GeneralCompanyRejectApply._defaults(this);
  }

  GeneralCompanyRejectApplyBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _applyId = $v.applyId;
      _hostCompanyId = $v.hostCompanyId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GeneralCompanyRejectApply other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GeneralCompanyRejectApply;
  }

  @override
  void update(void Function(GeneralCompanyRejectApplyBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GeneralCompanyRejectApply build() => _build();

  _$GeneralCompanyRejectApply _build() {
    final _$result = _$v ??
        new _$GeneralCompanyRejectApply._(
          applyId: BuiltValueNullFieldError.checkNotNull(
              applyId, r'GeneralCompanyRejectApply', 'applyId'),
          hostCompanyId: hostCompanyId,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
