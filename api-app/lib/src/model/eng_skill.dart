//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'eng_skill.g.dart';

/// EngSkill
///
/// Properties:
/// * [engineerId] 
/// * [skillCode] 
/// * [levelType] 
/// * [skillId] 
/// * [skillName] 
/// * [levelTypeName] 
/// * [jobCode] 
/// * [tempName] 
/// * [tempCategoryId] 
@BuiltValue()
abstract class EngSkill implements Built<EngSkill, EngSkillBuilder> {
  @BuiltValueField(wireName: r'engineer_id')
  int get engineerId;

  @BuiltValueField(wireName: r'skill_code')
  String? get skillCode;

  @BuiltValueField(wireName: r'level_type')
  int? get levelType;

  @BuiltValueField(wireName: r'skill_id')
  int? get skillId;

  @BuiltValueField(wireName: r'skill_name')
  String? get skillName;

  @BuiltValueField(wireName: r'level_type_name')
  String? get levelTypeName;

  @BuiltValueField(wireName: r'job_code')
  String? get jobCode;

  @BuiltValueField(wireName: r'temp_name')
  String? get tempName;

  @BuiltValueField(wireName: r'temp_category_id')
  int? get tempCategoryId;

  EngSkill._();

  factory EngSkill([void updates(EngSkillBuilder b)]) = _$EngSkill;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(EngSkillBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<EngSkill> get serializer => _$EngSkillSerializer();
}

class _$EngSkillSerializer implements PrimitiveSerializer<EngSkill> {
  @override
  final Iterable<Type> types = const [EngSkill, _$EngSkill];

  @override
  final String wireName = r'EngSkill';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    EngSkill object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'engineer_id';
    yield serializers.serialize(
      object.engineerId,
      specifiedType: const FullType(int),
    );
    if (object.skillCode != null) {
      yield r'skill_code';
      yield serializers.serialize(
        object.skillCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.levelType != null) {
      yield r'level_type';
      yield serializers.serialize(
        object.levelType,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.skillId != null) {
      yield r'skill_id';
      yield serializers.serialize(
        object.skillId,
        specifiedType: const FullType(int),
      );
    }
    if (object.skillName != null) {
      yield r'skill_name';
      yield serializers.serialize(
        object.skillName,
        specifiedType: const FullType(String),
      );
    }
    if (object.levelTypeName != null) {
      yield r'level_type_name';
      yield serializers.serialize(
        object.levelTypeName,
        specifiedType: const FullType(String),
      );
    }
    if (object.jobCode != null) {
      yield r'job_code';
      yield serializers.serialize(
        object.jobCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.tempName != null) {
      yield r'temp_name';
      yield serializers.serialize(
        object.tempName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.tempCategoryId != null) {
      yield r'temp_category_id';
      yield serializers.serialize(
        object.tempCategoryId,
        specifiedType: const FullType.nullable(int),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    EngSkill object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required EngSkillBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'engineer_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.engineerId = valueDes;
          break;
        case r'skill_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.skillCode = valueDes;
          break;
        case r'level_type':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.levelType = valueDes;
          break;
        case r'skill_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.skillId = valueDes;
          break;
        case r'skill_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.skillName = valueDes;
          break;
        case r'level_type_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.levelTypeName = valueDes;
          break;
        case r'job_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.jobCode = valueDes;
          break;
        case r'temp_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.tempName = valueDes;
          break;
        case r'temp_category_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.tempCategoryId = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  EngSkill deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = EngSkillBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

