// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'engineer_agency_company.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$EngineerAgencyCompany extends EngineerAgencyCompany {
  @override
  final int? companyId;
  @override
  final String? logoImagePath;
  @override
  final String? name;
  @override
  final double? agentFee;
  @override
  final String? agentFeeCurrCode;

  factory _$EngineerAgencyCompany(
          [void Function(EngineerAgencyCompanyBuilder)? updates]) =>
      (new EngineerAgencyCompanyBuilder()..update(updates))._build();

  _$EngineerAgencyCompany._(
      {this.companyId,
      this.logoImagePath,
      this.name,
      this.agentFee,
      this.agentFeeCurrCode})
      : super._();

  @override
  EngineerAgencyCompany rebuild(
          void Function(EngineerAgencyCompanyBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  EngineerAgencyCompanyBuilder toBuilder() =>
      new EngineerAgencyCompanyBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is EngineerAgencyCompany &&
        companyId == other.companyId &&
        logoImagePath == other.logoImagePath &&
        name == other.name &&
        agentFee == other.agentFee &&
        agentFeeCurrCode == other.agentFeeCurrCode;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, companyId.hashCode);
    _$hash = $jc(_$hash, logoImagePath.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, agentFee.hashCode);
    _$hash = $jc(_$hash, agentFeeCurrCode.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'EngineerAgencyCompany')
          ..add('companyId', companyId)
          ..add('logoImagePath', logoImagePath)
          ..add('name', name)
          ..add('agentFee', agentFee)
          ..add('agentFeeCurrCode', agentFeeCurrCode))
        .toString();
  }
}

class EngineerAgencyCompanyBuilder
    implements Builder<EngineerAgencyCompany, EngineerAgencyCompanyBuilder> {
  _$EngineerAgencyCompany? _$v;

  int? _companyId;
  int? get companyId => _$this._companyId;
  set companyId(int? companyId) => _$this._companyId = companyId;

  String? _logoImagePath;
  String? get logoImagePath => _$this._logoImagePath;
  set logoImagePath(String? logoImagePath) =>
      _$this._logoImagePath = logoImagePath;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  double? _agentFee;
  double? get agentFee => _$this._agentFee;
  set agentFee(double? agentFee) => _$this._agentFee = agentFee;

  String? _agentFeeCurrCode;
  String? get agentFeeCurrCode => _$this._agentFeeCurrCode;
  set agentFeeCurrCode(String? agentFeeCurrCode) =>
      _$this._agentFeeCurrCode = agentFeeCurrCode;

  EngineerAgencyCompanyBuilder() {
    EngineerAgencyCompany._defaults(this);
  }

  EngineerAgencyCompanyBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _companyId = $v.companyId;
      _logoImagePath = $v.logoImagePath;
      _name = $v.name;
      _agentFee = $v.agentFee;
      _agentFeeCurrCode = $v.agentFeeCurrCode;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(EngineerAgencyCompany other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$EngineerAgencyCompany;
  }

  @override
  void update(void Function(EngineerAgencyCompanyBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  EngineerAgencyCompany build() => _build();

  _$EngineerAgencyCompany _build() {
    final _$result = _$v ??
        new _$EngineerAgencyCompany._(
          companyId: companyId,
          logoImagePath: logoImagePath,
          name: name,
          agentFee: agentFee,
          agentFeeCurrCode: agentFeeCurrCode,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
