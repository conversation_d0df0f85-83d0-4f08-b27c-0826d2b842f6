//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'login_success.g.dart';

/// LoginSuccess
///
/// Properties:
/// * [captchaKey] 
/// * [captchaImageUrl] 
/// * [isEmailVerified] 
/// * [refresh] 
/// * [access] 
@BuiltValue()
abstract class LoginSuccess implements Built<LoginSuccess, LoginSuccessBuilder> {
  @BuiltValueField(wireName: r'captcha_key')
  String? get captchaKey;

  @BuiltValueField(wireName: r'captcha_image_url')
  String? get captchaImageUrl;

  @BuiltValueField(wireName: r'is_email_verified')
  bool get isEmailVerified;

  @BuiltValueField(wireName: r'refresh')
  String? get refresh;

  @BuiltValueField(wireName: r'access')
  String? get access;

  LoginSuccess._();

  factory LoginSuccess([void updates(LoginSuccessBuilder b)]) = _$LoginSuccess;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(LoginSuccessBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<LoginSuccess> get serializer => _$LoginSuccessSerializer();
}

class _$LoginSuccessSerializer implements PrimitiveSerializer<LoginSuccess> {
  @override
  final Iterable<Type> types = const [LoginSuccess, _$LoginSuccess];

  @override
  final String wireName = r'LoginSuccess';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    LoginSuccess object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'captcha_key';
    yield object.captchaKey == null ? null : serializers.serialize(
      object.captchaKey,
      specifiedType: const FullType.nullable(String),
    );
    yield r'captcha_image_url';
    yield object.captchaImageUrl == null ? null : serializers.serialize(
      object.captchaImageUrl,
      specifiedType: const FullType.nullable(String),
    );
    yield r'is_email_verified';
    yield serializers.serialize(
      object.isEmailVerified,
      specifiedType: const FullType(bool),
    );
    yield r'refresh';
    yield object.refresh == null ? null : serializers.serialize(
      object.refresh,
      specifiedType: const FullType.nullable(String),
    );
    yield r'access';
    yield object.access == null ? null : serializers.serialize(
      object.access,
      specifiedType: const FullType.nullable(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    LoginSuccess object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required LoginSuccessBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'captcha_key':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.captchaKey = valueDes;
          break;
        case r'captcha_image_url':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.captchaImageUrl = valueDes;
          break;
        case r'is_email_verified':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(bool),
          ) as bool;
          result.isEmailVerified = valueDes;
          break;
        case r'refresh':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.refresh = valueDes;
          break;
        case r'access':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.access = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  LoginSuccess deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = LoginSuccessBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

