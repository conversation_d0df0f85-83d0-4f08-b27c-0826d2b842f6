//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/date.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'eng_high_light_project.g.dart';

/// EngHighLightProject
///
/// Properties:
/// * [name] 
/// * [description] 
/// * [size] 
/// * [roleName] 
/// * [responsibilities] 
/// * [technologyUsed] 
/// * [fromDate] 
/// * [toDate] 
@BuiltValue()
abstract class EngHighLightProject implements Built<EngHighLightProject, EngHighLightProjectBuilder> {
  @BuiltValueField(wireName: r'name')
  String? get name;

  @BuiltValueField(wireName: r'description')
  String? get description;

  @BuiltValueField(wireName: r'size')
  String? get size;

  @BuiltValueField(wireName: r'role_name')
  String? get roleName;

  @BuiltValueField(wireName: r'responsibilities')
  String? get responsibilities;

  @BuiltValueField(wireName: r'technology_used')
  String? get technologyUsed;

  @BuiltValueField(wireName: r'from_date')
  Date? get fromDate;

  @BuiltValueField(wireName: r'to_date')
  Date? get toDate;

  EngHighLightProject._();

  factory EngHighLightProject([void updates(EngHighLightProjectBuilder b)]) = _$EngHighLightProject;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(EngHighLightProjectBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<EngHighLightProject> get serializer => _$EngHighLightProjectSerializer();
}

class _$EngHighLightProjectSerializer implements PrimitiveSerializer<EngHighLightProject> {
  @override
  final Iterable<Type> types = const [EngHighLightProject, _$EngHighLightProject];

  @override
  final String wireName = r'EngHighLightProject';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    EngHighLightProject object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'name';
    yield object.name == null ? null : serializers.serialize(
      object.name,
      specifiedType: const FullType.nullable(String),
    );
    yield r'description';
    yield object.description == null ? null : serializers.serialize(
      object.description,
      specifiedType: const FullType.nullable(String),
    );
    yield r'size';
    yield object.size == null ? null : serializers.serialize(
      object.size,
      specifiedType: const FullType.nullable(String),
    );
    yield r'role_name';
    yield object.roleName == null ? null : serializers.serialize(
      object.roleName,
      specifiedType: const FullType.nullable(String),
    );
    yield r'responsibilities';
    yield object.responsibilities == null ? null : serializers.serialize(
      object.responsibilities,
      specifiedType: const FullType.nullable(String),
    );
    yield r'technology_used';
    yield object.technologyUsed == null ? null : serializers.serialize(
      object.technologyUsed,
      specifiedType: const FullType.nullable(String),
    );
    yield r'from_date';
    yield object.fromDate == null ? null : serializers.serialize(
      object.fromDate,
      specifiedType: const FullType.nullable(Date),
    );
    yield r'to_date';
    yield object.toDate == null ? null : serializers.serialize(
      object.toDate,
      specifiedType: const FullType.nullable(Date),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    EngHighLightProject object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required EngHighLightProjectBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.name = valueDes;
          break;
        case r'description':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.description = valueDes;
          break;
        case r'size':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.size = valueDes;
          break;
        case r'role_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.roleName = valueDes;
          break;
        case r'responsibilities':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.responsibilities = valueDes;
          break;
        case r'technology_used':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.technologyUsed = valueDes;
          break;
        case r'from_date':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(Date),
          ) as Date?;
          if (valueDes == null) continue;
          result.fromDate = valueDes;
          break;
        case r'to_date':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(Date),
          ) as Date?;
          if (valueDes == null) continue;
          result.toDate = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  EngHighLightProject deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = EngHighLightProjectBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

