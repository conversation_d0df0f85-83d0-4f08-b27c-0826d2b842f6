//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'login_with_sns.g.dart';

/// LoginWithSNS
///
/// Properties:
/// * [snsType] 
/// * [code] 
/// * [redirectUri] 
/// * [codeVerifier] 
/// * [whatsappNumber] 
@BuiltValue()
abstract class LoginWithSNS implements Built<LoginWithSNS, LoginWithSNSBuilder> {
  @BuiltValueField(wireName: r'sns_type')
  String get snsType;

  @BuiltValueField(wireName: r'code')
  String? get code;

  @BuiltValueField(wireName: r'redirect_uri')
  String? get redirectUri;

  @BuiltValueField(wireName: r'code_verifier')
  String? get codeVerifier;

  @BuiltValueField(wireName: r'whatsapp_number')
  String? get whatsappNumber;

  LoginWithSNS._();

  factory LoginWithSNS([void updates(LoginWithSNSBuilder b)]) = _$LoginWithSNS;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(LoginWithSNSBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<LoginWithSNS> get serializer => _$LoginWithSNSSerializer();
}

class _$LoginWithSNSSerializer implements PrimitiveSerializer<LoginWithSNS> {
  @override
  final Iterable<Type> types = const [LoginWithSNS, _$LoginWithSNS];

  @override
  final String wireName = r'LoginWithSNS';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    LoginWithSNS object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'sns_type';
    yield serializers.serialize(
      object.snsType,
      specifiedType: const FullType(String),
    );
    if (object.code != null) {
      yield r'code';
      yield serializers.serialize(
        object.code,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.redirectUri != null) {
      yield r'redirect_uri';
      yield serializers.serialize(
        object.redirectUri,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.codeVerifier != null) {
      yield r'code_verifier';
      yield serializers.serialize(
        object.codeVerifier,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.whatsappNumber != null) {
      yield r'whatsapp_number';
      yield serializers.serialize(
        object.whatsappNumber,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    LoginWithSNS object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required LoginWithSNSBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'sns_type':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.snsType = valueDes;
          break;
        case r'code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.code = valueDes;
          break;
        case r'redirect_uri':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.redirectUri = valueDes;
          break;
        case r'code_verifier':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.codeVerifier = valueDes;
          break;
        case r'whatsapp_number':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.whatsappNumber = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  LoginWithSNS deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = LoginWithSNSBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

