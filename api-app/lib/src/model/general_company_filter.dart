//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'general_company_filter.g.dart';

/// GeneralCompanyFilter
///
/// Properties:
/// * [filterId] 
/// * [filterName] 
@BuiltValue()
abstract class GeneralCompanyFilter implements Built<GeneralCompanyFilter, GeneralCompanyFilterBuilder> {
  @BuiltValueField(wireName: r'filter_id')
  int? get filterId;

  @BuiltValueField(wireName: r'filter_name')
  String get filterName;

  GeneralCompanyFilter._();

  factory GeneralCompanyFilter([void updates(GeneralCompanyFilterBuilder b)]) = _$GeneralCompanyFilter;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(GeneralCompanyFilterBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<GeneralCompanyFilter> get serializer => _$GeneralCompanyFilterSerializer();
}

class _$GeneralCompanyFilterSerializer implements PrimitiveSerializer<GeneralCompanyFilter> {
  @override
  final Iterable<Type> types = const [GeneralCompanyFilter, _$GeneralCompanyFilter];

  @override
  final String wireName = r'GeneralCompanyFilter';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    GeneralCompanyFilter object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.filterId != null) {
      yield r'filter_id';
      yield serializers.serialize(
        object.filterId,
        specifiedType: const FullType(int),
      );
    }
    yield r'filter_name';
    yield serializers.serialize(
      object.filterName,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    GeneralCompanyFilter object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required GeneralCompanyFilterBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'filter_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.filterId = valueDes;
          break;
        case r'filter_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.filterName = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  GeneralCompanyFilter deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = GeneralCompanyFilterBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

