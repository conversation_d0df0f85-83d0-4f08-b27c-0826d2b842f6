// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_company_save_filter.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GeneralCompanySaveFilter extends GeneralCompanySaveFilter {
  @override
  final String filterName;
  @override
  final int? ageFrom;
  @override
  final int? ageTo;
  @override
  final String? sexType;
  @override
  final String? countryCode;
  @override
  final String? addressCode1;
  @override
  final String? addressCode2;
  @override
  final String? lastAcademicCode;
  @override
  final String? languageCode1;
  @override
  final int? languageLevelType1;
  @override
  final String? languageCode2;
  @override
  final int? languageLevelType2;
  @override
  final String? experiencedJobCode1;
  @override
  final int? yearsOfExperience1;
  @override
  final String? experiencedJobCode2;
  @override
  final int? yearsOfExperience2;
  @override
  final String? experiencedJobCode3;
  @override
  final int? yearsOfExperience3;
  @override
  final int? skillCode11;
  @override
  final int? skillLevelType11;
  @override
  final int? skillCode12;
  @override
  final int? skillLevelType12;
  @override
  final int? skillCode13;
  @override
  final int? skillLevelType13;
  @override
  final int? skillCode21;
  @override
  final int? skillLevelType21;
  @override
  final int? skillCode22;
  @override
  final int? skillLevelType22;
  @override
  final int? skillCode23;
  @override
  final int? skillLevelType23;
  @override
  final int? skillCode31;
  @override
  final int? skillLevelType31;
  @override
  final int? skillCode32;
  @override
  final int? skillLevelType32;
  @override
  final int? skillCode33;
  @override
  final int? skillLevelType33;
  @override
  final String? licenceCode1;
  @override
  final int? licencePoint1;
  @override
  final String? licenceCode2;
  @override
  final int? licencePoint2;
  @override
  final String? licenceCode3;
  @override
  final int? licencePoint3;
  @override
  final String? recruitingJobCode;
  @override
  final String? recruitingEmployCode;
  @override
  final String? workPlaceCode1;
  @override
  final String? workPlaceCode2;
  @override
  final String? workPlaceCode3;
  @override
  final String? payrollCode;
  @override
  final double? payrollPriceFrom;
  @override
  final double? payrollPriceTo;
  @override
  final double? agentFee;
  @override
  final String? agentFeeCurrCode;
  @override
  final int hostAgent;

  factory _$GeneralCompanySaveFilter(
          [void Function(GeneralCompanySaveFilterBuilder)? updates]) =>
      (new GeneralCompanySaveFilterBuilder()..update(updates))._build();

  _$GeneralCompanySaveFilter._(
      {required this.filterName,
      this.ageFrom,
      this.ageTo,
      this.sexType,
      this.countryCode,
      this.addressCode1,
      this.addressCode2,
      this.lastAcademicCode,
      this.languageCode1,
      this.languageLevelType1,
      this.languageCode2,
      this.languageLevelType2,
      this.experiencedJobCode1,
      this.yearsOfExperience1,
      this.experiencedJobCode2,
      this.yearsOfExperience2,
      this.experiencedJobCode3,
      this.yearsOfExperience3,
      this.skillCode11,
      this.skillLevelType11,
      this.skillCode12,
      this.skillLevelType12,
      this.skillCode13,
      this.skillLevelType13,
      this.skillCode21,
      this.skillLevelType21,
      this.skillCode22,
      this.skillLevelType22,
      this.skillCode23,
      this.skillLevelType23,
      this.skillCode31,
      this.skillLevelType31,
      this.skillCode32,
      this.skillLevelType32,
      this.skillCode33,
      this.skillLevelType33,
      this.licenceCode1,
      this.licencePoint1,
      this.licenceCode2,
      this.licencePoint2,
      this.licenceCode3,
      this.licencePoint3,
      this.recruitingJobCode,
      this.recruitingEmployCode,
      this.workPlaceCode1,
      this.workPlaceCode2,
      this.workPlaceCode3,
      this.payrollCode,
      this.payrollPriceFrom,
      this.payrollPriceTo,
      this.agentFee,
      this.agentFeeCurrCode,
      required this.hostAgent})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        filterName, r'GeneralCompanySaveFilter', 'filterName');
    BuiltValueNullFieldError.checkNotNull(
        hostAgent, r'GeneralCompanySaveFilter', 'hostAgent');
  }

  @override
  GeneralCompanySaveFilter rebuild(
          void Function(GeneralCompanySaveFilterBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GeneralCompanySaveFilterBuilder toBuilder() =>
      new GeneralCompanySaveFilterBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GeneralCompanySaveFilter &&
        filterName == other.filterName &&
        ageFrom == other.ageFrom &&
        ageTo == other.ageTo &&
        sexType == other.sexType &&
        countryCode == other.countryCode &&
        addressCode1 == other.addressCode1 &&
        addressCode2 == other.addressCode2 &&
        lastAcademicCode == other.lastAcademicCode &&
        languageCode1 == other.languageCode1 &&
        languageLevelType1 == other.languageLevelType1 &&
        languageCode2 == other.languageCode2 &&
        languageLevelType2 == other.languageLevelType2 &&
        experiencedJobCode1 == other.experiencedJobCode1 &&
        yearsOfExperience1 == other.yearsOfExperience1 &&
        experiencedJobCode2 == other.experiencedJobCode2 &&
        yearsOfExperience2 == other.yearsOfExperience2 &&
        experiencedJobCode3 == other.experiencedJobCode3 &&
        yearsOfExperience3 == other.yearsOfExperience3 &&
        skillCode11 == other.skillCode11 &&
        skillLevelType11 == other.skillLevelType11 &&
        skillCode12 == other.skillCode12 &&
        skillLevelType12 == other.skillLevelType12 &&
        skillCode13 == other.skillCode13 &&
        skillLevelType13 == other.skillLevelType13 &&
        skillCode21 == other.skillCode21 &&
        skillLevelType21 == other.skillLevelType21 &&
        skillCode22 == other.skillCode22 &&
        skillLevelType22 == other.skillLevelType22 &&
        skillCode23 == other.skillCode23 &&
        skillLevelType23 == other.skillLevelType23 &&
        skillCode31 == other.skillCode31 &&
        skillLevelType31 == other.skillLevelType31 &&
        skillCode32 == other.skillCode32 &&
        skillLevelType32 == other.skillLevelType32 &&
        skillCode33 == other.skillCode33 &&
        skillLevelType33 == other.skillLevelType33 &&
        licenceCode1 == other.licenceCode1 &&
        licencePoint1 == other.licencePoint1 &&
        licenceCode2 == other.licenceCode2 &&
        licencePoint2 == other.licencePoint2 &&
        licenceCode3 == other.licenceCode3 &&
        licencePoint3 == other.licencePoint3 &&
        recruitingJobCode == other.recruitingJobCode &&
        recruitingEmployCode == other.recruitingEmployCode &&
        workPlaceCode1 == other.workPlaceCode1 &&
        workPlaceCode2 == other.workPlaceCode2 &&
        workPlaceCode3 == other.workPlaceCode3 &&
        payrollCode == other.payrollCode &&
        payrollPriceFrom == other.payrollPriceFrom &&
        payrollPriceTo == other.payrollPriceTo &&
        agentFee == other.agentFee &&
        agentFeeCurrCode == other.agentFeeCurrCode &&
        hostAgent == other.hostAgent;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, filterName.hashCode);
    _$hash = $jc(_$hash, ageFrom.hashCode);
    _$hash = $jc(_$hash, ageTo.hashCode);
    _$hash = $jc(_$hash, sexType.hashCode);
    _$hash = $jc(_$hash, countryCode.hashCode);
    _$hash = $jc(_$hash, addressCode1.hashCode);
    _$hash = $jc(_$hash, addressCode2.hashCode);
    _$hash = $jc(_$hash, lastAcademicCode.hashCode);
    _$hash = $jc(_$hash, languageCode1.hashCode);
    _$hash = $jc(_$hash, languageLevelType1.hashCode);
    _$hash = $jc(_$hash, languageCode2.hashCode);
    _$hash = $jc(_$hash, languageLevelType2.hashCode);
    _$hash = $jc(_$hash, experiencedJobCode1.hashCode);
    _$hash = $jc(_$hash, yearsOfExperience1.hashCode);
    _$hash = $jc(_$hash, experiencedJobCode2.hashCode);
    _$hash = $jc(_$hash, yearsOfExperience2.hashCode);
    _$hash = $jc(_$hash, experiencedJobCode3.hashCode);
    _$hash = $jc(_$hash, yearsOfExperience3.hashCode);
    _$hash = $jc(_$hash, skillCode11.hashCode);
    _$hash = $jc(_$hash, skillLevelType11.hashCode);
    _$hash = $jc(_$hash, skillCode12.hashCode);
    _$hash = $jc(_$hash, skillLevelType12.hashCode);
    _$hash = $jc(_$hash, skillCode13.hashCode);
    _$hash = $jc(_$hash, skillLevelType13.hashCode);
    _$hash = $jc(_$hash, skillCode21.hashCode);
    _$hash = $jc(_$hash, skillLevelType21.hashCode);
    _$hash = $jc(_$hash, skillCode22.hashCode);
    _$hash = $jc(_$hash, skillLevelType22.hashCode);
    _$hash = $jc(_$hash, skillCode23.hashCode);
    _$hash = $jc(_$hash, skillLevelType23.hashCode);
    _$hash = $jc(_$hash, skillCode31.hashCode);
    _$hash = $jc(_$hash, skillLevelType31.hashCode);
    _$hash = $jc(_$hash, skillCode32.hashCode);
    _$hash = $jc(_$hash, skillLevelType32.hashCode);
    _$hash = $jc(_$hash, skillCode33.hashCode);
    _$hash = $jc(_$hash, skillLevelType33.hashCode);
    _$hash = $jc(_$hash, licenceCode1.hashCode);
    _$hash = $jc(_$hash, licencePoint1.hashCode);
    _$hash = $jc(_$hash, licenceCode2.hashCode);
    _$hash = $jc(_$hash, licencePoint2.hashCode);
    _$hash = $jc(_$hash, licenceCode3.hashCode);
    _$hash = $jc(_$hash, licencePoint3.hashCode);
    _$hash = $jc(_$hash, recruitingJobCode.hashCode);
    _$hash = $jc(_$hash, recruitingEmployCode.hashCode);
    _$hash = $jc(_$hash, workPlaceCode1.hashCode);
    _$hash = $jc(_$hash, workPlaceCode2.hashCode);
    _$hash = $jc(_$hash, workPlaceCode3.hashCode);
    _$hash = $jc(_$hash, payrollCode.hashCode);
    _$hash = $jc(_$hash, payrollPriceFrom.hashCode);
    _$hash = $jc(_$hash, payrollPriceTo.hashCode);
    _$hash = $jc(_$hash, agentFee.hashCode);
    _$hash = $jc(_$hash, agentFeeCurrCode.hashCode);
    _$hash = $jc(_$hash, hostAgent.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GeneralCompanySaveFilter')
          ..add('filterName', filterName)
          ..add('ageFrom', ageFrom)
          ..add('ageTo', ageTo)
          ..add('sexType', sexType)
          ..add('countryCode', countryCode)
          ..add('addressCode1', addressCode1)
          ..add('addressCode2', addressCode2)
          ..add('lastAcademicCode', lastAcademicCode)
          ..add('languageCode1', languageCode1)
          ..add('languageLevelType1', languageLevelType1)
          ..add('languageCode2', languageCode2)
          ..add('languageLevelType2', languageLevelType2)
          ..add('experiencedJobCode1', experiencedJobCode1)
          ..add('yearsOfExperience1', yearsOfExperience1)
          ..add('experiencedJobCode2', experiencedJobCode2)
          ..add('yearsOfExperience2', yearsOfExperience2)
          ..add('experiencedJobCode3', experiencedJobCode3)
          ..add('yearsOfExperience3', yearsOfExperience3)
          ..add('skillCode11', skillCode11)
          ..add('skillLevelType11', skillLevelType11)
          ..add('skillCode12', skillCode12)
          ..add('skillLevelType12', skillLevelType12)
          ..add('skillCode13', skillCode13)
          ..add('skillLevelType13', skillLevelType13)
          ..add('skillCode21', skillCode21)
          ..add('skillLevelType21', skillLevelType21)
          ..add('skillCode22', skillCode22)
          ..add('skillLevelType22', skillLevelType22)
          ..add('skillCode23', skillCode23)
          ..add('skillLevelType23', skillLevelType23)
          ..add('skillCode31', skillCode31)
          ..add('skillLevelType31', skillLevelType31)
          ..add('skillCode32', skillCode32)
          ..add('skillLevelType32', skillLevelType32)
          ..add('skillCode33', skillCode33)
          ..add('skillLevelType33', skillLevelType33)
          ..add('licenceCode1', licenceCode1)
          ..add('licencePoint1', licencePoint1)
          ..add('licenceCode2', licenceCode2)
          ..add('licencePoint2', licencePoint2)
          ..add('licenceCode3', licenceCode3)
          ..add('licencePoint3', licencePoint3)
          ..add('recruitingJobCode', recruitingJobCode)
          ..add('recruitingEmployCode', recruitingEmployCode)
          ..add('workPlaceCode1', workPlaceCode1)
          ..add('workPlaceCode2', workPlaceCode2)
          ..add('workPlaceCode3', workPlaceCode3)
          ..add('payrollCode', payrollCode)
          ..add('payrollPriceFrom', payrollPriceFrom)
          ..add('payrollPriceTo', payrollPriceTo)
          ..add('agentFee', agentFee)
          ..add('agentFeeCurrCode', agentFeeCurrCode)
          ..add('hostAgent', hostAgent))
        .toString();
  }
}

class GeneralCompanySaveFilterBuilder
    implements
        Builder<GeneralCompanySaveFilter, GeneralCompanySaveFilterBuilder> {
  _$GeneralCompanySaveFilter? _$v;

  String? _filterName;
  String? get filterName => _$this._filterName;
  set filterName(String? filterName) => _$this._filterName = filterName;

  int? _ageFrom;
  int? get ageFrom => _$this._ageFrom;
  set ageFrom(int? ageFrom) => _$this._ageFrom = ageFrom;

  int? _ageTo;
  int? get ageTo => _$this._ageTo;
  set ageTo(int? ageTo) => _$this._ageTo = ageTo;

  String? _sexType;
  String? get sexType => _$this._sexType;
  set sexType(String? sexType) => _$this._sexType = sexType;

  String? _countryCode;
  String? get countryCode => _$this._countryCode;
  set countryCode(String? countryCode) => _$this._countryCode = countryCode;

  String? _addressCode1;
  String? get addressCode1 => _$this._addressCode1;
  set addressCode1(String? addressCode1) => _$this._addressCode1 = addressCode1;

  String? _addressCode2;
  String? get addressCode2 => _$this._addressCode2;
  set addressCode2(String? addressCode2) => _$this._addressCode2 = addressCode2;

  String? _lastAcademicCode;
  String? get lastAcademicCode => _$this._lastAcademicCode;
  set lastAcademicCode(String? lastAcademicCode) =>
      _$this._lastAcademicCode = lastAcademicCode;

  String? _languageCode1;
  String? get languageCode1 => _$this._languageCode1;
  set languageCode1(String? languageCode1) =>
      _$this._languageCode1 = languageCode1;

  int? _languageLevelType1;
  int? get languageLevelType1 => _$this._languageLevelType1;
  set languageLevelType1(int? languageLevelType1) =>
      _$this._languageLevelType1 = languageLevelType1;

  String? _languageCode2;
  String? get languageCode2 => _$this._languageCode2;
  set languageCode2(String? languageCode2) =>
      _$this._languageCode2 = languageCode2;

  int? _languageLevelType2;
  int? get languageLevelType2 => _$this._languageLevelType2;
  set languageLevelType2(int? languageLevelType2) =>
      _$this._languageLevelType2 = languageLevelType2;

  String? _experiencedJobCode1;
  String? get experiencedJobCode1 => _$this._experiencedJobCode1;
  set experiencedJobCode1(String? experiencedJobCode1) =>
      _$this._experiencedJobCode1 = experiencedJobCode1;

  int? _yearsOfExperience1;
  int? get yearsOfExperience1 => _$this._yearsOfExperience1;
  set yearsOfExperience1(int? yearsOfExperience1) =>
      _$this._yearsOfExperience1 = yearsOfExperience1;

  String? _experiencedJobCode2;
  String? get experiencedJobCode2 => _$this._experiencedJobCode2;
  set experiencedJobCode2(String? experiencedJobCode2) =>
      _$this._experiencedJobCode2 = experiencedJobCode2;

  int? _yearsOfExperience2;
  int? get yearsOfExperience2 => _$this._yearsOfExperience2;
  set yearsOfExperience2(int? yearsOfExperience2) =>
      _$this._yearsOfExperience2 = yearsOfExperience2;

  String? _experiencedJobCode3;
  String? get experiencedJobCode3 => _$this._experiencedJobCode3;
  set experiencedJobCode3(String? experiencedJobCode3) =>
      _$this._experiencedJobCode3 = experiencedJobCode3;

  int? _yearsOfExperience3;
  int? get yearsOfExperience3 => _$this._yearsOfExperience3;
  set yearsOfExperience3(int? yearsOfExperience3) =>
      _$this._yearsOfExperience3 = yearsOfExperience3;

  int? _skillCode11;
  int? get skillCode11 => _$this._skillCode11;
  set skillCode11(int? skillCode11) => _$this._skillCode11 = skillCode11;

  int? _skillLevelType11;
  int? get skillLevelType11 => _$this._skillLevelType11;
  set skillLevelType11(int? skillLevelType11) =>
      _$this._skillLevelType11 = skillLevelType11;

  int? _skillCode12;
  int? get skillCode12 => _$this._skillCode12;
  set skillCode12(int? skillCode12) => _$this._skillCode12 = skillCode12;

  int? _skillLevelType12;
  int? get skillLevelType12 => _$this._skillLevelType12;
  set skillLevelType12(int? skillLevelType12) =>
      _$this._skillLevelType12 = skillLevelType12;

  int? _skillCode13;
  int? get skillCode13 => _$this._skillCode13;
  set skillCode13(int? skillCode13) => _$this._skillCode13 = skillCode13;

  int? _skillLevelType13;
  int? get skillLevelType13 => _$this._skillLevelType13;
  set skillLevelType13(int? skillLevelType13) =>
      _$this._skillLevelType13 = skillLevelType13;

  int? _skillCode21;
  int? get skillCode21 => _$this._skillCode21;
  set skillCode21(int? skillCode21) => _$this._skillCode21 = skillCode21;

  int? _skillLevelType21;
  int? get skillLevelType21 => _$this._skillLevelType21;
  set skillLevelType21(int? skillLevelType21) =>
      _$this._skillLevelType21 = skillLevelType21;

  int? _skillCode22;
  int? get skillCode22 => _$this._skillCode22;
  set skillCode22(int? skillCode22) => _$this._skillCode22 = skillCode22;

  int? _skillLevelType22;
  int? get skillLevelType22 => _$this._skillLevelType22;
  set skillLevelType22(int? skillLevelType22) =>
      _$this._skillLevelType22 = skillLevelType22;

  int? _skillCode23;
  int? get skillCode23 => _$this._skillCode23;
  set skillCode23(int? skillCode23) => _$this._skillCode23 = skillCode23;

  int? _skillLevelType23;
  int? get skillLevelType23 => _$this._skillLevelType23;
  set skillLevelType23(int? skillLevelType23) =>
      _$this._skillLevelType23 = skillLevelType23;

  int? _skillCode31;
  int? get skillCode31 => _$this._skillCode31;
  set skillCode31(int? skillCode31) => _$this._skillCode31 = skillCode31;

  int? _skillLevelType31;
  int? get skillLevelType31 => _$this._skillLevelType31;
  set skillLevelType31(int? skillLevelType31) =>
      _$this._skillLevelType31 = skillLevelType31;

  int? _skillCode32;
  int? get skillCode32 => _$this._skillCode32;
  set skillCode32(int? skillCode32) => _$this._skillCode32 = skillCode32;

  int? _skillLevelType32;
  int? get skillLevelType32 => _$this._skillLevelType32;
  set skillLevelType32(int? skillLevelType32) =>
      _$this._skillLevelType32 = skillLevelType32;

  int? _skillCode33;
  int? get skillCode33 => _$this._skillCode33;
  set skillCode33(int? skillCode33) => _$this._skillCode33 = skillCode33;

  int? _skillLevelType33;
  int? get skillLevelType33 => _$this._skillLevelType33;
  set skillLevelType33(int? skillLevelType33) =>
      _$this._skillLevelType33 = skillLevelType33;

  String? _licenceCode1;
  String? get licenceCode1 => _$this._licenceCode1;
  set licenceCode1(String? licenceCode1) => _$this._licenceCode1 = licenceCode1;

  int? _licencePoint1;
  int? get licencePoint1 => _$this._licencePoint1;
  set licencePoint1(int? licencePoint1) =>
      _$this._licencePoint1 = licencePoint1;

  String? _licenceCode2;
  String? get licenceCode2 => _$this._licenceCode2;
  set licenceCode2(String? licenceCode2) => _$this._licenceCode2 = licenceCode2;

  int? _licencePoint2;
  int? get licencePoint2 => _$this._licencePoint2;
  set licencePoint2(int? licencePoint2) =>
      _$this._licencePoint2 = licencePoint2;

  String? _licenceCode3;
  String? get licenceCode3 => _$this._licenceCode3;
  set licenceCode3(String? licenceCode3) => _$this._licenceCode3 = licenceCode3;

  int? _licencePoint3;
  int? get licencePoint3 => _$this._licencePoint3;
  set licencePoint3(int? licencePoint3) =>
      _$this._licencePoint3 = licencePoint3;

  String? _recruitingJobCode;
  String? get recruitingJobCode => _$this._recruitingJobCode;
  set recruitingJobCode(String? recruitingJobCode) =>
      _$this._recruitingJobCode = recruitingJobCode;

  String? _recruitingEmployCode;
  String? get recruitingEmployCode => _$this._recruitingEmployCode;
  set recruitingEmployCode(String? recruitingEmployCode) =>
      _$this._recruitingEmployCode = recruitingEmployCode;

  String? _workPlaceCode1;
  String? get workPlaceCode1 => _$this._workPlaceCode1;
  set workPlaceCode1(String? workPlaceCode1) =>
      _$this._workPlaceCode1 = workPlaceCode1;

  String? _workPlaceCode2;
  String? get workPlaceCode2 => _$this._workPlaceCode2;
  set workPlaceCode2(String? workPlaceCode2) =>
      _$this._workPlaceCode2 = workPlaceCode2;

  String? _workPlaceCode3;
  String? get workPlaceCode3 => _$this._workPlaceCode3;
  set workPlaceCode3(String? workPlaceCode3) =>
      _$this._workPlaceCode3 = workPlaceCode3;

  String? _payrollCode;
  String? get payrollCode => _$this._payrollCode;
  set payrollCode(String? payrollCode) => _$this._payrollCode = payrollCode;

  double? _payrollPriceFrom;
  double? get payrollPriceFrom => _$this._payrollPriceFrom;
  set payrollPriceFrom(double? payrollPriceFrom) =>
      _$this._payrollPriceFrom = payrollPriceFrom;

  double? _payrollPriceTo;
  double? get payrollPriceTo => _$this._payrollPriceTo;
  set payrollPriceTo(double? payrollPriceTo) =>
      _$this._payrollPriceTo = payrollPriceTo;

  double? _agentFee;
  double? get agentFee => _$this._agentFee;
  set agentFee(double? agentFee) => _$this._agentFee = agentFee;

  String? _agentFeeCurrCode;
  String? get agentFeeCurrCode => _$this._agentFeeCurrCode;
  set agentFeeCurrCode(String? agentFeeCurrCode) =>
      _$this._agentFeeCurrCode = agentFeeCurrCode;

  int? _hostAgent;
  int? get hostAgent => _$this._hostAgent;
  set hostAgent(int? hostAgent) => _$this._hostAgent = hostAgent;

  GeneralCompanySaveFilterBuilder() {
    GeneralCompanySaveFilter._defaults(this);
  }

  GeneralCompanySaveFilterBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _filterName = $v.filterName;
      _ageFrom = $v.ageFrom;
      _ageTo = $v.ageTo;
      _sexType = $v.sexType;
      _countryCode = $v.countryCode;
      _addressCode1 = $v.addressCode1;
      _addressCode2 = $v.addressCode2;
      _lastAcademicCode = $v.lastAcademicCode;
      _languageCode1 = $v.languageCode1;
      _languageLevelType1 = $v.languageLevelType1;
      _languageCode2 = $v.languageCode2;
      _languageLevelType2 = $v.languageLevelType2;
      _experiencedJobCode1 = $v.experiencedJobCode1;
      _yearsOfExperience1 = $v.yearsOfExperience1;
      _experiencedJobCode2 = $v.experiencedJobCode2;
      _yearsOfExperience2 = $v.yearsOfExperience2;
      _experiencedJobCode3 = $v.experiencedJobCode3;
      _yearsOfExperience3 = $v.yearsOfExperience3;
      _skillCode11 = $v.skillCode11;
      _skillLevelType11 = $v.skillLevelType11;
      _skillCode12 = $v.skillCode12;
      _skillLevelType12 = $v.skillLevelType12;
      _skillCode13 = $v.skillCode13;
      _skillLevelType13 = $v.skillLevelType13;
      _skillCode21 = $v.skillCode21;
      _skillLevelType21 = $v.skillLevelType21;
      _skillCode22 = $v.skillCode22;
      _skillLevelType22 = $v.skillLevelType22;
      _skillCode23 = $v.skillCode23;
      _skillLevelType23 = $v.skillLevelType23;
      _skillCode31 = $v.skillCode31;
      _skillLevelType31 = $v.skillLevelType31;
      _skillCode32 = $v.skillCode32;
      _skillLevelType32 = $v.skillLevelType32;
      _skillCode33 = $v.skillCode33;
      _skillLevelType33 = $v.skillLevelType33;
      _licenceCode1 = $v.licenceCode1;
      _licencePoint1 = $v.licencePoint1;
      _licenceCode2 = $v.licenceCode2;
      _licencePoint2 = $v.licencePoint2;
      _licenceCode3 = $v.licenceCode3;
      _licencePoint3 = $v.licencePoint3;
      _recruitingJobCode = $v.recruitingJobCode;
      _recruitingEmployCode = $v.recruitingEmployCode;
      _workPlaceCode1 = $v.workPlaceCode1;
      _workPlaceCode2 = $v.workPlaceCode2;
      _workPlaceCode3 = $v.workPlaceCode3;
      _payrollCode = $v.payrollCode;
      _payrollPriceFrom = $v.payrollPriceFrom;
      _payrollPriceTo = $v.payrollPriceTo;
      _agentFee = $v.agentFee;
      _agentFeeCurrCode = $v.agentFeeCurrCode;
      _hostAgent = $v.hostAgent;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GeneralCompanySaveFilter other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GeneralCompanySaveFilter;
  }

  @override
  void update(void Function(GeneralCompanySaveFilterBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GeneralCompanySaveFilter build() => _build();

  _$GeneralCompanySaveFilter _build() {
    final _$result = _$v ??
        new _$GeneralCompanySaveFilter._(
          filterName: BuiltValueNullFieldError.checkNotNull(
              filterName, r'GeneralCompanySaveFilter', 'filterName'),
          ageFrom: ageFrom,
          ageTo: ageTo,
          sexType: sexType,
          countryCode: countryCode,
          addressCode1: addressCode1,
          addressCode2: addressCode2,
          lastAcademicCode: lastAcademicCode,
          languageCode1: languageCode1,
          languageLevelType1: languageLevelType1,
          languageCode2: languageCode2,
          languageLevelType2: languageLevelType2,
          experiencedJobCode1: experiencedJobCode1,
          yearsOfExperience1: yearsOfExperience1,
          experiencedJobCode2: experiencedJobCode2,
          yearsOfExperience2: yearsOfExperience2,
          experiencedJobCode3: experiencedJobCode3,
          yearsOfExperience3: yearsOfExperience3,
          skillCode11: skillCode11,
          skillLevelType11: skillLevelType11,
          skillCode12: skillCode12,
          skillLevelType12: skillLevelType12,
          skillCode13: skillCode13,
          skillLevelType13: skillLevelType13,
          skillCode21: skillCode21,
          skillLevelType21: skillLevelType21,
          skillCode22: skillCode22,
          skillLevelType22: skillLevelType22,
          skillCode23: skillCode23,
          skillLevelType23: skillLevelType23,
          skillCode31: skillCode31,
          skillLevelType31: skillLevelType31,
          skillCode32: skillCode32,
          skillLevelType32: skillLevelType32,
          skillCode33: skillCode33,
          skillLevelType33: skillLevelType33,
          licenceCode1: licenceCode1,
          licencePoint1: licencePoint1,
          licenceCode2: licenceCode2,
          licencePoint2: licencePoint2,
          licenceCode3: licenceCode3,
          licencePoint3: licencePoint3,
          recruitingJobCode: recruitingJobCode,
          recruitingEmployCode: recruitingEmployCode,
          workPlaceCode1: workPlaceCode1,
          workPlaceCode2: workPlaceCode2,
          workPlaceCode3: workPlaceCode3,
          payrollCode: payrollCode,
          payrollPriceFrom: payrollPriceFrom,
          payrollPriceTo: payrollPriceTo,
          agentFee: agentFee,
          agentFeeCurrCode: agentFeeCurrCode,
          hostAgent: BuiltValueNullFieldError.checkNotNull(
              hostAgent, r'GeneralCompanySaveFilter', 'hostAgent'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
