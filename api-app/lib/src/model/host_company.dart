//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'host_company.g.dart';

/// HostCompany
///
/// Properties:
/// * [name] 
/// * [webUrl] 
/// * [employeesType] 
/// * [countryCode] 
/// * [addressCode] 
/// * [address] 
/// * [tel] 
/// * [contactMail] 
/// * [internationalTel] 
/// * [capitalStock] 
/// * [capitalStockCurrCode] 
/// * [logoImagePath] 
/// * [acceptingFee] 
/// * [acceptingFeeCurrCode] 
/// * [supportOutsourcingFee] 
/// * [supportOutsourcingFeeCurrCode] 
/// * [support] 
@BuiltValue()
abstract class HostCompany implements Built<HostCompany, HostCompanyBuilder> {
  @BuiltValueField(wireName: r'name')
  String? get name;

  @BuiltValueField(wireName: r'web_url')
  String? get webUrl;

  @BuiltValueField(wireName: r'employees_type')
  int? get employeesType;

  @BuiltValueField(wireName: r'country_code')
  String? get countryCode;

  @BuiltValueField(wireName: r'address_code')
  String? get addressCode;

  @BuiltValueField(wireName: r'address')
  String? get address;

  @BuiltValueField(wireName: r'tel')
  String? get tel;

  @BuiltValueField(wireName: r'contact_mail')
  String get contactMail;

  @BuiltValueField(wireName: r'international_tel')
  String? get internationalTel;

  @BuiltValueField(wireName: r'capital_stock')
  String? get capitalStock;

  @BuiltValueField(wireName: r'capital_stock_curr_code')
  String? get capitalStockCurrCode;

  @BuiltValueField(wireName: r'logo_image_path')
  String? get logoImagePath;

  @BuiltValueField(wireName: r'accepting_fee')
  String? get acceptingFee;

  @BuiltValueField(wireName: r'accepting_fee_curr_code')
  String? get acceptingFeeCurrCode;

  @BuiltValueField(wireName: r'support_outsourcing_fee')
  String? get supportOutsourcingFee;

  @BuiltValueField(wireName: r'support_outsourcing_fee_curr_code')
  String? get supportOutsourcingFeeCurrCode;

  @BuiltValueField(wireName: r'support')
  String? get support;

  HostCompany._();

  factory HostCompany([void updates(HostCompanyBuilder b)]) = _$HostCompany;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(HostCompanyBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<HostCompany> get serializer => _$HostCompanySerializer();
}

class _$HostCompanySerializer implements PrimitiveSerializer<HostCompany> {
  @override
  final Iterable<Type> types = const [HostCompany, _$HostCompany];

  @override
  final String wireName = r'HostCompany';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    HostCompany object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.name != null) {
      yield r'name';
      yield serializers.serialize(
        object.name,
        specifiedType: const FullType.nullable(String),
      );
    }
    yield r'web_url';
    yield object.webUrl == null ? null : serializers.serialize(
      object.webUrl,
      specifiedType: const FullType.nullable(String),
    );
    if (object.employeesType != null) {
      yield r'employees_type';
      yield serializers.serialize(
        object.employeesType,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.countryCode != null) {
      yield r'country_code';
      yield serializers.serialize(
        object.countryCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.addressCode != null) {
      yield r'address_code';
      yield serializers.serialize(
        object.addressCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.address != null) {
      yield r'address';
      yield serializers.serialize(
        object.address,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.tel != null) {
      yield r'tel';
      yield serializers.serialize(
        object.tel,
        specifiedType: const FullType.nullable(String),
      );
    }
    yield r'contact_mail';
    yield serializers.serialize(
      object.contactMail,
      specifiedType: const FullType(String),
    );
    if (object.internationalTel != null) {
      yield r'international_tel';
      yield serializers.serialize(
        object.internationalTel,
        specifiedType: const FullType.nullable(String),
      );
    }
    yield r'capital_stock';
    yield object.capitalStock == null ? null : serializers.serialize(
      object.capitalStock,
      specifiedType: const FullType.nullable(String),
    );
    yield r'capital_stock_curr_code';
    yield object.capitalStockCurrCode == null ? null : serializers.serialize(
      object.capitalStockCurrCode,
      specifiedType: const FullType.nullable(String),
    );
    if (object.logoImagePath != null) {
      yield r'logo_image_path';
      yield serializers.serialize(
        object.logoImagePath,
        specifiedType: const FullType.nullable(String),
      );
    }
    yield r'accepting_fee';
    yield object.acceptingFee == null ? null : serializers.serialize(
      object.acceptingFee,
      specifiedType: const FullType.nullable(String),
    );
    yield r'accepting_fee_curr_code';
    yield object.acceptingFeeCurrCode == null ? null : serializers.serialize(
      object.acceptingFeeCurrCode,
      specifiedType: const FullType.nullable(String),
    );
    yield r'support_outsourcing_fee';
    yield object.supportOutsourcingFee == null ? null : serializers.serialize(
      object.supportOutsourcingFee,
      specifiedType: const FullType.nullable(String),
    );
    yield r'support_outsourcing_fee_curr_code';
    yield object.supportOutsourcingFeeCurrCode == null ? null : serializers.serialize(
      object.supportOutsourcingFeeCurrCode,
      specifiedType: const FullType.nullable(String),
    );
    yield r'support';
    yield object.support == null ? null : serializers.serialize(
      object.support,
      specifiedType: const FullType.nullable(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    HostCompany object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required HostCompanyBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.name = valueDes;
          break;
        case r'web_url':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.webUrl = valueDes;
          break;
        case r'employees_type':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.employeesType = valueDes;
          break;
        case r'country_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.countryCode = valueDes;
          break;
        case r'address_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.addressCode = valueDes;
          break;
        case r'address':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.address = valueDes;
          break;
        case r'tel':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.tel = valueDes;
          break;
        case r'contact_mail':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.contactMail = valueDes;
          break;
        case r'international_tel':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.internationalTel = valueDes;
          break;
        case r'capital_stock':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.capitalStock = valueDes;
          break;
        case r'capital_stock_curr_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.capitalStockCurrCode = valueDes;
          break;
        case r'logo_image_path':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.logoImagePath = valueDes;
          break;
        case r'accepting_fee':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.acceptingFee = valueDes;
          break;
        case r'accepting_fee_curr_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.acceptingFeeCurrCode = valueDes;
          break;
        case r'support_outsourcing_fee':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.supportOutsourcingFee = valueDes;
          break;
        case r'support_outsourcing_fee_curr_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.supportOutsourcingFeeCurrCode = valueDes;
          break;
        case r'support':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.support = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  HostCompany deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = HostCompanyBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

