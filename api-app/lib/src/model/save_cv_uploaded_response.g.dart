// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'save_cv_uploaded_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$SaveCVUploadedResponse extends SaveCVUploadedResponse {
  @override
  final String? message;
  @override
  final BuiltList<ErrorDetail>? errors;
  @override
  final String? data;

  factory _$SaveCVUploadedResponse(
          [void Function(SaveCVUploadedResponseBuilder)? updates]) =>
      (new SaveCVUploadedResponseBuilder()..update(updates))._build();

  _$SaveCVUploadedResponse._({this.message, this.errors, this.data})
      : super._();

  @override
  SaveCVUploadedResponse rebuild(
          void Function(SaveCVUploadedResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SaveCVUploadedResponseBuilder toBuilder() =>
      new SaveCVUploadedResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SaveCVUploadedResponse &&
        message == other.message &&
        errors == other.errors &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SaveCVUploadedResponse')
          ..add('message', message)
          ..add('errors', errors)
          ..add('data', data))
        .toString();
  }
}

class SaveCVUploadedResponseBuilder
    implements Builder<SaveCVUploadedResponse, SaveCVUploadedResponseBuilder> {
  _$SaveCVUploadedResponse? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  String? _data;
  String? get data => _$this._data;
  set data(String? data) => _$this._data = data;

  SaveCVUploadedResponseBuilder() {
    SaveCVUploadedResponse._defaults(this);
  }

  SaveCVUploadedResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _errors = $v.errors?.toBuilder();
      _data = $v.data;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SaveCVUploadedResponse other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$SaveCVUploadedResponse;
  }

  @override
  void update(void Function(SaveCVUploadedResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SaveCVUploadedResponse build() => _build();

  _$SaveCVUploadedResponse _build() {
    _$SaveCVUploadedResponse _$result;
    try {
      _$result = _$v ??
          new _$SaveCVUploadedResponse._(
            message: message,
            errors: _errors?.build(),
            data: data,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'errors';
        _errors?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'SaveCVUploadedResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
