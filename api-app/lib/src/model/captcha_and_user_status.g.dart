// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'captcha_and_user_status.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$CaptchaAndUserStatus extends CaptchaAndUserStatus {
  @override
  final bool requireCaptcha;
  @override
  final bool isUserVerified;

  factory _$CaptchaAndUserStatus(
          [void Function(CaptchaAndUserStatusBuilder)? updates]) =>
      (new CaptchaAndUserStatusBuilder()..update(updates))._build();

  _$CaptchaAndUserStatus._(
      {required this.requireCaptcha, required this.isUserVerified})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        requireCaptcha, r'CaptchaAndUserStatus', 'requireCaptcha');
    BuiltValueNullFieldError.checkNotNull(
        isUserVerified, r'CaptchaAndUserStatus', 'isUserVerified');
  }

  @override
  CaptchaAndUserStatus rebuild(
          void Function(CaptchaAndUserStatusBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  CaptchaAndUserStatusBuilder toBuilder() =>
      new CaptchaAndUserStatusBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is CaptchaAndUserStatus &&
        requireCaptcha == other.requireCaptcha &&
        isUserVerified == other.isUserVerified;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, requireCaptcha.hashCode);
    _$hash = $jc(_$hash, isUserVerified.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'CaptchaAndUserStatus')
          ..add('requireCaptcha', requireCaptcha)
          ..add('isUserVerified', isUserVerified))
        .toString();
  }
}

class CaptchaAndUserStatusBuilder
    implements Builder<CaptchaAndUserStatus, CaptchaAndUserStatusBuilder> {
  _$CaptchaAndUserStatus? _$v;

  bool? _requireCaptcha;
  bool? get requireCaptcha => _$this._requireCaptcha;
  set requireCaptcha(bool? requireCaptcha) =>
      _$this._requireCaptcha = requireCaptcha;

  bool? _isUserVerified;
  bool? get isUserVerified => _$this._isUserVerified;
  set isUserVerified(bool? isUserVerified) =>
      _$this._isUserVerified = isUserVerified;

  CaptchaAndUserStatusBuilder() {
    CaptchaAndUserStatus._defaults(this);
  }

  CaptchaAndUserStatusBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _requireCaptcha = $v.requireCaptcha;
      _isUserVerified = $v.isUserVerified;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(CaptchaAndUserStatus other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$CaptchaAndUserStatus;
  }

  @override
  void update(void Function(CaptchaAndUserStatusBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  CaptchaAndUserStatus build() => _build();

  _$CaptchaAndUserStatus _build() {
    final _$result = _$v ??
        new _$CaptchaAndUserStatus._(
          requireCaptcha: BuiltValueNullFieldError.checkNotNull(
              requireCaptcha, r'CaptchaAndUserStatus', 'requireCaptcha'),
          isUserVerified: BuiltValueNullFieldError.checkNotNull(
              isUserVerified, r'CaptchaAndUserStatus', 'isUserVerified'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
