//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'assessment_answer.g.dart';

/// AssessmentAnswer
///
/// Properties:
/// * [uuid] 
/// * [text] 
/// * [textEn] 
/// * [textVi] 
/// * [isSelected] 
/// * [description] 
@BuiltValue()
abstract class AssessmentAnswer implements Built<AssessmentAnswer, AssessmentAnswerBuilder> {
  @BuiltValueField(wireName: r'uuid')
  String get uuid;

  @BuiltValueField(wireName: r'text')
  String? get text;

  @BuiltValueField(wireName: r'text_en')
  String get textEn;

  @BuiltValueField(wireName: r'text_vi')
  String get textVi;

  @BuiltValueField(wireName: r'is_selected')
  bool? get isSelected;

  @BuiltValueField(wireName: r'description')
  String? get description;

  AssessmentAnswer._();

  factory AssessmentAnswer([void updates(AssessmentAnswerBuilder b)]) = _$AssessmentAnswer;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AssessmentAnswerBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AssessmentAnswer> get serializer => _$AssessmentAnswerSerializer();
}

class _$AssessmentAnswerSerializer implements PrimitiveSerializer<AssessmentAnswer> {
  @override
  final Iterable<Type> types = const [AssessmentAnswer, _$AssessmentAnswer];

  @override
  final String wireName = r'AssessmentAnswer';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AssessmentAnswer object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'uuid';
    yield serializers.serialize(
      object.uuid,
      specifiedType: const FullType(String),
    );
    if (object.text != null) {
      yield r'text';
      yield serializers.serialize(
        object.text,
        specifiedType: const FullType(String),
      );
    }
    yield r'text_en';
    yield serializers.serialize(
      object.textEn,
      specifiedType: const FullType(String),
    );
    yield r'text_vi';
    yield serializers.serialize(
      object.textVi,
      specifiedType: const FullType(String),
    );
    yield r'is_selected';
    yield object.isSelected == null ? null : serializers.serialize(
      object.isSelected,
      specifiedType: const FullType.nullable(bool),
    );
    if (object.description != null) {
      yield r'description';
      yield serializers.serialize(
        object.description,
        specifiedType: const FullType(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    AssessmentAnswer object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AssessmentAnswerBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'uuid':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.uuid = valueDes;
          break;
        case r'text':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.text = valueDes;
          break;
        case r'text_en':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.textEn = valueDes;
          break;
        case r'text_vi':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.textVi = valueDes;
          break;
        case r'is_selected':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(bool),
          ) as bool?;
          if (valueDes == null) continue;
          result.isSelected = valueDes;
          break;
        case r'description':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.description = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AssessmentAnswer deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AssessmentAnswerBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

