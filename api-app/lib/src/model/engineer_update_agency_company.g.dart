// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'engineer_update_agency_company.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$EngineerUpdateAgencyCompany extends EngineerUpdateAgencyCompany {
  @override
  final int? optionalEngineerId;
  @override
  final String contactMail;

  factory _$EngineerUpdateAgencyCompany(
          [void Function(EngineerUpdateAgencyCompanyBuilder)? updates]) =>
      (new EngineerUpdateAgencyCompanyBuilder()..update(updates))._build();

  _$EngineerUpdateAgencyCompany._(
      {this.optionalEngineerId, required this.contactMail})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        contactMail, r'EngineerUpdateAgencyCompany', 'contactMail');
  }

  @override
  EngineerUpdateAgencyCompany rebuild(
          void Function(EngineerUpdateAgencyCompanyBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  EngineerUpdateAgencyCompanyBuilder toBuilder() =>
      new EngineerUpdateAgencyCompanyBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is EngineerUpdateAgencyCompany &&
        optionalEngineerId == other.optionalEngineerId &&
        contactMail == other.contactMail;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, optionalEngineerId.hashCode);
    _$hash = $jc(_$hash, contactMail.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'EngineerUpdateAgencyCompany')
          ..add('optionalEngineerId', optionalEngineerId)
          ..add('contactMail', contactMail))
        .toString();
  }
}

class EngineerUpdateAgencyCompanyBuilder
    implements
        Builder<EngineerUpdateAgencyCompany,
            EngineerUpdateAgencyCompanyBuilder> {
  _$EngineerUpdateAgencyCompany? _$v;

  int? _optionalEngineerId;
  int? get optionalEngineerId => _$this._optionalEngineerId;
  set optionalEngineerId(int? optionalEngineerId) =>
      _$this._optionalEngineerId = optionalEngineerId;

  String? _contactMail;
  String? get contactMail => _$this._contactMail;
  set contactMail(String? contactMail) => _$this._contactMail = contactMail;

  EngineerUpdateAgencyCompanyBuilder() {
    EngineerUpdateAgencyCompany._defaults(this);
  }

  EngineerUpdateAgencyCompanyBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _optionalEngineerId = $v.optionalEngineerId;
      _contactMail = $v.contactMail;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(EngineerUpdateAgencyCompany other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$EngineerUpdateAgencyCompany;
  }

  @override
  void update(void Function(EngineerUpdateAgencyCompanyBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  EngineerUpdateAgencyCompany build() => _build();

  _$EngineerUpdateAgencyCompany _build() {
    final _$result = _$v ??
        new _$EngineerUpdateAgencyCompany._(
          optionalEngineerId: optionalEngineerId,
          contactMail: BuiltValueNullFieldError.checkNotNull(
              contactMail, r'EngineerUpdateAgencyCompany', 'contactMail'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
