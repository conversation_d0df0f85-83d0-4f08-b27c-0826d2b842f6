// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_email_schedule.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$CreateEmailSchedule extends CreateEmailSchedule {
  @override
  final int? type;
  @override
  final String? subject;
  @override
  final String? body;
  @override
  final String? weekday;
  @override
  final String? sendTime;
  @override
  final DateTime? sendDatetime;
  @override
  final int? isValid;
  @override
  final int? isRepeat;
  @override
  final String? targetEmail;

  factory _$CreateEmailSchedule(
          [void Function(CreateEmailScheduleBuilder)? updates]) =>
      (new CreateEmailScheduleBuilder()..update(updates))._build();

  _$CreateEmailSchedule._(
      {this.type,
      this.subject,
      this.body,
      this.weekday,
      this.sendTime,
      this.sendDatetime,
      this.isValid,
      this.isRepeat,
      this.targetEmail})
      : super._();

  @override
  CreateEmailSchedule rebuild(
          void Function(CreateEmailScheduleBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  CreateEmailScheduleBuilder toBuilder() =>
      new CreateEmailScheduleBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is CreateEmailSchedule &&
        type == other.type &&
        subject == other.subject &&
        body == other.body &&
        weekday == other.weekday &&
        sendTime == other.sendTime &&
        sendDatetime == other.sendDatetime &&
        isValid == other.isValid &&
        isRepeat == other.isRepeat &&
        targetEmail == other.targetEmail;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, type.hashCode);
    _$hash = $jc(_$hash, subject.hashCode);
    _$hash = $jc(_$hash, body.hashCode);
    _$hash = $jc(_$hash, weekday.hashCode);
    _$hash = $jc(_$hash, sendTime.hashCode);
    _$hash = $jc(_$hash, sendDatetime.hashCode);
    _$hash = $jc(_$hash, isValid.hashCode);
    _$hash = $jc(_$hash, isRepeat.hashCode);
    _$hash = $jc(_$hash, targetEmail.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'CreateEmailSchedule')
          ..add('type', type)
          ..add('subject', subject)
          ..add('body', body)
          ..add('weekday', weekday)
          ..add('sendTime', sendTime)
          ..add('sendDatetime', sendDatetime)
          ..add('isValid', isValid)
          ..add('isRepeat', isRepeat)
          ..add('targetEmail', targetEmail))
        .toString();
  }
}

class CreateEmailScheduleBuilder
    implements Builder<CreateEmailSchedule, CreateEmailScheduleBuilder> {
  _$CreateEmailSchedule? _$v;

  int? _type;
  int? get type => _$this._type;
  set type(int? type) => _$this._type = type;

  String? _subject;
  String? get subject => _$this._subject;
  set subject(String? subject) => _$this._subject = subject;

  String? _body;
  String? get body => _$this._body;
  set body(String? body) => _$this._body = body;

  String? _weekday;
  String? get weekday => _$this._weekday;
  set weekday(String? weekday) => _$this._weekday = weekday;

  String? _sendTime;
  String? get sendTime => _$this._sendTime;
  set sendTime(String? sendTime) => _$this._sendTime = sendTime;

  DateTime? _sendDatetime;
  DateTime? get sendDatetime => _$this._sendDatetime;
  set sendDatetime(DateTime? sendDatetime) =>
      _$this._sendDatetime = sendDatetime;

  int? _isValid;
  int? get isValid => _$this._isValid;
  set isValid(int? isValid) => _$this._isValid = isValid;

  int? _isRepeat;
  int? get isRepeat => _$this._isRepeat;
  set isRepeat(int? isRepeat) => _$this._isRepeat = isRepeat;

  String? _targetEmail;
  String? get targetEmail => _$this._targetEmail;
  set targetEmail(String? targetEmail) => _$this._targetEmail = targetEmail;

  CreateEmailScheduleBuilder() {
    CreateEmailSchedule._defaults(this);
  }

  CreateEmailScheduleBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _type = $v.type;
      _subject = $v.subject;
      _body = $v.body;
      _weekday = $v.weekday;
      _sendTime = $v.sendTime;
      _sendDatetime = $v.sendDatetime;
      _isValid = $v.isValid;
      _isRepeat = $v.isRepeat;
      _targetEmail = $v.targetEmail;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(CreateEmailSchedule other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$CreateEmailSchedule;
  }

  @override
  void update(void Function(CreateEmailScheduleBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  CreateEmailSchedule build() => _build();

  _$CreateEmailSchedule _build() {
    final _$result = _$v ??
        new _$CreateEmailSchedule._(
          type: type,
          subject: subject,
          body: body,
          weekday: weekday,
          sendTime: sendTime,
          sendDatetime: sendDatetime,
          isValid: isValid,
          isRepeat: isRepeat,
          targetEmail: targetEmail,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
