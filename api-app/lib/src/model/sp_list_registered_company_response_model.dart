//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/error_detail.dart';
import 'package:openapi/src/model/sp_company_registered.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'sp_list_registered_company_response_model.g.dart';

/// SPListRegisteredCompanyResponseModel
///
/// Properties:
/// * [message] 
/// * [errors] 
/// * [data] 
@BuiltValue()
abstract class SPListRegisteredCompanyResponseModel implements Built<SPListRegisteredCompanyResponseModel, SPListRegisteredCompanyResponseModelBuilder> {
  @BuiltValueField(wireName: r'message')
  String? get message;

  @BuiltValueField(wireName: r'errors')
  BuiltList<ErrorDetail>? get errors;

  @BuiltValueField(wireName: r'data')
  BuiltList<SPCompanyRegistered> get data;

  SPListRegisteredCompanyResponseModel._();

  factory SPListRegisteredCompanyResponseModel([void updates(SPListRegisteredCompanyResponseModelBuilder b)]) = _$SPListRegisteredCompanyResponseModel;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(SPListRegisteredCompanyResponseModelBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<SPListRegisteredCompanyResponseModel> get serializer => _$SPListRegisteredCompanyResponseModelSerializer();
}

class _$SPListRegisteredCompanyResponseModelSerializer implements PrimitiveSerializer<SPListRegisteredCompanyResponseModel> {
  @override
  final Iterable<Type> types = const [SPListRegisteredCompanyResponseModel, _$SPListRegisteredCompanyResponseModel];

  @override
  final String wireName = r'SPListRegisteredCompanyResponseModel';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    SPListRegisteredCompanyResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'message';
    yield object.message == null ? null : serializers.serialize(
      object.message,
      specifiedType: const FullType.nullable(String),
    );
    yield r'errors';
    yield object.errors == null ? null : serializers.serialize(
      object.errors,
      specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
    );
    yield r'data';
    yield serializers.serialize(
      object.data,
      specifiedType: const FullType(BuiltList, [FullType(SPCompanyRegistered)]),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    SPListRegisteredCompanyResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required SPListRegisteredCompanyResponseModelBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'message':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.message = valueDes;
          break;
        case r'errors':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
          ) as BuiltList<ErrorDetail>?;
          if (valueDes == null) continue;
          result.errors.replace(valueDes);
          break;
        case r'data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(SPCompanyRegistered)]),
          ) as BuiltList<SPCompanyRegistered>;
          result.data.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  SPListRegisteredCompanyResponseModel deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = SPListRegisteredCompanyResponseModelBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

