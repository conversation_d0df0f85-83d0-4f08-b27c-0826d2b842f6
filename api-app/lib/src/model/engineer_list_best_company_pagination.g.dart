// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'engineer_list_best_company_pagination.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$EngineerListBestCompanyPagination
    extends EngineerListBestCompanyPagination {
  @override
  final String? next;
  @override
  final String? previous;
  @override
  final BuiltList<EngineerBestCompany> results;

  factory _$EngineerListBestCompanyPagination(
          [void Function(EngineerListBestCompanyPaginationBuilder)? updates]) =>
      (new EngineerListBestCompanyPaginationBuilder()..update(updates))
          ._build();

  _$EngineerListBestCompanyPagination._(
      {this.next, this.previous, required this.results})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        results, r'EngineerListBestCompanyPagination', 'results');
  }

  @override
  EngineerListBestCompanyPagination rebuild(
          void Function(EngineerListBestCompanyPaginationBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  EngineerListBestCompanyPaginationBuilder toBuilder() =>
      new EngineerListBestCompanyPaginationBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is EngineerListBestCompanyPagination &&
        next == other.next &&
        previous == other.previous &&
        results == other.results;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, next.hashCode);
    _$hash = $jc(_$hash, previous.hashCode);
    _$hash = $jc(_$hash, results.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'EngineerListBestCompanyPagination')
          ..add('next', next)
          ..add('previous', previous)
          ..add('results', results))
        .toString();
  }
}

class EngineerListBestCompanyPaginationBuilder
    implements
        Builder<EngineerListBestCompanyPagination,
            EngineerListBestCompanyPaginationBuilder> {
  _$EngineerListBestCompanyPagination? _$v;

  String? _next;
  String? get next => _$this._next;
  set next(String? next) => _$this._next = next;

  String? _previous;
  String? get previous => _$this._previous;
  set previous(String? previous) => _$this._previous = previous;

  ListBuilder<EngineerBestCompany>? _results;
  ListBuilder<EngineerBestCompany> get results =>
      _$this._results ??= new ListBuilder<EngineerBestCompany>();
  set results(ListBuilder<EngineerBestCompany>? results) =>
      _$this._results = results;

  EngineerListBestCompanyPaginationBuilder() {
    EngineerListBestCompanyPagination._defaults(this);
  }

  EngineerListBestCompanyPaginationBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _next = $v.next;
      _previous = $v.previous;
      _results = $v.results.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(EngineerListBestCompanyPagination other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$EngineerListBestCompanyPagination;
  }

  @override
  void update(
      void Function(EngineerListBestCompanyPaginationBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  EngineerListBestCompanyPagination build() => _build();

  _$EngineerListBestCompanyPagination _build() {
    _$EngineerListBestCompanyPagination _$result;
    try {
      _$result = _$v ??
          new _$EngineerListBestCompanyPagination._(
            next: next,
            previous: previous,
            results: results.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'results';
        results.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'EngineerListBestCompanyPagination', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
