// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_company_list_user_explore_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GeneralCompanyListUserExploreResponseModel
    extends GeneralCompanyListUserExploreResponseModel {
  @override
  final String? message;
  @override
  final PagingGeneralCompanyExploreUserResponseModel data;
  @override
  final BuiltList<ErrorDetail>? errors;

  factory _$GeneralCompanyListUserExploreResponseModel(
          [void Function(GeneralCompanyListUserExploreResponseModelBuilder)?
              updates]) =>
      (new GeneralCompanyListUserExploreResponseModelBuilder()..update(updates))
          ._build();

  _$GeneralCompanyListUserExploreResponseModel._(
      {this.message, required this.data, this.errors})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'GeneralCompanyListUserExploreResponseModel', 'data');
  }

  @override
  GeneralCompanyListUserExploreResponseModel rebuild(
          void Function(GeneralCompanyListUserExploreResponseModelBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GeneralCompanyListUserExploreResponseModelBuilder toBuilder() =>
      new GeneralCompanyListUserExploreResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GeneralCompanyListUserExploreResponseModel &&
        message == other.message &&
        data == other.data &&
        errors == other.errors;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'GeneralCompanyListUserExploreResponseModel')
          ..add('message', message)
          ..add('data', data)
          ..add('errors', errors))
        .toString();
  }
}

class GeneralCompanyListUserExploreResponseModelBuilder
    implements
        Builder<GeneralCompanyListUserExploreResponseModel,
            GeneralCompanyListUserExploreResponseModelBuilder> {
  _$GeneralCompanyListUserExploreResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  PagingGeneralCompanyExploreUserResponseModelBuilder? _data;
  PagingGeneralCompanyExploreUserResponseModelBuilder get data =>
      _$this._data ??=
          new PagingGeneralCompanyExploreUserResponseModelBuilder();
  set data(PagingGeneralCompanyExploreUserResponseModelBuilder? data) =>
      _$this._data = data;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  GeneralCompanyListUserExploreResponseModelBuilder() {
    GeneralCompanyListUserExploreResponseModel._defaults(this);
  }

  GeneralCompanyListUserExploreResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _data = $v.data.toBuilder();
      _errors = $v.errors?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GeneralCompanyListUserExploreResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GeneralCompanyListUserExploreResponseModel;
  }

  @override
  void update(
      void Function(GeneralCompanyListUserExploreResponseModelBuilder)?
          updates) {
    if (updates != null) updates(this);
  }

  @override
  GeneralCompanyListUserExploreResponseModel build() => _build();

  _$GeneralCompanyListUserExploreResponseModel _build() {
    _$GeneralCompanyListUserExploreResponseModel _$result;
    try {
      _$result = _$v ??
          new _$GeneralCompanyListUserExploreResponseModel._(
            message: message,
            data: data.build(),
            errors: _errors?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        data.build();
        _$failedField = 'errors';
        _errors?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'GeneralCompanyListUserExploreResponseModel',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
