//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'confirm_whatsapp_code.g.dart';

/// ConfirmWhatsappCode
///
/// Properties:
/// * [phoneNumber] 
/// * [code] 
@BuiltValue()
abstract class ConfirmWhatsappCode implements Built<ConfirmWhatsappCode, ConfirmWhatsappCodeBuilder> {
  @BuiltValueField(wireName: r'phone_number')
  String get phoneNumber;

  @BuiltValueField(wireName: r'code')
  String get code;

  ConfirmWhatsappCode._();

  factory ConfirmWhatsappCode([void updates(ConfirmWhatsappCodeBuilder b)]) = _$ConfirmWhatsappCode;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(ConfirmWhatsappCodeBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<ConfirmWhatsappCode> get serializer => _$ConfirmWhatsappCodeSerializer();
}

class _$ConfirmWhatsappCodeSerializer implements PrimitiveSerializer<ConfirmWhatsappCode> {
  @override
  final Iterable<Type> types = const [ConfirmWhatsappCode, _$ConfirmWhatsappCode];

  @override
  final String wireName = r'ConfirmWhatsappCode';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    ConfirmWhatsappCode object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'phone_number';
    yield serializers.serialize(
      object.phoneNumber,
      specifiedType: const FullType(String),
    );
    yield r'code';
    yield serializers.serialize(
      object.code,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    ConfirmWhatsappCode object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required ConfirmWhatsappCodeBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'phone_number':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.phoneNumber = valueDes;
          break;
        case r'code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.code = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  ConfirmWhatsappCode deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = ConfirmWhatsappCodeBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

