// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'list_group_chat.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ListGroupChat extends ListGroupChat {
  @override
  final String? next;
  @override
  final String? previous;
  @override
  final BuiltList<GroupChat> results;
  @override
  final int totalCount;

  factory _$ListGroupChat([void Function(ListGroupChatBuilder)? updates]) =>
      (new ListGroupChatBuilder()..update(updates))._build();

  _$ListGroupChat._(
      {this.next,
      this.previous,
      required this.results,
      required this.totalCount})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(results, r'ListGroupChat', 'results');
    BuiltValueNullFieldError.checkNotNull(
        totalCount, r'ListGroupChat', 'totalCount');
  }

  @override
  ListGroupChat rebuild(void Function(ListGroupChatBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ListGroupChatBuilder toBuilder() => new ListGroupChatBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ListGroupChat &&
        next == other.next &&
        previous == other.previous &&
        results == other.results &&
        totalCount == other.totalCount;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, next.hashCode);
    _$hash = $jc(_$hash, previous.hashCode);
    _$hash = $jc(_$hash, results.hashCode);
    _$hash = $jc(_$hash, totalCount.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ListGroupChat')
          ..add('next', next)
          ..add('previous', previous)
          ..add('results', results)
          ..add('totalCount', totalCount))
        .toString();
  }
}

class ListGroupChatBuilder
    implements Builder<ListGroupChat, ListGroupChatBuilder> {
  _$ListGroupChat? _$v;

  String? _next;
  String? get next => _$this._next;
  set next(String? next) => _$this._next = next;

  String? _previous;
  String? get previous => _$this._previous;
  set previous(String? previous) => _$this._previous = previous;

  ListBuilder<GroupChat>? _results;
  ListBuilder<GroupChat> get results =>
      _$this._results ??= new ListBuilder<GroupChat>();
  set results(ListBuilder<GroupChat>? results) => _$this._results = results;

  int? _totalCount;
  int? get totalCount => _$this._totalCount;
  set totalCount(int? totalCount) => _$this._totalCount = totalCount;

  ListGroupChatBuilder() {
    ListGroupChat._defaults(this);
  }

  ListGroupChatBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _next = $v.next;
      _previous = $v.previous;
      _results = $v.results.toBuilder();
      _totalCount = $v.totalCount;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ListGroupChat other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$ListGroupChat;
  }

  @override
  void update(void Function(ListGroupChatBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ListGroupChat build() => _build();

  _$ListGroupChat _build() {
    _$ListGroupChat _$result;
    try {
      _$result = _$v ??
          new _$ListGroupChat._(
            next: next,
            previous: previous,
            results: results.build(),
            totalCount: BuiltValueNullFieldError.checkNotNull(
                totalCount, r'ListGroupChat', 'totalCount'),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'results';
        results.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'ListGroupChat', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
