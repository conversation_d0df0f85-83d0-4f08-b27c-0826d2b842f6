// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'self_assessment_sheet.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$SelfAssessmentSheet extends SelfAssessmentSheet {
  @override
  final String textEn;
  @override
  final String textVi;
  @override
  final String text;

  factory _$SelfAssessmentSheet(
          [void Function(SelfAssessmentSheetBuilder)? updates]) =>
      (new SelfAssessmentSheetBuilder()..update(updates))._build();

  _$SelfAssessmentSheet._(
      {required this.textEn, required this.textVi, required this.text})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        textEn, r'SelfAssessmentSheet', 'textEn');
    BuiltValueNullFieldError.checkNotNull(
        textVi, r'SelfAssessmentSheet', 'textVi');
    BuiltValueNullFieldError.checkNotNull(text, r'SelfAssessmentSheet', 'text');
  }

  @override
  SelfAssessmentSheet rebuild(
          void Function(SelfAssessmentSheetBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SelfAssessmentSheetBuilder toBuilder() =>
      new SelfAssessmentSheetBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SelfAssessmentSheet &&
        textEn == other.textEn &&
        textVi == other.textVi &&
        text == other.text;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, textEn.hashCode);
    _$hash = $jc(_$hash, textVi.hashCode);
    _$hash = $jc(_$hash, text.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SelfAssessmentSheet')
          ..add('textEn', textEn)
          ..add('textVi', textVi)
          ..add('text', text))
        .toString();
  }
}

class SelfAssessmentSheetBuilder
    implements Builder<SelfAssessmentSheet, SelfAssessmentSheetBuilder> {
  _$SelfAssessmentSheet? _$v;

  String? _textEn;
  String? get textEn => _$this._textEn;
  set textEn(String? textEn) => _$this._textEn = textEn;

  String? _textVi;
  String? get textVi => _$this._textVi;
  set textVi(String? textVi) => _$this._textVi = textVi;

  String? _text;
  String? get text => _$this._text;
  set text(String? text) => _$this._text = text;

  SelfAssessmentSheetBuilder() {
    SelfAssessmentSheet._defaults(this);
  }

  SelfAssessmentSheetBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _textEn = $v.textEn;
      _textVi = $v.textVi;
      _text = $v.text;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SelfAssessmentSheet other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$SelfAssessmentSheet;
  }

  @override
  void update(void Function(SelfAssessmentSheetBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SelfAssessmentSheet build() => _build();

  _$SelfAssessmentSheet _build() {
    final _$result = _$v ??
        new _$SelfAssessmentSheet._(
          textEn: BuiltValueNullFieldError.checkNotNull(
              textEn, r'SelfAssessmentSheet', 'textEn'),
          textVi: BuiltValueNullFieldError.checkNotNull(
              textVi, r'SelfAssessmentSheet', 'textVi'),
          text: BuiltValueNullFieldError.checkNotNull(
              text, r'SelfAssessmentSheet', 'text'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
