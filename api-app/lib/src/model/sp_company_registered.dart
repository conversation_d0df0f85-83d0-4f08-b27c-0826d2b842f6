//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'sp_company_registered.g.dart';

/// SPCompanyRegistered
///
/// Properties:
/// * [companyId] 
/// * [name] 
/// * [logoImagePath] 
/// * [aboutUs] 
/// * [businessDetails] 
/// * [countryCode] 
/// * [addressCode] 
@BuiltValue()
abstract class SPCompanyRegistered implements Built<SPCompanyRegistered, SPCompanyRegisteredBuilder> {
  @BuiltValueField(wireName: r'company_id')
  int? get companyId;

  @BuiltValueField(wireName: r'name')
  String? get name;

  @BuiltValueField(wireName: r'logo_image_path')
  String? get logoImagePath;

  @BuiltValueField(wireName: r'about_us')
  String? get aboutUs;

  @BuiltValueField(wireName: r'business_details')
  String? get businessDetails;

  @BuiltValueField(wireName: r'country_code')
  String? get countryCode;

  @BuiltValueField(wireName: r'address_code')
  String? get addressCode;

  SPCompanyRegistered._();

  factory SPCompanyRegistered([void updates(SPCompanyRegisteredBuilder b)]) = _$SPCompanyRegistered;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(SPCompanyRegisteredBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<SPCompanyRegistered> get serializer => _$SPCompanyRegisteredSerializer();
}

class _$SPCompanyRegisteredSerializer implements PrimitiveSerializer<SPCompanyRegistered> {
  @override
  final Iterable<Type> types = const [SPCompanyRegistered, _$SPCompanyRegistered];

  @override
  final String wireName = r'SPCompanyRegistered';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    SPCompanyRegistered object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.companyId != null) {
      yield r'company_id';
      yield serializers.serialize(
        object.companyId,
        specifiedType: const FullType(int),
      );
    }
    if (object.name != null) {
      yield r'name';
      yield serializers.serialize(
        object.name,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.logoImagePath != null) {
      yield r'logo_image_path';
      yield serializers.serialize(
        object.logoImagePath,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.aboutUs != null) {
      yield r'about_us';
      yield serializers.serialize(
        object.aboutUs,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.businessDetails != null) {
      yield r'business_details';
      yield serializers.serialize(
        object.businessDetails,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.countryCode != null) {
      yield r'country_code';
      yield serializers.serialize(
        object.countryCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.addressCode != null) {
      yield r'address_code';
      yield serializers.serialize(
        object.addressCode,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    SPCompanyRegistered object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required SPCompanyRegisteredBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'company_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.companyId = valueDes;
          break;
        case r'name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.name = valueDes;
          break;
        case r'logo_image_path':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.logoImagePath = valueDes;
          break;
        case r'about_us':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.aboutUs = valueDes;
          break;
        case r'business_details':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.businessDetails = valueDes;
          break;
        case r'country_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.countryCode = valueDes;
          break;
        case r'address_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.addressCode = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  SPCompanyRegistered deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = SPCompanyRegisteredBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

