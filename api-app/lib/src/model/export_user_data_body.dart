//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'export_user_data_body.g.dart';

/// ExportUserDataBody
///
/// Properties:
/// * [engineerIds] 
/// * [exportType] 
@BuiltValue()
abstract class ExportUserDataBody implements Built<ExportUserDataBody, ExportUserDataBodyBuilder> {
  @BuiltValueField(wireName: r'engineer_ids')
  BuiltList<int> get engineerIds;

  @BuiltValueField(wireName: r'export_type')
  String get exportType;

  ExportUserDataBody._();

  factory ExportUserDataBody([void updates(ExportUserDataBodyBuilder b)]) = _$ExportUserDataBody;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(ExportUserDataBodyBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<ExportUserDataBody> get serializer => _$ExportUserDataBodySerializer();
}

class _$ExportUserDataBodySerializer implements PrimitiveSerializer<ExportUserDataBody> {
  @override
  final Iterable<Type> types = const [ExportUserDataBody, _$ExportUserDataBody];

  @override
  final String wireName = r'ExportUserDataBody';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    ExportUserDataBody object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'engineer_ids';
    yield serializers.serialize(
      object.engineerIds,
      specifiedType: const FullType(BuiltList, [FullType(int)]),
    );
    yield r'export_type';
    yield serializers.serialize(
      object.exportType,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    ExportUserDataBody object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required ExportUserDataBodyBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'engineer_ids':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(int)]),
          ) as BuiltList<int>;
          result.engineerIds.replace(valueDes);
          break;
        case r'export_type':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.exportType = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  ExportUserDataBody deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = ExportUserDataBodyBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

