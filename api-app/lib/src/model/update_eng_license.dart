//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'update_eng_license.g.dart';

/// UpdateEngLicense
///
/// Properties:
/// * [licenceCode] 
/// * [licenceName] 
/// * [getDate] 
/// * [licencePoint] 
@BuiltValue()
abstract class UpdateEngLicense implements Built<UpdateEngLicense, UpdateEngLicenseBuilder> {
  @BuiltValueField(wireName: r'licence_code')
  String? get licenceCode;

  @BuiltValueField(wireName: r'licence_name')
  String? get licenceName;

  @BuiltValueField(wireName: r'get_date')
  String? get getDate;

  @BuiltValueField(wireName: r'licence_point')
  double? get licencePoint;

  UpdateEngLicense._();

  factory UpdateEngLicense([void updates(UpdateEngLicenseBuilder b)]) = _$UpdateEngLicense;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(UpdateEngLicenseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<UpdateEngLicense> get serializer => _$UpdateEngLicenseSerializer();
}

class _$UpdateEngLicenseSerializer implements PrimitiveSerializer<UpdateEngLicense> {
  @override
  final Iterable<Type> types = const [UpdateEngLicense, _$UpdateEngLicense];

  @override
  final String wireName = r'UpdateEngLicense';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    UpdateEngLicense object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.licenceCode != null) {
      yield r'licence_code';
      yield serializers.serialize(
        object.licenceCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.licenceName != null) {
      yield r'licence_name';
      yield serializers.serialize(
        object.licenceName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.getDate != null) {
      yield r'get_date';
      yield serializers.serialize(
        object.getDate,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.licencePoint != null) {
      yield r'licence_point';
      yield serializers.serialize(
        object.licencePoint,
        specifiedType: const FullType.nullable(double),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    UpdateEngLicense object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required UpdateEngLicenseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'licence_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.licenceCode = valueDes;
          break;
        case r'licence_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.licenceName = valueDes;
          break;
        case r'get_date':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.getDate = valueDes;
          break;
        case r'licence_point':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(double),
          ) as double?;
          if (valueDes == null) continue;
          result.licencePoint = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  UpdateEngLicense deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = UpdateEngLicenseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

