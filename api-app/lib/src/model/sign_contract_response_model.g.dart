// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sign_contract_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$SignContractResponseModel extends SignContractResponseModel {
  @override
  final String? message;
  @override
  final SignContract? data;
  @override
  final BuiltList<ErrorDetail>? errors;

  factory _$SignContractResponseModel(
          [void Function(SignContractResponseModelBuilder)? updates]) =>
      (new SignContractResponseModelBuilder()..update(updates))._build();

  _$SignContractResponseModel._({this.message, this.data, this.errors})
      : super._();

  @override
  SignContractResponseModel rebuild(
          void Function(SignContractResponseModelBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SignContractResponseModelBuilder toBuilder() =>
      new SignContractResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SignContractResponseModel &&
        message == other.message &&
        data == other.data &&
        errors == other.errors;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SignContractResponseModel')
          ..add('message', message)
          ..add('data', data)
          ..add('errors', errors))
        .toString();
  }
}

class SignContractResponseModelBuilder
    implements
        Builder<SignContractResponseModel, SignContractResponseModelBuilder> {
  _$SignContractResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  SignContractBuilder? _data;
  SignContractBuilder get data => _$this._data ??= new SignContractBuilder();
  set data(SignContractBuilder? data) => _$this._data = data;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  SignContractResponseModelBuilder() {
    SignContractResponseModel._defaults(this);
  }

  SignContractResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _data = $v.data?.toBuilder();
      _errors = $v.errors?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SignContractResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$SignContractResponseModel;
  }

  @override
  void update(void Function(SignContractResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SignContractResponseModel build() => _build();

  _$SignContractResponseModel _build() {
    _$SignContractResponseModel _$result;
    try {
      _$result = _$v ??
          new _$SignContractResponseModel._(
            message: message,
            data: _data?.build(),
            errors: _errors?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
        _$failedField = 'errors';
        _errors?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'SignContractResponseModel', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
