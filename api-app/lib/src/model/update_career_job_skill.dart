//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'update_career_job_skill.g.dart';

/// UpdateCareerJobSkill
///
/// Properties:
/// * [jobCode] 
/// * [skillCode] 
/// * [yearsOfExperience] 
@BuiltValue()
abstract class UpdateCareerJobSkill implements Built<UpdateCareerJobSkill, UpdateCareerJobSkillBuilder> {
  @BuiltValueField(wireName: r'job_code')
  String? get jobCode;

  @BuiltValueField(wireName: r'skill_code')
  String? get skillCode;

  @BuiltValueField(wireName: r'years_of_experience')
  int? get yearsOfExperience;

  UpdateCareerJobSkill._();

  factory UpdateCareerJobSkill([void updates(UpdateCareerJobSkillBuilder b)]) = _$UpdateCareerJobSkill;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(UpdateCareerJobSkillBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<UpdateCareerJobSkill> get serializer => _$UpdateCareerJobSkillSerializer();
}

class _$UpdateCareerJobSkillSerializer implements PrimitiveSerializer<UpdateCareerJobSkill> {
  @override
  final Iterable<Type> types = const [UpdateCareerJobSkill, _$UpdateCareerJobSkill];

  @override
  final String wireName = r'UpdateCareerJobSkill';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    UpdateCareerJobSkill object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.jobCode != null) {
      yield r'job_code';
      yield serializers.serialize(
        object.jobCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.skillCode != null) {
      yield r'skill_code';
      yield serializers.serialize(
        object.skillCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.yearsOfExperience != null) {
      yield r'years_of_experience';
      yield serializers.serialize(
        object.yearsOfExperience,
        specifiedType: const FullType.nullable(int),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    UpdateCareerJobSkill object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required UpdateCareerJobSkillBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'job_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.jobCode = valueDes;
          break;
        case r'skill_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.skillCode = valueDes;
          break;
        case r'years_of_experience':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.yearsOfExperience = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  UpdateCareerJobSkill deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = UpdateCareerJobSkillBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

