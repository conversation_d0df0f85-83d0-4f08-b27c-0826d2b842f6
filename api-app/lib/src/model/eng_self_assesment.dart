//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'eng_self_assesment.g.dart';

/// EngSelfAssesment
///
/// Properties:
/// * [selfAssessmentId] 
/// * [remoteExpYears] 
/// * [remoteJobDescription] 
/// * [remoteSkill1] 
/// * [remoteSkill2] 
/// * [remoteSkill3] 
/// * [globalWorkExp] 
/// * [globalSkill1] 
/// * [globalSkill2] 
/// * [globalSkill3] 
/// * [socialStyle] 
/// * [communicationSkill1] 
/// * [communicationSkill2] 
/// * [communicationSkill3] 
/// * [reportSkill1] 
/// * [reportSkill2] 
/// * [reportSkill3] 
/// * [managementSkill1] 
/// * [managementSkill2] 
/// * [managementSkill3] 
/// * [durabilityScore] 
/// * [created] 
/// * [createdUser] 
/// * [updated] 
/// * [updatedUser] 
/// * [engineer] 
@BuiltValue()
abstract class EngSelfAssesment implements Built<EngSelfAssesment, EngSelfAssesmentBuilder> {
  @BuiltValueField(wireName: r'self_assessment_id')
  int? get selfAssessmentId;

  @BuiltValueField(wireName: r'remote_exp_years')
  int? get remoteExpYears;

  @BuiltValueField(wireName: r'remote_job_description')
  String? get remoteJobDescription;

  @BuiltValueField(wireName: r'remote_skill_1')
  int? get remoteSkill1;

  @BuiltValueField(wireName: r'remote_skill_2')
  int? get remoteSkill2;

  @BuiltValueField(wireName: r'remote_skill_3')
  int? get remoteSkill3;

  @BuiltValueField(wireName: r'global_work_exp')
  int? get globalWorkExp;

  @BuiltValueField(wireName: r'global_skill_1')
  int? get globalSkill1;

  @BuiltValueField(wireName: r'global_skill_2')
  int? get globalSkill2;

  @BuiltValueField(wireName: r'global_skill_3')
  int? get globalSkill3;

  @BuiltValueField(wireName: r'social_style')
  int? get socialStyle;

  @BuiltValueField(wireName: r'communication_skill_1')
  int? get communicationSkill1;

  @BuiltValueField(wireName: r'communication_skill_2')
  int? get communicationSkill2;

  @BuiltValueField(wireName: r'communication_skill_3')
  int? get communicationSkill3;

  @BuiltValueField(wireName: r'report_skill_1')
  int? get reportSkill1;

  @BuiltValueField(wireName: r'report_skill_2')
  int? get reportSkill2;

  @BuiltValueField(wireName: r'report_skill_3')
  int? get reportSkill3;

  @BuiltValueField(wireName: r'management_skill_1')
  int? get managementSkill1;

  @BuiltValueField(wireName: r'management_skill_2')
  int? get managementSkill2;

  @BuiltValueField(wireName: r'management_skill_3')
  int? get managementSkill3;

  @BuiltValueField(wireName: r'durability_score')
  int? get durabilityScore;

  @BuiltValueField(wireName: r'created')
  DateTime? get created;

  @BuiltValueField(wireName: r'created_user')
  String? get createdUser;

  @BuiltValueField(wireName: r'updated')
  DateTime? get updated;

  @BuiltValueField(wireName: r'updated_user')
  String? get updatedUser;

  @BuiltValueField(wireName: r'engineer')
  int get engineer;

  EngSelfAssesment._();

  factory EngSelfAssesment([void updates(EngSelfAssesmentBuilder b)]) = _$EngSelfAssesment;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(EngSelfAssesmentBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<EngSelfAssesment> get serializer => _$EngSelfAssesmentSerializer();
}

class _$EngSelfAssesmentSerializer implements PrimitiveSerializer<EngSelfAssesment> {
  @override
  final Iterable<Type> types = const [EngSelfAssesment, _$EngSelfAssesment];

  @override
  final String wireName = r'EngSelfAssesment';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    EngSelfAssesment object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.selfAssessmentId != null) {
      yield r'self_assessment_id';
      yield serializers.serialize(
        object.selfAssessmentId,
        specifiedType: const FullType(int),
      );
    }
    if (object.remoteExpYears != null) {
      yield r'remote_exp_years';
      yield serializers.serialize(
        object.remoteExpYears,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.remoteJobDescription != null) {
      yield r'remote_job_description';
      yield serializers.serialize(
        object.remoteJobDescription,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.remoteSkill1 != null) {
      yield r'remote_skill_1';
      yield serializers.serialize(
        object.remoteSkill1,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.remoteSkill2 != null) {
      yield r'remote_skill_2';
      yield serializers.serialize(
        object.remoteSkill2,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.remoteSkill3 != null) {
      yield r'remote_skill_3';
      yield serializers.serialize(
        object.remoteSkill3,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.globalWorkExp != null) {
      yield r'global_work_exp';
      yield serializers.serialize(
        object.globalWorkExp,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.globalSkill1 != null) {
      yield r'global_skill_1';
      yield serializers.serialize(
        object.globalSkill1,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.globalSkill2 != null) {
      yield r'global_skill_2';
      yield serializers.serialize(
        object.globalSkill2,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.globalSkill3 != null) {
      yield r'global_skill_3';
      yield serializers.serialize(
        object.globalSkill3,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.socialStyle != null) {
      yield r'social_style';
      yield serializers.serialize(
        object.socialStyle,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.communicationSkill1 != null) {
      yield r'communication_skill_1';
      yield serializers.serialize(
        object.communicationSkill1,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.communicationSkill2 != null) {
      yield r'communication_skill_2';
      yield serializers.serialize(
        object.communicationSkill2,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.communicationSkill3 != null) {
      yield r'communication_skill_3';
      yield serializers.serialize(
        object.communicationSkill3,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.reportSkill1 != null) {
      yield r'report_skill_1';
      yield serializers.serialize(
        object.reportSkill1,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.reportSkill2 != null) {
      yield r'report_skill_2';
      yield serializers.serialize(
        object.reportSkill2,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.reportSkill3 != null) {
      yield r'report_skill_3';
      yield serializers.serialize(
        object.reportSkill3,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.managementSkill1 != null) {
      yield r'management_skill_1';
      yield serializers.serialize(
        object.managementSkill1,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.managementSkill2 != null) {
      yield r'management_skill_2';
      yield serializers.serialize(
        object.managementSkill2,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.managementSkill3 != null) {
      yield r'management_skill_3';
      yield serializers.serialize(
        object.managementSkill3,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.durabilityScore != null) {
      yield r'durability_score';
      yield serializers.serialize(
        object.durabilityScore,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.created != null) {
      yield r'created';
      yield serializers.serialize(
        object.created,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.createdUser != null) {
      yield r'created_user';
      yield serializers.serialize(
        object.createdUser,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.updated != null) {
      yield r'updated';
      yield serializers.serialize(
        object.updated,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.updatedUser != null) {
      yield r'updated_user';
      yield serializers.serialize(
        object.updatedUser,
        specifiedType: const FullType.nullable(String),
      );
    }
    yield r'engineer';
    yield serializers.serialize(
      object.engineer,
      specifiedType: const FullType(int),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    EngSelfAssesment object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required EngSelfAssesmentBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'self_assessment_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.selfAssessmentId = valueDes;
          break;
        case r'remote_exp_years':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.remoteExpYears = valueDes;
          break;
        case r'remote_job_description':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.remoteJobDescription = valueDes;
          break;
        case r'remote_skill_1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.remoteSkill1 = valueDes;
          break;
        case r'remote_skill_2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.remoteSkill2 = valueDes;
          break;
        case r'remote_skill_3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.remoteSkill3 = valueDes;
          break;
        case r'global_work_exp':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.globalWorkExp = valueDes;
          break;
        case r'global_skill_1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.globalSkill1 = valueDes;
          break;
        case r'global_skill_2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.globalSkill2 = valueDes;
          break;
        case r'global_skill_3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.globalSkill3 = valueDes;
          break;
        case r'social_style':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.socialStyle = valueDes;
          break;
        case r'communication_skill_1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.communicationSkill1 = valueDes;
          break;
        case r'communication_skill_2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.communicationSkill2 = valueDes;
          break;
        case r'communication_skill_3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.communicationSkill3 = valueDes;
          break;
        case r'report_skill_1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.reportSkill1 = valueDes;
          break;
        case r'report_skill_2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.reportSkill2 = valueDes;
          break;
        case r'report_skill_3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.reportSkill3 = valueDes;
          break;
        case r'management_skill_1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.managementSkill1 = valueDes;
          break;
        case r'management_skill_2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.managementSkill2 = valueDes;
          break;
        case r'management_skill_3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.managementSkill3 = valueDes;
          break;
        case r'durability_score':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.durabilityScore = valueDes;
          break;
        case r'created':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.created = valueDes;
          break;
        case r'created_user':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.createdUser = valueDes;
          break;
        case r'updated':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.updated = valueDes;
          break;
        case r'updated_user':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.updatedUser = valueDes;
          break;
        case r'engineer':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.engineer = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  EngSelfAssesment deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = EngSelfAssesmentBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

