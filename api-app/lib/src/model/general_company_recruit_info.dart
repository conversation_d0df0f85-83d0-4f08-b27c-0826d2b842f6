//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'general_company_recruit_info.g.dart';

/// GeneralCompanyRecruitInfo
///
/// Properties:
/// * [recruitId] 
/// * [payrollPriceFrom] 
/// * [payrollPriceTo] 
/// * [payrollCode] 
/// * [saveType] 
/// * [title] 
/// * [catchCopy] 
/// * [startDate] 
/// * [endDate] 
/// * [displayFlag] 
/// * [jobCode] 
/// * [employCode] 
/// * [content] 
/// * [placeCode1] 
/// * [placeCode2] 
/// * [placeCode3] 
/// * [countryCode] 
/// * [ageFrom] 
/// * [ageTo] 
/// * [sexType] 
/// * [prefCode1] 
/// * [prefCode2] 
/// * [prefCode3] 
/// * [lastAcademicCode] 
/// * [languageCode1] 
/// * [languageLevelType1] 
/// * [languageCode2] 
/// * [languageLevelType2] 
/// * [experiencedJobCode] 
/// * [yearsOfExperience] 
/// * [skillJobCode1] 
/// * [skillCode1] 
/// * [skillLevelType1] 
/// * [skillJobCode2] 
/// * [skillCode2] 
/// * [skillLevelType2] 
/// * [skillJobCode3] 
/// * [skillCode3] 
/// * [skillLevelType3] 
/// * [createAgentId] 
/// * [created] 
/// * [updateAgentId] 
/// * [updated] 
/// * [licenceCode1] 
/// * [licenceName1] 
/// * [licencePoint1] 
/// * [licenceCode2] 
/// * [licenceName2] 
/// * [licencePoint2] 
/// * [licenceCode3] 
/// * [licenceName3] 
/// * [licencePoint3] 
/// * [recruitImagePath] 
/// * [remoteCode] 
/// * [payrollPriceFromUsd] 
/// * [payrollPriceToUsd] 
/// * [hostCompany] 
/// * [hostAgent] 
/// * [supportCompany] 
/// * [supportAgent] 
@BuiltValue()
abstract class GeneralCompanyRecruitInfo implements Built<GeneralCompanyRecruitInfo, GeneralCompanyRecruitInfoBuilder> {
  @BuiltValueField(wireName: r'recruit_id')
  int? get recruitId;

  @BuiltValueField(wireName: r'payroll_price_from')
  String? get payrollPriceFrom;

  @BuiltValueField(wireName: r'payroll_price_to')
  String? get payrollPriceTo;

  @BuiltValueField(wireName: r'payroll_code')
  String? get payrollCode;

  @BuiltValueField(wireName: r'save_type')
  int? get saveType;

  @BuiltValueField(wireName: r'title')
  String? get title;

  @BuiltValueField(wireName: r'catch_copy')
  String? get catchCopy;

  @BuiltValueField(wireName: r'start_date')
  DateTime? get startDate;

  @BuiltValueField(wireName: r'end_date')
  DateTime? get endDate;

  @BuiltValueField(wireName: r'display_flag')
  int? get displayFlag;

  @BuiltValueField(wireName: r'job_code')
  String? get jobCode;

  @BuiltValueField(wireName: r'employ_code')
  String? get employCode;

  @BuiltValueField(wireName: r'content')
  String? get content;

  @BuiltValueField(wireName: r'place_code1')
  String? get placeCode1;

  @BuiltValueField(wireName: r'place_code2')
  String? get placeCode2;

  @BuiltValueField(wireName: r'place_code3')
  String? get placeCode3;

  @BuiltValueField(wireName: r'country_code')
  String? get countryCode;

  @BuiltValueField(wireName: r'age_from')
  int? get ageFrom;

  @BuiltValueField(wireName: r'age_to')
  int? get ageTo;

  @BuiltValueField(wireName: r'sex_type')
  int? get sexType;

  @BuiltValueField(wireName: r'pref_code1')
  String? get prefCode1;

  @BuiltValueField(wireName: r'pref_code2')
  String? get prefCode2;

  @BuiltValueField(wireName: r'pref_code3')
  String? get prefCode3;

  @BuiltValueField(wireName: r'last_academic_code')
  String? get lastAcademicCode;

  @BuiltValueField(wireName: r'language_code1')
  String? get languageCode1;

  @BuiltValueField(wireName: r'language_level_type1')
  int? get languageLevelType1;

  @BuiltValueField(wireName: r'language_code2')
  String? get languageCode2;

  @BuiltValueField(wireName: r'language_level_type2')
  int? get languageLevelType2;

  @BuiltValueField(wireName: r'experienced_job_code')
  String? get experiencedJobCode;

  @BuiltValueField(wireName: r'years_of_experience')
  int? get yearsOfExperience;

  @BuiltValueField(wireName: r'skill_job_code1')
  String? get skillJobCode1;

  @BuiltValueField(wireName: r'skill_code1')
  String? get skillCode1;

  @BuiltValueField(wireName: r'skill_level_type1')
  int? get skillLevelType1;

  @BuiltValueField(wireName: r'skill_job_code2')
  String? get skillJobCode2;

  @BuiltValueField(wireName: r'skill_code2')
  String? get skillCode2;

  @BuiltValueField(wireName: r'skill_level_type2')
  int? get skillLevelType2;

  @BuiltValueField(wireName: r'skill_job_code3')
  String? get skillJobCode3;

  @BuiltValueField(wireName: r'skill_code3')
  String? get skillCode3;

  @BuiltValueField(wireName: r'skill_level_type3')
  int? get skillLevelType3;

  @BuiltValueField(wireName: r'create_agent_id')
  int? get createAgentId;

  @BuiltValueField(wireName: r'created')
  DateTime? get created;

  @BuiltValueField(wireName: r'update_agent_id')
  int? get updateAgentId;

  @BuiltValueField(wireName: r'updated')
  DateTime? get updated;

  @BuiltValueField(wireName: r'licence_code1')
  String? get licenceCode1;

  @BuiltValueField(wireName: r'licence_name1')
  String? get licenceName1;

  @BuiltValueField(wireName: r'licence_point1')
  int? get licencePoint1;

  @BuiltValueField(wireName: r'licence_code2')
  String? get licenceCode2;

  @BuiltValueField(wireName: r'licence_name2')
  String? get licenceName2;

  @BuiltValueField(wireName: r'licence_point2')
  int? get licencePoint2;

  @BuiltValueField(wireName: r'licence_code3')
  String? get licenceCode3;

  @BuiltValueField(wireName: r'licence_name3')
  String? get licenceName3;

  @BuiltValueField(wireName: r'licence_point3')
  int? get licencePoint3;

  @BuiltValueField(wireName: r'recruit_image_path')
  String? get recruitImagePath;

  @BuiltValueField(wireName: r'remote_code')
  String? get remoteCode;

  @BuiltValueField(wireName: r'payroll_price_from_usd')
  double? get payrollPriceFromUsd;

  @BuiltValueField(wireName: r'payroll_price_to_usd')
  double? get payrollPriceToUsd;

  @BuiltValueField(wireName: r'host_company')
  int get hostCompany;

  @BuiltValueField(wireName: r'host_agent')
  int get hostAgent;

  @BuiltValueField(wireName: r'support_company')
  int? get supportCompany;

  @BuiltValueField(wireName: r'support_agent')
  int? get supportAgent;

  GeneralCompanyRecruitInfo._();

  factory GeneralCompanyRecruitInfo([void updates(GeneralCompanyRecruitInfoBuilder b)]) = _$GeneralCompanyRecruitInfo;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(GeneralCompanyRecruitInfoBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<GeneralCompanyRecruitInfo> get serializer => _$GeneralCompanyRecruitInfoSerializer();
}

class _$GeneralCompanyRecruitInfoSerializer implements PrimitiveSerializer<GeneralCompanyRecruitInfo> {
  @override
  final Iterable<Type> types = const [GeneralCompanyRecruitInfo, _$GeneralCompanyRecruitInfo];

  @override
  final String wireName = r'GeneralCompanyRecruitInfo';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    GeneralCompanyRecruitInfo object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.recruitId != null) {
      yield r'recruit_id';
      yield serializers.serialize(
        object.recruitId,
        specifiedType: const FullType(int),
      );
    }
    if (object.payrollPriceFrom != null) {
      yield r'payroll_price_from';
      yield serializers.serialize(
        object.payrollPriceFrom,
        specifiedType: const FullType(String),
      );
    }
    if (object.payrollPriceTo != null) {
      yield r'payroll_price_to';
      yield serializers.serialize(
        object.payrollPriceTo,
        specifiedType: const FullType(String),
      );
    }
    if (object.payrollCode != null) {
      yield r'payroll_code';
      yield serializers.serialize(
        object.payrollCode,
        specifiedType: const FullType(String),
      );
    }
    if (object.saveType != null) {
      yield r'save_type';
      yield serializers.serialize(
        object.saveType,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.title != null) {
      yield r'title';
      yield serializers.serialize(
        object.title,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.catchCopy != null) {
      yield r'catch_copy';
      yield serializers.serialize(
        object.catchCopy,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.startDate != null) {
      yield r'start_date';
      yield serializers.serialize(
        object.startDate,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.endDate != null) {
      yield r'end_date';
      yield serializers.serialize(
        object.endDate,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.displayFlag != null) {
      yield r'display_flag';
      yield serializers.serialize(
        object.displayFlag,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.jobCode != null) {
      yield r'job_code';
      yield serializers.serialize(
        object.jobCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.employCode != null) {
      yield r'employ_code';
      yield serializers.serialize(
        object.employCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.content != null) {
      yield r'content';
      yield serializers.serialize(
        object.content,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.placeCode1 != null) {
      yield r'place_code1';
      yield serializers.serialize(
        object.placeCode1,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.placeCode2 != null) {
      yield r'place_code2';
      yield serializers.serialize(
        object.placeCode2,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.placeCode3 != null) {
      yield r'place_code3';
      yield serializers.serialize(
        object.placeCode3,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.countryCode != null) {
      yield r'country_code';
      yield serializers.serialize(
        object.countryCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.ageFrom != null) {
      yield r'age_from';
      yield serializers.serialize(
        object.ageFrom,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.ageTo != null) {
      yield r'age_to';
      yield serializers.serialize(
        object.ageTo,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.sexType != null) {
      yield r'sex_type';
      yield serializers.serialize(
        object.sexType,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.prefCode1 != null) {
      yield r'pref_code1';
      yield serializers.serialize(
        object.prefCode1,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.prefCode2 != null) {
      yield r'pref_code2';
      yield serializers.serialize(
        object.prefCode2,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.prefCode3 != null) {
      yield r'pref_code3';
      yield serializers.serialize(
        object.prefCode3,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.lastAcademicCode != null) {
      yield r'last_academic_code';
      yield serializers.serialize(
        object.lastAcademicCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.languageCode1 != null) {
      yield r'language_code1';
      yield serializers.serialize(
        object.languageCode1,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.languageLevelType1 != null) {
      yield r'language_level_type1';
      yield serializers.serialize(
        object.languageLevelType1,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.languageCode2 != null) {
      yield r'language_code2';
      yield serializers.serialize(
        object.languageCode2,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.languageLevelType2 != null) {
      yield r'language_level_type2';
      yield serializers.serialize(
        object.languageLevelType2,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.experiencedJobCode != null) {
      yield r'experienced_job_code';
      yield serializers.serialize(
        object.experiencedJobCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.yearsOfExperience != null) {
      yield r'years_of_experience';
      yield serializers.serialize(
        object.yearsOfExperience,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.skillJobCode1 != null) {
      yield r'skill_job_code1';
      yield serializers.serialize(
        object.skillJobCode1,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.skillCode1 != null) {
      yield r'skill_code1';
      yield serializers.serialize(
        object.skillCode1,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.skillLevelType1 != null) {
      yield r'skill_level_type1';
      yield serializers.serialize(
        object.skillLevelType1,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.skillJobCode2 != null) {
      yield r'skill_job_code2';
      yield serializers.serialize(
        object.skillJobCode2,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.skillCode2 != null) {
      yield r'skill_code2';
      yield serializers.serialize(
        object.skillCode2,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.skillLevelType2 != null) {
      yield r'skill_level_type2';
      yield serializers.serialize(
        object.skillLevelType2,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.skillJobCode3 != null) {
      yield r'skill_job_code3';
      yield serializers.serialize(
        object.skillJobCode3,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.skillCode3 != null) {
      yield r'skill_code3';
      yield serializers.serialize(
        object.skillCode3,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.skillLevelType3 != null) {
      yield r'skill_level_type3';
      yield serializers.serialize(
        object.skillLevelType3,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.createAgentId != null) {
      yield r'create_agent_id';
      yield serializers.serialize(
        object.createAgentId,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.created != null) {
      yield r'created';
      yield serializers.serialize(
        object.created,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.updateAgentId != null) {
      yield r'update_agent_id';
      yield serializers.serialize(
        object.updateAgentId,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.updated != null) {
      yield r'updated';
      yield serializers.serialize(
        object.updated,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.licenceCode1 != null) {
      yield r'licence_code1';
      yield serializers.serialize(
        object.licenceCode1,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.licenceName1 != null) {
      yield r'licence_name1';
      yield serializers.serialize(
        object.licenceName1,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.licencePoint1 != null) {
      yield r'licence_point1';
      yield serializers.serialize(
        object.licencePoint1,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.licenceCode2 != null) {
      yield r'licence_code2';
      yield serializers.serialize(
        object.licenceCode2,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.licenceName2 != null) {
      yield r'licence_name2';
      yield serializers.serialize(
        object.licenceName2,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.licencePoint2 != null) {
      yield r'licence_point2';
      yield serializers.serialize(
        object.licencePoint2,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.licenceCode3 != null) {
      yield r'licence_code3';
      yield serializers.serialize(
        object.licenceCode3,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.licenceName3 != null) {
      yield r'licence_name3';
      yield serializers.serialize(
        object.licenceName3,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.licencePoint3 != null) {
      yield r'licence_point3';
      yield serializers.serialize(
        object.licencePoint3,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.recruitImagePath != null) {
      yield r'recruit_image_path';
      yield serializers.serialize(
        object.recruitImagePath,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.remoteCode != null) {
      yield r'remote_code';
      yield serializers.serialize(
        object.remoteCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.payrollPriceFromUsd != null) {
      yield r'payroll_price_from_usd';
      yield serializers.serialize(
        object.payrollPriceFromUsd,
        specifiedType: const FullType.nullable(double),
      );
    }
    if (object.payrollPriceToUsd != null) {
      yield r'payroll_price_to_usd';
      yield serializers.serialize(
        object.payrollPriceToUsd,
        specifiedType: const FullType.nullable(double),
      );
    }
    yield r'host_company';
    yield serializers.serialize(
      object.hostCompany,
      specifiedType: const FullType(int),
    );
    yield r'host_agent';
    yield serializers.serialize(
      object.hostAgent,
      specifiedType: const FullType(int),
    );
    if (object.supportCompany != null) {
      yield r'support_company';
      yield serializers.serialize(
        object.supportCompany,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.supportAgent != null) {
      yield r'support_agent';
      yield serializers.serialize(
        object.supportAgent,
        specifiedType: const FullType.nullable(int),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    GeneralCompanyRecruitInfo object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required GeneralCompanyRecruitInfoBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'recruit_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.recruitId = valueDes;
          break;
        case r'payroll_price_from':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.payrollPriceFrom = valueDes;
          break;
        case r'payroll_price_to':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.payrollPriceTo = valueDes;
          break;
        case r'payroll_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.payrollCode = valueDes;
          break;
        case r'save_type':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.saveType = valueDes;
          break;
        case r'title':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.title = valueDes;
          break;
        case r'catch_copy':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.catchCopy = valueDes;
          break;
        case r'start_date':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.startDate = valueDes;
          break;
        case r'end_date':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.endDate = valueDes;
          break;
        case r'display_flag':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.displayFlag = valueDes;
          break;
        case r'job_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.jobCode = valueDes;
          break;
        case r'employ_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.employCode = valueDes;
          break;
        case r'content':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.content = valueDes;
          break;
        case r'place_code1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.placeCode1 = valueDes;
          break;
        case r'place_code2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.placeCode2 = valueDes;
          break;
        case r'place_code3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.placeCode3 = valueDes;
          break;
        case r'country_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.countryCode = valueDes;
          break;
        case r'age_from':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.ageFrom = valueDes;
          break;
        case r'age_to':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.ageTo = valueDes;
          break;
        case r'sex_type':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.sexType = valueDes;
          break;
        case r'pref_code1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.prefCode1 = valueDes;
          break;
        case r'pref_code2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.prefCode2 = valueDes;
          break;
        case r'pref_code3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.prefCode3 = valueDes;
          break;
        case r'last_academic_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.lastAcademicCode = valueDes;
          break;
        case r'language_code1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.languageCode1 = valueDes;
          break;
        case r'language_level_type1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.languageLevelType1 = valueDes;
          break;
        case r'language_code2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.languageCode2 = valueDes;
          break;
        case r'language_level_type2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.languageLevelType2 = valueDes;
          break;
        case r'experienced_job_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.experiencedJobCode = valueDes;
          break;
        case r'years_of_experience':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.yearsOfExperience = valueDes;
          break;
        case r'skill_job_code1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.skillJobCode1 = valueDes;
          break;
        case r'skill_code1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.skillCode1 = valueDes;
          break;
        case r'skill_level_type1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.skillLevelType1 = valueDes;
          break;
        case r'skill_job_code2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.skillJobCode2 = valueDes;
          break;
        case r'skill_code2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.skillCode2 = valueDes;
          break;
        case r'skill_level_type2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.skillLevelType2 = valueDes;
          break;
        case r'skill_job_code3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.skillJobCode3 = valueDes;
          break;
        case r'skill_code3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.skillCode3 = valueDes;
          break;
        case r'skill_level_type3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.skillLevelType3 = valueDes;
          break;
        case r'create_agent_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.createAgentId = valueDes;
          break;
        case r'created':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.created = valueDes;
          break;
        case r'update_agent_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.updateAgentId = valueDes;
          break;
        case r'updated':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.updated = valueDes;
          break;
        case r'licence_code1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.licenceCode1 = valueDes;
          break;
        case r'licence_name1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.licenceName1 = valueDes;
          break;
        case r'licence_point1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.licencePoint1 = valueDes;
          break;
        case r'licence_code2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.licenceCode2 = valueDes;
          break;
        case r'licence_name2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.licenceName2 = valueDes;
          break;
        case r'licence_point2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.licencePoint2 = valueDes;
          break;
        case r'licence_code3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.licenceCode3 = valueDes;
          break;
        case r'licence_name3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.licenceName3 = valueDes;
          break;
        case r'licence_point3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.licencePoint3 = valueDes;
          break;
        case r'recruit_image_path':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.recruitImagePath = valueDes;
          break;
        case r'remote_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.remoteCode = valueDes;
          break;
        case r'payroll_price_from_usd':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(double),
          ) as double?;
          if (valueDes == null) continue;
          result.payrollPriceFromUsd = valueDes;
          break;
        case r'payroll_price_to_usd':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(double),
          ) as double?;
          if (valueDes == null) continue;
          result.payrollPriceToUsd = valueDes;
          break;
        case r'host_company':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.hostCompany = valueDes;
          break;
        case r'host_agent':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.hostAgent = valueDes;
          break;
        case r'support_company':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.supportCompany = valueDes;
          break;
        case r'support_agent':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.supportAgent = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  GeneralCompanyRecruitInfo deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = GeneralCompanyRecruitInfoBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

