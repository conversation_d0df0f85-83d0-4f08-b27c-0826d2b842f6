//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'engineer_remove_agency_company.g.dart';

/// EngineerRemoveAgencyCompany
///
/// Properties:
/// * [optionalEngineerId] 
/// * [companyId] 
@BuiltValue()
abstract class EngineerRemoveAgencyCompany implements Built<EngineerRemoveAgencyCompany, EngineerRemoveAgencyCompanyBuilder> {
  @BuiltValueField(wireName: r'optional_engineer_id')
  int? get optionalEngineerId;

  @BuiltValueField(wireName: r'company_id')
  int get companyId;

  EngineerRemoveAgencyCompany._();

  factory EngineerRemoveAgencyCompany([void updates(EngineerRemoveAgencyCompanyBuilder b)]) = _$EngineerRemoveAgencyCompany;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(EngineerRemoveAgencyCompanyBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<EngineerRemoveAgencyCompany> get serializer => _$EngineerRemoveAgencyCompanySerializer();
}

class _$EngineerRemoveAgencyCompanySerializer implements PrimitiveSerializer<EngineerRemoveAgencyCompany> {
  @override
  final Iterable<Type> types = const [EngineerRemoveAgencyCompany, _$EngineerRemoveAgencyCompany];

  @override
  final String wireName = r'EngineerRemoveAgencyCompany';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    EngineerRemoveAgencyCompany object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.optionalEngineerId != null) {
      yield r'optional_engineer_id';
      yield serializers.serialize(
        object.optionalEngineerId,
        specifiedType: const FullType(int),
      );
    }
    yield r'company_id';
    yield serializers.serialize(
      object.companyId,
      specifiedType: const FullType(int),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    EngineerRemoveAgencyCompany object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required EngineerRemoveAgencyCompanyBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'optional_engineer_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.optionalEngineerId = valueDes;
          break;
        case r'company_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.companyId = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  EngineerRemoveAgencyCompany deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = EngineerRemoveAgencyCompanyBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

