// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'delete_recruitment.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$DeleteRecruitment extends DeleteRecruitment {
  @override
  final String recruitId;

  factory _$DeleteRecruitment(
          [void Function(DeleteRecruitmentBuilder)? updates]) =>
      (new DeleteRecruitmentBuilder()..update(updates))._build();

  _$DeleteRecruitment._({required this.recruitId}) : super._() {
    BuiltValueNullFieldError.checkNotNull(
        recruitId, r'DeleteRecruitment', 'recruitId');
  }

  @override
  DeleteRecruitment rebuild(void Function(DeleteRecruitmentBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  DeleteRecruitmentBuilder toBuilder() =>
      new DeleteRecruitmentBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is DeleteRecruitment && recruitId == other.recruitId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, recruitId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'DeleteRecruitment')
          ..add('recruitId', recruitId))
        .toString();
  }
}

class DeleteRecruitmentBuilder
    implements Builder<DeleteRecruitment, DeleteRecruitmentBuilder> {
  _$DeleteRecruitment? _$v;

  String? _recruitId;
  String? get recruitId => _$this._recruitId;
  set recruitId(String? recruitId) => _$this._recruitId = recruitId;

  DeleteRecruitmentBuilder() {
    DeleteRecruitment._defaults(this);
  }

  DeleteRecruitmentBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _recruitId = $v.recruitId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(DeleteRecruitment other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$DeleteRecruitment;
  }

  @override
  void update(void Function(DeleteRecruitmentBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  DeleteRecruitment build() => _build();

  _$DeleteRecruitment _build() {
    final _$result = _$v ??
        new _$DeleteRecruitment._(
          recruitId: BuiltValueNullFieldError.checkNotNull(
              recruitId, r'DeleteRecruitment', 'recruitId'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
