// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'paging_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$PagingResponseModel extends PagingResponseModel {
  @override
  final String? next;
  @override
  final String? previous;
  @override
  final BuiltList<RecruitExplore> results;

  factory _$PagingResponseModel(
          [void Function(PagingResponseModelBuilder)? updates]) =>
      (new PagingResponseModelBuilder()..update(updates))._build();

  _$PagingResponseModel._({this.next, this.previous, required this.results})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        results, r'PagingResponseModel', 'results');
  }

  @override
  PagingResponseModel rebuild(
          void Function(PagingResponseModelBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PagingResponseModelBuilder toBuilder() =>
      new PagingResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PagingResponseModel &&
        next == other.next &&
        previous == other.previous &&
        results == other.results;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, next.hashCode);
    _$hash = $jc(_$hash, previous.hashCode);
    _$hash = $jc(_$hash, results.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PagingResponseModel')
          ..add('next', next)
          ..add('previous', previous)
          ..add('results', results))
        .toString();
  }
}

class PagingResponseModelBuilder
    implements Builder<PagingResponseModel, PagingResponseModelBuilder> {
  _$PagingResponseModel? _$v;

  String? _next;
  String? get next => _$this._next;
  set next(String? next) => _$this._next = next;

  String? _previous;
  String? get previous => _$this._previous;
  set previous(String? previous) => _$this._previous = previous;

  ListBuilder<RecruitExplore>? _results;
  ListBuilder<RecruitExplore> get results =>
      _$this._results ??= new ListBuilder<RecruitExplore>();
  set results(ListBuilder<RecruitExplore>? results) =>
      _$this._results = results;

  PagingResponseModelBuilder() {
    PagingResponseModel._defaults(this);
  }

  PagingResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _next = $v.next;
      _previous = $v.previous;
      _results = $v.results.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PagingResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$PagingResponseModel;
  }

  @override
  void update(void Function(PagingResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PagingResponseModel build() => _build();

  _$PagingResponseModel _build() {
    _$PagingResponseModel _$result;
    try {
      _$result = _$v ??
          new _$PagingResponseModel._(
            next: next,
            previous: previous,
            results: results.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'results';
        results.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'PagingResponseModel', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
