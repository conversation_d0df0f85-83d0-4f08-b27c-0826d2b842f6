// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_eng_career.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UpdateEngCareer extends UpdateEngCareer {
  @override
  final String? companyName;
  @override
  final int? careerType;
  @override
  final Date? enteringDate;
  @override
  final BuiltList<UpdateCareerJobSkill> careerJobSkills;
  @override
  final String? jobDescription;
  @override
  final Date? quittingDate;
  @override
  final String? roleName;
  @override
  final String? jobCode;

  factory _$UpdateEngCareer([void Function(UpdateEngCareerBuilder)? updates]) =>
      (new UpdateEngCareerBuilder()..update(updates))._build();

  _$UpdateEngCareer._(
      {this.companyName,
      this.careerType,
      this.enteringDate,
      required this.careerJobSkills,
      this.jobDescription,
      this.quittingDate,
      this.roleName,
      this.jobCode})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        careerJobSkills, r'UpdateEngCareer', 'careerJobSkills');
  }

  @override
  UpdateEngCareer rebuild(void Function(UpdateEngCareerBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UpdateEngCareerBuilder toBuilder() =>
      new UpdateEngCareerBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UpdateEngCareer &&
        companyName == other.companyName &&
        careerType == other.careerType &&
        enteringDate == other.enteringDate &&
        careerJobSkills == other.careerJobSkills &&
        jobDescription == other.jobDescription &&
        quittingDate == other.quittingDate &&
        roleName == other.roleName &&
        jobCode == other.jobCode;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, companyName.hashCode);
    _$hash = $jc(_$hash, careerType.hashCode);
    _$hash = $jc(_$hash, enteringDate.hashCode);
    _$hash = $jc(_$hash, careerJobSkills.hashCode);
    _$hash = $jc(_$hash, jobDescription.hashCode);
    _$hash = $jc(_$hash, quittingDate.hashCode);
    _$hash = $jc(_$hash, roleName.hashCode);
    _$hash = $jc(_$hash, jobCode.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UpdateEngCareer')
          ..add('companyName', companyName)
          ..add('careerType', careerType)
          ..add('enteringDate', enteringDate)
          ..add('careerJobSkills', careerJobSkills)
          ..add('jobDescription', jobDescription)
          ..add('quittingDate', quittingDate)
          ..add('roleName', roleName)
          ..add('jobCode', jobCode))
        .toString();
  }
}

class UpdateEngCareerBuilder
    implements Builder<UpdateEngCareer, UpdateEngCareerBuilder> {
  _$UpdateEngCareer? _$v;

  String? _companyName;
  String? get companyName => _$this._companyName;
  set companyName(String? companyName) => _$this._companyName = companyName;

  int? _careerType;
  int? get careerType => _$this._careerType;
  set careerType(int? careerType) => _$this._careerType = careerType;

  Date? _enteringDate;
  Date? get enteringDate => _$this._enteringDate;
  set enteringDate(Date? enteringDate) => _$this._enteringDate = enteringDate;

  ListBuilder<UpdateCareerJobSkill>? _careerJobSkills;
  ListBuilder<UpdateCareerJobSkill> get careerJobSkills =>
      _$this._careerJobSkills ??= new ListBuilder<UpdateCareerJobSkill>();
  set careerJobSkills(ListBuilder<UpdateCareerJobSkill>? careerJobSkills) =>
      _$this._careerJobSkills = careerJobSkills;

  String? _jobDescription;
  String? get jobDescription => _$this._jobDescription;
  set jobDescription(String? jobDescription) =>
      _$this._jobDescription = jobDescription;

  Date? _quittingDate;
  Date? get quittingDate => _$this._quittingDate;
  set quittingDate(Date? quittingDate) => _$this._quittingDate = quittingDate;

  String? _roleName;
  String? get roleName => _$this._roleName;
  set roleName(String? roleName) => _$this._roleName = roleName;

  String? _jobCode;
  String? get jobCode => _$this._jobCode;
  set jobCode(String? jobCode) => _$this._jobCode = jobCode;

  UpdateEngCareerBuilder() {
    UpdateEngCareer._defaults(this);
  }

  UpdateEngCareerBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _companyName = $v.companyName;
      _careerType = $v.careerType;
      _enteringDate = $v.enteringDate;
      _careerJobSkills = $v.careerJobSkills.toBuilder();
      _jobDescription = $v.jobDescription;
      _quittingDate = $v.quittingDate;
      _roleName = $v.roleName;
      _jobCode = $v.jobCode;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UpdateEngCareer other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$UpdateEngCareer;
  }

  @override
  void update(void Function(UpdateEngCareerBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UpdateEngCareer build() => _build();

  _$UpdateEngCareer _build() {
    _$UpdateEngCareer _$result;
    try {
      _$result = _$v ??
          new _$UpdateEngCareer._(
            companyName: companyName,
            careerType: careerType,
            enteringDate: enteringDate,
            careerJobSkills: careerJobSkills.build(),
            jobDescription: jobDescription,
            quittingDate: quittingDate,
            roleName: roleName,
            jobCode: jobCode,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'careerJobSkills';
        careerJobSkills.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'UpdateEngCareer', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
