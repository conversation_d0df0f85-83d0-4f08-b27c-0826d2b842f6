// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'support_language200_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$SupportLanguage200Response extends SupportLanguage200Response {
  @override
  final BuiltList<String>? support;

  factory _$SupportLanguage200Response(
          [void Function(SupportLanguage200ResponseBuilder)? updates]) =>
      (new SupportLanguage200ResponseBuilder()..update(updates))._build();

  _$SupportLanguage200Response._({this.support}) : super._();

  @override
  SupportLanguage200Response rebuild(
          void Function(SupportLanguage200ResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SupportLanguage200ResponseBuilder toBuilder() =>
      new SupportLanguage200ResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SupportLanguage200Response && support == other.support;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, support.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SupportLanguage200Response')
          ..add('support', support))
        .toString();
  }
}

class SupportLanguage200ResponseBuilder
    implements
        Builder<SupportLanguage200Response, SupportLanguage200ResponseBuilder> {
  _$SupportLanguage200Response? _$v;

  ListBuilder<String>? _support;
  ListBuilder<String> get support =>
      _$this._support ??= new ListBuilder<String>();
  set support(ListBuilder<String>? support) => _$this._support = support;

  SupportLanguage200ResponseBuilder() {
    SupportLanguage200Response._defaults(this);
  }

  SupportLanguage200ResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _support = $v.support?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SupportLanguage200Response other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$SupportLanguage200Response;
  }

  @override
  void update(void Function(SupportLanguage200ResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SupportLanguage200Response build() => _build();

  _$SupportLanguage200Response _build() {
    _$SupportLanguage200Response _$result;
    try {
      _$result = _$v ??
          new _$SupportLanguage200Response._(
            support: _support?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'support';
        _support?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'SupportLanguage200Response', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
