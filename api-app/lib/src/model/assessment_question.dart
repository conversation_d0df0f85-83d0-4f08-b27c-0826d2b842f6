//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'assessment_question.g.dart';

/// AssessmentQuestion
///
/// Properties:
/// * [text] 
/// * [textEn] 
/// * [textVi] 
@BuiltValue()
abstract class AssessmentQuestion implements Built<AssessmentQuestion, AssessmentQuestionBuilder> {
  @BuiltValueField(wireName: r'text')
  String? get text;

  @BuiltValueField(wireName: r'text_en')
  String get textEn;

  @BuiltValueField(wireName: r'text_vi')
  String get textVi;

  AssessmentQuestion._();

  factory AssessmentQuestion([void updates(AssessmentQuestionBuilder b)]) = _$AssessmentQuestion;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AssessmentQuestionBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AssessmentQuestion> get serializer => _$AssessmentQuestionSerializer();
}

class _$AssessmentQuestionSerializer implements PrimitiveSerializer<AssessmentQuestion> {
  @override
  final Iterable<Type> types = const [AssessmentQuestion, _$AssessmentQuestion];

  @override
  final String wireName = r'AssessmentQuestion';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AssessmentQuestion object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.text != null) {
      yield r'text';
      yield serializers.serialize(
        object.text,
        specifiedType: const FullType(String),
      );
    }
    yield r'text_en';
    yield serializers.serialize(
      object.textEn,
      specifiedType: const FullType(String),
    );
    yield r'text_vi';
    yield serializers.serialize(
      object.textVi,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    AssessmentQuestion object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AssessmentQuestionBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'text':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.text = valueDes;
          break;
        case r'text_en':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.textEn = valueDes;
          break;
        case r'text_vi':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.textVi = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AssessmentQuestion deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AssessmentQuestionBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

