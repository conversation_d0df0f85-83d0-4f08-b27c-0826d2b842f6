// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'list_manage_host_company_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ListManageHostCompanyResponseModel
    extends ListManageHostCompanyResponseModel {
  @override
  final String? message;
  @override
  final ListManageHostCompany data;
  @override
  final BuiltList<ErrorDetail>? errors;

  factory _$ListManageHostCompanyResponseModel(
          [void Function(ListManageHostCompanyResponseModelBuilder)?
              updates]) =>
      (new ListManageHostCompanyResponseModelBuilder()..update(updates))
          ._build();

  _$ListManageHostCompanyResponseModel._(
      {this.message, required this.data, this.errors})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'ListManageHostCompanyResponseModel', 'data');
  }

  @override
  ListManageHostCompanyResponseModel rebuild(
          void Function(ListManageHostCompanyResponseModelBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ListManageHostCompanyResponseModelBuilder toBuilder() =>
      new ListManageHostCompanyResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ListManageHostCompanyResponseModel &&
        message == other.message &&
        data == other.data &&
        errors == other.errors;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ListManageHostCompanyResponseModel')
          ..add('message', message)
          ..add('data', data)
          ..add('errors', errors))
        .toString();
  }
}

class ListManageHostCompanyResponseModelBuilder
    implements
        Builder<ListManageHostCompanyResponseModel,
            ListManageHostCompanyResponseModelBuilder> {
  _$ListManageHostCompanyResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  ListManageHostCompanyBuilder? _data;
  ListManageHostCompanyBuilder get data =>
      _$this._data ??= new ListManageHostCompanyBuilder();
  set data(ListManageHostCompanyBuilder? data) => _$this._data = data;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  ListManageHostCompanyResponseModelBuilder() {
    ListManageHostCompanyResponseModel._defaults(this);
  }

  ListManageHostCompanyResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _data = $v.data.toBuilder();
      _errors = $v.errors?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ListManageHostCompanyResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$ListManageHostCompanyResponseModel;
  }

  @override
  void update(
      void Function(ListManageHostCompanyResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ListManageHostCompanyResponseModel build() => _build();

  _$ListManageHostCompanyResponseModel _build() {
    _$ListManageHostCompanyResponseModel _$result;
    try {
      _$result = _$v ??
          new _$ListManageHostCompanyResponseModel._(
            message: message,
            data: data.build(),
            errors: _errors?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        data.build();
        _$failedField = 'errors';
        _errors?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'ListManageHostCompanyResponseModel', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
