// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_company_request_interview_apply.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GeneralCompanyRequestInterviewApply
    extends GeneralCompanyRequestInterviewApply {
  @override
  final int applyId;
  @override
  final int? hostCompanyId;

  factory _$GeneralCompanyRequestInterviewApply(
          [void Function(GeneralCompanyRequestInterviewApplyBuilder)?
              updates]) =>
      (new GeneralCompanyRequestInterviewApplyBuilder()..update(updates))
          ._build();

  _$GeneralCompanyRequestInterviewApply._(
      {required this.applyId, this.hostCompanyId})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        applyId, r'GeneralCompanyRequestInterviewApply', 'applyId');
  }

  @override
  GeneralCompanyRequestInterviewApply rebuild(
          void Function(GeneralCompanyRequestInterviewApplyBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GeneralCompanyRequestInterviewApplyBuilder toBuilder() =>
      new GeneralCompanyRequestInterviewApplyBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GeneralCompanyRequestInterviewApply &&
        applyId == other.applyId &&
        hostCompanyId == other.hostCompanyId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, applyId.hashCode);
    _$hash = $jc(_$hash, hostCompanyId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GeneralCompanyRequestInterviewApply')
          ..add('applyId', applyId)
          ..add('hostCompanyId', hostCompanyId))
        .toString();
  }
}

class GeneralCompanyRequestInterviewApplyBuilder
    implements
        Builder<GeneralCompanyRequestInterviewApply,
            GeneralCompanyRequestInterviewApplyBuilder> {
  _$GeneralCompanyRequestInterviewApply? _$v;

  int? _applyId;
  int? get applyId => _$this._applyId;
  set applyId(int? applyId) => _$this._applyId = applyId;

  int? _hostCompanyId;
  int? get hostCompanyId => _$this._hostCompanyId;
  set hostCompanyId(int? hostCompanyId) =>
      _$this._hostCompanyId = hostCompanyId;

  GeneralCompanyRequestInterviewApplyBuilder() {
    GeneralCompanyRequestInterviewApply._defaults(this);
  }

  GeneralCompanyRequestInterviewApplyBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _applyId = $v.applyId;
      _hostCompanyId = $v.hostCompanyId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GeneralCompanyRequestInterviewApply other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GeneralCompanyRequestInterviewApply;
  }

  @override
  void update(
      void Function(GeneralCompanyRequestInterviewApplyBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GeneralCompanyRequestInterviewApply build() => _build();

  _$GeneralCompanyRequestInterviewApply _build() {
    final _$result = _$v ??
        new _$GeneralCompanyRequestInterviewApply._(
          applyId: BuiltValueNullFieldError.checkNotNull(
              applyId, r'GeneralCompanyRequestInterviewApply', 'applyId'),
          hostCompanyId: hostCompanyId,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
