// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_company_param_get_contract_details.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GeneralCompanyParamGetContractDetails
    extends GeneralCompanyParamGetContractDetails {
  @override
  final int applyId;
  @override
  final int? hostCompanyId;

  factory _$GeneralCompanyParamGetContractDetails(
          [void Function(GeneralCompanyParamGetContractDetailsBuilder)?
              updates]) =>
      (new GeneralCompanyParamGetContractDetailsBuilder()..update(updates))
          ._build();

  _$GeneralCompanyParamGetContractDetails._(
      {required this.applyId, this.hostCompanyId})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        applyId, r'GeneralCompanyParamGetContractDetails', 'applyId');
  }

  @override
  GeneralCompanyParamGetContractDetails rebuild(
          void Function(GeneralCompanyParamGetContractDetailsBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GeneralCompanyParamGetContractDetailsBuilder toBuilder() =>
      new GeneralCompanyParamGetContractDetailsBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GeneralCompanyParamGetContractDetails &&
        applyId == other.applyId &&
        hostCompanyId == other.hostCompanyId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, applyId.hashCode);
    _$hash = $jc(_$hash, hostCompanyId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'GeneralCompanyParamGetContractDetails')
          ..add('applyId', applyId)
          ..add('hostCompanyId', hostCompanyId))
        .toString();
  }
}

class GeneralCompanyParamGetContractDetailsBuilder
    implements
        Builder<GeneralCompanyParamGetContractDetails,
            GeneralCompanyParamGetContractDetailsBuilder> {
  _$GeneralCompanyParamGetContractDetails? _$v;

  int? _applyId;
  int? get applyId => _$this._applyId;
  set applyId(int? applyId) => _$this._applyId = applyId;

  int? _hostCompanyId;
  int? get hostCompanyId => _$this._hostCompanyId;
  set hostCompanyId(int? hostCompanyId) =>
      _$this._hostCompanyId = hostCompanyId;

  GeneralCompanyParamGetContractDetailsBuilder() {
    GeneralCompanyParamGetContractDetails._defaults(this);
  }

  GeneralCompanyParamGetContractDetailsBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _applyId = $v.applyId;
      _hostCompanyId = $v.hostCompanyId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GeneralCompanyParamGetContractDetails other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GeneralCompanyParamGetContractDetails;
  }

  @override
  void update(
      void Function(GeneralCompanyParamGetContractDetailsBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GeneralCompanyParamGetContractDetails build() => _build();

  _$GeneralCompanyParamGetContractDetails _build() {
    final _$result = _$v ??
        new _$GeneralCompanyParamGetContractDetails._(
          applyId: BuiltValueNullFieldError.checkNotNull(
              applyId, r'GeneralCompanyParamGetContractDetails', 'applyId'),
          hostCompanyId: hostCompanyId,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
