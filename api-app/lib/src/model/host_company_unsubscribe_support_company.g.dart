// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'host_company_unsubscribe_support_company.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$HostCompanyUnsubscribeSupportCompany
    extends HostCompanyUnsubscribeSupportCompany {
  @override
  final String supportCompanyId;

  factory _$HostCompanyUnsubscribeSupportCompany(
          [void Function(HostCompanyUnsubscribeSupportCompanyBuilder)?
              updates]) =>
      (new HostCompanyUnsubscribeSupportCompanyBuilder()..update(updates))
          ._build();

  _$HostCompanyUnsubscribeSupportCompany._({required this.supportCompanyId})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(supportCompanyId,
        r'HostCompanyUnsubscribeSupportCompany', 'supportCompanyId');
  }

  @override
  HostCompanyUnsubscribeSupportCompany rebuild(
          void Function(HostCompanyUnsubscribeSupportCompanyBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  HostCompanyUnsubscribeSupportCompanyBuilder toBuilder() =>
      new HostCompanyUnsubscribeSupportCompanyBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is HostCompanyUnsubscribeSupportCompany &&
        supportCompanyId == other.supportCompanyId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, supportCompanyId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'HostCompanyUnsubscribeSupportCompany')
          ..add('supportCompanyId', supportCompanyId))
        .toString();
  }
}

class HostCompanyUnsubscribeSupportCompanyBuilder
    implements
        Builder<HostCompanyUnsubscribeSupportCompany,
            HostCompanyUnsubscribeSupportCompanyBuilder> {
  _$HostCompanyUnsubscribeSupportCompany? _$v;

  String? _supportCompanyId;
  String? get supportCompanyId => _$this._supportCompanyId;
  set supportCompanyId(String? supportCompanyId) =>
      _$this._supportCompanyId = supportCompanyId;

  HostCompanyUnsubscribeSupportCompanyBuilder() {
    HostCompanyUnsubscribeSupportCompany._defaults(this);
  }

  HostCompanyUnsubscribeSupportCompanyBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _supportCompanyId = $v.supportCompanyId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(HostCompanyUnsubscribeSupportCompany other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$HostCompanyUnsubscribeSupportCompany;
  }

  @override
  void update(
      void Function(HostCompanyUnsubscribeSupportCompanyBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  HostCompanyUnsubscribeSupportCompany build() => _build();

  _$HostCompanyUnsubscribeSupportCompany _build() {
    final _$result = _$v ??
        new _$HostCompanyUnsubscribeSupportCompany._(
          supportCompanyId: BuiltValueNullFieldError.checkNotNull(
              supportCompanyId,
              r'HostCompanyUnsubscribeSupportCompany',
              'supportCompanyId'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
