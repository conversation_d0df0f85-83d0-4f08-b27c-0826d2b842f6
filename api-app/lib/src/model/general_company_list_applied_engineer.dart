//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/general_company_applied_engineers.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'general_company_list_applied_engineer.g.dart';

/// GeneralCompanyListAppliedEngineer
///
/// Properties:
/// * [data] 
/// * [totalEngineers] 
/// * [hostCompanyId] 
@BuiltValue()
abstract class GeneralCompanyListAppliedEngineer implements Built<GeneralCompanyListAppliedEngineer, GeneralCompanyListAppliedEngineerBuilder> {
  @BuiltValueField(wireName: r'data')
  BuiltList<GeneralCompanyAppliedEngineers> get data;

  @BuiltValueField(wireName: r'total_engineers')
  int get totalEngineers;

  @BuiltValueField(wireName: r'host_company_id')
  int? get hostCompanyId;

  GeneralCompanyListAppliedEngineer._();

  factory GeneralCompanyListAppliedEngineer([void updates(GeneralCompanyListAppliedEngineerBuilder b)]) = _$GeneralCompanyListAppliedEngineer;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(GeneralCompanyListAppliedEngineerBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<GeneralCompanyListAppliedEngineer> get serializer => _$GeneralCompanyListAppliedEngineerSerializer();
}

class _$GeneralCompanyListAppliedEngineerSerializer implements PrimitiveSerializer<GeneralCompanyListAppliedEngineer> {
  @override
  final Iterable<Type> types = const [GeneralCompanyListAppliedEngineer, _$GeneralCompanyListAppliedEngineer];

  @override
  final String wireName = r'GeneralCompanyListAppliedEngineer';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    GeneralCompanyListAppliedEngineer object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'data';
    yield serializers.serialize(
      object.data,
      specifiedType: const FullType(BuiltList, [FullType(GeneralCompanyAppliedEngineers)]),
    );
    yield r'total_engineers';
    yield serializers.serialize(
      object.totalEngineers,
      specifiedType: const FullType(int),
    );
    if (object.hostCompanyId != null) {
      yield r'host_company_id';
      yield serializers.serialize(
        object.hostCompanyId,
        specifiedType: const FullType.nullable(int),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    GeneralCompanyListAppliedEngineer object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required GeneralCompanyListAppliedEngineerBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(GeneralCompanyAppliedEngineers)]),
          ) as BuiltList<GeneralCompanyAppliedEngineers>;
          result.data.replace(valueDes);
          break;
        case r'total_engineers':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.totalEngineers = valueDes;
          break;
        case r'host_company_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.hostCompanyId = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  GeneralCompanyListAppliedEngineer deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = GeneralCompanyListAppliedEngineerBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

