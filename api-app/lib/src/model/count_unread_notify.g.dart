// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'count_unread_notify.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$CountUnreadNotify extends CountUnreadNotify {
  @override
  final int totalNotify;
  @override
  final int totalUnreadNotify;

  factory _$CountUnreadNotify(
          [void Function(CountUnreadNotifyBuilder)? updates]) =>
      (new CountUnreadNotifyBuilder()..update(updates))._build();

  _$CountUnreadNotify._(
      {required this.totalNotify, required this.totalUnreadNotify})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        totalNotify, r'CountUnreadNotify', 'totalNotify');
    BuiltValueNullFieldError.checkNotNull(
        totalUnreadNotify, r'CountUnreadNotify', 'totalUnreadNotify');
  }

  @override
  CountUnreadNotify rebuild(void Function(CountUnreadNotifyBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  CountUnreadNotifyBuilder toBuilder() =>
      new CountUnreadNotifyBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is CountUnreadNotify &&
        totalNotify == other.totalNotify &&
        totalUnreadNotify == other.totalUnreadNotify;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, totalNotify.hashCode);
    _$hash = $jc(_$hash, totalUnreadNotify.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'CountUnreadNotify')
          ..add('totalNotify', totalNotify)
          ..add('totalUnreadNotify', totalUnreadNotify))
        .toString();
  }
}

class CountUnreadNotifyBuilder
    implements Builder<CountUnreadNotify, CountUnreadNotifyBuilder> {
  _$CountUnreadNotify? _$v;

  int? _totalNotify;
  int? get totalNotify => _$this._totalNotify;
  set totalNotify(int? totalNotify) => _$this._totalNotify = totalNotify;

  int? _totalUnreadNotify;
  int? get totalUnreadNotify => _$this._totalUnreadNotify;
  set totalUnreadNotify(int? totalUnreadNotify) =>
      _$this._totalUnreadNotify = totalUnreadNotify;

  CountUnreadNotifyBuilder() {
    CountUnreadNotify._defaults(this);
  }

  CountUnreadNotifyBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _totalNotify = $v.totalNotify;
      _totalUnreadNotify = $v.totalUnreadNotify;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(CountUnreadNotify other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$CountUnreadNotify;
  }

  @override
  void update(void Function(CountUnreadNotifyBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  CountUnreadNotify build() => _build();

  _$CountUnreadNotify _build() {
    final _$result = _$v ??
        new _$CountUnreadNotify._(
          totalNotify: BuiltValueNullFieldError.checkNotNull(
              totalNotify, r'CountUnreadNotify', 'totalNotify'),
          totalUnreadNotify: BuiltValueNullFieldError.checkNotNull(
              totalUnreadNotify, r'CountUnreadNotify', 'totalUnreadNotify'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
