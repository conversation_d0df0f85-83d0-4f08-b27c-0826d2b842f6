// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_company_filter.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GeneralCompanyFilter extends GeneralCompanyFilter {
  @override
  final int? filterId;
  @override
  final String filterName;

  factory _$GeneralCompanyFilter(
          [void Function(GeneralCompanyFilterBuilder)? updates]) =>
      (new GeneralCompanyFilterBuilder()..update(updates))._build();

  _$GeneralCompanyFilter._({this.filterId, required this.filterName})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        filterName, r'GeneralCompanyFilter', 'filterName');
  }

  @override
  GeneralCompanyFilter rebuild(
          void Function(GeneralCompanyFilterBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GeneralCompanyFilterBuilder toBuilder() =>
      new GeneralCompanyFilterBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GeneralCompanyFilter &&
        filterId == other.filterId &&
        filterName == other.filterName;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, filterId.hashCode);
    _$hash = $jc(_$hash, filterName.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GeneralCompanyFilter')
          ..add('filterId', filterId)
          ..add('filterName', filterName))
        .toString();
  }
}

class GeneralCompanyFilterBuilder
    implements Builder<GeneralCompanyFilter, GeneralCompanyFilterBuilder> {
  _$GeneralCompanyFilter? _$v;

  int? _filterId;
  int? get filterId => _$this._filterId;
  set filterId(int? filterId) => _$this._filterId = filterId;

  String? _filterName;
  String? get filterName => _$this._filterName;
  set filterName(String? filterName) => _$this._filterName = filterName;

  GeneralCompanyFilterBuilder() {
    GeneralCompanyFilter._defaults(this);
  }

  GeneralCompanyFilterBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _filterId = $v.filterId;
      _filterName = $v.filterName;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GeneralCompanyFilter other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GeneralCompanyFilter;
  }

  @override
  void update(void Function(GeneralCompanyFilterBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GeneralCompanyFilter build() => _build();

  _$GeneralCompanyFilter _build() {
    final _$result = _$v ??
        new _$GeneralCompanyFilter._(
          filterId: filterId,
          filterName: BuiltValueNullFieldError.checkNotNull(
              filterName, r'GeneralCompanyFilter', 'filterName'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
