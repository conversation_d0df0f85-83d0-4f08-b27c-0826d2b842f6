// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login_with_sns_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$LoginWithSNSResponseModel extends LoginWithSNSResponseModel {
  @override
  final String? message;
  @override
  final LoginWithSNSResponseData data;
  @override
  final BuiltList<ErrorDetail>? errors;

  factory _$LoginWithSNSResponseModel(
          [void Function(LoginWithSNSResponseModelBuilder)? updates]) =>
      (new LoginWithSNSResponseModelBuilder()..update(updates))._build();

  _$LoginWithSNSResponseModel._({this.message, required this.data, this.errors})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'LoginWithSNSResponseModel', 'data');
  }

  @override
  LoginWithSNSResponseModel rebuild(
          void Function(LoginWithSNSResponseModelBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  LoginWithSNSResponseModelBuilder toBuilder() =>
      new LoginWithSNSResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is LoginWithSNSResponseModel &&
        message == other.message &&
        data == other.data &&
        errors == other.errors;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'LoginWithSNSResponseModel')
          ..add('message', message)
          ..add('data', data)
          ..add('errors', errors))
        .toString();
  }
}

class LoginWithSNSResponseModelBuilder
    implements
        Builder<LoginWithSNSResponseModel, LoginWithSNSResponseModelBuilder> {
  _$LoginWithSNSResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  LoginWithSNSResponseDataBuilder? _data;
  LoginWithSNSResponseDataBuilder get data =>
      _$this._data ??= new LoginWithSNSResponseDataBuilder();
  set data(LoginWithSNSResponseDataBuilder? data) => _$this._data = data;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  LoginWithSNSResponseModelBuilder() {
    LoginWithSNSResponseModel._defaults(this);
  }

  LoginWithSNSResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _data = $v.data.toBuilder();
      _errors = $v.errors?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(LoginWithSNSResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$LoginWithSNSResponseModel;
  }

  @override
  void update(void Function(LoginWithSNSResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  LoginWithSNSResponseModel build() => _build();

  _$LoginWithSNSResponseModel _build() {
    _$LoginWithSNSResponseModel _$result;
    try {
      _$result = _$v ??
          new _$LoginWithSNSResponseModel._(
            message: message,
            data: data.build(),
            errors: _errors?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        data.build();
        _$failedField = 'errors';
        _errors?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'LoginWithSNSResponseModel', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
