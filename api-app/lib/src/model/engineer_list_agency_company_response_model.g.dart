// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'engineer_list_agency_company_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$EngineerListAgencyCompanyResponseModel
    extends EngineerListAgencyCompanyResponseModel {
  @override
  final String? message;
  @override
  final BuiltList<ErrorDetail>? errors;
  @override
  final BuiltList<EngineerListAgencyCompany> data;

  factory _$EngineerListAgencyCompanyResponseModel(
          [void Function(EngineerListAgencyCompanyResponseModelBuilder)?
              updates]) =>
      (new EngineerListAgencyCompanyResponseModelBuilder()..update(updates))
          ._build();

  _$EngineerListAgencyCompanyResponseModel._(
      {this.message, this.errors, required this.data})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'EngineerListAgencyCompanyResponseModel', 'data');
  }

  @override
  EngineerListAgencyCompanyResponseModel rebuild(
          void Function(EngineerListAgencyCompanyResponseModelBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  EngineerListAgencyCompanyResponseModelBuilder toBuilder() =>
      new EngineerListAgencyCompanyResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is EngineerListAgencyCompanyResponseModel &&
        message == other.message &&
        errors == other.errors &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'EngineerListAgencyCompanyResponseModel')
          ..add('message', message)
          ..add('errors', errors)
          ..add('data', data))
        .toString();
  }
}

class EngineerListAgencyCompanyResponseModelBuilder
    implements
        Builder<EngineerListAgencyCompanyResponseModel,
            EngineerListAgencyCompanyResponseModelBuilder> {
  _$EngineerListAgencyCompanyResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  ListBuilder<EngineerListAgencyCompany>? _data;
  ListBuilder<EngineerListAgencyCompany> get data =>
      _$this._data ??= new ListBuilder<EngineerListAgencyCompany>();
  set data(ListBuilder<EngineerListAgencyCompany>? data) => _$this._data = data;

  EngineerListAgencyCompanyResponseModelBuilder() {
    EngineerListAgencyCompanyResponseModel._defaults(this);
  }

  EngineerListAgencyCompanyResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _errors = $v.errors?.toBuilder();
      _data = $v.data.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(EngineerListAgencyCompanyResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$EngineerListAgencyCompanyResponseModel;
  }

  @override
  void update(
      void Function(EngineerListAgencyCompanyResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  EngineerListAgencyCompanyResponseModel build() => _build();

  _$EngineerListAgencyCompanyResponseModel _build() {
    _$EngineerListAgencyCompanyResponseModel _$result;
    try {
      _$result = _$v ??
          new _$EngineerListAgencyCompanyResponseModel._(
            message: message,
            errors: _errors?.build(),
            data: data.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'errors';
        _errors?.build();
        _$failedField = 'data';
        data.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'EngineerListAgencyCompanyResponseModel',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
