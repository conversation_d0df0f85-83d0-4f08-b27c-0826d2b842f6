//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'hope_job_skill.g.dart';

/// HopeJobSkill
///
/// Properties:
/// * [jobCode] 
/// * [skillCode] 
@BuiltValue()
abstract class HopeJobSkill implements Built<HopeJobSkill, HopeJobSkillBuilder> {
  @BuiltValueField(wireName: r'job_code')
  String? get jobCode;

  @BuiltValueField(wireName: r'skill_code')
  String? get skillCode;

  HopeJobSkill._();

  factory HopeJobSkill([void updates(HopeJobSkillBuilder b)]) = _$HopeJobSkill;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(HopeJobSkillBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<HopeJobSkill> get serializer => _$HopeJobSkillSerializer();
}

class _$HopeJobSkillSerializer implements PrimitiveSerializer<HopeJobSkill> {
  @override
  final Iterable<Type> types = const [HopeJobSkill, _$HopeJobSkill];

  @override
  final String wireName = r'HopeJobSkill';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    HopeJobSkill object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.jobCode != null) {
      yield r'job_code';
      yield serializers.serialize(
        object.jobCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.skillCode != null) {
      yield r'skill_code';
      yield serializers.serialize(
        object.skillCode,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    HopeJobSkill object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required HopeJobSkillBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'job_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.jobCode = valueDes;
          break;
        case r'skill_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.skillCode = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  HopeJobSkill deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = HopeJobSkillBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

