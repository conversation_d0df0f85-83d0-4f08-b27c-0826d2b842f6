// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'eng_self_assesment.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$EngSelfAssesment extends EngSelfAssesment {
  @override
  final int? selfAssessmentId;
  @override
  final int? remoteExpYears;
  @override
  final String? remoteJobDescription;
  @override
  final int? remoteSkill1;
  @override
  final int? remoteSkill2;
  @override
  final int? remoteSkill3;
  @override
  final int? globalWorkExp;
  @override
  final int? globalSkill1;
  @override
  final int? globalSkill2;
  @override
  final int? globalSkill3;
  @override
  final int? socialStyle;
  @override
  final int? communicationSkill1;
  @override
  final int? communicationSkill2;
  @override
  final int? communicationSkill3;
  @override
  final int? reportSkill1;
  @override
  final int? reportSkill2;
  @override
  final int? reportSkill3;
  @override
  final int? managementSkill1;
  @override
  final int? managementSkill2;
  @override
  final int? managementSkill3;
  @override
  final int? durabilityScore;
  @override
  final DateTime? created;
  @override
  final String? createdUser;
  @override
  final DateTime? updated;
  @override
  final String? updatedUser;
  @override
  final int engineer;

  factory _$EngSelfAssesment(
          [void Function(EngSelfAssesmentBuilder)? updates]) =>
      (new EngSelfAssesmentBuilder()..update(updates))._build();

  _$EngSelfAssesment._(
      {this.selfAssessmentId,
      this.remoteExpYears,
      this.remoteJobDescription,
      this.remoteSkill1,
      this.remoteSkill2,
      this.remoteSkill3,
      this.globalWorkExp,
      this.globalSkill1,
      this.globalSkill2,
      this.globalSkill3,
      this.socialStyle,
      this.communicationSkill1,
      this.communicationSkill2,
      this.communicationSkill3,
      this.reportSkill1,
      this.reportSkill2,
      this.reportSkill3,
      this.managementSkill1,
      this.managementSkill2,
      this.managementSkill3,
      this.durabilityScore,
      this.created,
      this.createdUser,
      this.updated,
      this.updatedUser,
      required this.engineer})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        engineer, r'EngSelfAssesment', 'engineer');
  }

  @override
  EngSelfAssesment rebuild(void Function(EngSelfAssesmentBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  EngSelfAssesmentBuilder toBuilder() =>
      new EngSelfAssesmentBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is EngSelfAssesment &&
        selfAssessmentId == other.selfAssessmentId &&
        remoteExpYears == other.remoteExpYears &&
        remoteJobDescription == other.remoteJobDescription &&
        remoteSkill1 == other.remoteSkill1 &&
        remoteSkill2 == other.remoteSkill2 &&
        remoteSkill3 == other.remoteSkill3 &&
        globalWorkExp == other.globalWorkExp &&
        globalSkill1 == other.globalSkill1 &&
        globalSkill2 == other.globalSkill2 &&
        globalSkill3 == other.globalSkill3 &&
        socialStyle == other.socialStyle &&
        communicationSkill1 == other.communicationSkill1 &&
        communicationSkill2 == other.communicationSkill2 &&
        communicationSkill3 == other.communicationSkill3 &&
        reportSkill1 == other.reportSkill1 &&
        reportSkill2 == other.reportSkill2 &&
        reportSkill3 == other.reportSkill3 &&
        managementSkill1 == other.managementSkill1 &&
        managementSkill2 == other.managementSkill2 &&
        managementSkill3 == other.managementSkill3 &&
        durabilityScore == other.durabilityScore &&
        created == other.created &&
        createdUser == other.createdUser &&
        updated == other.updated &&
        updatedUser == other.updatedUser &&
        engineer == other.engineer;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, selfAssessmentId.hashCode);
    _$hash = $jc(_$hash, remoteExpYears.hashCode);
    _$hash = $jc(_$hash, remoteJobDescription.hashCode);
    _$hash = $jc(_$hash, remoteSkill1.hashCode);
    _$hash = $jc(_$hash, remoteSkill2.hashCode);
    _$hash = $jc(_$hash, remoteSkill3.hashCode);
    _$hash = $jc(_$hash, globalWorkExp.hashCode);
    _$hash = $jc(_$hash, globalSkill1.hashCode);
    _$hash = $jc(_$hash, globalSkill2.hashCode);
    _$hash = $jc(_$hash, globalSkill3.hashCode);
    _$hash = $jc(_$hash, socialStyle.hashCode);
    _$hash = $jc(_$hash, communicationSkill1.hashCode);
    _$hash = $jc(_$hash, communicationSkill2.hashCode);
    _$hash = $jc(_$hash, communicationSkill3.hashCode);
    _$hash = $jc(_$hash, reportSkill1.hashCode);
    _$hash = $jc(_$hash, reportSkill2.hashCode);
    _$hash = $jc(_$hash, reportSkill3.hashCode);
    _$hash = $jc(_$hash, managementSkill1.hashCode);
    _$hash = $jc(_$hash, managementSkill2.hashCode);
    _$hash = $jc(_$hash, managementSkill3.hashCode);
    _$hash = $jc(_$hash, durabilityScore.hashCode);
    _$hash = $jc(_$hash, created.hashCode);
    _$hash = $jc(_$hash, createdUser.hashCode);
    _$hash = $jc(_$hash, updated.hashCode);
    _$hash = $jc(_$hash, updatedUser.hashCode);
    _$hash = $jc(_$hash, engineer.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'EngSelfAssesment')
          ..add('selfAssessmentId', selfAssessmentId)
          ..add('remoteExpYears', remoteExpYears)
          ..add('remoteJobDescription', remoteJobDescription)
          ..add('remoteSkill1', remoteSkill1)
          ..add('remoteSkill2', remoteSkill2)
          ..add('remoteSkill3', remoteSkill3)
          ..add('globalWorkExp', globalWorkExp)
          ..add('globalSkill1', globalSkill1)
          ..add('globalSkill2', globalSkill2)
          ..add('globalSkill3', globalSkill3)
          ..add('socialStyle', socialStyle)
          ..add('communicationSkill1', communicationSkill1)
          ..add('communicationSkill2', communicationSkill2)
          ..add('communicationSkill3', communicationSkill3)
          ..add('reportSkill1', reportSkill1)
          ..add('reportSkill2', reportSkill2)
          ..add('reportSkill3', reportSkill3)
          ..add('managementSkill1', managementSkill1)
          ..add('managementSkill2', managementSkill2)
          ..add('managementSkill3', managementSkill3)
          ..add('durabilityScore', durabilityScore)
          ..add('created', created)
          ..add('createdUser', createdUser)
          ..add('updated', updated)
          ..add('updatedUser', updatedUser)
          ..add('engineer', engineer))
        .toString();
  }
}

class EngSelfAssesmentBuilder
    implements Builder<EngSelfAssesment, EngSelfAssesmentBuilder> {
  _$EngSelfAssesment? _$v;

  int? _selfAssessmentId;
  int? get selfAssessmentId => _$this._selfAssessmentId;
  set selfAssessmentId(int? selfAssessmentId) =>
      _$this._selfAssessmentId = selfAssessmentId;

  int? _remoteExpYears;
  int? get remoteExpYears => _$this._remoteExpYears;
  set remoteExpYears(int? remoteExpYears) =>
      _$this._remoteExpYears = remoteExpYears;

  String? _remoteJobDescription;
  String? get remoteJobDescription => _$this._remoteJobDescription;
  set remoteJobDescription(String? remoteJobDescription) =>
      _$this._remoteJobDescription = remoteJobDescription;

  int? _remoteSkill1;
  int? get remoteSkill1 => _$this._remoteSkill1;
  set remoteSkill1(int? remoteSkill1) => _$this._remoteSkill1 = remoteSkill1;

  int? _remoteSkill2;
  int? get remoteSkill2 => _$this._remoteSkill2;
  set remoteSkill2(int? remoteSkill2) => _$this._remoteSkill2 = remoteSkill2;

  int? _remoteSkill3;
  int? get remoteSkill3 => _$this._remoteSkill3;
  set remoteSkill3(int? remoteSkill3) => _$this._remoteSkill3 = remoteSkill3;

  int? _globalWorkExp;
  int? get globalWorkExp => _$this._globalWorkExp;
  set globalWorkExp(int? globalWorkExp) =>
      _$this._globalWorkExp = globalWorkExp;

  int? _globalSkill1;
  int? get globalSkill1 => _$this._globalSkill1;
  set globalSkill1(int? globalSkill1) => _$this._globalSkill1 = globalSkill1;

  int? _globalSkill2;
  int? get globalSkill2 => _$this._globalSkill2;
  set globalSkill2(int? globalSkill2) => _$this._globalSkill2 = globalSkill2;

  int? _globalSkill3;
  int? get globalSkill3 => _$this._globalSkill3;
  set globalSkill3(int? globalSkill3) => _$this._globalSkill3 = globalSkill3;

  int? _socialStyle;
  int? get socialStyle => _$this._socialStyle;
  set socialStyle(int? socialStyle) => _$this._socialStyle = socialStyle;

  int? _communicationSkill1;
  int? get communicationSkill1 => _$this._communicationSkill1;
  set communicationSkill1(int? communicationSkill1) =>
      _$this._communicationSkill1 = communicationSkill1;

  int? _communicationSkill2;
  int? get communicationSkill2 => _$this._communicationSkill2;
  set communicationSkill2(int? communicationSkill2) =>
      _$this._communicationSkill2 = communicationSkill2;

  int? _communicationSkill3;
  int? get communicationSkill3 => _$this._communicationSkill3;
  set communicationSkill3(int? communicationSkill3) =>
      _$this._communicationSkill3 = communicationSkill3;

  int? _reportSkill1;
  int? get reportSkill1 => _$this._reportSkill1;
  set reportSkill1(int? reportSkill1) => _$this._reportSkill1 = reportSkill1;

  int? _reportSkill2;
  int? get reportSkill2 => _$this._reportSkill2;
  set reportSkill2(int? reportSkill2) => _$this._reportSkill2 = reportSkill2;

  int? _reportSkill3;
  int? get reportSkill3 => _$this._reportSkill3;
  set reportSkill3(int? reportSkill3) => _$this._reportSkill3 = reportSkill3;

  int? _managementSkill1;
  int? get managementSkill1 => _$this._managementSkill1;
  set managementSkill1(int? managementSkill1) =>
      _$this._managementSkill1 = managementSkill1;

  int? _managementSkill2;
  int? get managementSkill2 => _$this._managementSkill2;
  set managementSkill2(int? managementSkill2) =>
      _$this._managementSkill2 = managementSkill2;

  int? _managementSkill3;
  int? get managementSkill3 => _$this._managementSkill3;
  set managementSkill3(int? managementSkill3) =>
      _$this._managementSkill3 = managementSkill3;

  int? _durabilityScore;
  int? get durabilityScore => _$this._durabilityScore;
  set durabilityScore(int? durabilityScore) =>
      _$this._durabilityScore = durabilityScore;

  DateTime? _created;
  DateTime? get created => _$this._created;
  set created(DateTime? created) => _$this._created = created;

  String? _createdUser;
  String? get createdUser => _$this._createdUser;
  set createdUser(String? createdUser) => _$this._createdUser = createdUser;

  DateTime? _updated;
  DateTime? get updated => _$this._updated;
  set updated(DateTime? updated) => _$this._updated = updated;

  String? _updatedUser;
  String? get updatedUser => _$this._updatedUser;
  set updatedUser(String? updatedUser) => _$this._updatedUser = updatedUser;

  int? _engineer;
  int? get engineer => _$this._engineer;
  set engineer(int? engineer) => _$this._engineer = engineer;

  EngSelfAssesmentBuilder() {
    EngSelfAssesment._defaults(this);
  }

  EngSelfAssesmentBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _selfAssessmentId = $v.selfAssessmentId;
      _remoteExpYears = $v.remoteExpYears;
      _remoteJobDescription = $v.remoteJobDescription;
      _remoteSkill1 = $v.remoteSkill1;
      _remoteSkill2 = $v.remoteSkill2;
      _remoteSkill3 = $v.remoteSkill3;
      _globalWorkExp = $v.globalWorkExp;
      _globalSkill1 = $v.globalSkill1;
      _globalSkill2 = $v.globalSkill2;
      _globalSkill3 = $v.globalSkill3;
      _socialStyle = $v.socialStyle;
      _communicationSkill1 = $v.communicationSkill1;
      _communicationSkill2 = $v.communicationSkill2;
      _communicationSkill3 = $v.communicationSkill3;
      _reportSkill1 = $v.reportSkill1;
      _reportSkill2 = $v.reportSkill2;
      _reportSkill3 = $v.reportSkill3;
      _managementSkill1 = $v.managementSkill1;
      _managementSkill2 = $v.managementSkill2;
      _managementSkill3 = $v.managementSkill3;
      _durabilityScore = $v.durabilityScore;
      _created = $v.created;
      _createdUser = $v.createdUser;
      _updated = $v.updated;
      _updatedUser = $v.updatedUser;
      _engineer = $v.engineer;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(EngSelfAssesment other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$EngSelfAssesment;
  }

  @override
  void update(void Function(EngSelfAssesmentBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  EngSelfAssesment build() => _build();

  _$EngSelfAssesment _build() {
    final _$result = _$v ??
        new _$EngSelfAssesment._(
          selfAssessmentId: selfAssessmentId,
          remoteExpYears: remoteExpYears,
          remoteJobDescription: remoteJobDescription,
          remoteSkill1: remoteSkill1,
          remoteSkill2: remoteSkill2,
          remoteSkill3: remoteSkill3,
          globalWorkExp: globalWorkExp,
          globalSkill1: globalSkill1,
          globalSkill2: globalSkill2,
          globalSkill3: globalSkill3,
          socialStyle: socialStyle,
          communicationSkill1: communicationSkill1,
          communicationSkill2: communicationSkill2,
          communicationSkill3: communicationSkill3,
          reportSkill1: reportSkill1,
          reportSkill2: reportSkill2,
          reportSkill3: reportSkill3,
          managementSkill1: managementSkill1,
          managementSkill2: managementSkill2,
          managementSkill3: managementSkill3,
          durabilityScore: durabilityScore,
          created: created,
          createdUser: createdUser,
          updated: updated,
          updatedUser: updatedUser,
          engineer: BuiltValueNullFieldError.checkNotNull(
              engineer, r'EngSelfAssesment', 'engineer'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
