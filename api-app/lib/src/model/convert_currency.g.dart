// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'convert_currency.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ConvertCurrency extends ConvertCurrency {
  @override
  final num amount;
  @override
  final String fromCurrency;
  @override
  final String toCurrency;

  factory _$ConvertCurrency([void Function(ConvertCurrencyBuilder)? updates]) =>
      (new ConvertCurrencyBuilder()..update(updates))._build();

  _$ConvertCurrency._(
      {required this.amount,
      required this.fromCurrency,
      required this.toCurrency})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(amount, r'ConvertCurrency', 'amount');
    BuiltValueNullFieldError.checkNotNull(
        fromCurrency, r'ConvertCurrency', 'fromCurrency');
    BuiltValueNullFieldError.checkNotNull(
        toCurrency, r'ConvertCurrency', 'toCurrency');
  }

  @override
  ConvertCurrency rebuild(void Function(ConvertCurrencyBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ConvertCurrencyBuilder toBuilder() =>
      new ConvertCurrencyBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ConvertCurrency &&
        amount == other.amount &&
        fromCurrency == other.fromCurrency &&
        toCurrency == other.toCurrency;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, fromCurrency.hashCode);
    _$hash = $jc(_$hash, toCurrency.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ConvertCurrency')
          ..add('amount', amount)
          ..add('fromCurrency', fromCurrency)
          ..add('toCurrency', toCurrency))
        .toString();
  }
}

class ConvertCurrencyBuilder
    implements Builder<ConvertCurrency, ConvertCurrencyBuilder> {
  _$ConvertCurrency? _$v;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  String? _fromCurrency;
  String? get fromCurrency => _$this._fromCurrency;
  set fromCurrency(String? fromCurrency) => _$this._fromCurrency = fromCurrency;

  String? _toCurrency;
  String? get toCurrency => _$this._toCurrency;
  set toCurrency(String? toCurrency) => _$this._toCurrency = toCurrency;

  ConvertCurrencyBuilder() {
    ConvertCurrency._defaults(this);
  }

  ConvertCurrencyBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _amount = $v.amount;
      _fromCurrency = $v.fromCurrency;
      _toCurrency = $v.toCurrency;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ConvertCurrency other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$ConvertCurrency;
  }

  @override
  void update(void Function(ConvertCurrencyBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ConvertCurrency build() => _build();

  _$ConvertCurrency _build() {
    final _$result = _$v ??
        new _$ConvertCurrency._(
          amount: BuiltValueNullFieldError.checkNotNull(
              amount, r'ConvertCurrency', 'amount'),
          fromCurrency: BuiltValueNullFieldError.checkNotNull(
              fromCurrency, r'ConvertCurrency', 'fromCurrency'),
          toCurrency: BuiltValueNullFieldError.checkNotNull(
              toCurrency, r'ConvertCurrency', 'toCurrency'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
