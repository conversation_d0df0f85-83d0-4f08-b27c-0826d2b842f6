//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'send_whatsapp_code.g.dart';

/// SendWhatsappCode
///
/// Properties:
/// * [phoneNumber] 
@BuiltValue()
abstract class SendWhatsappCode implements Built<SendWhatsappCode, SendWhatsappCodeBuilder> {
  @BuiltValueField(wireName: r'phone_number')
  String get phoneNumber;

  SendWhatsappCode._();

  factory SendWhatsappCode([void updates(SendWhatsappCodeBuilder b)]) = _$SendWhatsappCode;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(SendWhatsappCodeBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<SendWhatsappCode> get serializer => _$SendWhatsappCodeSerializer();
}

class _$SendWhatsappCodeSerializer implements PrimitiveSerializer<SendWhatsappCode> {
  @override
  final Iterable<Type> types = const [SendWhatsappCode, _$SendWhatsappCode];

  @override
  final String wireName = r'SendWhatsappCode';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    SendWhatsappCode object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'phone_number';
    yield serializers.serialize(
      object.phoneNumber,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    SendWhatsappCode object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required SendWhatsappCodeBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'phone_number':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.phoneNumber = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  SendWhatsappCode deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = SendWhatsappCodeBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

