//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'engineer_update_data_policy.g.dart';

/// EngineerUpdateDataPolicy
///
/// Properties:
/// * [isDataPolicyAccept] 
/// * [code] 
@BuiltValue()
abstract class EngineerUpdateDataPolicy implements Built<EngineerUpdateDataPolicy, EngineerUpdateDataPolicyBuilder> {
  @BuiltValueField(wireName: r'is_data_policy_accept')
  bool get isDataPolicyAccept;

  @BuiltValueField(wireName: r'code')
  String get code;

  EngineerUpdateDataPolicy._();

  factory EngineerUpdateDataPolicy([void updates(EngineerUpdateDataPolicyBuilder b)]) = _$EngineerUpdateDataPolicy;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(EngineerUpdateDataPolicyBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<EngineerUpdateDataPolicy> get serializer => _$EngineerUpdateDataPolicySerializer();
}

class _$EngineerUpdateDataPolicySerializer implements PrimitiveSerializer<EngineerUpdateDataPolicy> {
  @override
  final Iterable<Type> types = const [EngineerUpdateDataPolicy, _$EngineerUpdateDataPolicy];

  @override
  final String wireName = r'EngineerUpdateDataPolicy';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    EngineerUpdateDataPolicy object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'is_data_policy_accept';
    yield serializers.serialize(
      object.isDataPolicyAccept,
      specifiedType: const FullType(bool),
    );
    yield r'code';
    yield serializers.serialize(
      object.code,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    EngineerUpdateDataPolicy object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required EngineerUpdateDataPolicyBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'is_data_policy_accept':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(bool),
          ) as bool;
          result.isDataPolicyAccept = valueDes;
          break;
        case r'code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.code = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  EngineerUpdateDataPolicy deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = EngineerUpdateDataPolicyBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

