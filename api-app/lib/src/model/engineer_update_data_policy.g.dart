// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'engineer_update_data_policy.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$EngineerUpdateDataPolicy extends EngineerUpdateDataPolicy {
  @override
  final bool isDataPolicyAccept;
  @override
  final String code;

  factory _$EngineerUpdateDataPolicy(
          [void Function(EngineerUpdateDataPolicyBuilder)? updates]) =>
      (new EngineerUpdateDataPolicyBuilder()..update(updates))._build();

  _$EngineerUpdateDataPolicy._(
      {required this.isDataPolicyAccept, required this.code})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        isDataPolicyAccept, r'EngineerUpdateDataPolicy', 'isDataPolicyAccept');
    BuiltValueNullFieldError.checkNotNull(
        code, r'EngineerUpdateDataPolicy', 'code');
  }

  @override
  EngineerUpdateDataPolicy rebuild(
          void Function(EngineerUpdateDataPolicyBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  EngineerUpdateDataPolicyBuilder toBuilder() =>
      new EngineerUpdateDataPolicyBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is EngineerUpdateDataPolicy &&
        isDataPolicyAccept == other.isDataPolicyAccept &&
        code == other.code;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, isDataPolicyAccept.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'EngineerUpdateDataPolicy')
          ..add('isDataPolicyAccept', isDataPolicyAccept)
          ..add('code', code))
        .toString();
  }
}

class EngineerUpdateDataPolicyBuilder
    implements
        Builder<EngineerUpdateDataPolicy, EngineerUpdateDataPolicyBuilder> {
  _$EngineerUpdateDataPolicy? _$v;

  bool? _isDataPolicyAccept;
  bool? get isDataPolicyAccept => _$this._isDataPolicyAccept;
  set isDataPolicyAccept(bool? isDataPolicyAccept) =>
      _$this._isDataPolicyAccept = isDataPolicyAccept;

  String? _code;
  String? get code => _$this._code;
  set code(String? code) => _$this._code = code;

  EngineerUpdateDataPolicyBuilder() {
    EngineerUpdateDataPolicy._defaults(this);
  }

  EngineerUpdateDataPolicyBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _isDataPolicyAccept = $v.isDataPolicyAccept;
      _code = $v.code;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(EngineerUpdateDataPolicy other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$EngineerUpdateDataPolicy;
  }

  @override
  void update(void Function(EngineerUpdateDataPolicyBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  EngineerUpdateDataPolicy build() => _build();

  _$EngineerUpdateDataPolicy _build() {
    final _$result = _$v ??
        new _$EngineerUpdateDataPolicy._(
          isDataPolicyAccept: BuiltValueNullFieldError.checkNotNull(
              isDataPolicyAccept,
              r'EngineerUpdateDataPolicy',
              'isDataPolicyAccept'),
          code: BuiltValueNullFieldError.checkNotNull(
              code, r'EngineerUpdateDataPolicy', 'code'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
