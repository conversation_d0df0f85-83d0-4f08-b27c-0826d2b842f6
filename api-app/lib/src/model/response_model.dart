//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/error_detail.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'response_model.g.dart';

/// ResponseModel
///
/// Properties:
/// * [message] 
/// * [data] 
/// * [errors] 
@BuiltValue()
abstract class ResponseModel implements Built<ResponseModel, ResponseModelBuilder> {
  @BuiltValueField(wireName: r'message')
  String? get message;

  @BuiltValueField(wireName: r'data')
  BuiltMap<String, String?>? get data;

  @BuiltValueField(wireName: r'errors')
  BuiltList<ErrorDetail>? get errors;

  ResponseModel._();

  factory ResponseModel([void updates(ResponseModelBuilder b)]) = _$ResponseModel;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(ResponseModelBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<ResponseModel> get serializer => _$ResponseModelSerializer();
}

class _$ResponseModelSerializer implements PrimitiveSerializer<ResponseModel> {
  @override
  final Iterable<Type> types = const [ResponseModel, _$ResponseModel];

  @override
  final String wireName = r'ResponseModel';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    ResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'message';
    yield object.message == null ? null : serializers.serialize(
      object.message,
      specifiedType: const FullType.nullable(String),
    );
    if (object.data != null) {
      yield r'data';
      yield serializers.serialize(
        object.data,
        specifiedType: const FullType.nullable(BuiltMap, [FullType(String), FullType.nullable(String)]),
      );
    }
    yield r'errors';
    yield object.errors == null ? null : serializers.serialize(
      object.errors,
      specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    ResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required ResponseModelBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'message':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.message = valueDes;
          break;
        case r'data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(BuiltMap, [FullType(String), FullType.nullable(String)]),
          ) as BuiltMap<String, String?>?;
          if (valueDes == null) continue;
          result.data.replace(valueDes);
          break;
        case r'errors':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
          ) as BuiltList<ErrorDetail>?;
          if (valueDes == null) continue;
          result.errors.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  ResponseModel deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = ResponseModelBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

