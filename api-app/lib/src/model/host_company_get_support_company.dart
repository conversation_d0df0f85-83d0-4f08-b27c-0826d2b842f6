//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'host_company_get_support_company.g.dart';

/// HostCompanyGetSupportCompany
///
/// Properties:
/// * [companyId] 
/// * [name] 
/// * [logoImagePath] 
/// * [acceptingFee] 
/// * [supportOutsourcingFee] 
/// * [addressCode] 
/// * [countryCode] 
/// * [supportOutsourcingFeeCurrCode] 
/// * [contactMail] 
/// * [tel] 
/// * [internationalTel] 
/// * [acceptingFeeCurrCode] 
/// * [introductionPr] 
@BuiltValue()
abstract class HostCompanyGetSupportCompany implements Built<HostCompanyGetSupportCompany, HostCompanyGetSupportCompanyBuilder> {
  @BuiltValueField(wireName: r'company_id')
  int? get companyId;

  @BuiltValueField(wireName: r'name')
  String? get name;

  @BuiltValueField(wireName: r'logo_image_path')
  String? get logoImagePath;

  @BuiltValueField(wireName: r'accepting_fee')
  double? get acceptingFee;

  @BuiltValueField(wireName: r'support_outsourcing_fee')
  double? get supportOutsourcingFee;

  @BuiltValueField(wireName: r'address_code')
  String? get addressCode;

  @BuiltValueField(wireName: r'country_code')
  String? get countryCode;

  @BuiltValueField(wireName: r'support_outsourcing_fee_curr_code')
  String? get supportOutsourcingFeeCurrCode;

  @BuiltValueField(wireName: r'contact_mail')
  String? get contactMail;

  @BuiltValueField(wireName: r'tel')
  String? get tel;

  @BuiltValueField(wireName: r'international_tel')
  String? get internationalTel;

  @BuiltValueField(wireName: r'accepting_fee_curr_code')
  String? get acceptingFeeCurrCode;

  @BuiltValueField(wireName: r'introduction_pr')
  String? get introductionPr;

  HostCompanyGetSupportCompany._();

  factory HostCompanyGetSupportCompany([void updates(HostCompanyGetSupportCompanyBuilder b)]) = _$HostCompanyGetSupportCompany;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(HostCompanyGetSupportCompanyBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<HostCompanyGetSupportCompany> get serializer => _$HostCompanyGetSupportCompanySerializer();
}

class _$HostCompanyGetSupportCompanySerializer implements PrimitiveSerializer<HostCompanyGetSupportCompany> {
  @override
  final Iterable<Type> types = const [HostCompanyGetSupportCompany, _$HostCompanyGetSupportCompany];

  @override
  final String wireName = r'HostCompanyGetSupportCompany';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    HostCompanyGetSupportCompany object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.companyId != null) {
      yield r'company_id';
      yield serializers.serialize(
        object.companyId,
        specifiedType: const FullType(int),
      );
    }
    if (object.name != null) {
      yield r'name';
      yield serializers.serialize(
        object.name,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.logoImagePath != null) {
      yield r'logo_image_path';
      yield serializers.serialize(
        object.logoImagePath,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.acceptingFee != null) {
      yield r'accepting_fee';
      yield serializers.serialize(
        object.acceptingFee,
        specifiedType: const FullType.nullable(double),
      );
    }
    if (object.supportOutsourcingFee != null) {
      yield r'support_outsourcing_fee';
      yield serializers.serialize(
        object.supportOutsourcingFee,
        specifiedType: const FullType.nullable(double),
      );
    }
    if (object.addressCode != null) {
      yield r'address_code';
      yield serializers.serialize(
        object.addressCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.countryCode != null) {
      yield r'country_code';
      yield serializers.serialize(
        object.countryCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.supportOutsourcingFeeCurrCode != null) {
      yield r'support_outsourcing_fee_curr_code';
      yield serializers.serialize(
        object.supportOutsourcingFeeCurrCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.contactMail != null) {
      yield r'contact_mail';
      yield serializers.serialize(
        object.contactMail,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.tel != null) {
      yield r'tel';
      yield serializers.serialize(
        object.tel,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.internationalTel != null) {
      yield r'international_tel';
      yield serializers.serialize(
        object.internationalTel,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.acceptingFeeCurrCode != null) {
      yield r'accepting_fee_curr_code';
      yield serializers.serialize(
        object.acceptingFeeCurrCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.introductionPr != null) {
      yield r'introduction_pr';
      yield serializers.serialize(
        object.introductionPr,
        specifiedType: const FullType(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    HostCompanyGetSupportCompany object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required HostCompanyGetSupportCompanyBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'company_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.companyId = valueDes;
          break;
        case r'name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.name = valueDes;
          break;
        case r'logo_image_path':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.logoImagePath = valueDes;
          break;
        case r'accepting_fee':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(double),
          ) as double?;
          if (valueDes == null) continue;
          result.acceptingFee = valueDes;
          break;
        case r'support_outsourcing_fee':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(double),
          ) as double?;
          if (valueDes == null) continue;
          result.supportOutsourcingFee = valueDes;
          break;
        case r'address_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.addressCode = valueDes;
          break;
        case r'country_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.countryCode = valueDes;
          break;
        case r'support_outsourcing_fee_curr_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.supportOutsourcingFeeCurrCode = valueDes;
          break;
        case r'contact_mail':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.contactMail = valueDes;
          break;
        case r'tel':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.tel = valueDes;
          break;
        case r'international_tel':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.internationalTel = valueDes;
          break;
        case r'accepting_fee_curr_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.acceptingFeeCurrCode = valueDes;
          break;
        case r'introduction_pr':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.introductionPr = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  HostCompanyGetSupportCompany deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = HostCompanyGetSupportCompanyBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

