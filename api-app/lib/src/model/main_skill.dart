//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'main_skill.g.dart';

/// MainSkill
///
/// Properties:
/// * [skillCode] 
/// * [skillName] 
@BuiltValue()
abstract class MainSkill implements Built<MainSkill, MainSkillBuilder> {
  @BuiltValueField(wireName: r'skill_code')
  String get skillCode;

  @BuiltValueField(wireName: r'skill_name')
  String get skillName;

  MainSkill._();

  factory MainSkill([void updates(MainSkillBuilder b)]) = _$MainSkill;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(MainSkillBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<MainSkill> get serializer => _$MainSkillSerializer();
}

class _$MainSkillSerializer implements PrimitiveSerializer<MainSkill> {
  @override
  final Iterable<Type> types = const [MainSkill, _$MainSkill];

  @override
  final String wireName = r'MainSkill';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    MainSkill object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'skill_code';
    yield serializers.serialize(
      object.skillCode,
      specifiedType: const FullType(String),
    );
    yield r'skill_name';
    yield serializers.serialize(
      object.skillName,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    MainSkill object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required MainSkillBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'skill_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.skillCode = valueDes;
          break;
        case r'skill_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.skillName = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  MainSkill deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = MainSkillBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

