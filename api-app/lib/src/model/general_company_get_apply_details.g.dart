// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_company_get_apply_details.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GeneralCompanyGetApplyDetails extends GeneralCompanyGetApplyDetails {
  @override
  final GeneralCompanyApplyDetails apply;
  @override
  final GeneralCompanyRecruitInfo recruit;
  @override
  final UserExploreDetailsSerializers? engineer;
  @override
  final BuiltList<GeneralCompanyPRComment> prComments;
  @override
  final GeneralCompanyCompareDetails? compareDetails;
  @override
  final String? hostCompanyAddress;
  @override
  final String? hostCompanyContactEmail;
  @override
  final String? supportCompanyContactEmail;
  @override
  final String? agencyCompanyContactEmail;

  factory _$GeneralCompanyGetApplyDetails(
          [void Function(GeneralCompanyGetApplyDetailsBuilder)? updates]) =>
      (new GeneralCompanyGetApplyDetailsBuilder()..update(updates))._build();

  _$GeneralCompanyGetApplyDetails._(
      {required this.apply,
      required this.recruit,
      this.engineer,
      required this.prComments,
      this.compareDetails,
      this.hostCompanyAddress,
      this.hostCompanyContactEmail,
      this.supportCompanyContactEmail,
      this.agencyCompanyContactEmail})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        apply, r'GeneralCompanyGetApplyDetails', 'apply');
    BuiltValueNullFieldError.checkNotNull(
        recruit, r'GeneralCompanyGetApplyDetails', 'recruit');
    BuiltValueNullFieldError.checkNotNull(
        prComments, r'GeneralCompanyGetApplyDetails', 'prComments');
  }

  @override
  GeneralCompanyGetApplyDetails rebuild(
          void Function(GeneralCompanyGetApplyDetailsBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GeneralCompanyGetApplyDetailsBuilder toBuilder() =>
      new GeneralCompanyGetApplyDetailsBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GeneralCompanyGetApplyDetails &&
        apply == other.apply &&
        recruit == other.recruit &&
        engineer == other.engineer &&
        prComments == other.prComments &&
        compareDetails == other.compareDetails &&
        hostCompanyAddress == other.hostCompanyAddress &&
        hostCompanyContactEmail == other.hostCompanyContactEmail &&
        supportCompanyContactEmail == other.supportCompanyContactEmail &&
        agencyCompanyContactEmail == other.agencyCompanyContactEmail;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, apply.hashCode);
    _$hash = $jc(_$hash, recruit.hashCode);
    _$hash = $jc(_$hash, engineer.hashCode);
    _$hash = $jc(_$hash, prComments.hashCode);
    _$hash = $jc(_$hash, compareDetails.hashCode);
    _$hash = $jc(_$hash, hostCompanyAddress.hashCode);
    _$hash = $jc(_$hash, hostCompanyContactEmail.hashCode);
    _$hash = $jc(_$hash, supportCompanyContactEmail.hashCode);
    _$hash = $jc(_$hash, agencyCompanyContactEmail.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GeneralCompanyGetApplyDetails')
          ..add('apply', apply)
          ..add('recruit', recruit)
          ..add('engineer', engineer)
          ..add('prComments', prComments)
          ..add('compareDetails', compareDetails)
          ..add('hostCompanyAddress', hostCompanyAddress)
          ..add('hostCompanyContactEmail', hostCompanyContactEmail)
          ..add('supportCompanyContactEmail', supportCompanyContactEmail)
          ..add('agencyCompanyContactEmail', agencyCompanyContactEmail))
        .toString();
  }
}

class GeneralCompanyGetApplyDetailsBuilder
    implements
        Builder<GeneralCompanyGetApplyDetails,
            GeneralCompanyGetApplyDetailsBuilder> {
  _$GeneralCompanyGetApplyDetails? _$v;

  GeneralCompanyApplyDetailsBuilder? _apply;
  GeneralCompanyApplyDetailsBuilder get apply =>
      _$this._apply ??= new GeneralCompanyApplyDetailsBuilder();
  set apply(GeneralCompanyApplyDetailsBuilder? apply) => _$this._apply = apply;

  GeneralCompanyRecruitInfoBuilder? _recruit;
  GeneralCompanyRecruitInfoBuilder get recruit =>
      _$this._recruit ??= new GeneralCompanyRecruitInfoBuilder();
  set recruit(GeneralCompanyRecruitInfoBuilder? recruit) =>
      _$this._recruit = recruit;

  UserExploreDetailsSerializersBuilder? _engineer;
  UserExploreDetailsSerializersBuilder get engineer =>
      _$this._engineer ??= new UserExploreDetailsSerializersBuilder();
  set engineer(UserExploreDetailsSerializersBuilder? engineer) =>
      _$this._engineer = engineer;

  ListBuilder<GeneralCompanyPRComment>? _prComments;
  ListBuilder<GeneralCompanyPRComment> get prComments =>
      _$this._prComments ??= new ListBuilder<GeneralCompanyPRComment>();
  set prComments(ListBuilder<GeneralCompanyPRComment>? prComments) =>
      _$this._prComments = prComments;

  GeneralCompanyCompareDetailsBuilder? _compareDetails;
  GeneralCompanyCompareDetailsBuilder get compareDetails =>
      _$this._compareDetails ??= new GeneralCompanyCompareDetailsBuilder();
  set compareDetails(GeneralCompanyCompareDetailsBuilder? compareDetails) =>
      _$this._compareDetails = compareDetails;

  String? _hostCompanyAddress;
  String? get hostCompanyAddress => _$this._hostCompanyAddress;
  set hostCompanyAddress(String? hostCompanyAddress) =>
      _$this._hostCompanyAddress = hostCompanyAddress;

  String? _hostCompanyContactEmail;
  String? get hostCompanyContactEmail => _$this._hostCompanyContactEmail;
  set hostCompanyContactEmail(String? hostCompanyContactEmail) =>
      _$this._hostCompanyContactEmail = hostCompanyContactEmail;

  String? _supportCompanyContactEmail;
  String? get supportCompanyContactEmail => _$this._supportCompanyContactEmail;
  set supportCompanyContactEmail(String? supportCompanyContactEmail) =>
      _$this._supportCompanyContactEmail = supportCompanyContactEmail;

  String? _agencyCompanyContactEmail;
  String? get agencyCompanyContactEmail => _$this._agencyCompanyContactEmail;
  set agencyCompanyContactEmail(String? agencyCompanyContactEmail) =>
      _$this._agencyCompanyContactEmail = agencyCompanyContactEmail;

  GeneralCompanyGetApplyDetailsBuilder() {
    GeneralCompanyGetApplyDetails._defaults(this);
  }

  GeneralCompanyGetApplyDetailsBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _apply = $v.apply.toBuilder();
      _recruit = $v.recruit.toBuilder();
      _engineer = $v.engineer?.toBuilder();
      _prComments = $v.prComments.toBuilder();
      _compareDetails = $v.compareDetails?.toBuilder();
      _hostCompanyAddress = $v.hostCompanyAddress;
      _hostCompanyContactEmail = $v.hostCompanyContactEmail;
      _supportCompanyContactEmail = $v.supportCompanyContactEmail;
      _agencyCompanyContactEmail = $v.agencyCompanyContactEmail;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GeneralCompanyGetApplyDetails other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GeneralCompanyGetApplyDetails;
  }

  @override
  void update(void Function(GeneralCompanyGetApplyDetailsBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GeneralCompanyGetApplyDetails build() => _build();

  _$GeneralCompanyGetApplyDetails _build() {
    _$GeneralCompanyGetApplyDetails _$result;
    try {
      _$result = _$v ??
          new _$GeneralCompanyGetApplyDetails._(
            apply: apply.build(),
            recruit: recruit.build(),
            engineer: _engineer?.build(),
            prComments: prComments.build(),
            compareDetails: _compareDetails?.build(),
            hostCompanyAddress: hostCompanyAddress,
            hostCompanyContactEmail: hostCompanyContactEmail,
            supportCompanyContactEmail: supportCompanyContactEmail,
            agencyCompanyContactEmail: agencyCompanyContactEmail,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'apply';
        apply.build();
        _$failedField = 'recruit';
        recruit.build();
        _$failedField = 'engineer';
        _engineer?.build();
        _$failedField = 'prComments';
        prComments.build();
        _$failedField = 'compareDetails';
        _compareDetails?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'GeneralCompanyGetApplyDetails', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
