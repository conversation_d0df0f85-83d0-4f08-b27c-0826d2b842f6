//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'update_eng_skill.g.dart';

/// UpdateEngSkill
///
/// Properties:
/// * [skillCode] 
/// * [jobCode] 
/// * [levelType] 
/// * [tempName] 
/// * [tempCategoryId] 
@BuiltValue()
abstract class UpdateEngSkill implements Built<UpdateEngSkill, UpdateEngSkillBuilder> {
  @BuiltValueField(wireName: r'skill_code')
  String? get skillCode;

  @BuiltValueField(wireName: r'job_code')
  String? get jobCode;

  @BuiltValueField(wireName: r'level_type')
  int? get levelType;

  @BuiltValueField(wireName: r'temp_name')
  String? get tempName;

  @BuiltValueField(wireName: r'temp_category_id')
  int? get tempCategoryId;

  UpdateEngSkill._();

  factory UpdateEngSkill([void updates(UpdateEngSkillBuilder b)]) = _$UpdateEngSkill;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(UpdateEngSkillBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<UpdateEngSkill> get serializer => _$UpdateEngSkillSerializer();
}

class _$UpdateEngSkillSerializer implements PrimitiveSerializer<UpdateEngSkill> {
  @override
  final Iterable<Type> types = const [UpdateEngSkill, _$UpdateEngSkill];

  @override
  final String wireName = r'UpdateEngSkill';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    UpdateEngSkill object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.skillCode != null) {
      yield r'skill_code';
      yield serializers.serialize(
        object.skillCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.jobCode != null) {
      yield r'job_code';
      yield serializers.serialize(
        object.jobCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.levelType != null) {
      yield r'level_type';
      yield serializers.serialize(
        object.levelType,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.tempName != null) {
      yield r'temp_name';
      yield serializers.serialize(
        object.tempName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.tempCategoryId != null) {
      yield r'temp_category_id';
      yield serializers.serialize(
        object.tempCategoryId,
        specifiedType: const FullType.nullable(int),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    UpdateEngSkill object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required UpdateEngSkillBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'skill_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.skillCode = valueDes;
          break;
        case r'job_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.jobCode = valueDes;
          break;
        case r'level_type':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.levelType = valueDes;
          break;
        case r'temp_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.tempName = valueDes;
          break;
        case r'temp_category_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.tempCategoryId = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  UpdateEngSkill deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = UpdateEngSkillBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

