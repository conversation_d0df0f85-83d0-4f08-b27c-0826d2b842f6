// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$Chat extends Chat {
  @override
  final int? chatId;
  @override
  final String? text;
  @override
  final DateTime? send;
  @override
  final DateTime? created;
  @override
  final int? group;
  @override
  final int? user;

  factory _$Chat([void Function(ChatBuilder)? updates]) =>
      (new ChatBuilder()..update(updates))._build();

  _$Chat._(
      {this.chatId, this.text, this.send, this.created, this.group, this.user})
      : super._();

  @override
  Chat rebuild(void Function(ChatBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ChatBuilder toBuilder() => new ChatBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is Chat &&
        chatId == other.chatId &&
        text == other.text &&
        send == other.send &&
        created == other.created &&
        group == other.group &&
        user == other.user;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, chatId.hashCode);
    _$hash = $jc(_$hash, text.hashCode);
    _$hash = $jc(_$hash, send.hashCode);
    _$hash = $jc(_$hash, created.hashCode);
    _$hash = $jc(_$hash, group.hashCode);
    _$hash = $jc(_$hash, user.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'Chat')
          ..add('chatId', chatId)
          ..add('text', text)
          ..add('send', send)
          ..add('created', created)
          ..add('group', group)
          ..add('user', user))
        .toString();
  }
}

class ChatBuilder implements Builder<Chat, ChatBuilder> {
  _$Chat? _$v;

  int? _chatId;
  int? get chatId => _$this._chatId;
  set chatId(int? chatId) => _$this._chatId = chatId;

  String? _text;
  String? get text => _$this._text;
  set text(String? text) => _$this._text = text;

  DateTime? _send;
  DateTime? get send => _$this._send;
  set send(DateTime? send) => _$this._send = send;

  DateTime? _created;
  DateTime? get created => _$this._created;
  set created(DateTime? created) => _$this._created = created;

  int? _group;
  int? get group => _$this._group;
  set group(int? group) => _$this._group = group;

  int? _user;
  int? get user => _$this._user;
  set user(int? user) => _$this._user = user;

  ChatBuilder() {
    Chat._defaults(this);
  }

  ChatBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _chatId = $v.chatId;
      _text = $v.text;
      _send = $v.send;
      _created = $v.created;
      _group = $v.group;
      _user = $v.user;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(Chat other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$Chat;
  }

  @override
  void update(void Function(ChatBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  Chat build() => _build();

  _$Chat _build() {
    final _$result = _$v ??
        new _$Chat._(
          chatId: chatId,
          text: text,
          send: send,
          created: created,
          group: group,
          user: user,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
