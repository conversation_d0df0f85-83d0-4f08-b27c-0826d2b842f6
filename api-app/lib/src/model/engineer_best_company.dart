//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'engineer_best_company.g.dart';

/// EngineerBestCompany
///
/// Properties:
/// * [companyId] 
/// * [name] 
/// * [logoImagePath] 
@BuiltValue()
abstract class EngineerBestCompany implements Built<EngineerBestCompany, EngineerBestCompanyBuilder> {
  @BuiltValueField(wireName: r'company_id')
  int? get companyId;

  @BuiltValueField(wireName: r'name')
  String? get name;

  @BuiltValueField(wireName: r'logo_image_path')
  String? get logoImagePath;

  EngineerBestCompany._();

  factory EngineerBestCompany([void updates(EngineerBestCompanyBuilder b)]) = _$EngineerBestCompany;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(EngineerBestCompanyBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<EngineerBestCompany> get serializer => _$EngineerBestCompanySerializer();
}

class _$EngineerBestCompanySerializer implements PrimitiveSerializer<EngineerBestCompany> {
  @override
  final Iterable<Type> types = const [EngineerBestCompany, _$EngineerBestCompany];

  @override
  final String wireName = r'EngineerBestCompany';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    EngineerBestCompany object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.companyId != null) {
      yield r'company_id';
      yield serializers.serialize(
        object.companyId,
        specifiedType: const FullType(int),
      );
    }
    if (object.name != null) {
      yield r'name';
      yield serializers.serialize(
        object.name,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.logoImagePath != null) {
      yield r'logo_image_path';
      yield serializers.serialize(
        object.logoImagePath,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    EngineerBestCompany object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required EngineerBestCompanyBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'company_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.companyId = valueDes;
          break;
        case r'name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.name = valueDes;
          break;
        case r'logo_image_path':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.logoImagePath = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  EngineerBestCompany deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = EngineerBestCompanyBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

