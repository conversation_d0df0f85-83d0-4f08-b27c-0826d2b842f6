//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/error_detail.dart';
import 'package:openapi/src/model/general_company_get_apply_details.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'general_company_apply_details_response_model.g.dart';

/// GeneralCompanyApplyDetailsResponseModel
///
/// Properties:
/// * [message] 
/// * [errors] 
/// * [data] 
@BuiltValue()
abstract class GeneralCompanyApplyDetailsResponseModel implements Built<GeneralCompanyApplyDetailsResponseModel, GeneralCompanyApplyDetailsResponseModelBuilder> {
  @BuiltValueField(wireName: r'message')
  String? get message;

  @BuiltValueField(wireName: r'errors')
  BuiltList<ErrorDetail>? get errors;

  @BuiltValueField(wireName: r'data')
  GeneralCompanyGetApplyDetails get data;

  GeneralCompanyApplyDetailsResponseModel._();

  factory GeneralCompanyApplyDetailsResponseModel([void updates(GeneralCompanyApplyDetailsResponseModelBuilder b)]) = _$GeneralCompanyApplyDetailsResponseModel;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(GeneralCompanyApplyDetailsResponseModelBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<GeneralCompanyApplyDetailsResponseModel> get serializer => _$GeneralCompanyApplyDetailsResponseModelSerializer();
}

class _$GeneralCompanyApplyDetailsResponseModelSerializer implements PrimitiveSerializer<GeneralCompanyApplyDetailsResponseModel> {
  @override
  final Iterable<Type> types = const [GeneralCompanyApplyDetailsResponseModel, _$GeneralCompanyApplyDetailsResponseModel];

  @override
  final String wireName = r'GeneralCompanyApplyDetailsResponseModel';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    GeneralCompanyApplyDetailsResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'message';
    yield object.message == null ? null : serializers.serialize(
      object.message,
      specifiedType: const FullType.nullable(String),
    );
    yield r'errors';
    yield object.errors == null ? null : serializers.serialize(
      object.errors,
      specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
    );
    yield r'data';
    yield serializers.serialize(
      object.data,
      specifiedType: const FullType(GeneralCompanyGetApplyDetails),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    GeneralCompanyApplyDetailsResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required GeneralCompanyApplyDetailsResponseModelBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'message':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.message = valueDes;
          break;
        case r'errors':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
          ) as BuiltList<ErrorDetail>?;
          if (valueDes == null) continue;
          result.errors.replace(valueDes);
          break;
        case r'data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(GeneralCompanyGetApplyDetails),
          ) as GeneralCompanyGetApplyDetails;
          result.data.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  GeneralCompanyApplyDetailsResponseModel deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = GeneralCompanyApplyDetailsResponseModelBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

