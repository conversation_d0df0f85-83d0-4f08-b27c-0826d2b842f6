// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_company_request_interview_apply_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GeneralCompanyRequestInterviewApplyResponseModel
    extends GeneralCompanyRequestInterviewApplyResponseModel {
  @override
  final String? message;
  @override
  final BuiltList<ErrorDetail>? errors;
  @override
  final RequestInterviewSuccess data;

  factory _$GeneralCompanyRequestInterviewApplyResponseModel(
          [void Function(
                  GeneralCompanyRequestInterviewApplyResponseModelBuilder)?
              updates]) =>
      (new GeneralCompanyRequestInterviewApplyResponseModelBuilder()
            ..update(updates))
          ._build();

  _$GeneralCompanyRequestInterviewApplyResponseModel._(
      {this.message, this.errors, required this.data})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'GeneralCompanyRequestInterviewApplyResponseModel', 'data');
  }

  @override
  GeneralCompanyRequestInterviewApplyResponseModel rebuild(
          void Function(GeneralCompanyRequestInterviewApplyResponseModelBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GeneralCompanyRequestInterviewApplyResponseModelBuilder toBuilder() =>
      new GeneralCompanyRequestInterviewApplyResponseModelBuilder()
        ..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GeneralCompanyRequestInterviewApplyResponseModel &&
        message == other.message &&
        errors == other.errors &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'GeneralCompanyRequestInterviewApplyResponseModel')
          ..add('message', message)
          ..add('errors', errors)
          ..add('data', data))
        .toString();
  }
}

class GeneralCompanyRequestInterviewApplyResponseModelBuilder
    implements
        Builder<GeneralCompanyRequestInterviewApplyResponseModel,
            GeneralCompanyRequestInterviewApplyResponseModelBuilder> {
  _$GeneralCompanyRequestInterviewApplyResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  RequestInterviewSuccessBuilder? _data;
  RequestInterviewSuccessBuilder get data =>
      _$this._data ??= new RequestInterviewSuccessBuilder();
  set data(RequestInterviewSuccessBuilder? data) => _$this._data = data;

  GeneralCompanyRequestInterviewApplyResponseModelBuilder() {
    GeneralCompanyRequestInterviewApplyResponseModel._defaults(this);
  }

  GeneralCompanyRequestInterviewApplyResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _errors = $v.errors?.toBuilder();
      _data = $v.data.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GeneralCompanyRequestInterviewApplyResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GeneralCompanyRequestInterviewApplyResponseModel;
  }

  @override
  void update(
      void Function(GeneralCompanyRequestInterviewApplyResponseModelBuilder)?
          updates) {
    if (updates != null) updates(this);
  }

  @override
  GeneralCompanyRequestInterviewApplyResponseModel build() => _build();

  _$GeneralCompanyRequestInterviewApplyResponseModel _build() {
    _$GeneralCompanyRequestInterviewApplyResponseModel _$result;
    try {
      _$result = _$v ??
          new _$GeneralCompanyRequestInterviewApplyResponseModel._(
            message: message,
            errors: _errors?.build(),
            data: data.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'errors';
        _errors?.build();
        _$failedField = 'data';
        data.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'GeneralCompanyRequestInterviewApplyResponseModel',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
