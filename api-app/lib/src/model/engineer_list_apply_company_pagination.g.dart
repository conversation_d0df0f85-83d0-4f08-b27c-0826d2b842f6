// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'engineer_list_apply_company_pagination.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$EngineerListApplyCompanyPagination
    extends EngineerListApplyCompanyPagination {
  @override
  final String? next;
  @override
  final String? previous;
  @override
  final BuiltList<EngineerListApplyCompany> results;

  factory _$EngineerListApplyCompanyPagination(
          [void Function(EngineerListApplyCompanyPaginationBuilder)?
              updates]) =>
      (new EngineerListApplyCompanyPaginationBuilder()..update(updates))
          ._build();

  _$EngineerListApplyCompanyPagination._(
      {this.next, this.previous, required this.results})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        results, r'EngineerListApplyCompanyPagination', 'results');
  }

  @override
  EngineerListApplyCompanyPagination rebuild(
          void Function(EngineerListApplyCompanyPaginationBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  EngineerListApplyCompanyPaginationBuilder toBuilder() =>
      new EngineerListApplyCompanyPaginationBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is EngineerListApplyCompanyPagination &&
        next == other.next &&
        previous == other.previous &&
        results == other.results;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, next.hashCode);
    _$hash = $jc(_$hash, previous.hashCode);
    _$hash = $jc(_$hash, results.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'EngineerListApplyCompanyPagination')
          ..add('next', next)
          ..add('previous', previous)
          ..add('results', results))
        .toString();
  }
}

class EngineerListApplyCompanyPaginationBuilder
    implements
        Builder<EngineerListApplyCompanyPagination,
            EngineerListApplyCompanyPaginationBuilder> {
  _$EngineerListApplyCompanyPagination? _$v;

  String? _next;
  String? get next => _$this._next;
  set next(String? next) => _$this._next = next;

  String? _previous;
  String? get previous => _$this._previous;
  set previous(String? previous) => _$this._previous = previous;

  ListBuilder<EngineerListApplyCompany>? _results;
  ListBuilder<EngineerListApplyCompany> get results =>
      _$this._results ??= new ListBuilder<EngineerListApplyCompany>();
  set results(ListBuilder<EngineerListApplyCompany>? results) =>
      _$this._results = results;

  EngineerListApplyCompanyPaginationBuilder() {
    EngineerListApplyCompanyPagination._defaults(this);
  }

  EngineerListApplyCompanyPaginationBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _next = $v.next;
      _previous = $v.previous;
      _results = $v.results.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(EngineerListApplyCompanyPagination other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$EngineerListApplyCompanyPagination;
  }

  @override
  void update(
      void Function(EngineerListApplyCompanyPaginationBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  EngineerListApplyCompanyPagination build() => _build();

  _$EngineerListApplyCompanyPagination _build() {
    _$EngineerListApplyCompanyPagination _$result;
    try {
      _$result = _$v ??
          new _$EngineerListApplyCompanyPagination._(
            next: next,
            previous: previous,
            results: results.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'results';
        results.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'EngineerListApplyCompanyPagination', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
