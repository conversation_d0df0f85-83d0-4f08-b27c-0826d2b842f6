//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/eng_career_job_skill.dart';
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/date.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'eng_career.g.dart';

/// EngCareer
///
/// Properties:
/// * [careerId] 
/// * [engineerId] 
/// * [companyName] 
/// * [jobDescription] 
/// * [careerType] 
/// * [enteringDate] 
/// * [quittingDate] 
/// * [careerJobSkills] 
/// * [roleName] 
/// * [jobCode] 
@BuiltValue()
abstract class EngCareer implements Built<EngCareer, EngCareerBuilder> {
  @BuiltValueField(wireName: r'career_id')
  int? get careerId;

  @BuiltValueField(wireName: r'engineer_id')
  int get engineerId;

  @BuiltValueField(wireName: r'company_name')
  String? get companyName;

  @BuiltValueField(wireName: r'job_description')
  String? get jobDescription;

  @BuiltValueField(wireName: r'career_type')
  int? get careerType;

  @BuiltValueField(wireName: r'entering_date')
  Date? get enteringDate;

  @BuiltValueField(wireName: r'quitting_date')
  Date? get quittingDate;

  @BuiltValueField(wireName: r'career_job_skills')
  BuiltList<EngCareerJobSkill>? get careerJobSkills;

  @BuiltValueField(wireName: r'role_name')
  String? get roleName;

  @BuiltValueField(wireName: r'job_code')
  String? get jobCode;

  EngCareer._();

  factory EngCareer([void updates(EngCareerBuilder b)]) = _$EngCareer;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(EngCareerBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<EngCareer> get serializer => _$EngCareerSerializer();
}

class _$EngCareerSerializer implements PrimitiveSerializer<EngCareer> {
  @override
  final Iterable<Type> types = const [EngCareer, _$EngCareer];

  @override
  final String wireName = r'EngCareer';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    EngCareer object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.careerId != null) {
      yield r'career_id';
      yield serializers.serialize(
        object.careerId,
        specifiedType: const FullType(int),
      );
    }
    yield r'engineer_id';
    yield serializers.serialize(
      object.engineerId,
      specifiedType: const FullType(int),
    );
    if (object.companyName != null) {
      yield r'company_name';
      yield serializers.serialize(
        object.companyName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.jobDescription != null) {
      yield r'job_description';
      yield serializers.serialize(
        object.jobDescription,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.careerType != null) {
      yield r'career_type';
      yield serializers.serialize(
        object.careerType,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.enteringDate != null) {
      yield r'entering_date';
      yield serializers.serialize(
        object.enteringDate,
        specifiedType: const FullType.nullable(Date),
      );
    }
    if (object.quittingDate != null) {
      yield r'quitting_date';
      yield serializers.serialize(
        object.quittingDate,
        specifiedType: const FullType.nullable(Date),
      );
    }
    if (object.careerJobSkills != null) {
      yield r'career_job_skills';
      yield serializers.serialize(
        object.careerJobSkills,
        specifiedType: const FullType(BuiltList, [FullType(EngCareerJobSkill)]),
      );
    }
    if (object.roleName != null) {
      yield r'role_name';
      yield serializers.serialize(
        object.roleName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.jobCode != null) {
      yield r'job_code';
      yield serializers.serialize(
        object.jobCode,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    EngCareer object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required EngCareerBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'career_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.careerId = valueDes;
          break;
        case r'engineer_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.engineerId = valueDes;
          break;
        case r'company_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.companyName = valueDes;
          break;
        case r'job_description':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.jobDescription = valueDes;
          break;
        case r'career_type':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.careerType = valueDes;
          break;
        case r'entering_date':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(Date),
          ) as Date?;
          if (valueDes == null) continue;
          result.enteringDate = valueDes;
          break;
        case r'quitting_date':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(Date),
          ) as Date?;
          if (valueDes == null) continue;
          result.quittingDate = valueDes;
          break;
        case r'career_job_skills':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(EngCareerJobSkill)]),
          ) as BuiltList<EngCareerJobSkill>;
          result.careerJobSkills.replace(valueDes);
          break;
        case r'role_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.roleName = valueDes;
          break;
        case r'job_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.jobCode = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  EngCareer deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = EngCareerBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

