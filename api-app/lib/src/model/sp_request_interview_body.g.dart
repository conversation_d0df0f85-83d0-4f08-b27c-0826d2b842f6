// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sp_request_interview_body.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$SPRequestInterviewBody extends SPRequestInterviewBody {
  @override
  final int hostCompanyId;
  @override
  final int hostCompanyRecruitId;
  @override
  final int engineerId;
  @override
  final String message;

  factory _$SPRequestInterviewBody(
          [void Function(SPRequestInterviewBodyBuilder)? updates]) =>
      (new SPRequestInterviewBodyBuilder()..update(updates))._build();

  _$SPRequestInterviewBody._(
      {required this.hostCompanyId,
      required this.hostCompanyRecruitId,
      required this.engineerId,
      required this.message})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        hostCompanyId, r'SPRequestInterviewBody', 'hostCompanyId');
    BuiltValueNullFieldError.checkNotNull(hostCompanyRecruitId,
        r'SPRequestInterviewBody', 'hostCompanyRecruitId');
    BuiltValueNullFieldError.checkNotNull(
        engineerId, r'SPRequestInterviewBody', 'engineerId');
    BuiltValueNullFieldError.checkNotNull(
        message, r'SPRequestInterviewBody', 'message');
  }

  @override
  SPRequestInterviewBody rebuild(
          void Function(SPRequestInterviewBodyBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SPRequestInterviewBodyBuilder toBuilder() =>
      new SPRequestInterviewBodyBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SPRequestInterviewBody &&
        hostCompanyId == other.hostCompanyId &&
        hostCompanyRecruitId == other.hostCompanyRecruitId &&
        engineerId == other.engineerId &&
        message == other.message;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, hostCompanyId.hashCode);
    _$hash = $jc(_$hash, hostCompanyRecruitId.hashCode);
    _$hash = $jc(_$hash, engineerId.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SPRequestInterviewBody')
          ..add('hostCompanyId', hostCompanyId)
          ..add('hostCompanyRecruitId', hostCompanyRecruitId)
          ..add('engineerId', engineerId)
          ..add('message', message))
        .toString();
  }
}

class SPRequestInterviewBodyBuilder
    implements Builder<SPRequestInterviewBody, SPRequestInterviewBodyBuilder> {
  _$SPRequestInterviewBody? _$v;

  int? _hostCompanyId;
  int? get hostCompanyId => _$this._hostCompanyId;
  set hostCompanyId(int? hostCompanyId) =>
      _$this._hostCompanyId = hostCompanyId;

  int? _hostCompanyRecruitId;
  int? get hostCompanyRecruitId => _$this._hostCompanyRecruitId;
  set hostCompanyRecruitId(int? hostCompanyRecruitId) =>
      _$this._hostCompanyRecruitId = hostCompanyRecruitId;

  int? _engineerId;
  int? get engineerId => _$this._engineerId;
  set engineerId(int? engineerId) => _$this._engineerId = engineerId;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  SPRequestInterviewBodyBuilder() {
    SPRequestInterviewBody._defaults(this);
  }

  SPRequestInterviewBodyBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _hostCompanyId = $v.hostCompanyId;
      _hostCompanyRecruitId = $v.hostCompanyRecruitId;
      _engineerId = $v.engineerId;
      _message = $v.message;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SPRequestInterviewBody other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$SPRequestInterviewBody;
  }

  @override
  void update(void Function(SPRequestInterviewBodyBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SPRequestInterviewBody build() => _build();

  _$SPRequestInterviewBody _build() {
    final _$result = _$v ??
        new _$SPRequestInterviewBody._(
          hostCompanyId: BuiltValueNullFieldError.checkNotNull(
              hostCompanyId, r'SPRequestInterviewBody', 'hostCompanyId'),
          hostCompanyRecruitId: BuiltValueNullFieldError.checkNotNull(
              hostCompanyRecruitId,
              r'SPRequestInterviewBody',
              'hostCompanyRecruitId'),
          engineerId: BuiltValueNullFieldError.checkNotNull(
              engineerId, r'SPRequestInterviewBody', 'engineerId'),
          message: BuiltValueNullFieldError.checkNotNull(
              message, r'SPRequestInterviewBody', 'message'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
