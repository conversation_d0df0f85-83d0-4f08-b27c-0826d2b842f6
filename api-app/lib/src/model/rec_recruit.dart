//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/recruit_company.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'rec_recruit.g.dart';

/// RecRecruit
///
/// Properties:
/// * [title] 
/// * [recruitId] 
/// * [jobCode] 
/// * [catchCopy] 
/// * [hostCompanyId] 
/// * [hostCompany] 
@BuiltValue()
abstract class RecRecruit implements Built<RecRecruit, RecRecruitBuilder> {
  @BuiltValueField(wireName: r'title')
  String? get title;

  @BuiltValueField(wireName: r'recruit_id')
  int? get recruitId;

  @BuiltValueField(wireName: r'job_code')
  String? get jobCode;

  @BuiltValueField(wireName: r'catch_copy')
  String? get catchCopy;

  @BuiltValueField(wireName: r'host_company_id')
  int get hostCompanyId;

  @BuiltValueField(wireName: r'host_company')
  RecruitCompany get hostCompany;

  RecRecruit._();

  factory RecRecruit([void updates(RecRecruitBuilder b)]) = _$RecRecruit;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(RecRecruitBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<RecRecruit> get serializer => _$RecRecruitSerializer();
}

class _$RecRecruitSerializer implements PrimitiveSerializer<RecRecruit> {
  @override
  final Iterable<Type> types = const [RecRecruit, _$RecRecruit];

  @override
  final String wireName = r'RecRecruit';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    RecRecruit object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.title != null) {
      yield r'title';
      yield serializers.serialize(
        object.title,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.recruitId != null) {
      yield r'recruit_id';
      yield serializers.serialize(
        object.recruitId,
        specifiedType: const FullType(int),
      );
    }
    if (object.jobCode != null) {
      yield r'job_code';
      yield serializers.serialize(
        object.jobCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.catchCopy != null) {
      yield r'catch_copy';
      yield serializers.serialize(
        object.catchCopy,
        specifiedType: const FullType.nullable(String),
      );
    }
    yield r'host_company_id';
    yield serializers.serialize(
      object.hostCompanyId,
      specifiedType: const FullType(int),
    );
    yield r'host_company';
    yield serializers.serialize(
      object.hostCompany,
      specifiedType: const FullType(RecruitCompany),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    RecRecruit object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required RecRecruitBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'title':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.title = valueDes;
          break;
        case r'recruit_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.recruitId = valueDes;
          break;
        case r'job_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.jobCode = valueDes;
          break;
        case r'catch_copy':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.catchCopy = valueDes;
          break;
        case r'host_company_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.hostCompanyId = valueDes;
          break;
        case r'host_company':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(RecruitCompany),
          ) as RecruitCompany;
          result.hostCompany.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  RecRecruit deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = RecRecruitBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

