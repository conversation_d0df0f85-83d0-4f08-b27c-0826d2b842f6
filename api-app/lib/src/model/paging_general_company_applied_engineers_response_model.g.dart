// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'paging_general_company_applied_engineers_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$PagingGeneralCompanyAppliedEngineersResponseModel
    extends PagingGeneralCompanyAppliedEngineersResponseModel {
  @override
  final int count;
  @override
  final String? next;
  @override
  final String? previous;
  @override
  final BuiltList<GeneralCompanyAppliedEngineers> results;

  factory _$PagingGeneralCompanyAppliedEngineersResponseModel(
          [void Function(
                  PagingGeneralCompanyAppliedEngineersResponseModelBuilder)?
              updates]) =>
      (new PagingGeneralCompanyAppliedEngineersResponseModelBuilder()
            ..update(updates))
          ._build();

  _$PagingGeneralCompanyAppliedEngineersResponseModel._(
      {required this.count, this.next, this.previous, required this.results})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        count, r'PagingGeneralCompanyAppliedEngineersResponseModel', 'count');
    BuiltValueNullFieldError.checkNotNull(results,
        r'PagingGeneralCompanyAppliedEngineersResponseModel', 'results');
  }

  @override
  PagingGeneralCompanyAppliedEngineersResponseModel rebuild(
          void Function(
                  PagingGeneralCompanyAppliedEngineersResponseModelBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PagingGeneralCompanyAppliedEngineersResponseModelBuilder toBuilder() =>
      new PagingGeneralCompanyAppliedEngineersResponseModelBuilder()
        ..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PagingGeneralCompanyAppliedEngineersResponseModel &&
        count == other.count &&
        next == other.next &&
        previous == other.previous &&
        results == other.results;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, count.hashCode);
    _$hash = $jc(_$hash, next.hashCode);
    _$hash = $jc(_$hash, previous.hashCode);
    _$hash = $jc(_$hash, results.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'PagingGeneralCompanyAppliedEngineersResponseModel')
          ..add('count', count)
          ..add('next', next)
          ..add('previous', previous)
          ..add('results', results))
        .toString();
  }
}

class PagingGeneralCompanyAppliedEngineersResponseModelBuilder
    implements
        Builder<PagingGeneralCompanyAppliedEngineersResponseModel,
            PagingGeneralCompanyAppliedEngineersResponseModelBuilder> {
  _$PagingGeneralCompanyAppliedEngineersResponseModel? _$v;

  int? _count;
  int? get count => _$this._count;
  set count(int? count) => _$this._count = count;

  String? _next;
  String? get next => _$this._next;
  set next(String? next) => _$this._next = next;

  String? _previous;
  String? get previous => _$this._previous;
  set previous(String? previous) => _$this._previous = previous;

  ListBuilder<GeneralCompanyAppliedEngineers>? _results;
  ListBuilder<GeneralCompanyAppliedEngineers> get results =>
      _$this._results ??= new ListBuilder<GeneralCompanyAppliedEngineers>();
  set results(ListBuilder<GeneralCompanyAppliedEngineers>? results) =>
      _$this._results = results;

  PagingGeneralCompanyAppliedEngineersResponseModelBuilder() {
    PagingGeneralCompanyAppliedEngineersResponseModel._defaults(this);
  }

  PagingGeneralCompanyAppliedEngineersResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _count = $v.count;
      _next = $v.next;
      _previous = $v.previous;
      _results = $v.results.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PagingGeneralCompanyAppliedEngineersResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$PagingGeneralCompanyAppliedEngineersResponseModel;
  }

  @override
  void update(
      void Function(PagingGeneralCompanyAppliedEngineersResponseModelBuilder)?
          updates) {
    if (updates != null) updates(this);
  }

  @override
  PagingGeneralCompanyAppliedEngineersResponseModel build() => _build();

  _$PagingGeneralCompanyAppliedEngineersResponseModel _build() {
    _$PagingGeneralCompanyAppliedEngineersResponseModel _$result;
    try {
      _$result = _$v ??
          new _$PagingGeneralCompanyAppliedEngineersResponseModel._(
            count: BuiltValueNullFieldError.checkNotNull(count,
                r'PagingGeneralCompanyAppliedEngineersResponseModel', 'count'),
            next: next,
            previous: previous,
            results: results.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'results';
        results.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'PagingGeneralCompanyAppliedEngineersResponseModel',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
