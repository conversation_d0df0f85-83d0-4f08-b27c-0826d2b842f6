//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'reset_password_confirm.g.dart';

/// ResetPasswordConfirm
///
/// Properties:
/// * [token] 
@BuiltValue()
abstract class ResetPasswordConfirm implements Built<ResetPasswordConfirm, ResetPasswordConfirmBuilder> {
  @BuiltValueField(wireName: r'token')
  String get token;

  ResetPasswordConfirm._();

  factory ResetPasswordConfirm([void updates(ResetPasswordConfirmBuilder b)]) = _$ResetPasswordConfirm;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(ResetPasswordConfirmBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<ResetPasswordConfirm> get serializer => _$ResetPasswordConfirmSerializer();
}

class _$ResetPasswordConfirmSerializer implements PrimitiveSerializer<ResetPasswordConfirm> {
  @override
  final Iterable<Type> types = const [ResetPasswordConfirm, _$ResetPasswordConfirm];

  @override
  final String wireName = r'ResetPasswordConfirm';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    ResetPasswordConfirm object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'token';
    yield serializers.serialize(
      object.token,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    ResetPasswordConfirm object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required ResetPasswordConfirmBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'token':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.token = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  ResetPasswordConfirm deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = ResetPasswordConfirmBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

