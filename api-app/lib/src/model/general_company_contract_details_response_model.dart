//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/general_company_get_contract_details.dart';
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/error_detail.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'general_company_contract_details_response_model.g.dart';

/// GeneralCompanyContractDetailsResponseModel
///
/// Properties:
/// * [message] 
/// * [errors] 
/// * [data] 
@BuiltValue()
abstract class GeneralCompanyContractDetailsResponseModel implements Built<GeneralCompanyContractDetailsResponseModel, GeneralCompanyContractDetailsResponseModelBuilder> {
  @BuiltValueField(wireName: r'message')
  String? get message;

  @BuiltValueField(wireName: r'errors')
  BuiltList<ErrorDetail>? get errors;

  @BuiltValueField(wireName: r'data')
  GeneralCompanyGetContractDetails get data;

  GeneralCompanyContractDetailsResponseModel._();

  factory GeneralCompanyContractDetailsResponseModel([void updates(GeneralCompanyContractDetailsResponseModelBuilder b)]) = _$GeneralCompanyContractDetailsResponseModel;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(GeneralCompanyContractDetailsResponseModelBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<GeneralCompanyContractDetailsResponseModel> get serializer => _$GeneralCompanyContractDetailsResponseModelSerializer();
}

class _$GeneralCompanyContractDetailsResponseModelSerializer implements PrimitiveSerializer<GeneralCompanyContractDetailsResponseModel> {
  @override
  final Iterable<Type> types = const [GeneralCompanyContractDetailsResponseModel, _$GeneralCompanyContractDetailsResponseModel];

  @override
  final String wireName = r'GeneralCompanyContractDetailsResponseModel';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    GeneralCompanyContractDetailsResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'message';
    yield object.message == null ? null : serializers.serialize(
      object.message,
      specifiedType: const FullType.nullable(String),
    );
    yield r'errors';
    yield object.errors == null ? null : serializers.serialize(
      object.errors,
      specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
    );
    yield r'data';
    yield serializers.serialize(
      object.data,
      specifiedType: const FullType(GeneralCompanyGetContractDetails),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    GeneralCompanyContractDetailsResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required GeneralCompanyContractDetailsResponseModelBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'message':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.message = valueDes;
          break;
        case r'errors':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
          ) as BuiltList<ErrorDetail>?;
          if (valueDes == null) continue;
          result.errors.replace(valueDes);
          break;
        case r'data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(GeneralCompanyGetContractDetails),
          ) as GeneralCompanyGetContractDetails;
          result.data.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  GeneralCompanyContractDetailsResponseModel deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = GeneralCompanyContractDetailsResponseModelBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

