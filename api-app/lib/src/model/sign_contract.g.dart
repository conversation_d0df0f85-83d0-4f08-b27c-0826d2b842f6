// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sign_contract.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$SignContract extends SignContract {
  @override
  final int? applyId;
  @override
  final String? jobCode;
  @override
  final String? employCode;
  @override
  final String? payrollPrice;
  @override
  final String? payrollCode;
  @override
  final String? placeCode;
  @override
  final Date? joingDate;
  @override
  final RecruitCompany hostCompany;
  @override
  final DateTime? interviewDatetime;
  @override
  final String? recruitId;
  @override
  final int? recruitProgressCode;

  factory _$SignContract([void Function(SignContractBuilder)? updates]) =>
      (new SignContractBuilder()..update(updates))._build();

  _$SignContract._(
      {this.applyId,
      this.jobCode,
      this.employCode,
      this.payrollPrice,
      this.payrollCode,
      this.placeCode,
      this.joingDate,
      required this.hostCompany,
      this.interviewDatetime,
      this.recruitId,
      this.recruitProgressCode})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        hostCompany, r'SignContract', 'hostCompany');
  }

  @override
  SignContract rebuild(void Function(SignContractBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SignContractBuilder toBuilder() => new SignContractBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SignContract &&
        applyId == other.applyId &&
        jobCode == other.jobCode &&
        employCode == other.employCode &&
        payrollPrice == other.payrollPrice &&
        payrollCode == other.payrollCode &&
        placeCode == other.placeCode &&
        joingDate == other.joingDate &&
        hostCompany == other.hostCompany &&
        interviewDatetime == other.interviewDatetime &&
        recruitId == other.recruitId &&
        recruitProgressCode == other.recruitProgressCode;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, applyId.hashCode);
    _$hash = $jc(_$hash, jobCode.hashCode);
    _$hash = $jc(_$hash, employCode.hashCode);
    _$hash = $jc(_$hash, payrollPrice.hashCode);
    _$hash = $jc(_$hash, payrollCode.hashCode);
    _$hash = $jc(_$hash, placeCode.hashCode);
    _$hash = $jc(_$hash, joingDate.hashCode);
    _$hash = $jc(_$hash, hostCompany.hashCode);
    _$hash = $jc(_$hash, interviewDatetime.hashCode);
    _$hash = $jc(_$hash, recruitId.hashCode);
    _$hash = $jc(_$hash, recruitProgressCode.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SignContract')
          ..add('applyId', applyId)
          ..add('jobCode', jobCode)
          ..add('employCode', employCode)
          ..add('payrollPrice', payrollPrice)
          ..add('payrollCode', payrollCode)
          ..add('placeCode', placeCode)
          ..add('joingDate', joingDate)
          ..add('hostCompany', hostCompany)
          ..add('interviewDatetime', interviewDatetime)
          ..add('recruitId', recruitId)
          ..add('recruitProgressCode', recruitProgressCode))
        .toString();
  }
}

class SignContractBuilder
    implements Builder<SignContract, SignContractBuilder> {
  _$SignContract? _$v;

  int? _applyId;
  int? get applyId => _$this._applyId;
  set applyId(int? applyId) => _$this._applyId = applyId;

  String? _jobCode;
  String? get jobCode => _$this._jobCode;
  set jobCode(String? jobCode) => _$this._jobCode = jobCode;

  String? _employCode;
  String? get employCode => _$this._employCode;
  set employCode(String? employCode) => _$this._employCode = employCode;

  String? _payrollPrice;
  String? get payrollPrice => _$this._payrollPrice;
  set payrollPrice(String? payrollPrice) => _$this._payrollPrice = payrollPrice;

  String? _payrollCode;
  String? get payrollCode => _$this._payrollCode;
  set payrollCode(String? payrollCode) => _$this._payrollCode = payrollCode;

  String? _placeCode;
  String? get placeCode => _$this._placeCode;
  set placeCode(String? placeCode) => _$this._placeCode = placeCode;

  Date? _joingDate;
  Date? get joingDate => _$this._joingDate;
  set joingDate(Date? joingDate) => _$this._joingDate = joingDate;

  RecruitCompanyBuilder? _hostCompany;
  RecruitCompanyBuilder get hostCompany =>
      _$this._hostCompany ??= new RecruitCompanyBuilder();
  set hostCompany(RecruitCompanyBuilder? hostCompany) =>
      _$this._hostCompany = hostCompany;

  DateTime? _interviewDatetime;
  DateTime? get interviewDatetime => _$this._interviewDatetime;
  set interviewDatetime(DateTime? interviewDatetime) =>
      _$this._interviewDatetime = interviewDatetime;

  String? _recruitId;
  String? get recruitId => _$this._recruitId;
  set recruitId(String? recruitId) => _$this._recruitId = recruitId;

  int? _recruitProgressCode;
  int? get recruitProgressCode => _$this._recruitProgressCode;
  set recruitProgressCode(int? recruitProgressCode) =>
      _$this._recruitProgressCode = recruitProgressCode;

  SignContractBuilder() {
    SignContract._defaults(this);
  }

  SignContractBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _applyId = $v.applyId;
      _jobCode = $v.jobCode;
      _employCode = $v.employCode;
      _payrollPrice = $v.payrollPrice;
      _payrollCode = $v.payrollCode;
      _placeCode = $v.placeCode;
      _joingDate = $v.joingDate;
      _hostCompany = $v.hostCompany.toBuilder();
      _interviewDatetime = $v.interviewDatetime;
      _recruitId = $v.recruitId;
      _recruitProgressCode = $v.recruitProgressCode;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SignContract other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$SignContract;
  }

  @override
  void update(void Function(SignContractBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SignContract build() => _build();

  _$SignContract _build() {
    _$SignContract _$result;
    try {
      _$result = _$v ??
          new _$SignContract._(
            applyId: applyId,
            jobCode: jobCode,
            employCode: employCode,
            payrollPrice: payrollPrice,
            payrollCode: payrollCode,
            placeCode: placeCode,
            joingDate: joingDate,
            hostCompany: hostCompany.build(),
            interviewDatetime: interviewDatetime,
            recruitId: recruitId,
            recruitProgressCode: recruitProgressCode,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'hostCompany';
        hostCompany.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'SignContract', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
