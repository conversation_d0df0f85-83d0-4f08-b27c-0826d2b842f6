//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/general_company_compare_details.dart';
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/general_company_apply_details.dart';
import 'package:openapi/src/model/general_company_pr_comment.dart';
import 'package:openapi/src/model/general_company_recruit_info.dart';
import 'package:openapi/src/model/user_explore_details_serializers.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'general_company_get_apply_details.g.dart';

/// GeneralCompanyGetApplyDetails
///
/// Properties:
/// * [apply] 
/// * [recruit] 
/// * [engineer] 
/// * [prComments] 
/// * [compareDetails] 
/// * [hostCompanyAddress] 
/// * [hostCompanyContactEmail] 
/// * [supportCompanyContactEmail] 
/// * [agencyCompanyContactEmail] 
@BuiltValue()
abstract class GeneralCompanyGetApplyDetails implements Built<GeneralCompanyGetApplyDetails, GeneralCompanyGetApplyDetailsBuilder> {
  @BuiltValueField(wireName: r'apply')
  GeneralCompanyApplyDetails get apply;

  @BuiltValueField(wireName: r'recruit')
  GeneralCompanyRecruitInfo get recruit;

  @BuiltValueField(wireName: r'engineer')
  UserExploreDetailsSerializers? get engineer;

  @BuiltValueField(wireName: r'pr_comments')
  BuiltList<GeneralCompanyPRComment> get prComments;

  @BuiltValueField(wireName: r'compare_details')
  GeneralCompanyCompareDetails? get compareDetails;

  @BuiltValueField(wireName: r'host_company_address')
  String? get hostCompanyAddress;

  @BuiltValueField(wireName: r'host_company_contact_email')
  String? get hostCompanyContactEmail;

  @BuiltValueField(wireName: r'support_company_contact_email')
  String? get supportCompanyContactEmail;

  @BuiltValueField(wireName: r'agency_company_contact_email')
  String? get agencyCompanyContactEmail;

  GeneralCompanyGetApplyDetails._();

  factory GeneralCompanyGetApplyDetails([void updates(GeneralCompanyGetApplyDetailsBuilder b)]) = _$GeneralCompanyGetApplyDetails;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(GeneralCompanyGetApplyDetailsBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<GeneralCompanyGetApplyDetails> get serializer => _$GeneralCompanyGetApplyDetailsSerializer();
}

class _$GeneralCompanyGetApplyDetailsSerializer implements PrimitiveSerializer<GeneralCompanyGetApplyDetails> {
  @override
  final Iterable<Type> types = const [GeneralCompanyGetApplyDetails, _$GeneralCompanyGetApplyDetails];

  @override
  final String wireName = r'GeneralCompanyGetApplyDetails';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    GeneralCompanyGetApplyDetails object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'apply';
    yield serializers.serialize(
      object.apply,
      specifiedType: const FullType(GeneralCompanyApplyDetails),
    );
    yield r'recruit';
    yield serializers.serialize(
      object.recruit,
      specifiedType: const FullType(GeneralCompanyRecruitInfo),
    );
    if (object.engineer != null) {
      yield r'engineer';
      yield serializers.serialize(
        object.engineer,
        specifiedType: const FullType(UserExploreDetailsSerializers),
      );
    }
    yield r'pr_comments';
    yield serializers.serialize(
      object.prComments,
      specifiedType: const FullType(BuiltList, [FullType(GeneralCompanyPRComment)]),
    );
    if (object.compareDetails != null) {
      yield r'compare_details';
      yield serializers.serialize(
        object.compareDetails,
        specifiedType: const FullType(GeneralCompanyCompareDetails),
      );
    }
    if (object.hostCompanyAddress != null) {
      yield r'host_company_address';
      yield serializers.serialize(
        object.hostCompanyAddress,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.hostCompanyContactEmail != null) {
      yield r'host_company_contact_email';
      yield serializers.serialize(
        object.hostCompanyContactEmail,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.supportCompanyContactEmail != null) {
      yield r'support_company_contact_email';
      yield serializers.serialize(
        object.supportCompanyContactEmail,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.agencyCompanyContactEmail != null) {
      yield r'agency_company_contact_email';
      yield serializers.serialize(
        object.agencyCompanyContactEmail,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    GeneralCompanyGetApplyDetails object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required GeneralCompanyGetApplyDetailsBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'apply':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(GeneralCompanyApplyDetails),
          ) as GeneralCompanyApplyDetails;
          result.apply.replace(valueDes);
          break;
        case r'recruit':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(GeneralCompanyRecruitInfo),
          ) as GeneralCompanyRecruitInfo;
          result.recruit.replace(valueDes);
          break;
        case r'engineer':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(UserExploreDetailsSerializers),
          ) as UserExploreDetailsSerializers;
          result.engineer.replace(valueDes);
          break;
        case r'pr_comments':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(GeneralCompanyPRComment)]),
          ) as BuiltList<GeneralCompanyPRComment>;
          result.prComments.replace(valueDes);
          break;
        case r'compare_details':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(GeneralCompanyCompareDetails),
          ) as GeneralCompanyCompareDetails;
          result.compareDetails.replace(valueDes);
          break;
        case r'host_company_address':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.hostCompanyAddress = valueDes;
          break;
        case r'host_company_contact_email':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.hostCompanyContactEmail = valueDes;
          break;
        case r'support_company_contact_email':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.supportCompanyContactEmail = valueDes;
          break;
        case r'agency_company_contact_email':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.agencyCompanyContactEmail = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  GeneralCompanyGetApplyDetails deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = GeneralCompanyGetApplyDetailsBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

