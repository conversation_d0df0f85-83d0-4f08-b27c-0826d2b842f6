// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'host_company_request_interview.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$HostCompanyRequestInterview extends HostCompanyRequestInterview {
  @override
  final int recruitId;
  @override
  final int userId;
  @override
  final String message;

  factory _$HostCompanyRequestInterview(
          [void Function(HostCompanyRequestInterviewBuilder)? updates]) =>
      (new HostCompanyRequestInterviewBuilder()..update(updates))._build();

  _$HostCompanyRequestInterview._(
      {required this.recruitId, required this.userId, required this.message})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        recruitId, r'HostCompanyRequestInterview', 'recruitId');
    BuiltValueNullFieldError.checkNotNull(
        userId, r'HostCompanyRequestInterview', 'userId');
    BuiltValueNullFieldError.checkNotNull(
        message, r'HostCompanyRequestInterview', 'message');
  }

  @override
  HostCompanyRequestInterview rebuild(
          void Function(HostCompanyRequestInterviewBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  HostCompanyRequestInterviewBuilder toBuilder() =>
      new HostCompanyRequestInterviewBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is HostCompanyRequestInterview &&
        recruitId == other.recruitId &&
        userId == other.userId &&
        message == other.message;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, recruitId.hashCode);
    _$hash = $jc(_$hash, userId.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'HostCompanyRequestInterview')
          ..add('recruitId', recruitId)
          ..add('userId', userId)
          ..add('message', message))
        .toString();
  }
}

class HostCompanyRequestInterviewBuilder
    implements
        Builder<HostCompanyRequestInterview,
            HostCompanyRequestInterviewBuilder> {
  _$HostCompanyRequestInterview? _$v;

  int? _recruitId;
  int? get recruitId => _$this._recruitId;
  set recruitId(int? recruitId) => _$this._recruitId = recruitId;

  int? _userId;
  int? get userId => _$this._userId;
  set userId(int? userId) => _$this._userId = userId;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  HostCompanyRequestInterviewBuilder() {
    HostCompanyRequestInterview._defaults(this);
  }

  HostCompanyRequestInterviewBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _recruitId = $v.recruitId;
      _userId = $v.userId;
      _message = $v.message;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(HostCompanyRequestInterview other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$HostCompanyRequestInterview;
  }

  @override
  void update(void Function(HostCompanyRequestInterviewBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  HostCompanyRequestInterview build() => _build();

  _$HostCompanyRequestInterview _build() {
    final _$result = _$v ??
        new _$HostCompanyRequestInterview._(
          recruitId: BuiltValueNullFieldError.checkNotNull(
              recruitId, r'HostCompanyRequestInterview', 'recruitId'),
          userId: BuiltValueNullFieldError.checkNotNull(
              userId, r'HostCompanyRequestInterview', 'userId'),
          message: BuiltValueNullFieldError.checkNotNull(
              message, r'HostCompanyRequestInterview', 'message'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
