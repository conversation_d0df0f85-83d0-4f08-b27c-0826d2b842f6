// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'company_recruit_detail.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$CompanyRecruitDetail extends CompanyRecruitDetail {
  @override
  final int? recruitId;
  @override
  final RecruitCompany? supportCompany;
  @override
  final RecruitCompany? hostCompany;
  @override
  final String? payrollPriceFrom;
  @override
  final String? payrollPriceTo;
  @override
  final int? saveType;
  @override
  final String? title;
  @override
  final String? catchCopy;
  @override
  final DateTime? startDate;
  @override
  final DateTime? endDate;
  @override
  final int? displayFlag;
  @override
  final String? jobCode;
  @override
  final String? employCode;
  @override
  final String? content;
  @override
  final String? placeCode1;
  @override
  final String? placeCode2;
  @override
  final String? placeCode3;
  @override
  final String? payrollCode;
  @override
  final String? countryCode;
  @override
  final int? ageFrom;
  @override
  final int? ageTo;
  @override
  final int? sexType;
  @override
  final String? prefCode1;
  @override
  final String? prefCode2;
  @override
  final String? prefCode3;
  @override
  final String? lastAcademicCode;
  @override
  final String? languageCode1;
  @override
  final int? languageLevelType1;
  @override
  final String? languageCode2;
  @override
  final int? languageLevelType2;
  @override
  final String? experiencedJobCode;
  @override
  final int? yearsOfExperience;
  @override
  final String? skillJobCode1;
  @override
  final String? skillCode1;
  @override
  final int? skillLevelType1;
  @override
  final String? skillJobCode2;
  @override
  final String? skillCode2;
  @override
  final int? skillLevelType2;
  @override
  final String? skillJobCode3;
  @override
  final String? skillCode3;
  @override
  final int? skillLevelType3;
  @override
  final int? createAgentId;
  @override
  final DateTime? created;
  @override
  final int? updateAgentId;
  @override
  final DateTime? updated;
  @override
  final String? licenceCode1;
  @override
  final String? licenceName1;
  @override
  final int? licencePoint1;
  @override
  final String? licenceCode2;
  @override
  final String? licenceName2;
  @override
  final int? licencePoint2;
  @override
  final String? licenceCode3;
  @override
  final String? licenceName3;
  @override
  final int? licencePoint3;
  @override
  final String? recruitImagePath;
  @override
  final String? remoteCode;
  @override
  final double? payrollPriceFromUsd;
  @override
  final double? payrollPriceToUsd;
  @override
  final int hostAgent;
  @override
  final int? supportAgent;

  factory _$CompanyRecruitDetail(
          [void Function(CompanyRecruitDetailBuilder)? updates]) =>
      (new CompanyRecruitDetailBuilder()..update(updates))._build();

  _$CompanyRecruitDetail._(
      {this.recruitId,
      this.supportCompany,
      this.hostCompany,
      this.payrollPriceFrom,
      this.payrollPriceTo,
      this.saveType,
      this.title,
      this.catchCopy,
      this.startDate,
      this.endDate,
      this.displayFlag,
      this.jobCode,
      this.employCode,
      this.content,
      this.placeCode1,
      this.placeCode2,
      this.placeCode3,
      this.payrollCode,
      this.countryCode,
      this.ageFrom,
      this.ageTo,
      this.sexType,
      this.prefCode1,
      this.prefCode2,
      this.prefCode3,
      this.lastAcademicCode,
      this.languageCode1,
      this.languageLevelType1,
      this.languageCode2,
      this.languageLevelType2,
      this.experiencedJobCode,
      this.yearsOfExperience,
      this.skillJobCode1,
      this.skillCode1,
      this.skillLevelType1,
      this.skillJobCode2,
      this.skillCode2,
      this.skillLevelType2,
      this.skillJobCode3,
      this.skillCode3,
      this.skillLevelType3,
      this.createAgentId,
      this.created,
      this.updateAgentId,
      this.updated,
      this.licenceCode1,
      this.licenceName1,
      this.licencePoint1,
      this.licenceCode2,
      this.licenceName2,
      this.licencePoint2,
      this.licenceCode3,
      this.licenceName3,
      this.licencePoint3,
      this.recruitImagePath,
      this.remoteCode,
      this.payrollPriceFromUsd,
      this.payrollPriceToUsd,
      required this.hostAgent,
      this.supportAgent})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        hostAgent, r'CompanyRecruitDetail', 'hostAgent');
  }

  @override
  CompanyRecruitDetail rebuild(
          void Function(CompanyRecruitDetailBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  CompanyRecruitDetailBuilder toBuilder() =>
      new CompanyRecruitDetailBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is CompanyRecruitDetail &&
        recruitId == other.recruitId &&
        supportCompany == other.supportCompany &&
        hostCompany == other.hostCompany &&
        payrollPriceFrom == other.payrollPriceFrom &&
        payrollPriceTo == other.payrollPriceTo &&
        saveType == other.saveType &&
        title == other.title &&
        catchCopy == other.catchCopy &&
        startDate == other.startDate &&
        endDate == other.endDate &&
        displayFlag == other.displayFlag &&
        jobCode == other.jobCode &&
        employCode == other.employCode &&
        content == other.content &&
        placeCode1 == other.placeCode1 &&
        placeCode2 == other.placeCode2 &&
        placeCode3 == other.placeCode3 &&
        payrollCode == other.payrollCode &&
        countryCode == other.countryCode &&
        ageFrom == other.ageFrom &&
        ageTo == other.ageTo &&
        sexType == other.sexType &&
        prefCode1 == other.prefCode1 &&
        prefCode2 == other.prefCode2 &&
        prefCode3 == other.prefCode3 &&
        lastAcademicCode == other.lastAcademicCode &&
        languageCode1 == other.languageCode1 &&
        languageLevelType1 == other.languageLevelType1 &&
        languageCode2 == other.languageCode2 &&
        languageLevelType2 == other.languageLevelType2 &&
        experiencedJobCode == other.experiencedJobCode &&
        yearsOfExperience == other.yearsOfExperience &&
        skillJobCode1 == other.skillJobCode1 &&
        skillCode1 == other.skillCode1 &&
        skillLevelType1 == other.skillLevelType1 &&
        skillJobCode2 == other.skillJobCode2 &&
        skillCode2 == other.skillCode2 &&
        skillLevelType2 == other.skillLevelType2 &&
        skillJobCode3 == other.skillJobCode3 &&
        skillCode3 == other.skillCode3 &&
        skillLevelType3 == other.skillLevelType3 &&
        createAgentId == other.createAgentId &&
        created == other.created &&
        updateAgentId == other.updateAgentId &&
        updated == other.updated &&
        licenceCode1 == other.licenceCode1 &&
        licenceName1 == other.licenceName1 &&
        licencePoint1 == other.licencePoint1 &&
        licenceCode2 == other.licenceCode2 &&
        licenceName2 == other.licenceName2 &&
        licencePoint2 == other.licencePoint2 &&
        licenceCode3 == other.licenceCode3 &&
        licenceName3 == other.licenceName3 &&
        licencePoint3 == other.licencePoint3 &&
        recruitImagePath == other.recruitImagePath &&
        remoteCode == other.remoteCode &&
        payrollPriceFromUsd == other.payrollPriceFromUsd &&
        payrollPriceToUsd == other.payrollPriceToUsd &&
        hostAgent == other.hostAgent &&
        supportAgent == other.supportAgent;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, recruitId.hashCode);
    _$hash = $jc(_$hash, supportCompany.hashCode);
    _$hash = $jc(_$hash, hostCompany.hashCode);
    _$hash = $jc(_$hash, payrollPriceFrom.hashCode);
    _$hash = $jc(_$hash, payrollPriceTo.hashCode);
    _$hash = $jc(_$hash, saveType.hashCode);
    _$hash = $jc(_$hash, title.hashCode);
    _$hash = $jc(_$hash, catchCopy.hashCode);
    _$hash = $jc(_$hash, startDate.hashCode);
    _$hash = $jc(_$hash, endDate.hashCode);
    _$hash = $jc(_$hash, displayFlag.hashCode);
    _$hash = $jc(_$hash, jobCode.hashCode);
    _$hash = $jc(_$hash, employCode.hashCode);
    _$hash = $jc(_$hash, content.hashCode);
    _$hash = $jc(_$hash, placeCode1.hashCode);
    _$hash = $jc(_$hash, placeCode2.hashCode);
    _$hash = $jc(_$hash, placeCode3.hashCode);
    _$hash = $jc(_$hash, payrollCode.hashCode);
    _$hash = $jc(_$hash, countryCode.hashCode);
    _$hash = $jc(_$hash, ageFrom.hashCode);
    _$hash = $jc(_$hash, ageTo.hashCode);
    _$hash = $jc(_$hash, sexType.hashCode);
    _$hash = $jc(_$hash, prefCode1.hashCode);
    _$hash = $jc(_$hash, prefCode2.hashCode);
    _$hash = $jc(_$hash, prefCode3.hashCode);
    _$hash = $jc(_$hash, lastAcademicCode.hashCode);
    _$hash = $jc(_$hash, languageCode1.hashCode);
    _$hash = $jc(_$hash, languageLevelType1.hashCode);
    _$hash = $jc(_$hash, languageCode2.hashCode);
    _$hash = $jc(_$hash, languageLevelType2.hashCode);
    _$hash = $jc(_$hash, experiencedJobCode.hashCode);
    _$hash = $jc(_$hash, yearsOfExperience.hashCode);
    _$hash = $jc(_$hash, skillJobCode1.hashCode);
    _$hash = $jc(_$hash, skillCode1.hashCode);
    _$hash = $jc(_$hash, skillLevelType1.hashCode);
    _$hash = $jc(_$hash, skillJobCode2.hashCode);
    _$hash = $jc(_$hash, skillCode2.hashCode);
    _$hash = $jc(_$hash, skillLevelType2.hashCode);
    _$hash = $jc(_$hash, skillJobCode3.hashCode);
    _$hash = $jc(_$hash, skillCode3.hashCode);
    _$hash = $jc(_$hash, skillLevelType3.hashCode);
    _$hash = $jc(_$hash, createAgentId.hashCode);
    _$hash = $jc(_$hash, created.hashCode);
    _$hash = $jc(_$hash, updateAgentId.hashCode);
    _$hash = $jc(_$hash, updated.hashCode);
    _$hash = $jc(_$hash, licenceCode1.hashCode);
    _$hash = $jc(_$hash, licenceName1.hashCode);
    _$hash = $jc(_$hash, licencePoint1.hashCode);
    _$hash = $jc(_$hash, licenceCode2.hashCode);
    _$hash = $jc(_$hash, licenceName2.hashCode);
    _$hash = $jc(_$hash, licencePoint2.hashCode);
    _$hash = $jc(_$hash, licenceCode3.hashCode);
    _$hash = $jc(_$hash, licenceName3.hashCode);
    _$hash = $jc(_$hash, licencePoint3.hashCode);
    _$hash = $jc(_$hash, recruitImagePath.hashCode);
    _$hash = $jc(_$hash, remoteCode.hashCode);
    _$hash = $jc(_$hash, payrollPriceFromUsd.hashCode);
    _$hash = $jc(_$hash, payrollPriceToUsd.hashCode);
    _$hash = $jc(_$hash, hostAgent.hashCode);
    _$hash = $jc(_$hash, supportAgent.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'CompanyRecruitDetail')
          ..add('recruitId', recruitId)
          ..add('supportCompany', supportCompany)
          ..add('hostCompany', hostCompany)
          ..add('payrollPriceFrom', payrollPriceFrom)
          ..add('payrollPriceTo', payrollPriceTo)
          ..add('saveType', saveType)
          ..add('title', title)
          ..add('catchCopy', catchCopy)
          ..add('startDate', startDate)
          ..add('endDate', endDate)
          ..add('displayFlag', displayFlag)
          ..add('jobCode', jobCode)
          ..add('employCode', employCode)
          ..add('content', content)
          ..add('placeCode1', placeCode1)
          ..add('placeCode2', placeCode2)
          ..add('placeCode3', placeCode3)
          ..add('payrollCode', payrollCode)
          ..add('countryCode', countryCode)
          ..add('ageFrom', ageFrom)
          ..add('ageTo', ageTo)
          ..add('sexType', sexType)
          ..add('prefCode1', prefCode1)
          ..add('prefCode2', prefCode2)
          ..add('prefCode3', prefCode3)
          ..add('lastAcademicCode', lastAcademicCode)
          ..add('languageCode1', languageCode1)
          ..add('languageLevelType1', languageLevelType1)
          ..add('languageCode2', languageCode2)
          ..add('languageLevelType2', languageLevelType2)
          ..add('experiencedJobCode', experiencedJobCode)
          ..add('yearsOfExperience', yearsOfExperience)
          ..add('skillJobCode1', skillJobCode1)
          ..add('skillCode1', skillCode1)
          ..add('skillLevelType1', skillLevelType1)
          ..add('skillJobCode2', skillJobCode2)
          ..add('skillCode2', skillCode2)
          ..add('skillLevelType2', skillLevelType2)
          ..add('skillJobCode3', skillJobCode3)
          ..add('skillCode3', skillCode3)
          ..add('skillLevelType3', skillLevelType3)
          ..add('createAgentId', createAgentId)
          ..add('created', created)
          ..add('updateAgentId', updateAgentId)
          ..add('updated', updated)
          ..add('licenceCode1', licenceCode1)
          ..add('licenceName1', licenceName1)
          ..add('licencePoint1', licencePoint1)
          ..add('licenceCode2', licenceCode2)
          ..add('licenceName2', licenceName2)
          ..add('licencePoint2', licencePoint2)
          ..add('licenceCode3', licenceCode3)
          ..add('licenceName3', licenceName3)
          ..add('licencePoint3', licencePoint3)
          ..add('recruitImagePath', recruitImagePath)
          ..add('remoteCode', remoteCode)
          ..add('payrollPriceFromUsd', payrollPriceFromUsd)
          ..add('payrollPriceToUsd', payrollPriceToUsd)
          ..add('hostAgent', hostAgent)
          ..add('supportAgent', supportAgent))
        .toString();
  }
}

class CompanyRecruitDetailBuilder
    implements Builder<CompanyRecruitDetail, CompanyRecruitDetailBuilder> {
  _$CompanyRecruitDetail? _$v;

  int? _recruitId;
  int? get recruitId => _$this._recruitId;
  set recruitId(int? recruitId) => _$this._recruitId = recruitId;

  RecruitCompanyBuilder? _supportCompany;
  RecruitCompanyBuilder get supportCompany =>
      _$this._supportCompany ??= new RecruitCompanyBuilder();
  set supportCompany(RecruitCompanyBuilder? supportCompany) =>
      _$this._supportCompany = supportCompany;

  RecruitCompanyBuilder? _hostCompany;
  RecruitCompanyBuilder get hostCompany =>
      _$this._hostCompany ??= new RecruitCompanyBuilder();
  set hostCompany(RecruitCompanyBuilder? hostCompany) =>
      _$this._hostCompany = hostCompany;

  String? _payrollPriceFrom;
  String? get payrollPriceFrom => _$this._payrollPriceFrom;
  set payrollPriceFrom(String? payrollPriceFrom) =>
      _$this._payrollPriceFrom = payrollPriceFrom;

  String? _payrollPriceTo;
  String? get payrollPriceTo => _$this._payrollPriceTo;
  set payrollPriceTo(String? payrollPriceTo) =>
      _$this._payrollPriceTo = payrollPriceTo;

  int? _saveType;
  int? get saveType => _$this._saveType;
  set saveType(int? saveType) => _$this._saveType = saveType;

  String? _title;
  String? get title => _$this._title;
  set title(String? title) => _$this._title = title;

  String? _catchCopy;
  String? get catchCopy => _$this._catchCopy;
  set catchCopy(String? catchCopy) => _$this._catchCopy = catchCopy;

  DateTime? _startDate;
  DateTime? get startDate => _$this._startDate;
  set startDate(DateTime? startDate) => _$this._startDate = startDate;

  DateTime? _endDate;
  DateTime? get endDate => _$this._endDate;
  set endDate(DateTime? endDate) => _$this._endDate = endDate;

  int? _displayFlag;
  int? get displayFlag => _$this._displayFlag;
  set displayFlag(int? displayFlag) => _$this._displayFlag = displayFlag;

  String? _jobCode;
  String? get jobCode => _$this._jobCode;
  set jobCode(String? jobCode) => _$this._jobCode = jobCode;

  String? _employCode;
  String? get employCode => _$this._employCode;
  set employCode(String? employCode) => _$this._employCode = employCode;

  String? _content;
  String? get content => _$this._content;
  set content(String? content) => _$this._content = content;

  String? _placeCode1;
  String? get placeCode1 => _$this._placeCode1;
  set placeCode1(String? placeCode1) => _$this._placeCode1 = placeCode1;

  String? _placeCode2;
  String? get placeCode2 => _$this._placeCode2;
  set placeCode2(String? placeCode2) => _$this._placeCode2 = placeCode2;

  String? _placeCode3;
  String? get placeCode3 => _$this._placeCode3;
  set placeCode3(String? placeCode3) => _$this._placeCode3 = placeCode3;

  String? _payrollCode;
  String? get payrollCode => _$this._payrollCode;
  set payrollCode(String? payrollCode) => _$this._payrollCode = payrollCode;

  String? _countryCode;
  String? get countryCode => _$this._countryCode;
  set countryCode(String? countryCode) => _$this._countryCode = countryCode;

  int? _ageFrom;
  int? get ageFrom => _$this._ageFrom;
  set ageFrom(int? ageFrom) => _$this._ageFrom = ageFrom;

  int? _ageTo;
  int? get ageTo => _$this._ageTo;
  set ageTo(int? ageTo) => _$this._ageTo = ageTo;

  int? _sexType;
  int? get sexType => _$this._sexType;
  set sexType(int? sexType) => _$this._sexType = sexType;

  String? _prefCode1;
  String? get prefCode1 => _$this._prefCode1;
  set prefCode1(String? prefCode1) => _$this._prefCode1 = prefCode1;

  String? _prefCode2;
  String? get prefCode2 => _$this._prefCode2;
  set prefCode2(String? prefCode2) => _$this._prefCode2 = prefCode2;

  String? _prefCode3;
  String? get prefCode3 => _$this._prefCode3;
  set prefCode3(String? prefCode3) => _$this._prefCode3 = prefCode3;

  String? _lastAcademicCode;
  String? get lastAcademicCode => _$this._lastAcademicCode;
  set lastAcademicCode(String? lastAcademicCode) =>
      _$this._lastAcademicCode = lastAcademicCode;

  String? _languageCode1;
  String? get languageCode1 => _$this._languageCode1;
  set languageCode1(String? languageCode1) =>
      _$this._languageCode1 = languageCode1;

  int? _languageLevelType1;
  int? get languageLevelType1 => _$this._languageLevelType1;
  set languageLevelType1(int? languageLevelType1) =>
      _$this._languageLevelType1 = languageLevelType1;

  String? _languageCode2;
  String? get languageCode2 => _$this._languageCode2;
  set languageCode2(String? languageCode2) =>
      _$this._languageCode2 = languageCode2;

  int? _languageLevelType2;
  int? get languageLevelType2 => _$this._languageLevelType2;
  set languageLevelType2(int? languageLevelType2) =>
      _$this._languageLevelType2 = languageLevelType2;

  String? _experiencedJobCode;
  String? get experiencedJobCode => _$this._experiencedJobCode;
  set experiencedJobCode(String? experiencedJobCode) =>
      _$this._experiencedJobCode = experiencedJobCode;

  int? _yearsOfExperience;
  int? get yearsOfExperience => _$this._yearsOfExperience;
  set yearsOfExperience(int? yearsOfExperience) =>
      _$this._yearsOfExperience = yearsOfExperience;

  String? _skillJobCode1;
  String? get skillJobCode1 => _$this._skillJobCode1;
  set skillJobCode1(String? skillJobCode1) =>
      _$this._skillJobCode1 = skillJobCode1;

  String? _skillCode1;
  String? get skillCode1 => _$this._skillCode1;
  set skillCode1(String? skillCode1) => _$this._skillCode1 = skillCode1;

  int? _skillLevelType1;
  int? get skillLevelType1 => _$this._skillLevelType1;
  set skillLevelType1(int? skillLevelType1) =>
      _$this._skillLevelType1 = skillLevelType1;

  String? _skillJobCode2;
  String? get skillJobCode2 => _$this._skillJobCode2;
  set skillJobCode2(String? skillJobCode2) =>
      _$this._skillJobCode2 = skillJobCode2;

  String? _skillCode2;
  String? get skillCode2 => _$this._skillCode2;
  set skillCode2(String? skillCode2) => _$this._skillCode2 = skillCode2;

  int? _skillLevelType2;
  int? get skillLevelType2 => _$this._skillLevelType2;
  set skillLevelType2(int? skillLevelType2) =>
      _$this._skillLevelType2 = skillLevelType2;

  String? _skillJobCode3;
  String? get skillJobCode3 => _$this._skillJobCode3;
  set skillJobCode3(String? skillJobCode3) =>
      _$this._skillJobCode3 = skillJobCode3;

  String? _skillCode3;
  String? get skillCode3 => _$this._skillCode3;
  set skillCode3(String? skillCode3) => _$this._skillCode3 = skillCode3;

  int? _skillLevelType3;
  int? get skillLevelType3 => _$this._skillLevelType3;
  set skillLevelType3(int? skillLevelType3) =>
      _$this._skillLevelType3 = skillLevelType3;

  int? _createAgentId;
  int? get createAgentId => _$this._createAgentId;
  set createAgentId(int? createAgentId) =>
      _$this._createAgentId = createAgentId;

  DateTime? _created;
  DateTime? get created => _$this._created;
  set created(DateTime? created) => _$this._created = created;

  int? _updateAgentId;
  int? get updateAgentId => _$this._updateAgentId;
  set updateAgentId(int? updateAgentId) =>
      _$this._updateAgentId = updateAgentId;

  DateTime? _updated;
  DateTime? get updated => _$this._updated;
  set updated(DateTime? updated) => _$this._updated = updated;

  String? _licenceCode1;
  String? get licenceCode1 => _$this._licenceCode1;
  set licenceCode1(String? licenceCode1) => _$this._licenceCode1 = licenceCode1;

  String? _licenceName1;
  String? get licenceName1 => _$this._licenceName1;
  set licenceName1(String? licenceName1) => _$this._licenceName1 = licenceName1;

  int? _licencePoint1;
  int? get licencePoint1 => _$this._licencePoint1;
  set licencePoint1(int? licencePoint1) =>
      _$this._licencePoint1 = licencePoint1;

  String? _licenceCode2;
  String? get licenceCode2 => _$this._licenceCode2;
  set licenceCode2(String? licenceCode2) => _$this._licenceCode2 = licenceCode2;

  String? _licenceName2;
  String? get licenceName2 => _$this._licenceName2;
  set licenceName2(String? licenceName2) => _$this._licenceName2 = licenceName2;

  int? _licencePoint2;
  int? get licencePoint2 => _$this._licencePoint2;
  set licencePoint2(int? licencePoint2) =>
      _$this._licencePoint2 = licencePoint2;

  String? _licenceCode3;
  String? get licenceCode3 => _$this._licenceCode3;
  set licenceCode3(String? licenceCode3) => _$this._licenceCode3 = licenceCode3;

  String? _licenceName3;
  String? get licenceName3 => _$this._licenceName3;
  set licenceName3(String? licenceName3) => _$this._licenceName3 = licenceName3;

  int? _licencePoint3;
  int? get licencePoint3 => _$this._licencePoint3;
  set licencePoint3(int? licencePoint3) =>
      _$this._licencePoint3 = licencePoint3;

  String? _recruitImagePath;
  String? get recruitImagePath => _$this._recruitImagePath;
  set recruitImagePath(String? recruitImagePath) =>
      _$this._recruitImagePath = recruitImagePath;

  String? _remoteCode;
  String? get remoteCode => _$this._remoteCode;
  set remoteCode(String? remoteCode) => _$this._remoteCode = remoteCode;

  double? _payrollPriceFromUsd;
  double? get payrollPriceFromUsd => _$this._payrollPriceFromUsd;
  set payrollPriceFromUsd(double? payrollPriceFromUsd) =>
      _$this._payrollPriceFromUsd = payrollPriceFromUsd;

  double? _payrollPriceToUsd;
  double? get payrollPriceToUsd => _$this._payrollPriceToUsd;
  set payrollPriceToUsd(double? payrollPriceToUsd) =>
      _$this._payrollPriceToUsd = payrollPriceToUsd;

  int? _hostAgent;
  int? get hostAgent => _$this._hostAgent;
  set hostAgent(int? hostAgent) => _$this._hostAgent = hostAgent;

  int? _supportAgent;
  int? get supportAgent => _$this._supportAgent;
  set supportAgent(int? supportAgent) => _$this._supportAgent = supportAgent;

  CompanyRecruitDetailBuilder() {
    CompanyRecruitDetail._defaults(this);
  }

  CompanyRecruitDetailBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _recruitId = $v.recruitId;
      _supportCompany = $v.supportCompany?.toBuilder();
      _hostCompany = $v.hostCompany?.toBuilder();
      _payrollPriceFrom = $v.payrollPriceFrom;
      _payrollPriceTo = $v.payrollPriceTo;
      _saveType = $v.saveType;
      _title = $v.title;
      _catchCopy = $v.catchCopy;
      _startDate = $v.startDate;
      _endDate = $v.endDate;
      _displayFlag = $v.displayFlag;
      _jobCode = $v.jobCode;
      _employCode = $v.employCode;
      _content = $v.content;
      _placeCode1 = $v.placeCode1;
      _placeCode2 = $v.placeCode2;
      _placeCode3 = $v.placeCode3;
      _payrollCode = $v.payrollCode;
      _countryCode = $v.countryCode;
      _ageFrom = $v.ageFrom;
      _ageTo = $v.ageTo;
      _sexType = $v.sexType;
      _prefCode1 = $v.prefCode1;
      _prefCode2 = $v.prefCode2;
      _prefCode3 = $v.prefCode3;
      _lastAcademicCode = $v.lastAcademicCode;
      _languageCode1 = $v.languageCode1;
      _languageLevelType1 = $v.languageLevelType1;
      _languageCode2 = $v.languageCode2;
      _languageLevelType2 = $v.languageLevelType2;
      _experiencedJobCode = $v.experiencedJobCode;
      _yearsOfExperience = $v.yearsOfExperience;
      _skillJobCode1 = $v.skillJobCode1;
      _skillCode1 = $v.skillCode1;
      _skillLevelType1 = $v.skillLevelType1;
      _skillJobCode2 = $v.skillJobCode2;
      _skillCode2 = $v.skillCode2;
      _skillLevelType2 = $v.skillLevelType2;
      _skillJobCode3 = $v.skillJobCode3;
      _skillCode3 = $v.skillCode3;
      _skillLevelType3 = $v.skillLevelType3;
      _createAgentId = $v.createAgentId;
      _created = $v.created;
      _updateAgentId = $v.updateAgentId;
      _updated = $v.updated;
      _licenceCode1 = $v.licenceCode1;
      _licenceName1 = $v.licenceName1;
      _licencePoint1 = $v.licencePoint1;
      _licenceCode2 = $v.licenceCode2;
      _licenceName2 = $v.licenceName2;
      _licencePoint2 = $v.licencePoint2;
      _licenceCode3 = $v.licenceCode3;
      _licenceName3 = $v.licenceName3;
      _licencePoint3 = $v.licencePoint3;
      _recruitImagePath = $v.recruitImagePath;
      _remoteCode = $v.remoteCode;
      _payrollPriceFromUsd = $v.payrollPriceFromUsd;
      _payrollPriceToUsd = $v.payrollPriceToUsd;
      _hostAgent = $v.hostAgent;
      _supportAgent = $v.supportAgent;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(CompanyRecruitDetail other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$CompanyRecruitDetail;
  }

  @override
  void update(void Function(CompanyRecruitDetailBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  CompanyRecruitDetail build() => _build();

  _$CompanyRecruitDetail _build() {
    _$CompanyRecruitDetail _$result;
    try {
      _$result = _$v ??
          new _$CompanyRecruitDetail._(
            recruitId: recruitId,
            supportCompany: _supportCompany?.build(),
            hostCompany: _hostCompany?.build(),
            payrollPriceFrom: payrollPriceFrom,
            payrollPriceTo: payrollPriceTo,
            saveType: saveType,
            title: title,
            catchCopy: catchCopy,
            startDate: startDate,
            endDate: endDate,
            displayFlag: displayFlag,
            jobCode: jobCode,
            employCode: employCode,
            content: content,
            placeCode1: placeCode1,
            placeCode2: placeCode2,
            placeCode3: placeCode3,
            payrollCode: payrollCode,
            countryCode: countryCode,
            ageFrom: ageFrom,
            ageTo: ageTo,
            sexType: sexType,
            prefCode1: prefCode1,
            prefCode2: prefCode2,
            prefCode3: prefCode3,
            lastAcademicCode: lastAcademicCode,
            languageCode1: languageCode1,
            languageLevelType1: languageLevelType1,
            languageCode2: languageCode2,
            languageLevelType2: languageLevelType2,
            experiencedJobCode: experiencedJobCode,
            yearsOfExperience: yearsOfExperience,
            skillJobCode1: skillJobCode1,
            skillCode1: skillCode1,
            skillLevelType1: skillLevelType1,
            skillJobCode2: skillJobCode2,
            skillCode2: skillCode2,
            skillLevelType2: skillLevelType2,
            skillJobCode3: skillJobCode3,
            skillCode3: skillCode3,
            skillLevelType3: skillLevelType3,
            createAgentId: createAgentId,
            created: created,
            updateAgentId: updateAgentId,
            updated: updated,
            licenceCode1: licenceCode1,
            licenceName1: licenceName1,
            licencePoint1: licencePoint1,
            licenceCode2: licenceCode2,
            licenceName2: licenceName2,
            licencePoint2: licencePoint2,
            licenceCode3: licenceCode3,
            licenceName3: licenceName3,
            licencePoint3: licencePoint3,
            recruitImagePath: recruitImagePath,
            remoteCode: remoteCode,
            payrollPriceFromUsd: payrollPriceFromUsd,
            payrollPriceToUsd: payrollPriceToUsd,
            hostAgent: BuiltValueNullFieldError.checkNotNull(
                hostAgent, r'CompanyRecruitDetail', 'hostAgent'),
            supportAgent: supportAgent,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'supportCompany';
        _supportCompany?.build();
        _$failedField = 'hostCompany';
        _hostCompany?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'CompanyRecruitDetail', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
