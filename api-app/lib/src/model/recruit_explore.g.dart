// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recruit_explore.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$RecruitExplore extends RecruitExplore {
  @override
  final int? recruitId;
  @override
  final String? title;
  @override
  final String? catchCopy;
  @override
  final String payrollPriceFrom;
  @override
  final String payrollPriceTo;
  @override
  final String? payrollCode;
  @override
  final DateTime? startDate;
  @override
  final DateTime? endDate;
  @override
  final String? jobCode;
  @override
  final RecruitCompany? hostCompany;
  @override
  final String? skillJobCode1;
  @override
  final String? skillJobCode2;
  @override
  final String? skillJobCode3;
  @override
  final String? waitingFlag;
  @override
  final DateTime? created;

  factory _$RecruitExplore([void Function(RecruitExploreBuilder)? updates]) =>
      (new RecruitExploreBuilder()..update(updates))._build();

  _$RecruitExplore._(
      {this.recruitId,
      this.title,
      this.catchCopy,
      required this.payrollPriceFrom,
      required this.payrollPriceTo,
      this.payrollCode,
      this.startDate,
      this.endDate,
      this.jobCode,
      this.hostCompany,
      this.skillJobCode1,
      this.skillJobCode2,
      this.skillJobCode3,
      this.waitingFlag,
      this.created})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        payrollPriceFrom, r'RecruitExplore', 'payrollPriceFrom');
    BuiltValueNullFieldError.checkNotNull(
        payrollPriceTo, r'RecruitExplore', 'payrollPriceTo');
  }

  @override
  RecruitExplore rebuild(void Function(RecruitExploreBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  RecruitExploreBuilder toBuilder() =>
      new RecruitExploreBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is RecruitExplore &&
        recruitId == other.recruitId &&
        title == other.title &&
        catchCopy == other.catchCopy &&
        payrollPriceFrom == other.payrollPriceFrom &&
        payrollPriceTo == other.payrollPriceTo &&
        payrollCode == other.payrollCode &&
        startDate == other.startDate &&
        endDate == other.endDate &&
        jobCode == other.jobCode &&
        hostCompany == other.hostCompany &&
        skillJobCode1 == other.skillJobCode1 &&
        skillJobCode2 == other.skillJobCode2 &&
        skillJobCode3 == other.skillJobCode3 &&
        waitingFlag == other.waitingFlag &&
        created == other.created;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, recruitId.hashCode);
    _$hash = $jc(_$hash, title.hashCode);
    _$hash = $jc(_$hash, catchCopy.hashCode);
    _$hash = $jc(_$hash, payrollPriceFrom.hashCode);
    _$hash = $jc(_$hash, payrollPriceTo.hashCode);
    _$hash = $jc(_$hash, payrollCode.hashCode);
    _$hash = $jc(_$hash, startDate.hashCode);
    _$hash = $jc(_$hash, endDate.hashCode);
    _$hash = $jc(_$hash, jobCode.hashCode);
    _$hash = $jc(_$hash, hostCompany.hashCode);
    _$hash = $jc(_$hash, skillJobCode1.hashCode);
    _$hash = $jc(_$hash, skillJobCode2.hashCode);
    _$hash = $jc(_$hash, skillJobCode3.hashCode);
    _$hash = $jc(_$hash, waitingFlag.hashCode);
    _$hash = $jc(_$hash, created.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'RecruitExplore')
          ..add('recruitId', recruitId)
          ..add('title', title)
          ..add('catchCopy', catchCopy)
          ..add('payrollPriceFrom', payrollPriceFrom)
          ..add('payrollPriceTo', payrollPriceTo)
          ..add('payrollCode', payrollCode)
          ..add('startDate', startDate)
          ..add('endDate', endDate)
          ..add('jobCode', jobCode)
          ..add('hostCompany', hostCompany)
          ..add('skillJobCode1', skillJobCode1)
          ..add('skillJobCode2', skillJobCode2)
          ..add('skillJobCode3', skillJobCode3)
          ..add('waitingFlag', waitingFlag)
          ..add('created', created))
        .toString();
  }
}

class RecruitExploreBuilder
    implements Builder<RecruitExplore, RecruitExploreBuilder> {
  _$RecruitExplore? _$v;

  int? _recruitId;
  int? get recruitId => _$this._recruitId;
  set recruitId(int? recruitId) => _$this._recruitId = recruitId;

  String? _title;
  String? get title => _$this._title;
  set title(String? title) => _$this._title = title;

  String? _catchCopy;
  String? get catchCopy => _$this._catchCopy;
  set catchCopy(String? catchCopy) => _$this._catchCopy = catchCopy;

  String? _payrollPriceFrom;
  String? get payrollPriceFrom => _$this._payrollPriceFrom;
  set payrollPriceFrom(String? payrollPriceFrom) =>
      _$this._payrollPriceFrom = payrollPriceFrom;

  String? _payrollPriceTo;
  String? get payrollPriceTo => _$this._payrollPriceTo;
  set payrollPriceTo(String? payrollPriceTo) =>
      _$this._payrollPriceTo = payrollPriceTo;

  String? _payrollCode;
  String? get payrollCode => _$this._payrollCode;
  set payrollCode(String? payrollCode) => _$this._payrollCode = payrollCode;

  DateTime? _startDate;
  DateTime? get startDate => _$this._startDate;
  set startDate(DateTime? startDate) => _$this._startDate = startDate;

  DateTime? _endDate;
  DateTime? get endDate => _$this._endDate;
  set endDate(DateTime? endDate) => _$this._endDate = endDate;

  String? _jobCode;
  String? get jobCode => _$this._jobCode;
  set jobCode(String? jobCode) => _$this._jobCode = jobCode;

  RecruitCompanyBuilder? _hostCompany;
  RecruitCompanyBuilder get hostCompany =>
      _$this._hostCompany ??= new RecruitCompanyBuilder();
  set hostCompany(RecruitCompanyBuilder? hostCompany) =>
      _$this._hostCompany = hostCompany;

  String? _skillJobCode1;
  String? get skillJobCode1 => _$this._skillJobCode1;
  set skillJobCode1(String? skillJobCode1) =>
      _$this._skillJobCode1 = skillJobCode1;

  String? _skillJobCode2;
  String? get skillJobCode2 => _$this._skillJobCode2;
  set skillJobCode2(String? skillJobCode2) =>
      _$this._skillJobCode2 = skillJobCode2;

  String? _skillJobCode3;
  String? get skillJobCode3 => _$this._skillJobCode3;
  set skillJobCode3(String? skillJobCode3) =>
      _$this._skillJobCode3 = skillJobCode3;

  String? _waitingFlag;
  String? get waitingFlag => _$this._waitingFlag;
  set waitingFlag(String? waitingFlag) => _$this._waitingFlag = waitingFlag;

  DateTime? _created;
  DateTime? get created => _$this._created;
  set created(DateTime? created) => _$this._created = created;

  RecruitExploreBuilder() {
    RecruitExplore._defaults(this);
  }

  RecruitExploreBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _recruitId = $v.recruitId;
      _title = $v.title;
      _catchCopy = $v.catchCopy;
      _payrollPriceFrom = $v.payrollPriceFrom;
      _payrollPriceTo = $v.payrollPriceTo;
      _payrollCode = $v.payrollCode;
      _startDate = $v.startDate;
      _endDate = $v.endDate;
      _jobCode = $v.jobCode;
      _hostCompany = $v.hostCompany?.toBuilder();
      _skillJobCode1 = $v.skillJobCode1;
      _skillJobCode2 = $v.skillJobCode2;
      _skillJobCode3 = $v.skillJobCode3;
      _waitingFlag = $v.waitingFlag;
      _created = $v.created;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(RecruitExplore other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$RecruitExplore;
  }

  @override
  void update(void Function(RecruitExploreBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  RecruitExplore build() => _build();

  _$RecruitExplore _build() {
    _$RecruitExplore _$result;
    try {
      _$result = _$v ??
          new _$RecruitExplore._(
            recruitId: recruitId,
            title: title,
            catchCopy: catchCopy,
            payrollPriceFrom: BuiltValueNullFieldError.checkNotNull(
                payrollPriceFrom, r'RecruitExplore', 'payrollPriceFrom'),
            payrollPriceTo: BuiltValueNullFieldError.checkNotNull(
                payrollPriceTo, r'RecruitExplore', 'payrollPriceTo'),
            payrollCode: payrollCode,
            startDate: startDate,
            endDate: endDate,
            jobCode: jobCode,
            hostCompany: _hostCompany?.build(),
            skillJobCode1: skillJobCode1,
            skillJobCode2: skillJobCode2,
            skillJobCode3: skillJobCode3,
            waitingFlag: waitingFlag,
            created: created,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'hostCompany';
        _hostCompany?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'RecruitExplore', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
