//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/eng_self_assesment.dart';
import 'package:openapi/src/model/eng_academic.dart';
import 'package:openapi/src/model/eng_skill.dart';
import 'package:openapi/src/model/eng_high_light_project.dart';
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/eng_license.dart';
import 'package:openapi/src/model/eng_hope.dart';
import 'package:openapi/src/model/eng_career.dart';
import 'package:openapi/src/model/category_skill.dart';
import 'package:openapi/src/model/date.dart';
import 'package:openapi/src/model/eng_language.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'user_details_serializers.g.dart';

/// UserDetailsSerializers
///
/// Properties:
/// * [profileImagePath] 
/// * [email] 
/// * [firstName] 
/// * [lastName] 
/// * [nickname] 
/// * [sexType] 
/// * [birthDate] 
/// * [countryCode] 
/// * [tel] 
/// * [addressCode] 
/// * [cityName] 
/// * [passportNumber] 
/// * [passportImagePath] 
/// * [educations] 
/// * [languages] 
/// * [qualifications] 
/// * [skills] 
/// * [experiences] 
/// * [requirements] 
/// * [lastAcademicCode] 
/// * [pr] 
/// * [selfIntroductionUrl] 
/// * [internationalTel] 
/// * [updated] 
/// * [userType] 
/// * [facebookUrl] 
/// * [linkedinUrl] 
/// * [whatsappUrl] 
/// * [lastAcademicName] 
/// * [selfAssesment] 
/// * [emailTemp] 
/// * [categorySkills] 
/// * [skillsForCvDisplay] 
/// * [professionalSummary] 
/// * [highlightProjects] 
@BuiltValue()
abstract class UserDetailsSerializers implements Built<UserDetailsSerializers, UserDetailsSerializersBuilder> {
  @BuiltValueField(wireName: r'profile_image_path')
  String? get profileImagePath;

  @BuiltValueField(wireName: r'email')
  String get email;

  @BuiltValueField(wireName: r'first_name')
  String? get firstName;

  @BuiltValueField(wireName: r'last_name')
  String? get lastName;

  @BuiltValueField(wireName: r'nickname')
  String? get nickname;

  @BuiltValueField(wireName: r'sex_type')
  int? get sexType;

  @BuiltValueField(wireName: r'birth_date')
  Date? get birthDate;

  @BuiltValueField(wireName: r'country_code')
  String? get countryCode;

  @BuiltValueField(wireName: r'tel')
  String? get tel;

  @BuiltValueField(wireName: r'address_code')
  String? get addressCode;

  @BuiltValueField(wireName: r'city_name')
  String? get cityName;

  @BuiltValueField(wireName: r'passport_number')
  String? get passportNumber;

  @BuiltValueField(wireName: r'passport_image_path')
  String? get passportImagePath;

  @BuiltValueField(wireName: r'educations')
  BuiltList<EngAcademic>? get educations;

  @BuiltValueField(wireName: r'languages')
  BuiltList<EngLanguage>? get languages;

  @BuiltValueField(wireName: r'qualifications')
  BuiltList<EngLicense>? get qualifications;

  @BuiltValueField(wireName: r'skills')
  BuiltList<EngSkill>? get skills;

  @BuiltValueField(wireName: r'experiences')
  BuiltList<EngCareer>? get experiences;

  @BuiltValueField(wireName: r'requirements')
  EngHope? get requirements;

  @BuiltValueField(wireName: r'last_academic_code')
  String? get lastAcademicCode;

  @BuiltValueField(wireName: r'pr')
  String? get pr;

  @BuiltValueField(wireName: r'self_introduction_url')
  String? get selfIntroductionUrl;

  @BuiltValueField(wireName: r'international_tel')
  String? get internationalTel;

  @BuiltValueField(wireName: r'updated')
  DateTime? get updated;

  @BuiltValueField(wireName: r'user_type')
  int get userType;

  @BuiltValueField(wireName: r'facebook_url')
  String? get facebookUrl;

  @BuiltValueField(wireName: r'linkedin_url')
  String? get linkedinUrl;

  @BuiltValueField(wireName: r'whatsapp_url')
  String? get whatsappUrl;

  @BuiltValueField(wireName: r'last_academic_name')
  String? get lastAcademicName;

  @BuiltValueField(wireName: r'self_assesment')
  EngSelfAssesment? get selfAssesment;

  @BuiltValueField(wireName: r'email_temp')
  String? get emailTemp;

  @BuiltValueField(wireName: r'category_skills')
  BuiltList<CategorySkill>? get categorySkills;

  @BuiltValueField(wireName: r'skills_for_cv_display')
  String? get skillsForCvDisplay;

  @BuiltValueField(wireName: r'professional_summary')
  String? get professionalSummary;

  @BuiltValueField(wireName: r'highlight_projects')
  BuiltList<EngHighLightProject>? get highlightProjects;

  UserDetailsSerializers._();

  factory UserDetailsSerializers([void updates(UserDetailsSerializersBuilder b)]) = _$UserDetailsSerializers;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(UserDetailsSerializersBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<UserDetailsSerializers> get serializer => _$UserDetailsSerializersSerializer();
}

class _$UserDetailsSerializersSerializer implements PrimitiveSerializer<UserDetailsSerializers> {
  @override
  final Iterable<Type> types = const [UserDetailsSerializers, _$UserDetailsSerializers];

  @override
  final String wireName = r'UserDetailsSerializers';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    UserDetailsSerializers object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.profileImagePath != null) {
      yield r'profile_image_path';
      yield serializers.serialize(
        object.profileImagePath,
        specifiedType: const FullType.nullable(String),
      );
    }
    yield r'email';
    yield serializers.serialize(
      object.email,
      specifiedType: const FullType(String),
    );
    if (object.firstName != null) {
      yield r'first_name';
      yield serializers.serialize(
        object.firstName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.lastName != null) {
      yield r'last_name';
      yield serializers.serialize(
        object.lastName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.nickname != null) {
      yield r'nickname';
      yield serializers.serialize(
        object.nickname,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.sexType != null) {
      yield r'sex_type';
      yield serializers.serialize(
        object.sexType,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.birthDate != null) {
      yield r'birth_date';
      yield serializers.serialize(
        object.birthDate,
        specifiedType: const FullType.nullable(Date),
      );
    }
    if (object.countryCode != null) {
      yield r'country_code';
      yield serializers.serialize(
        object.countryCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.tel != null) {
      yield r'tel';
      yield serializers.serialize(
        object.tel,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.addressCode != null) {
      yield r'address_code';
      yield serializers.serialize(
        object.addressCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.cityName != null) {
      yield r'city_name';
      yield serializers.serialize(
        object.cityName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.passportNumber != null) {
      yield r'passport_number';
      yield serializers.serialize(
        object.passportNumber,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.passportImagePath != null) {
      yield r'passport_image_path';
      yield serializers.serialize(
        object.passportImagePath,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.educations != null) {
      yield r'educations';
      yield serializers.serialize(
        object.educations,
        specifiedType: const FullType(BuiltList, [FullType(EngAcademic)]),
      );
    }
    if (object.languages != null) {
      yield r'languages';
      yield serializers.serialize(
        object.languages,
        specifiedType: const FullType(BuiltList, [FullType(EngLanguage)]),
      );
    }
    if (object.qualifications != null) {
      yield r'qualifications';
      yield serializers.serialize(
        object.qualifications,
        specifiedType: const FullType(BuiltList, [FullType(EngLicense)]),
      );
    }
    if (object.skills != null) {
      yield r'skills';
      yield serializers.serialize(
        object.skills,
        specifiedType: const FullType(BuiltList, [FullType(EngSkill)]),
      );
    }
    if (object.experiences != null) {
      yield r'experiences';
      yield serializers.serialize(
        object.experiences,
        specifiedType: const FullType(BuiltList, [FullType(EngCareer)]),
      );
    }
    if (object.requirements != null) {
      yield r'requirements';
      yield serializers.serialize(
        object.requirements,
        specifiedType: const FullType(EngHope),
      );
    }
    if (object.lastAcademicCode != null) {
      yield r'last_academic_code';
      yield serializers.serialize(
        object.lastAcademicCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.pr != null) {
      yield r'pr';
      yield serializers.serialize(
        object.pr,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.selfIntroductionUrl != null) {
      yield r'self_introduction_url';
      yield serializers.serialize(
        object.selfIntroductionUrl,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.internationalTel != null) {
      yield r'international_tel';
      yield serializers.serialize(
        object.internationalTel,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.updated != null) {
      yield r'updated';
      yield serializers.serialize(
        object.updated,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    yield r'user_type';
    yield serializers.serialize(
      object.userType,
      specifiedType: const FullType(int),
    );
    if (object.facebookUrl != null) {
      yield r'facebook_url';
      yield serializers.serialize(
        object.facebookUrl,
        specifiedType: const FullType(String),
      );
    }
    if (object.linkedinUrl != null) {
      yield r'linkedin_url';
      yield serializers.serialize(
        object.linkedinUrl,
        specifiedType: const FullType(String),
      );
    }
    if (object.whatsappUrl != null) {
      yield r'whatsapp_url';
      yield serializers.serialize(
        object.whatsappUrl,
        specifiedType: const FullType(String),
      );
    }
    if (object.lastAcademicName != null) {
      yield r'last_academic_name';
      yield serializers.serialize(
        object.lastAcademicName,
        specifiedType: const FullType(String),
      );
    }
    if (object.selfAssesment != null) {
      yield r'self_assesment';
      yield serializers.serialize(
        object.selfAssesment,
        specifiedType: const FullType(EngSelfAssesment),
      );
    }
    if (object.emailTemp != null) {
      yield r'email_temp';
      yield serializers.serialize(
        object.emailTemp,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.categorySkills != null) {
      yield r'category_skills';
      yield serializers.serialize(
        object.categorySkills,
        specifiedType: const FullType(BuiltList, [FullType(CategorySkill)]),
      );
    }
    if (object.skillsForCvDisplay != null) {
      yield r'skills_for_cv_display';
      yield serializers.serialize(
        object.skillsForCvDisplay,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.professionalSummary != null) {
      yield r'professional_summary';
      yield serializers.serialize(
        object.professionalSummary,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.highlightProjects != null) {
      yield r'highlight_projects';
      yield serializers.serialize(
        object.highlightProjects,
        specifiedType: const FullType(BuiltList, [FullType(EngHighLightProject)]),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    UserDetailsSerializers object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required UserDetailsSerializersBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'profile_image_path':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.profileImagePath = valueDes;
          break;
        case r'email':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.email = valueDes;
          break;
        case r'first_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.firstName = valueDes;
          break;
        case r'last_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.lastName = valueDes;
          break;
        case r'nickname':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.nickname = valueDes;
          break;
        case r'sex_type':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.sexType = valueDes;
          break;
        case r'birth_date':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(Date),
          ) as Date?;
          if (valueDes == null) continue;
          result.birthDate = valueDes;
          break;
        case r'country_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.countryCode = valueDes;
          break;
        case r'tel':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.tel = valueDes;
          break;
        case r'address_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.addressCode = valueDes;
          break;
        case r'city_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.cityName = valueDes;
          break;
        case r'passport_number':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.passportNumber = valueDes;
          break;
        case r'passport_image_path':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.passportImagePath = valueDes;
          break;
        case r'educations':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(EngAcademic)]),
          ) as BuiltList<EngAcademic>;
          result.educations.replace(valueDes);
          break;
        case r'languages':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(EngLanguage)]),
          ) as BuiltList<EngLanguage>;
          result.languages.replace(valueDes);
          break;
        case r'qualifications':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(EngLicense)]),
          ) as BuiltList<EngLicense>;
          result.qualifications.replace(valueDes);
          break;
        case r'skills':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(EngSkill)]),
          ) as BuiltList<EngSkill>;
          result.skills.replace(valueDes);
          break;
        case r'experiences':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(EngCareer)]),
          ) as BuiltList<EngCareer>;
          result.experiences.replace(valueDes);
          break;
        case r'requirements':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(EngHope),
          ) as EngHope;
          result.requirements.replace(valueDes);
          break;
        case r'last_academic_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.lastAcademicCode = valueDes;
          break;
        case r'pr':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.pr = valueDes;
          break;
        case r'self_introduction_url':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.selfIntroductionUrl = valueDes;
          break;
        case r'international_tel':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.internationalTel = valueDes;
          break;
        case r'updated':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.updated = valueDes;
          break;
        case r'user_type':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.userType = valueDes;
          break;
        case r'facebook_url':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.facebookUrl = valueDes;
          break;
        case r'linkedin_url':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.linkedinUrl = valueDes;
          break;
        case r'whatsapp_url':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.whatsappUrl = valueDes;
          break;
        case r'last_academic_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.lastAcademicName = valueDes;
          break;
        case r'self_assesment':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(EngSelfAssesment),
          ) as EngSelfAssesment;
          result.selfAssesment.replace(valueDes);
          break;
        case r'email_temp':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.emailTemp = valueDes;
          break;
        case r'category_skills':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(CategorySkill)]),
          ) as BuiltList<CategorySkill>;
          result.categorySkills.replace(valueDes);
          break;
        case r'skills_for_cv_display':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.skillsForCvDisplay = valueDes;
          break;
        case r'professional_summary':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.professionalSummary = valueDes;
          break;
        case r'highlight_projects':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(EngHighLightProject)]),
          ) as BuiltList<EngHighLightProject>;
          result.highlightProjects.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  UserDetailsSerializers deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = UserDetailsSerializersBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

