// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'paging_get_list_engineer.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$PagingGetListEngineer extends PagingGetListEngineer {
  @override
  final String? next;
  @override
  final String? previous;
  @override
  final BuiltList<GetListEngineer> results;
  @override
  final int totalCount;

  factory _$PagingGetListEngineer(
          [void Function(PagingGetListEngineerBuilder)? updates]) =>
      (new PagingGetListEngineerBuilder()..update(updates))._build();

  _$PagingGetListEngineer._(
      {this.next,
      this.previous,
      required this.results,
      required this.totalCount})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        results, r'PagingGetListEngineer', 'results');
    BuiltValueNullFieldError.checkNotNull(
        totalCount, r'PagingGetListEngineer', 'totalCount');
  }

  @override
  PagingGetListEngineer rebuild(
          void Function(PagingGetListEngineerBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PagingGetListEngineerBuilder toBuilder() =>
      new PagingGetListEngineerBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PagingGetListEngineer &&
        next == other.next &&
        previous == other.previous &&
        results == other.results &&
        totalCount == other.totalCount;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, next.hashCode);
    _$hash = $jc(_$hash, previous.hashCode);
    _$hash = $jc(_$hash, results.hashCode);
    _$hash = $jc(_$hash, totalCount.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PagingGetListEngineer')
          ..add('next', next)
          ..add('previous', previous)
          ..add('results', results)
          ..add('totalCount', totalCount))
        .toString();
  }
}

class PagingGetListEngineerBuilder
    implements Builder<PagingGetListEngineer, PagingGetListEngineerBuilder> {
  _$PagingGetListEngineer? _$v;

  String? _next;
  String? get next => _$this._next;
  set next(String? next) => _$this._next = next;

  String? _previous;
  String? get previous => _$this._previous;
  set previous(String? previous) => _$this._previous = previous;

  ListBuilder<GetListEngineer>? _results;
  ListBuilder<GetListEngineer> get results =>
      _$this._results ??= new ListBuilder<GetListEngineer>();
  set results(ListBuilder<GetListEngineer>? results) =>
      _$this._results = results;

  int? _totalCount;
  int? get totalCount => _$this._totalCount;
  set totalCount(int? totalCount) => _$this._totalCount = totalCount;

  PagingGetListEngineerBuilder() {
    PagingGetListEngineer._defaults(this);
  }

  PagingGetListEngineerBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _next = $v.next;
      _previous = $v.previous;
      _results = $v.results.toBuilder();
      _totalCount = $v.totalCount;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PagingGetListEngineer other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$PagingGetListEngineer;
  }

  @override
  void update(void Function(PagingGetListEngineerBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PagingGetListEngineer build() => _build();

  _$PagingGetListEngineer _build() {
    _$PagingGetListEngineer _$result;
    try {
      _$result = _$v ??
          new _$PagingGetListEngineer._(
            next: next,
            previous: previous,
            results: results.build(),
            totalCount: BuiltValueNullFieldError.checkNotNull(
                totalCount, r'PagingGetListEngineer', 'totalCount'),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'results';
        results.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'PagingGetListEngineer', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
