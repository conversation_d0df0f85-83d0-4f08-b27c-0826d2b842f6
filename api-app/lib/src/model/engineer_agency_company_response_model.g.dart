// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'engineer_agency_company_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$EngineerAgencyCompanyResponseModel
    extends EngineerAgencyCompanyResponseModel {
  @override
  final String? message;
  @override
  final BuiltList<EngineerAgencyCompany> data;
  @override
  final BuiltList<ErrorDetail>? errors;

  factory _$EngineerAgencyCompanyResponseModel(
          [void Function(EngineerAgencyCompanyResponseModelBuilder)?
              updates]) =>
      (new EngineerAgencyCompanyResponseModelBuilder()..update(updates))
          ._build();

  _$EngineerAgencyCompanyResponseModel._(
      {this.message, required this.data, this.errors})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'EngineerAgencyCompanyResponseModel', 'data');
  }

  @override
  EngineerAgencyCompanyResponseModel rebuild(
          void Function(EngineerAgencyCompanyResponseModelBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  EngineerAgencyCompanyResponseModelBuilder toBuilder() =>
      new EngineerAgencyCompanyResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is EngineerAgencyCompanyResponseModel &&
        message == other.message &&
        data == other.data &&
        errors == other.errors;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'EngineerAgencyCompanyResponseModel')
          ..add('message', message)
          ..add('data', data)
          ..add('errors', errors))
        .toString();
  }
}

class EngineerAgencyCompanyResponseModelBuilder
    implements
        Builder<EngineerAgencyCompanyResponseModel,
            EngineerAgencyCompanyResponseModelBuilder> {
  _$EngineerAgencyCompanyResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  ListBuilder<EngineerAgencyCompany>? _data;
  ListBuilder<EngineerAgencyCompany> get data =>
      _$this._data ??= new ListBuilder<EngineerAgencyCompany>();
  set data(ListBuilder<EngineerAgencyCompany>? data) => _$this._data = data;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  EngineerAgencyCompanyResponseModelBuilder() {
    EngineerAgencyCompanyResponseModel._defaults(this);
  }

  EngineerAgencyCompanyResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _data = $v.data.toBuilder();
      _errors = $v.errors?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(EngineerAgencyCompanyResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$EngineerAgencyCompanyResponseModel;
  }

  @override
  void update(
      void Function(EngineerAgencyCompanyResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  EngineerAgencyCompanyResponseModel build() => _build();

  _$EngineerAgencyCompanyResponseModel _build() {
    _$EngineerAgencyCompanyResponseModel _$result;
    try {
      _$result = _$v ??
          new _$EngineerAgencyCompanyResponseModel._(
            message: message,
            data: data.build(),
            errors: _errors?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        data.build();
        _$failedField = 'errors';
        _errors?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'EngineerAgencyCompanyResponseModel', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
