// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'paging_general_company_explore_user_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$PagingGeneralCompanyExploreUserResponseModel
    extends PagingGeneralCompanyExploreUserResponseModel {
  @override
  final int count;
  @override
  final String? next;
  @override
  final String? previous;
  @override
  final String? aiSummary;
  @override
  final String? aiSummaryJa;
  @override
  final String? aiSummaryVi;
  @override
  final int? recruitId;
  @override
  final BuiltList<GeneralCompanyExploreUser> results;

  factory _$PagingGeneralCompanyExploreUserResponseModel(
          [void Function(PagingGeneralCompanyExploreUserResponseModelBuilder)?
              updates]) =>
      (new PagingGeneralCompanyExploreUserResponseModelBuilder()
            ..update(updates))
          ._build();

  _$PagingGeneralCompanyExploreUserResponseModel._(
      {required this.count,
      this.next,
      this.previous,
      this.aiSummary,
      this.aiSummaryJa,
      this.aiSummaryVi,
      this.recruitId,
      required this.results})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        count, r'PagingGeneralCompanyExploreUserResponseModel', 'count');
    BuiltValueNullFieldError.checkNotNull(
        results, r'PagingGeneralCompanyExploreUserResponseModel', 'results');
  }

  @override
  PagingGeneralCompanyExploreUserResponseModel rebuild(
          void Function(PagingGeneralCompanyExploreUserResponseModelBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PagingGeneralCompanyExploreUserResponseModelBuilder toBuilder() =>
      new PagingGeneralCompanyExploreUserResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PagingGeneralCompanyExploreUserResponseModel &&
        count == other.count &&
        next == other.next &&
        previous == other.previous &&
        aiSummary == other.aiSummary &&
        aiSummaryJa == other.aiSummaryJa &&
        aiSummaryVi == other.aiSummaryVi &&
        recruitId == other.recruitId &&
        results == other.results;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, count.hashCode);
    _$hash = $jc(_$hash, next.hashCode);
    _$hash = $jc(_$hash, previous.hashCode);
    _$hash = $jc(_$hash, aiSummary.hashCode);
    _$hash = $jc(_$hash, aiSummaryJa.hashCode);
    _$hash = $jc(_$hash, aiSummaryVi.hashCode);
    _$hash = $jc(_$hash, recruitId.hashCode);
    _$hash = $jc(_$hash, results.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'PagingGeneralCompanyExploreUserResponseModel')
          ..add('count', count)
          ..add('next', next)
          ..add('previous', previous)
          ..add('aiSummary', aiSummary)
          ..add('aiSummaryJa', aiSummaryJa)
          ..add('aiSummaryVi', aiSummaryVi)
          ..add('recruitId', recruitId)
          ..add('results', results))
        .toString();
  }
}

class PagingGeneralCompanyExploreUserResponseModelBuilder
    implements
        Builder<PagingGeneralCompanyExploreUserResponseModel,
            PagingGeneralCompanyExploreUserResponseModelBuilder> {
  _$PagingGeneralCompanyExploreUserResponseModel? _$v;

  int? _count;
  int? get count => _$this._count;
  set count(int? count) => _$this._count = count;

  String? _next;
  String? get next => _$this._next;
  set next(String? next) => _$this._next = next;

  String? _previous;
  String? get previous => _$this._previous;
  set previous(String? previous) => _$this._previous = previous;

  String? _aiSummary;
  String? get aiSummary => _$this._aiSummary;
  set aiSummary(String? aiSummary) => _$this._aiSummary = aiSummary;

  String? _aiSummaryJa;
  String? get aiSummaryJa => _$this._aiSummaryJa;
  set aiSummaryJa(String? aiSummaryJa) => _$this._aiSummaryJa = aiSummaryJa;

  String? _aiSummaryVi;
  String? get aiSummaryVi => _$this._aiSummaryVi;
  set aiSummaryVi(String? aiSummaryVi) => _$this._aiSummaryVi = aiSummaryVi;

  int? _recruitId;
  int? get recruitId => _$this._recruitId;
  set recruitId(int? recruitId) => _$this._recruitId = recruitId;

  ListBuilder<GeneralCompanyExploreUser>? _results;
  ListBuilder<GeneralCompanyExploreUser> get results =>
      _$this._results ??= new ListBuilder<GeneralCompanyExploreUser>();
  set results(ListBuilder<GeneralCompanyExploreUser>? results) =>
      _$this._results = results;

  PagingGeneralCompanyExploreUserResponseModelBuilder() {
    PagingGeneralCompanyExploreUserResponseModel._defaults(this);
  }

  PagingGeneralCompanyExploreUserResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _count = $v.count;
      _next = $v.next;
      _previous = $v.previous;
      _aiSummary = $v.aiSummary;
      _aiSummaryJa = $v.aiSummaryJa;
      _aiSummaryVi = $v.aiSummaryVi;
      _recruitId = $v.recruitId;
      _results = $v.results.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PagingGeneralCompanyExploreUserResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$PagingGeneralCompanyExploreUserResponseModel;
  }

  @override
  void update(
      void Function(PagingGeneralCompanyExploreUserResponseModelBuilder)?
          updates) {
    if (updates != null) updates(this);
  }

  @override
  PagingGeneralCompanyExploreUserResponseModel build() => _build();

  _$PagingGeneralCompanyExploreUserResponseModel _build() {
    _$PagingGeneralCompanyExploreUserResponseModel _$result;
    try {
      _$result = _$v ??
          new _$PagingGeneralCompanyExploreUserResponseModel._(
            count: BuiltValueNullFieldError.checkNotNull(count,
                r'PagingGeneralCompanyExploreUserResponseModel', 'count'),
            next: next,
            previous: previous,
            aiSummary: aiSummary,
            aiSummaryJa: aiSummaryJa,
            aiSummaryVi: aiSummaryVi,
            recruitId: recruitId,
            results: results.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'results';
        results.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'PagingGeneralCompanyExploreUserResponseModel',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
