// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'host_company_param_list_user_applied_company.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$HostCompanyParamListUserAppliedCompany
    extends HostCompanyParamListUserAppliedCompany {
  @override
  final BuiltList<int>? recruitIds;
  @override
  final String? ordering;
  @override
  final BuiltList<int>? applyStatusFilterCodes;
  @override
  final String? search;

  factory _$HostCompanyParamListUserAppliedCompany(
          [void Function(HostCompanyParamListUserAppliedCompanyBuilder)?
              updates]) =>
      (new HostCompanyParamListUserAppliedCompanyBuilder()..update(updates))
          ._build();

  _$HostCompanyParamListUserAppliedCompany._(
      {this.recruitIds,
      this.ordering,
      this.applyStatusFilterCodes,
      this.search})
      : super._();

  @override
  HostCompanyParamListUserAppliedCompany rebuild(
          void Function(HostCompanyParamListUserAppliedCompanyBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  HostCompanyParamListUserAppliedCompanyBuilder toBuilder() =>
      new HostCompanyParamListUserAppliedCompanyBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is HostCompanyParamListUserAppliedCompany &&
        recruitIds == other.recruitIds &&
        ordering == other.ordering &&
        applyStatusFilterCodes == other.applyStatusFilterCodes &&
        search == other.search;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, recruitIds.hashCode);
    _$hash = $jc(_$hash, ordering.hashCode);
    _$hash = $jc(_$hash, applyStatusFilterCodes.hashCode);
    _$hash = $jc(_$hash, search.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'HostCompanyParamListUserAppliedCompany')
          ..add('recruitIds', recruitIds)
          ..add('ordering', ordering)
          ..add('applyStatusFilterCodes', applyStatusFilterCodes)
          ..add('search', search))
        .toString();
  }
}

class HostCompanyParamListUserAppliedCompanyBuilder
    implements
        Builder<HostCompanyParamListUserAppliedCompany,
            HostCompanyParamListUserAppliedCompanyBuilder> {
  _$HostCompanyParamListUserAppliedCompany? _$v;

  ListBuilder<int>? _recruitIds;
  ListBuilder<int> get recruitIds =>
      _$this._recruitIds ??= new ListBuilder<int>();
  set recruitIds(ListBuilder<int>? recruitIds) =>
      _$this._recruitIds = recruitIds;

  String? _ordering;
  String? get ordering => _$this._ordering;
  set ordering(String? ordering) => _$this._ordering = ordering;

  ListBuilder<int>? _applyStatusFilterCodes;
  ListBuilder<int> get applyStatusFilterCodes =>
      _$this._applyStatusFilterCodes ??= new ListBuilder<int>();
  set applyStatusFilterCodes(ListBuilder<int>? applyStatusFilterCodes) =>
      _$this._applyStatusFilterCodes = applyStatusFilterCodes;

  String? _search;
  String? get search => _$this._search;
  set search(String? search) => _$this._search = search;

  HostCompanyParamListUserAppliedCompanyBuilder() {
    HostCompanyParamListUserAppliedCompany._defaults(this);
  }

  HostCompanyParamListUserAppliedCompanyBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _recruitIds = $v.recruitIds?.toBuilder();
      _ordering = $v.ordering;
      _applyStatusFilterCodes = $v.applyStatusFilterCodes?.toBuilder();
      _search = $v.search;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(HostCompanyParamListUserAppliedCompany other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$HostCompanyParamListUserAppliedCompany;
  }

  @override
  void update(
      void Function(HostCompanyParamListUserAppliedCompanyBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  HostCompanyParamListUserAppliedCompany build() => _build();

  _$HostCompanyParamListUserAppliedCompany _build() {
    _$HostCompanyParamListUserAppliedCompany _$result;
    try {
      _$result = _$v ??
          new _$HostCompanyParamListUserAppliedCompany._(
            recruitIds: _recruitIds?.build(),
            ordering: ordering,
            applyStatusFilterCodes: _applyStatusFilterCodes?.build(),
            search: search,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'recruitIds';
        _recruitIds?.build();

        _$failedField = 'applyStatusFilterCodes';
        _applyStatusFilterCodes?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'HostCompanyParamListUserAppliedCompany',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
