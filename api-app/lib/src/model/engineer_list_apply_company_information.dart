//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'engineer_list_apply_company_information.g.dart';

/// EngineerListApplyCompanyInformation
///
/// Properties:
/// * [name] 
/// * [logoImagePath] 
@BuiltValue()
abstract class EngineerListApplyCompanyInformation implements Built<EngineerListApplyCompanyInformation, EngineerListApplyCompanyInformationBuilder> {
  @BuiltValueField(wireName: r'name')
  String? get name;

  @BuiltValueField(wireName: r'logo_image_path')
  String? get logoImagePath;

  EngineerListApplyCompanyInformation._();

  factory EngineerListApplyCompanyInformation([void updates(EngineerListApplyCompanyInformationBuilder b)]) = _$EngineerListApplyCompanyInformation;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(EngineerListApplyCompanyInformationBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<EngineerListApplyCompanyInformation> get serializer => _$EngineerListApplyCompanyInformationSerializer();
}

class _$EngineerListApplyCompanyInformationSerializer implements PrimitiveSerializer<EngineerListApplyCompanyInformation> {
  @override
  final Iterable<Type> types = const [EngineerListApplyCompanyInformation, _$EngineerListApplyCompanyInformation];

  @override
  final String wireName = r'EngineerListApplyCompanyInformation';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    EngineerListApplyCompanyInformation object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.name != null) {
      yield r'name';
      yield serializers.serialize(
        object.name,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.logoImagePath != null) {
      yield r'logo_image_path';
      yield serializers.serialize(
        object.logoImagePath,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    EngineerListApplyCompanyInformation object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required EngineerListApplyCompanyInformationBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.name = valueDes;
          break;
        case r'logo_image_path':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.logoImagePath = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  EngineerListApplyCompanyInformation deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = EngineerListApplyCompanyInformationBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

