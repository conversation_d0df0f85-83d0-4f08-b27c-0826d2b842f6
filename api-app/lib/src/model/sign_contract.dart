//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/recruit_company.dart';
import 'package:openapi/src/model/date.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'sign_contract.g.dart';

/// SignContract
///
/// Properties:
/// * [applyId] 
/// * [jobCode] 
/// * [employCode] 
/// * [payrollPrice] 
/// * [payrollCode] 
/// * [placeCode] 
/// * [joingDate] 
/// * [hostCompany] 
/// * [interviewDatetime] 
/// * [recruitId] 
/// * [recruitProgressCode] 
@BuiltValue()
abstract class SignContract implements Built<SignContract, SignContractBuilder> {
  @BuiltValueField(wireName: r'apply_id')
  int? get applyId;

  @BuiltValueField(wireName: r'job_code')
  String? get jobCode;

  @BuiltValueField(wireName: r'employ_code')
  String? get employCode;

  @BuiltValueField(wireName: r'payroll_price')
  String? get payrollPrice;

  @BuiltValueField(wireName: r'payroll_code')
  String? get payrollCode;

  @BuiltValueField(wireName: r'place_code')
  String? get placeCode;

  @BuiltValueField(wireName: r'joing_date')
  Date? get joingDate;

  @BuiltValueField(wireName: r'host_company')
  RecruitCompany get hostCompany;

  @BuiltValueField(wireName: r'interview_datetime')
  DateTime? get interviewDatetime;

  @BuiltValueField(wireName: r'recruit_id')
  String? get recruitId;

  @BuiltValueField(wireName: r'recruit_progress_code')
  int? get recruitProgressCode;

  SignContract._();

  factory SignContract([void updates(SignContractBuilder b)]) = _$SignContract;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(SignContractBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<SignContract> get serializer => _$SignContractSerializer();
}

class _$SignContractSerializer implements PrimitiveSerializer<SignContract> {
  @override
  final Iterable<Type> types = const [SignContract, _$SignContract];

  @override
  final String wireName = r'SignContract';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    SignContract object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.applyId != null) {
      yield r'apply_id';
      yield serializers.serialize(
        object.applyId,
        specifiedType: const FullType(int),
      );
    }
    if (object.jobCode != null) {
      yield r'job_code';
      yield serializers.serialize(
        object.jobCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.employCode != null) {
      yield r'employ_code';
      yield serializers.serialize(
        object.employCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    yield r'payroll_price';
    yield object.payrollPrice == null ? null : serializers.serialize(
      object.payrollPrice,
      specifiedType: const FullType.nullable(String),
    );
    if (object.payrollCode != null) {
      yield r'payroll_code';
      yield serializers.serialize(
        object.payrollCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.placeCode != null) {
      yield r'place_code';
      yield serializers.serialize(
        object.placeCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.joingDate != null) {
      yield r'joing_date';
      yield serializers.serialize(
        object.joingDate,
        specifiedType: const FullType.nullable(Date),
      );
    }
    yield r'host_company';
    yield serializers.serialize(
      object.hostCompany,
      specifiedType: const FullType(RecruitCompany),
    );
    if (object.interviewDatetime != null) {
      yield r'interview_datetime';
      yield serializers.serialize(
        object.interviewDatetime,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.recruitId != null) {
      yield r'recruit_id';
      yield serializers.serialize(
        object.recruitId,
        specifiedType: const FullType(String),
      );
    }
    if (object.recruitProgressCode != null) {
      yield r'recruit_progress_code';
      yield serializers.serialize(
        object.recruitProgressCode,
        specifiedType: const FullType.nullable(int),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    SignContract object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required SignContractBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'apply_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.applyId = valueDes;
          break;
        case r'job_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.jobCode = valueDes;
          break;
        case r'employ_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.employCode = valueDes;
          break;
        case r'payroll_price':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.payrollPrice = valueDes;
          break;
        case r'payroll_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.payrollCode = valueDes;
          break;
        case r'place_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.placeCode = valueDes;
          break;
        case r'joing_date':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(Date),
          ) as Date?;
          if (valueDes == null) continue;
          result.joingDate = valueDes;
          break;
        case r'host_company':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(RecruitCompany),
          ) as RecruitCompany;
          result.hostCompany.replace(valueDes);
          break;
        case r'interview_datetime':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.interviewDatetime = valueDes;
          break;
        case r'recruit_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.recruitId = valueDes;
          break;
        case r'recruit_progress_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.recruitProgressCode = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  SignContract deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = SignContractBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

