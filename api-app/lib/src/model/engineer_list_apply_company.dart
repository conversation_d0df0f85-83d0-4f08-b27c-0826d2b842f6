//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/engineer_list_apply_company_information.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'engineer_list_apply_company.g.dart';

/// EngineerListApplyCompany
///
/// Properties:
/// * [applyId] 
/// * [engineerId] 
/// * [recruitId] 
/// * [payrollPriceFrom] 
/// * [payrollPriceTo] 
/// * [payrollCode] 
/// * [hostCompanyId] 
/// * [recruitProgressCode] 
/// * [updated] 
/// * [progressUpdateDatetime] 
/// * [isClosed] 
/// * [isRead] 
/// * [hostCompany] 
@BuiltValue()
abstract class EngineerListApplyCompany implements Built<EngineerListApplyCompany, EngineerListApplyCompanyBuilder> {
  @BuiltValueField(wireName: r'apply_id')
  int? get applyId;

  @BuiltValueField(wireName: r'engineer_id')
  int? get engineerId;

  @BuiltValueField(wireName: r'recruit_id')
  int? get recruitId;

  @BuiltValueField(wireName: r'payroll_price_from')
  String? get payrollPriceFrom;

  @BuiltValueField(wireName: r'payroll_price_to')
  String? get payrollPriceTo;

  @BuiltValueField(wireName: r'payroll_code')
  String? get payrollCode;

  @BuiltValueField(wireName: r'host_company_id')
  int? get hostCompanyId;

  @BuiltValueField(wireName: r'recruit_progress_code')
  int? get recruitProgressCode;

  @BuiltValueField(wireName: r'updated')
  DateTime? get updated;

  @BuiltValueField(wireName: r'progress_update_datetime')
  DateTime? get progressUpdateDatetime;

  @BuiltValueField(wireName: r'is_closed')
  bool? get isClosed;

  @BuiltValueField(wireName: r'is_read')
  bool? get isRead;

  @BuiltValueField(wireName: r'host_company')
  EngineerListApplyCompanyInformation? get hostCompany;

  EngineerListApplyCompany._();

  factory EngineerListApplyCompany([void updates(EngineerListApplyCompanyBuilder b)]) = _$EngineerListApplyCompany;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(EngineerListApplyCompanyBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<EngineerListApplyCompany> get serializer => _$EngineerListApplyCompanySerializer();
}

class _$EngineerListApplyCompanySerializer implements PrimitiveSerializer<EngineerListApplyCompany> {
  @override
  final Iterable<Type> types = const [EngineerListApplyCompany, _$EngineerListApplyCompany];

  @override
  final String wireName = r'EngineerListApplyCompany';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    EngineerListApplyCompany object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.applyId != null) {
      yield r'apply_id';
      yield serializers.serialize(
        object.applyId,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.engineerId != null) {
      yield r'engineer_id';
      yield serializers.serialize(
        object.engineerId,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.recruitId != null) {
      yield r'recruit_id';
      yield serializers.serialize(
        object.recruitId,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.payrollPriceFrom != null) {
      yield r'payroll_price_from';
      yield serializers.serialize(
        object.payrollPriceFrom,
        specifiedType: const FullType(String),
      );
    }
    if (object.payrollPriceTo != null) {
      yield r'payroll_price_to';
      yield serializers.serialize(
        object.payrollPriceTo,
        specifiedType: const FullType(String),
      );
    }
    if (object.payrollCode != null) {
      yield r'payroll_code';
      yield serializers.serialize(
        object.payrollCode,
        specifiedType: const FullType(String),
      );
    }
    if (object.hostCompanyId != null) {
      yield r'host_company_id';
      yield serializers.serialize(
        object.hostCompanyId,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.recruitProgressCode != null) {
      yield r'recruit_progress_code';
      yield serializers.serialize(
        object.recruitProgressCode,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.updated != null) {
      yield r'updated';
      yield serializers.serialize(
        object.updated,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.progressUpdateDatetime != null) {
      yield r'progress_update_datetime';
      yield serializers.serialize(
        object.progressUpdateDatetime,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    yield r'is_closed';
    yield object.isClosed == null ? null : serializers.serialize(
      object.isClosed,
      specifiedType: const FullType.nullable(bool),
    );
    yield r'is_read';
    yield object.isRead == null ? null : serializers.serialize(
      object.isRead,
      specifiedType: const FullType.nullable(bool),
    );
    if (object.hostCompany != null) {
      yield r'host_company';
      yield serializers.serialize(
        object.hostCompany,
        specifiedType: const FullType.nullable(EngineerListApplyCompanyInformation),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    EngineerListApplyCompany object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required EngineerListApplyCompanyBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'apply_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.applyId = valueDes;
          break;
        case r'engineer_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.engineerId = valueDes;
          break;
        case r'recruit_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.recruitId = valueDes;
          break;
        case r'payroll_price_from':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.payrollPriceFrom = valueDes;
          break;
        case r'payroll_price_to':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.payrollPriceTo = valueDes;
          break;
        case r'payroll_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.payrollCode = valueDes;
          break;
        case r'host_company_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.hostCompanyId = valueDes;
          break;
        case r'recruit_progress_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.recruitProgressCode = valueDes;
          break;
        case r'updated':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.updated = valueDes;
          break;
        case r'progress_update_datetime':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.progressUpdateDatetime = valueDes;
          break;
        case r'is_closed':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(bool),
          ) as bool?;
          if (valueDes == null) continue;
          result.isClosed = valueDes;
          break;
        case r'is_read':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(bool),
          ) as bool?;
          if (valueDes == null) continue;
          result.isRead = valueDes;
          break;
        case r'host_company':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(EngineerListApplyCompanyInformation),
          ) as EngineerListApplyCompanyInformation?;
          if (valueDes == null) continue;
          result.hostCompany.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  EngineerListApplyCompany deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = EngineerListApplyCompanyBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

