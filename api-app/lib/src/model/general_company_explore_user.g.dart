// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_company_explore_user.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GeneralCompanyExploreUser extends GeneralCompanyExploreUser {
  @override
  final int? userId;
  @override
  final String? profileImagePath;
  @override
  final String? firstName;
  @override
  final String? lastName;
  @override
  final int? age;
  @override
  final DateTime? created;
  @override
  final BuiltList<EngLanguage>? languages;
  @override
  final String? lastAcademicCode;
  @override
  final int? interestedFlag;
  @override
  final BuiltList<EngSkill>? skills;
  @override
  final String? countryCode;
  @override
  final String? academicLevelName;
  @override
  final EngHope? requirements;
  @override
  final String? nickname;
  @override
  final String? schoolName;
  @override
  final int? existApplyId;
  @override
  final DateTime? updated;
  @override
  final BuiltList<EngLicense>? qualifications;
  @override
  final String? payrollPriceUsd;
  @override
  final DateTime? lastLogin;
  @override
  final int? totalApplyCount;
  @override
  final int? totalCompanyChangedCount;
  @override
  final int? jobCodeHasWorkedRecently;
  @override
  final int? totalRecruitProgressCodeActive;
  @override
  final int? currentRecruitProgressCodeWithMyCompany;
  @override
  final int? totalMatchSkillCodes;
  @override
  final int? remoteCodeScore;
  @override
  final int? salaryScore;
  @override
  final int? totalScore;
  @override
  final int? sexType;
  @override
  final String? addressCode;
  @override
  final BuiltList<MainSkill>? mainSkill;
  @override
  final BuiltList<String>? hopeJobCodes;
  @override
  final BuiltList<EngWorkHistory>? workHistories;
  @override
  final String? reasonAiRecommend;
  @override
  final String? reasonAiRecommendJa;
  @override
  final String? reasonAiRecommendVi;
  @override
  final int? recruitId;

  factory _$GeneralCompanyExploreUser(
          [void Function(GeneralCompanyExploreUserBuilder)? updates]) =>
      (new GeneralCompanyExploreUserBuilder()..update(updates))._build();

  _$GeneralCompanyExploreUser._(
      {this.userId,
      this.profileImagePath,
      this.firstName,
      this.lastName,
      this.age,
      this.created,
      this.languages,
      this.lastAcademicCode,
      this.interestedFlag,
      this.skills,
      this.countryCode,
      this.academicLevelName,
      this.requirements,
      this.nickname,
      this.schoolName,
      this.existApplyId,
      this.updated,
      this.qualifications,
      this.payrollPriceUsd,
      this.lastLogin,
      this.totalApplyCount,
      this.totalCompanyChangedCount,
      this.jobCodeHasWorkedRecently,
      this.totalRecruitProgressCodeActive,
      this.currentRecruitProgressCodeWithMyCompany,
      this.totalMatchSkillCodes,
      this.remoteCodeScore,
      this.salaryScore,
      this.totalScore,
      this.sexType,
      this.addressCode,
      this.mainSkill,
      this.hopeJobCodes,
      this.workHistories,
      this.reasonAiRecommend,
      this.reasonAiRecommendJa,
      this.reasonAiRecommendVi,
      this.recruitId})
      : super._();

  @override
  GeneralCompanyExploreUser rebuild(
          void Function(GeneralCompanyExploreUserBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GeneralCompanyExploreUserBuilder toBuilder() =>
      new GeneralCompanyExploreUserBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GeneralCompanyExploreUser &&
        userId == other.userId &&
        profileImagePath == other.profileImagePath &&
        firstName == other.firstName &&
        lastName == other.lastName &&
        age == other.age &&
        created == other.created &&
        languages == other.languages &&
        lastAcademicCode == other.lastAcademicCode &&
        interestedFlag == other.interestedFlag &&
        skills == other.skills &&
        countryCode == other.countryCode &&
        academicLevelName == other.academicLevelName &&
        requirements == other.requirements &&
        nickname == other.nickname &&
        schoolName == other.schoolName &&
        existApplyId == other.existApplyId &&
        updated == other.updated &&
        qualifications == other.qualifications &&
        payrollPriceUsd == other.payrollPriceUsd &&
        lastLogin == other.lastLogin &&
        totalApplyCount == other.totalApplyCount &&
        totalCompanyChangedCount == other.totalCompanyChangedCount &&
        jobCodeHasWorkedRecently == other.jobCodeHasWorkedRecently &&
        totalRecruitProgressCodeActive ==
            other.totalRecruitProgressCodeActive &&
        currentRecruitProgressCodeWithMyCompany ==
            other.currentRecruitProgressCodeWithMyCompany &&
        totalMatchSkillCodes == other.totalMatchSkillCodes &&
        remoteCodeScore == other.remoteCodeScore &&
        salaryScore == other.salaryScore &&
        totalScore == other.totalScore &&
        sexType == other.sexType &&
        addressCode == other.addressCode &&
        mainSkill == other.mainSkill &&
        hopeJobCodes == other.hopeJobCodes &&
        workHistories == other.workHistories &&
        reasonAiRecommend == other.reasonAiRecommend &&
        reasonAiRecommendJa == other.reasonAiRecommendJa &&
        reasonAiRecommendVi == other.reasonAiRecommendVi &&
        recruitId == other.recruitId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, userId.hashCode);
    _$hash = $jc(_$hash, profileImagePath.hashCode);
    _$hash = $jc(_$hash, firstName.hashCode);
    _$hash = $jc(_$hash, lastName.hashCode);
    _$hash = $jc(_$hash, age.hashCode);
    _$hash = $jc(_$hash, created.hashCode);
    _$hash = $jc(_$hash, languages.hashCode);
    _$hash = $jc(_$hash, lastAcademicCode.hashCode);
    _$hash = $jc(_$hash, interestedFlag.hashCode);
    _$hash = $jc(_$hash, skills.hashCode);
    _$hash = $jc(_$hash, countryCode.hashCode);
    _$hash = $jc(_$hash, academicLevelName.hashCode);
    _$hash = $jc(_$hash, requirements.hashCode);
    _$hash = $jc(_$hash, nickname.hashCode);
    _$hash = $jc(_$hash, schoolName.hashCode);
    _$hash = $jc(_$hash, existApplyId.hashCode);
    _$hash = $jc(_$hash, updated.hashCode);
    _$hash = $jc(_$hash, qualifications.hashCode);
    _$hash = $jc(_$hash, payrollPriceUsd.hashCode);
    _$hash = $jc(_$hash, lastLogin.hashCode);
    _$hash = $jc(_$hash, totalApplyCount.hashCode);
    _$hash = $jc(_$hash, totalCompanyChangedCount.hashCode);
    _$hash = $jc(_$hash, jobCodeHasWorkedRecently.hashCode);
    _$hash = $jc(_$hash, totalRecruitProgressCodeActive.hashCode);
    _$hash = $jc(_$hash, currentRecruitProgressCodeWithMyCompany.hashCode);
    _$hash = $jc(_$hash, totalMatchSkillCodes.hashCode);
    _$hash = $jc(_$hash, remoteCodeScore.hashCode);
    _$hash = $jc(_$hash, salaryScore.hashCode);
    _$hash = $jc(_$hash, totalScore.hashCode);
    _$hash = $jc(_$hash, sexType.hashCode);
    _$hash = $jc(_$hash, addressCode.hashCode);
    _$hash = $jc(_$hash, mainSkill.hashCode);
    _$hash = $jc(_$hash, hopeJobCodes.hashCode);
    _$hash = $jc(_$hash, workHistories.hashCode);
    _$hash = $jc(_$hash, reasonAiRecommend.hashCode);
    _$hash = $jc(_$hash, reasonAiRecommendJa.hashCode);
    _$hash = $jc(_$hash, reasonAiRecommendVi.hashCode);
    _$hash = $jc(_$hash, recruitId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GeneralCompanyExploreUser')
          ..add('userId', userId)
          ..add('profileImagePath', profileImagePath)
          ..add('firstName', firstName)
          ..add('lastName', lastName)
          ..add('age', age)
          ..add('created', created)
          ..add('languages', languages)
          ..add('lastAcademicCode', lastAcademicCode)
          ..add('interestedFlag', interestedFlag)
          ..add('skills', skills)
          ..add('countryCode', countryCode)
          ..add('academicLevelName', academicLevelName)
          ..add('requirements', requirements)
          ..add('nickname', nickname)
          ..add('schoolName', schoolName)
          ..add('existApplyId', existApplyId)
          ..add('updated', updated)
          ..add('qualifications', qualifications)
          ..add('payrollPriceUsd', payrollPriceUsd)
          ..add('lastLogin', lastLogin)
          ..add('totalApplyCount', totalApplyCount)
          ..add('totalCompanyChangedCount', totalCompanyChangedCount)
          ..add('jobCodeHasWorkedRecently', jobCodeHasWorkedRecently)
          ..add(
              'totalRecruitProgressCodeActive', totalRecruitProgressCodeActive)
          ..add('currentRecruitProgressCodeWithMyCompany',
              currentRecruitProgressCodeWithMyCompany)
          ..add('totalMatchSkillCodes', totalMatchSkillCodes)
          ..add('remoteCodeScore', remoteCodeScore)
          ..add('salaryScore', salaryScore)
          ..add('totalScore', totalScore)
          ..add('sexType', sexType)
          ..add('addressCode', addressCode)
          ..add('mainSkill', mainSkill)
          ..add('hopeJobCodes', hopeJobCodes)
          ..add('workHistories', workHistories)
          ..add('reasonAiRecommend', reasonAiRecommend)
          ..add('reasonAiRecommendJa', reasonAiRecommendJa)
          ..add('reasonAiRecommendVi', reasonAiRecommendVi)
          ..add('recruitId', recruitId))
        .toString();
  }
}

class GeneralCompanyExploreUserBuilder
    implements
        Builder<GeneralCompanyExploreUser, GeneralCompanyExploreUserBuilder> {
  _$GeneralCompanyExploreUser? _$v;

  int? _userId;
  int? get userId => _$this._userId;
  set userId(int? userId) => _$this._userId = userId;

  String? _profileImagePath;
  String? get profileImagePath => _$this._profileImagePath;
  set profileImagePath(String? profileImagePath) =>
      _$this._profileImagePath = profileImagePath;

  String? _firstName;
  String? get firstName => _$this._firstName;
  set firstName(String? firstName) => _$this._firstName = firstName;

  String? _lastName;
  String? get lastName => _$this._lastName;
  set lastName(String? lastName) => _$this._lastName = lastName;

  int? _age;
  int? get age => _$this._age;
  set age(int? age) => _$this._age = age;

  DateTime? _created;
  DateTime? get created => _$this._created;
  set created(DateTime? created) => _$this._created = created;

  ListBuilder<EngLanguage>? _languages;
  ListBuilder<EngLanguage> get languages =>
      _$this._languages ??= new ListBuilder<EngLanguage>();
  set languages(ListBuilder<EngLanguage>? languages) =>
      _$this._languages = languages;

  String? _lastAcademicCode;
  String? get lastAcademicCode => _$this._lastAcademicCode;
  set lastAcademicCode(String? lastAcademicCode) =>
      _$this._lastAcademicCode = lastAcademicCode;

  int? _interestedFlag;
  int? get interestedFlag => _$this._interestedFlag;
  set interestedFlag(int? interestedFlag) =>
      _$this._interestedFlag = interestedFlag;

  ListBuilder<EngSkill>? _skills;
  ListBuilder<EngSkill> get skills =>
      _$this._skills ??= new ListBuilder<EngSkill>();
  set skills(ListBuilder<EngSkill>? skills) => _$this._skills = skills;

  String? _countryCode;
  String? get countryCode => _$this._countryCode;
  set countryCode(String? countryCode) => _$this._countryCode = countryCode;

  String? _academicLevelName;
  String? get academicLevelName => _$this._academicLevelName;
  set academicLevelName(String? academicLevelName) =>
      _$this._academicLevelName = academicLevelName;

  EngHopeBuilder? _requirements;
  EngHopeBuilder get requirements =>
      _$this._requirements ??= new EngHopeBuilder();
  set requirements(EngHopeBuilder? requirements) =>
      _$this._requirements = requirements;

  String? _nickname;
  String? get nickname => _$this._nickname;
  set nickname(String? nickname) => _$this._nickname = nickname;

  String? _schoolName;
  String? get schoolName => _$this._schoolName;
  set schoolName(String? schoolName) => _$this._schoolName = schoolName;

  int? _existApplyId;
  int? get existApplyId => _$this._existApplyId;
  set existApplyId(int? existApplyId) => _$this._existApplyId = existApplyId;

  DateTime? _updated;
  DateTime? get updated => _$this._updated;
  set updated(DateTime? updated) => _$this._updated = updated;

  ListBuilder<EngLicense>? _qualifications;
  ListBuilder<EngLicense> get qualifications =>
      _$this._qualifications ??= new ListBuilder<EngLicense>();
  set qualifications(ListBuilder<EngLicense>? qualifications) =>
      _$this._qualifications = qualifications;

  String? _payrollPriceUsd;
  String? get payrollPriceUsd => _$this._payrollPriceUsd;
  set payrollPriceUsd(String? payrollPriceUsd) =>
      _$this._payrollPriceUsd = payrollPriceUsd;

  DateTime? _lastLogin;
  DateTime? get lastLogin => _$this._lastLogin;
  set lastLogin(DateTime? lastLogin) => _$this._lastLogin = lastLogin;

  int? _totalApplyCount;
  int? get totalApplyCount => _$this._totalApplyCount;
  set totalApplyCount(int? totalApplyCount) =>
      _$this._totalApplyCount = totalApplyCount;

  int? _totalCompanyChangedCount;
  int? get totalCompanyChangedCount => _$this._totalCompanyChangedCount;
  set totalCompanyChangedCount(int? totalCompanyChangedCount) =>
      _$this._totalCompanyChangedCount = totalCompanyChangedCount;

  int? _jobCodeHasWorkedRecently;
  int? get jobCodeHasWorkedRecently => _$this._jobCodeHasWorkedRecently;
  set jobCodeHasWorkedRecently(int? jobCodeHasWorkedRecently) =>
      _$this._jobCodeHasWorkedRecently = jobCodeHasWorkedRecently;

  int? _totalRecruitProgressCodeActive;
  int? get totalRecruitProgressCodeActive =>
      _$this._totalRecruitProgressCodeActive;
  set totalRecruitProgressCodeActive(int? totalRecruitProgressCodeActive) =>
      _$this._totalRecruitProgressCodeActive = totalRecruitProgressCodeActive;

  int? _currentRecruitProgressCodeWithMyCompany;
  int? get currentRecruitProgressCodeWithMyCompany =>
      _$this._currentRecruitProgressCodeWithMyCompany;
  set currentRecruitProgressCodeWithMyCompany(
          int? currentRecruitProgressCodeWithMyCompany) =>
      _$this._currentRecruitProgressCodeWithMyCompany =
          currentRecruitProgressCodeWithMyCompany;

  int? _totalMatchSkillCodes;
  int? get totalMatchSkillCodes => _$this._totalMatchSkillCodes;
  set totalMatchSkillCodes(int? totalMatchSkillCodes) =>
      _$this._totalMatchSkillCodes = totalMatchSkillCodes;

  int? _remoteCodeScore;
  int? get remoteCodeScore => _$this._remoteCodeScore;
  set remoteCodeScore(int? remoteCodeScore) =>
      _$this._remoteCodeScore = remoteCodeScore;

  int? _salaryScore;
  int? get salaryScore => _$this._salaryScore;
  set salaryScore(int? salaryScore) => _$this._salaryScore = salaryScore;

  int? _totalScore;
  int? get totalScore => _$this._totalScore;
  set totalScore(int? totalScore) => _$this._totalScore = totalScore;

  int? _sexType;
  int? get sexType => _$this._sexType;
  set sexType(int? sexType) => _$this._sexType = sexType;

  String? _addressCode;
  String? get addressCode => _$this._addressCode;
  set addressCode(String? addressCode) => _$this._addressCode = addressCode;

  ListBuilder<MainSkill>? _mainSkill;
  ListBuilder<MainSkill> get mainSkill =>
      _$this._mainSkill ??= new ListBuilder<MainSkill>();
  set mainSkill(ListBuilder<MainSkill>? mainSkill) =>
      _$this._mainSkill = mainSkill;

  ListBuilder<String>? _hopeJobCodes;
  ListBuilder<String> get hopeJobCodes =>
      _$this._hopeJobCodes ??= new ListBuilder<String>();
  set hopeJobCodes(ListBuilder<String>? hopeJobCodes) =>
      _$this._hopeJobCodes = hopeJobCodes;

  ListBuilder<EngWorkHistory>? _workHistories;
  ListBuilder<EngWorkHistory> get workHistories =>
      _$this._workHistories ??= new ListBuilder<EngWorkHistory>();
  set workHistories(ListBuilder<EngWorkHistory>? workHistories) =>
      _$this._workHistories = workHistories;

  String? _reasonAiRecommend;
  String? get reasonAiRecommend => _$this._reasonAiRecommend;
  set reasonAiRecommend(String? reasonAiRecommend) =>
      _$this._reasonAiRecommend = reasonAiRecommend;

  String? _reasonAiRecommendJa;
  String? get reasonAiRecommendJa => _$this._reasonAiRecommendJa;
  set reasonAiRecommendJa(String? reasonAiRecommendJa) =>
      _$this._reasonAiRecommendJa = reasonAiRecommendJa;

  String? _reasonAiRecommendVi;
  String? get reasonAiRecommendVi => _$this._reasonAiRecommendVi;
  set reasonAiRecommendVi(String? reasonAiRecommendVi) =>
      _$this._reasonAiRecommendVi = reasonAiRecommendVi;

  int? _recruitId;
  int? get recruitId => _$this._recruitId;
  set recruitId(int? recruitId) => _$this._recruitId = recruitId;

  GeneralCompanyExploreUserBuilder() {
    GeneralCompanyExploreUser._defaults(this);
  }

  GeneralCompanyExploreUserBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _userId = $v.userId;
      _profileImagePath = $v.profileImagePath;
      _firstName = $v.firstName;
      _lastName = $v.lastName;
      _age = $v.age;
      _created = $v.created;
      _languages = $v.languages?.toBuilder();
      _lastAcademicCode = $v.lastAcademicCode;
      _interestedFlag = $v.interestedFlag;
      _skills = $v.skills?.toBuilder();
      _countryCode = $v.countryCode;
      _academicLevelName = $v.academicLevelName;
      _requirements = $v.requirements?.toBuilder();
      _nickname = $v.nickname;
      _schoolName = $v.schoolName;
      _existApplyId = $v.existApplyId;
      _updated = $v.updated;
      _qualifications = $v.qualifications?.toBuilder();
      _payrollPriceUsd = $v.payrollPriceUsd;
      _lastLogin = $v.lastLogin;
      _totalApplyCount = $v.totalApplyCount;
      _totalCompanyChangedCount = $v.totalCompanyChangedCount;
      _jobCodeHasWorkedRecently = $v.jobCodeHasWorkedRecently;
      _totalRecruitProgressCodeActive = $v.totalRecruitProgressCodeActive;
      _currentRecruitProgressCodeWithMyCompany =
          $v.currentRecruitProgressCodeWithMyCompany;
      _totalMatchSkillCodes = $v.totalMatchSkillCodes;
      _remoteCodeScore = $v.remoteCodeScore;
      _salaryScore = $v.salaryScore;
      _totalScore = $v.totalScore;
      _sexType = $v.sexType;
      _addressCode = $v.addressCode;
      _mainSkill = $v.mainSkill?.toBuilder();
      _hopeJobCodes = $v.hopeJobCodes?.toBuilder();
      _workHistories = $v.workHistories?.toBuilder();
      _reasonAiRecommend = $v.reasonAiRecommend;
      _reasonAiRecommendJa = $v.reasonAiRecommendJa;
      _reasonAiRecommendVi = $v.reasonAiRecommendVi;
      _recruitId = $v.recruitId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GeneralCompanyExploreUser other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GeneralCompanyExploreUser;
  }

  @override
  void update(void Function(GeneralCompanyExploreUserBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GeneralCompanyExploreUser build() => _build();

  _$GeneralCompanyExploreUser _build() {
    _$GeneralCompanyExploreUser _$result;
    try {
      _$result = _$v ??
          new _$GeneralCompanyExploreUser._(
            userId: userId,
            profileImagePath: profileImagePath,
            firstName: firstName,
            lastName: lastName,
            age: age,
            created: created,
            languages: _languages?.build(),
            lastAcademicCode: lastAcademicCode,
            interestedFlag: interestedFlag,
            skills: _skills?.build(),
            countryCode: countryCode,
            academicLevelName: academicLevelName,
            requirements: _requirements?.build(),
            nickname: nickname,
            schoolName: schoolName,
            existApplyId: existApplyId,
            updated: updated,
            qualifications: _qualifications?.build(),
            payrollPriceUsd: payrollPriceUsd,
            lastLogin: lastLogin,
            totalApplyCount: totalApplyCount,
            totalCompanyChangedCount: totalCompanyChangedCount,
            jobCodeHasWorkedRecently: jobCodeHasWorkedRecently,
            totalRecruitProgressCodeActive: totalRecruitProgressCodeActive,
            currentRecruitProgressCodeWithMyCompany:
                currentRecruitProgressCodeWithMyCompany,
            totalMatchSkillCodes: totalMatchSkillCodes,
            remoteCodeScore: remoteCodeScore,
            salaryScore: salaryScore,
            totalScore: totalScore,
            sexType: sexType,
            addressCode: addressCode,
            mainSkill: _mainSkill?.build(),
            hopeJobCodes: _hopeJobCodes?.build(),
            workHistories: _workHistories?.build(),
            reasonAiRecommend: reasonAiRecommend,
            reasonAiRecommendJa: reasonAiRecommendJa,
            reasonAiRecommendVi: reasonAiRecommendVi,
            recruitId: recruitId,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'languages';
        _languages?.build();

        _$failedField = 'skills';
        _skills?.build();

        _$failedField = 'requirements';
        _requirements?.build();

        _$failedField = 'qualifications';
        _qualifications?.build();

        _$failedField = 'mainSkill';
        _mainSkill?.build();
        _$failedField = 'hopeJobCodes';
        _hopeJobCodes?.build();
        _$failedField = 'workHistories';
        _workHistories?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'GeneralCompanyExploreUser', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
