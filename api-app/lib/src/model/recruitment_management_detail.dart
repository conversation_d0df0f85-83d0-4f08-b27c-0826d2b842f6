//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/rec_recruit.dart';
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/date.dart';
import 'package:openapi/src/model/recruit_company_information.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'recruitment_management_detail.g.dart';

/// RecruitmentManagementDetail
///
/// Properties:
/// * [recruitId] 
/// * [title] 
/// * [totalRecruit] 
/// * [catchCopy] 
/// * [payrollPriceFrom] 
/// * [payrollPriceTo] 
/// * [payrollCode] 
/// * [startDate] 
/// * [endDate] 
/// * [jobCode] 
/// * [employCode] 
/// * [placeCode1] 
/// * [placeCode2] 
/// * [placeCode3] 
/// * [prefCode1] 
/// * [prefCode2] 
/// * [prefCode3] 
/// * [countryCode] 
/// * [ageFrom] 
/// * [ageTo] 
/// * [lastAcademicCode] 
/// * [languageCode1] 
/// * [languageLevelType1] 
/// * [languageCode2] 
/// * [languageLevelType2] 
/// * [experiencedJobCode] 
/// * [yearsOfExperience] 
/// * [skillJobCode1] 
/// * [skillCode1] 
/// * [skillLevelType1] 
/// * [skillJobCode2] 
/// * [skillCode2] 
/// * [skillLevelType2] 
/// * [skillJobCode3] 
/// * [skillCode3] 
/// * [skillLevelType3] 
/// * [content] 
/// * [sexType] 
/// * [licenceCode1] 
/// * [licencePoint1] 
/// * [licenceCode2] 
/// * [licenceName1] 
/// * [licenceName2] 
/// * [licenceName3] 
/// * [licencePoint2] 
/// * [licenceCode3] 
/// * [licencePoint3] 
/// * [recruitProgressCode] 
/// * [waitingFlag] 
/// * [hostCompany] 
/// * [similarRecruits] 
/// * [interviewDatetime] 
/// * [payrollPrice] 
/// * [placeCode] 
/// * [joingDate] 
/// * [groupId] 
/// * [applyId] 
@BuiltValue()
abstract class RecruitmentManagementDetail implements Built<RecruitmentManagementDetail, RecruitmentManagementDetailBuilder> {
  @BuiltValueField(wireName: r'recruit_id')
  int? get recruitId;

  @BuiltValueField(wireName: r'title')
  String? get title;

  @BuiltValueField(wireName: r'total_recruit')
  int? get totalRecruit;

  @BuiltValueField(wireName: r'catch_copy')
  String? get catchCopy;

  @BuiltValueField(wireName: r'payroll_price_from')
  String? get payrollPriceFrom;

  @BuiltValueField(wireName: r'payroll_price_to')
  String? get payrollPriceTo;

  @BuiltValueField(wireName: r'payroll_code')
  String? get payrollCode;

  @BuiltValueField(wireName: r'start_date')
  DateTime? get startDate;

  @BuiltValueField(wireName: r'end_date')
  DateTime? get endDate;

  @BuiltValueField(wireName: r'job_code')
  String? get jobCode;

  @BuiltValueField(wireName: r'employ_code')
  String? get employCode;

  @BuiltValueField(wireName: r'place_code1')
  String? get placeCode1;

  @BuiltValueField(wireName: r'place_code2')
  String? get placeCode2;

  @BuiltValueField(wireName: r'place_code3')
  String? get placeCode3;

  @BuiltValueField(wireName: r'pref_code1')
  String? get prefCode1;

  @BuiltValueField(wireName: r'pref_code2')
  String? get prefCode2;

  @BuiltValueField(wireName: r'pref_code3')
  String? get prefCode3;

  @BuiltValueField(wireName: r'country_code')
  String? get countryCode;

  @BuiltValueField(wireName: r'age_from')
  int? get ageFrom;

  @BuiltValueField(wireName: r'age_to')
  int? get ageTo;

  @BuiltValueField(wireName: r'last_academic_code')
  String? get lastAcademicCode;

  @BuiltValueField(wireName: r'language_code1')
  String? get languageCode1;

  @BuiltValueField(wireName: r'language_level_type1')
  int? get languageLevelType1;

  @BuiltValueField(wireName: r'language_code2')
  String? get languageCode2;

  @BuiltValueField(wireName: r'language_level_type2')
  int? get languageLevelType2;

  @BuiltValueField(wireName: r'experienced_job_code')
  String? get experiencedJobCode;

  @BuiltValueField(wireName: r'years_of_experience')
  int? get yearsOfExperience;

  @BuiltValueField(wireName: r'skill_job_code1')
  String? get skillJobCode1;

  @BuiltValueField(wireName: r'skill_code1')
  String? get skillCode1;

  @BuiltValueField(wireName: r'skill_level_type1')
  int? get skillLevelType1;

  @BuiltValueField(wireName: r'skill_job_code2')
  String? get skillJobCode2;

  @BuiltValueField(wireName: r'skill_code2')
  String? get skillCode2;

  @BuiltValueField(wireName: r'skill_level_type2')
  int? get skillLevelType2;

  @BuiltValueField(wireName: r'skill_job_code3')
  String? get skillJobCode3;

  @BuiltValueField(wireName: r'skill_code3')
  String? get skillCode3;

  @BuiltValueField(wireName: r'skill_level_type3')
  int? get skillLevelType3;

  @BuiltValueField(wireName: r'content')
  String? get content;

  @BuiltValueField(wireName: r'sex_type')
  int? get sexType;

  @BuiltValueField(wireName: r'licence_code1')
  String? get licenceCode1;

  @BuiltValueField(wireName: r'licence_point1')
  int? get licencePoint1;

  @BuiltValueField(wireName: r'licence_code2')
  String? get licenceCode2;

  @BuiltValueField(wireName: r'licence_name1')
  String? get licenceName1;

  @BuiltValueField(wireName: r'licence_name2')
  String? get licenceName2;

  @BuiltValueField(wireName: r'licence_name3')
  String? get licenceName3;

  @BuiltValueField(wireName: r'licence_point2')
  int? get licencePoint2;

  @BuiltValueField(wireName: r'licence_code3')
  String? get licenceCode3;

  @BuiltValueField(wireName: r'licence_point3')
  int? get licencePoint3;

  @BuiltValueField(wireName: r'recruit_progress_code')
  int? get recruitProgressCode;

  @BuiltValueField(wireName: r'waiting_flag')
  int? get waitingFlag;

  @BuiltValueField(wireName: r'host_company')
  RecruitCompanyInformation? get hostCompany;

  @BuiltValueField(wireName: r'similar_recruits')
  BuiltList<RecRecruit>? get similarRecruits;

  @BuiltValueField(wireName: r'interview_datetime')
  DateTime? get interviewDatetime;

  @BuiltValueField(wireName: r'payroll_price')
  double? get payrollPrice;

  @BuiltValueField(wireName: r'place_code')
  String? get placeCode;

  @BuiltValueField(wireName: r'joing_date')
  Date? get joingDate;

  @BuiltValueField(wireName: r'group_id')
  int? get groupId;

  @BuiltValueField(wireName: r'apply_id')
  int? get applyId;

  RecruitmentManagementDetail._();

  factory RecruitmentManagementDetail([void updates(RecruitmentManagementDetailBuilder b)]) = _$RecruitmentManagementDetail;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(RecruitmentManagementDetailBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<RecruitmentManagementDetail> get serializer => _$RecruitmentManagementDetailSerializer();
}

class _$RecruitmentManagementDetailSerializer implements PrimitiveSerializer<RecruitmentManagementDetail> {
  @override
  final Iterable<Type> types = const [RecruitmentManagementDetail, _$RecruitmentManagementDetail];

  @override
  final String wireName = r'RecruitmentManagementDetail';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    RecruitmentManagementDetail object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.recruitId != null) {
      yield r'recruit_id';
      yield serializers.serialize(
        object.recruitId,
        specifiedType: const FullType(int),
      );
    }
    if (object.title != null) {
      yield r'title';
      yield serializers.serialize(
        object.title,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.totalRecruit != null) {
      yield r'total_recruit';
      yield serializers.serialize(
        object.totalRecruit,
        specifiedType: const FullType(int),
      );
    }
    if (object.catchCopy != null) {
      yield r'catch_copy';
      yield serializers.serialize(
        object.catchCopy,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.payrollPriceFrom != null) {
      yield r'payroll_price_from';
      yield serializers.serialize(
        object.payrollPriceFrom,
        specifiedType: const FullType(String),
      );
    }
    if (object.payrollPriceTo != null) {
      yield r'payroll_price_to';
      yield serializers.serialize(
        object.payrollPriceTo,
        specifiedType: const FullType(String),
      );
    }
    if (object.payrollCode != null) {
      yield r'payroll_code';
      yield serializers.serialize(
        object.payrollCode,
        specifiedType: const FullType(String),
      );
    }
    if (object.startDate != null) {
      yield r'start_date';
      yield serializers.serialize(
        object.startDate,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.endDate != null) {
      yield r'end_date';
      yield serializers.serialize(
        object.endDate,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.jobCode != null) {
      yield r'job_code';
      yield serializers.serialize(
        object.jobCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.employCode != null) {
      yield r'employ_code';
      yield serializers.serialize(
        object.employCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.placeCode1 != null) {
      yield r'place_code1';
      yield serializers.serialize(
        object.placeCode1,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.placeCode2 != null) {
      yield r'place_code2';
      yield serializers.serialize(
        object.placeCode2,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.placeCode3 != null) {
      yield r'place_code3';
      yield serializers.serialize(
        object.placeCode3,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.prefCode1 != null) {
      yield r'pref_code1';
      yield serializers.serialize(
        object.prefCode1,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.prefCode2 != null) {
      yield r'pref_code2';
      yield serializers.serialize(
        object.prefCode2,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.prefCode3 != null) {
      yield r'pref_code3';
      yield serializers.serialize(
        object.prefCode3,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.countryCode != null) {
      yield r'country_code';
      yield serializers.serialize(
        object.countryCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.ageFrom != null) {
      yield r'age_from';
      yield serializers.serialize(
        object.ageFrom,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.ageTo != null) {
      yield r'age_to';
      yield serializers.serialize(
        object.ageTo,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.lastAcademicCode != null) {
      yield r'last_academic_code';
      yield serializers.serialize(
        object.lastAcademicCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.languageCode1 != null) {
      yield r'language_code1';
      yield serializers.serialize(
        object.languageCode1,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.languageLevelType1 != null) {
      yield r'language_level_type1';
      yield serializers.serialize(
        object.languageLevelType1,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.languageCode2 != null) {
      yield r'language_code2';
      yield serializers.serialize(
        object.languageCode2,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.languageLevelType2 != null) {
      yield r'language_level_type2';
      yield serializers.serialize(
        object.languageLevelType2,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.experiencedJobCode != null) {
      yield r'experienced_job_code';
      yield serializers.serialize(
        object.experiencedJobCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.yearsOfExperience != null) {
      yield r'years_of_experience';
      yield serializers.serialize(
        object.yearsOfExperience,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.skillJobCode1 != null) {
      yield r'skill_job_code1';
      yield serializers.serialize(
        object.skillJobCode1,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.skillCode1 != null) {
      yield r'skill_code1';
      yield serializers.serialize(
        object.skillCode1,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.skillLevelType1 != null) {
      yield r'skill_level_type1';
      yield serializers.serialize(
        object.skillLevelType1,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.skillJobCode2 != null) {
      yield r'skill_job_code2';
      yield serializers.serialize(
        object.skillJobCode2,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.skillCode2 != null) {
      yield r'skill_code2';
      yield serializers.serialize(
        object.skillCode2,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.skillLevelType2 != null) {
      yield r'skill_level_type2';
      yield serializers.serialize(
        object.skillLevelType2,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.skillJobCode3 != null) {
      yield r'skill_job_code3';
      yield serializers.serialize(
        object.skillJobCode3,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.skillCode3 != null) {
      yield r'skill_code3';
      yield serializers.serialize(
        object.skillCode3,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.skillLevelType3 != null) {
      yield r'skill_level_type3';
      yield serializers.serialize(
        object.skillLevelType3,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.content != null) {
      yield r'content';
      yield serializers.serialize(
        object.content,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.sexType != null) {
      yield r'sex_type';
      yield serializers.serialize(
        object.sexType,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.licenceCode1 != null) {
      yield r'licence_code1';
      yield serializers.serialize(
        object.licenceCode1,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.licencePoint1 != null) {
      yield r'licence_point1';
      yield serializers.serialize(
        object.licencePoint1,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.licenceCode2 != null) {
      yield r'licence_code2';
      yield serializers.serialize(
        object.licenceCode2,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.licenceName1 != null) {
      yield r'licence_name1';
      yield serializers.serialize(
        object.licenceName1,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.licenceName2 != null) {
      yield r'licence_name2';
      yield serializers.serialize(
        object.licenceName2,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.licenceName3 != null) {
      yield r'licence_name3';
      yield serializers.serialize(
        object.licenceName3,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.licencePoint2 != null) {
      yield r'licence_point2';
      yield serializers.serialize(
        object.licencePoint2,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.licenceCode3 != null) {
      yield r'licence_code3';
      yield serializers.serialize(
        object.licenceCode3,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.licencePoint3 != null) {
      yield r'licence_point3';
      yield serializers.serialize(
        object.licencePoint3,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.recruitProgressCode != null) {
      yield r'recruit_progress_code';
      yield serializers.serialize(
        object.recruitProgressCode,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.waitingFlag != null) {
      yield r'waiting_flag';
      yield serializers.serialize(
        object.waitingFlag,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.hostCompany != null) {
      yield r'host_company';
      yield serializers.serialize(
        object.hostCompany,
        specifiedType: const FullType(RecruitCompanyInformation),
      );
    }
    if (object.similarRecruits != null) {
      yield r'similar_recruits';
      yield serializers.serialize(
        object.similarRecruits,
        specifiedType: const FullType(BuiltList, [FullType(RecRecruit)]),
      );
    }
    if (object.interviewDatetime != null) {
      yield r'interview_datetime';
      yield serializers.serialize(
        object.interviewDatetime,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.payrollPrice != null) {
      yield r'payroll_price';
      yield serializers.serialize(
        object.payrollPrice,
        specifiedType: const FullType.nullable(double),
      );
    }
    if (object.placeCode != null) {
      yield r'place_code';
      yield serializers.serialize(
        object.placeCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.joingDate != null) {
      yield r'joing_date';
      yield serializers.serialize(
        object.joingDate,
        specifiedType: const FullType.nullable(Date),
      );
    }
    if (object.groupId != null) {
      yield r'group_id';
      yield serializers.serialize(
        object.groupId,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.applyId != null) {
      yield r'apply_id';
      yield serializers.serialize(
        object.applyId,
        specifiedType: const FullType(int),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    RecruitmentManagementDetail object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required RecruitmentManagementDetailBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'recruit_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.recruitId = valueDes;
          break;
        case r'title':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.title = valueDes;
          break;
        case r'total_recruit':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.totalRecruit = valueDes;
          break;
        case r'catch_copy':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.catchCopy = valueDes;
          break;
        case r'payroll_price_from':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.payrollPriceFrom = valueDes;
          break;
        case r'payroll_price_to':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.payrollPriceTo = valueDes;
          break;
        case r'payroll_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.payrollCode = valueDes;
          break;
        case r'start_date':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.startDate = valueDes;
          break;
        case r'end_date':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.endDate = valueDes;
          break;
        case r'job_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.jobCode = valueDes;
          break;
        case r'employ_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.employCode = valueDes;
          break;
        case r'place_code1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.placeCode1 = valueDes;
          break;
        case r'place_code2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.placeCode2 = valueDes;
          break;
        case r'place_code3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.placeCode3 = valueDes;
          break;
        case r'pref_code1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.prefCode1 = valueDes;
          break;
        case r'pref_code2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.prefCode2 = valueDes;
          break;
        case r'pref_code3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.prefCode3 = valueDes;
          break;
        case r'country_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.countryCode = valueDes;
          break;
        case r'age_from':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.ageFrom = valueDes;
          break;
        case r'age_to':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.ageTo = valueDes;
          break;
        case r'last_academic_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.lastAcademicCode = valueDes;
          break;
        case r'language_code1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.languageCode1 = valueDes;
          break;
        case r'language_level_type1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.languageLevelType1 = valueDes;
          break;
        case r'language_code2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.languageCode2 = valueDes;
          break;
        case r'language_level_type2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.languageLevelType2 = valueDes;
          break;
        case r'experienced_job_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.experiencedJobCode = valueDes;
          break;
        case r'years_of_experience':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.yearsOfExperience = valueDes;
          break;
        case r'skill_job_code1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.skillJobCode1 = valueDes;
          break;
        case r'skill_code1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.skillCode1 = valueDes;
          break;
        case r'skill_level_type1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.skillLevelType1 = valueDes;
          break;
        case r'skill_job_code2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.skillJobCode2 = valueDes;
          break;
        case r'skill_code2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.skillCode2 = valueDes;
          break;
        case r'skill_level_type2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.skillLevelType2 = valueDes;
          break;
        case r'skill_job_code3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.skillJobCode3 = valueDes;
          break;
        case r'skill_code3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.skillCode3 = valueDes;
          break;
        case r'skill_level_type3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.skillLevelType3 = valueDes;
          break;
        case r'content':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.content = valueDes;
          break;
        case r'sex_type':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.sexType = valueDes;
          break;
        case r'licence_code1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.licenceCode1 = valueDes;
          break;
        case r'licence_point1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.licencePoint1 = valueDes;
          break;
        case r'licence_code2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.licenceCode2 = valueDes;
          break;
        case r'licence_name1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.licenceName1 = valueDes;
          break;
        case r'licence_name2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.licenceName2 = valueDes;
          break;
        case r'licence_name3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.licenceName3 = valueDes;
          break;
        case r'licence_point2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.licencePoint2 = valueDes;
          break;
        case r'licence_code3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.licenceCode3 = valueDes;
          break;
        case r'licence_point3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.licencePoint3 = valueDes;
          break;
        case r'recruit_progress_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.recruitProgressCode = valueDes;
          break;
        case r'waiting_flag':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.waitingFlag = valueDes;
          break;
        case r'host_company':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(RecruitCompanyInformation),
          ) as RecruitCompanyInformation;
          result.hostCompany.replace(valueDes);
          break;
        case r'similar_recruits':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(RecRecruit)]),
          ) as BuiltList<RecRecruit>;
          result.similarRecruits.replace(valueDes);
          break;
        case r'interview_datetime':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.interviewDatetime = valueDes;
          break;
        case r'payroll_price':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(double),
          ) as double?;
          if (valueDes == null) continue;
          result.payrollPrice = valueDes;
          break;
        case r'place_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.placeCode = valueDes;
          break;
        case r'joing_date':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(Date),
          ) as Date?;
          if (valueDes == null) continue;
          result.joingDate = valueDes;
          break;
        case r'group_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.groupId = valueDes;
          break;
        case r'apply_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.applyId = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  RecruitmentManagementDetail deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = RecruitmentManagementDetailBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

