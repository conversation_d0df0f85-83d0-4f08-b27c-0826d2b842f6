//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/general_company_user.dart';
import 'package:openapi/src/model/general_company.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'general_company_register.g.dart';

/// GeneralCompanyRegister
///
/// Properties:
/// * [user] 
/// * [company] 
/// * [userType] 
@BuiltValue()
abstract class GeneralCompanyRegister implements Built<GeneralCompanyRegister, GeneralCompanyRegisterBuilder> {
  @BuiltValueField(wireName: r'user')
  GeneralCompanyUser get user;

  @BuiltValueField(wireName: r'company')
  GeneralCompany get company;

  @BuiltValueField(wireName: r'user_type')
  int get userType;

  GeneralCompanyRegister._();

  factory GeneralCompanyRegister([void updates(GeneralCompanyRegisterBuilder b)]) = _$GeneralCompanyRegister;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(GeneralCompanyRegisterBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<GeneralCompanyRegister> get serializer => _$GeneralCompanyRegisterSerializer();
}

class _$GeneralCompanyRegisterSerializer implements PrimitiveSerializer<GeneralCompanyRegister> {
  @override
  final Iterable<Type> types = const [GeneralCompanyRegister, _$GeneralCompanyRegister];

  @override
  final String wireName = r'GeneralCompanyRegister';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    GeneralCompanyRegister object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'user';
    yield serializers.serialize(
      object.user,
      specifiedType: const FullType(GeneralCompanyUser),
    );
    yield r'company';
    yield serializers.serialize(
      object.company,
      specifiedType: const FullType(GeneralCompany),
    );
    yield r'user_type';
    yield serializers.serialize(
      object.userType,
      specifiedType: const FullType(int),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    GeneralCompanyRegister object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required GeneralCompanyRegisterBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'user':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(GeneralCompanyUser),
          ) as GeneralCompanyUser;
          result.user.replace(valueDes);
          break;
        case r'company':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(GeneralCompany),
          ) as GeneralCompany;
          result.company.replace(valueDes);
          break;
        case r'user_type':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.userType = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  GeneralCompanyRegister deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = GeneralCompanyRegisterBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

