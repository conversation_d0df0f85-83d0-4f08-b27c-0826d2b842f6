//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'recruit_uploaded_serializers.g.dart';

/// RecruitUploadedSerializers
///
/// Properties:
/// * [recruitId] 
/// * [title] 
/// * [catchCopy] 
/// * [payrollPriceFrom] 
/// * [payrollPriceTo] 
/// * [payrollCode] 
/// * [startDate] 
/// * [endDate] 
/// * [jobCode] 
/// * [hostCompany] 
/// * [skillJobCode1] 
/// * [skillJobCode2] 
/// * [skillJobCode3] 
/// * [hostAgent] 
/// * [companyUserId] 
/// * [totalApply] 
/// * [jobName] 
/// * [applied] 
/// * [recruitImagePath] 
/// * [isExpired] 
/// * [placeCode1] 
/// * [placeCode2] 
/// * [placeCode3] 
/// * [totalRequest] 
/// * [totalUnderSelection] 
/// * [displayFlag] 
/// * [remoteCode] 
@BuiltValue()
abstract class RecruitUploadedSerializers implements Built<RecruitUploadedSerializers, RecruitUploadedSerializersBuilder> {
  @BuiltValueField(wireName: r'recruit_id')
  int? get recruitId;

  @BuiltValueField(wireName: r'title')
  String? get title;

  @BuiltValueField(wireName: r'catch_copy')
  String? get catchCopy;

  @BuiltValueField(wireName: r'payroll_price_from')
  String? get payrollPriceFrom;

  @BuiltValueField(wireName: r'payroll_price_to')
  String? get payrollPriceTo;

  @BuiltValueField(wireName: r'payroll_code')
  String? get payrollCode;

  @BuiltValueField(wireName: r'start_date')
  DateTime? get startDate;

  @BuiltValueField(wireName: r'end_date')
  DateTime? get endDate;

  @BuiltValueField(wireName: r'job_code')
  String? get jobCode;

  @BuiltValueField(wireName: r'host_company')
  int get hostCompany;

  @BuiltValueField(wireName: r'skill_job_code1')
  String? get skillJobCode1;

  @BuiltValueField(wireName: r'skill_job_code2')
  String? get skillJobCode2;

  @BuiltValueField(wireName: r'skill_job_code3')
  String? get skillJobCode3;

  @BuiltValueField(wireName: r'host_agent')
  int get hostAgent;

  @BuiltValueField(wireName: r'company_user_id')
  int? get companyUserId;

  @BuiltValueField(wireName: r'total_apply')
  int? get totalApply;

  @BuiltValueField(wireName: r'job_name')
  String? get jobName;

  @BuiltValueField(wireName: r'applied')
  bool? get applied;

  @BuiltValueField(wireName: r'recruit_image_path')
  String? get recruitImagePath;

  @BuiltValueField(wireName: r'is_expired')
  bool? get isExpired;

  @BuiltValueField(wireName: r'place_code1')
  String? get placeCode1;

  @BuiltValueField(wireName: r'place_code2')
  String? get placeCode2;

  @BuiltValueField(wireName: r'place_code3')
  String? get placeCode3;

  @BuiltValueField(wireName: r'total_request')
  int? get totalRequest;

  @BuiltValueField(wireName: r'total_under_selection')
  int? get totalUnderSelection;

  @BuiltValueField(wireName: r'display_flag')
  int? get displayFlag;

  @BuiltValueField(wireName: r'remote_code')
  String? get remoteCode;

  RecruitUploadedSerializers._();

  factory RecruitUploadedSerializers([void updates(RecruitUploadedSerializersBuilder b)]) = _$RecruitUploadedSerializers;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(RecruitUploadedSerializersBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<RecruitUploadedSerializers> get serializer => _$RecruitUploadedSerializersSerializer();
}

class _$RecruitUploadedSerializersSerializer implements PrimitiveSerializer<RecruitUploadedSerializers> {
  @override
  final Iterable<Type> types = const [RecruitUploadedSerializers, _$RecruitUploadedSerializers];

  @override
  final String wireName = r'RecruitUploadedSerializers';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    RecruitUploadedSerializers object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.recruitId != null) {
      yield r'recruit_id';
      yield serializers.serialize(
        object.recruitId,
        specifiedType: const FullType(int),
      );
    }
    if (object.title != null) {
      yield r'title';
      yield serializers.serialize(
        object.title,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.catchCopy != null) {
      yield r'catch_copy';
      yield serializers.serialize(
        object.catchCopy,
        specifiedType: const FullType.nullable(String),
      );
    }
    yield r'payroll_price_from';
    yield object.payrollPriceFrom == null ? null : serializers.serialize(
      object.payrollPriceFrom,
      specifiedType: const FullType.nullable(String),
    );
    yield r'payroll_price_to';
    yield object.payrollPriceTo == null ? null : serializers.serialize(
      object.payrollPriceTo,
      specifiedType: const FullType.nullable(String),
    );
    if (object.payrollCode != null) {
      yield r'payroll_code';
      yield serializers.serialize(
        object.payrollCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.startDate != null) {
      yield r'start_date';
      yield serializers.serialize(
        object.startDate,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.endDate != null) {
      yield r'end_date';
      yield serializers.serialize(
        object.endDate,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.jobCode != null) {
      yield r'job_code';
      yield serializers.serialize(
        object.jobCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    yield r'host_company';
    yield serializers.serialize(
      object.hostCompany,
      specifiedType: const FullType(int),
    );
    if (object.skillJobCode1 != null) {
      yield r'skill_job_code1';
      yield serializers.serialize(
        object.skillJobCode1,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.skillJobCode2 != null) {
      yield r'skill_job_code2';
      yield serializers.serialize(
        object.skillJobCode2,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.skillJobCode3 != null) {
      yield r'skill_job_code3';
      yield serializers.serialize(
        object.skillJobCode3,
        specifiedType: const FullType.nullable(String),
      );
    }
    yield r'host_agent';
    yield serializers.serialize(
      object.hostAgent,
      specifiedType: const FullType(int),
    );
    if (object.companyUserId != null) {
      yield r'company_user_id';
      yield serializers.serialize(
        object.companyUserId,
        specifiedType: const FullType(int),
      );
    }
    if (object.totalApply != null) {
      yield r'total_apply';
      yield serializers.serialize(
        object.totalApply,
        specifiedType: const FullType(int),
      );
    }
    if (object.jobName != null) {
      yield r'job_name';
      yield serializers.serialize(
        object.jobName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.applied != null) {
      yield r'applied';
      yield serializers.serialize(
        object.applied,
        specifiedType: const FullType(bool),
      );
    }
    if (object.recruitImagePath != null) {
      yield r'recruit_image_path';
      yield serializers.serialize(
        object.recruitImagePath,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.isExpired != null) {
      yield r'is_expired';
      yield serializers.serialize(
        object.isExpired,
        specifiedType: const FullType(bool),
      );
    }
    if (object.placeCode1 != null) {
      yield r'place_code1';
      yield serializers.serialize(
        object.placeCode1,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.placeCode2 != null) {
      yield r'place_code2';
      yield serializers.serialize(
        object.placeCode2,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.placeCode3 != null) {
      yield r'place_code3';
      yield serializers.serialize(
        object.placeCode3,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.totalRequest != null) {
      yield r'total_request';
      yield serializers.serialize(
        object.totalRequest,
        specifiedType: const FullType(int),
      );
    }
    if (object.totalUnderSelection != null) {
      yield r'total_under_selection';
      yield serializers.serialize(
        object.totalUnderSelection,
        specifiedType: const FullType(int),
      );
    }
    if (object.displayFlag != null) {
      yield r'display_flag';
      yield serializers.serialize(
        object.displayFlag,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.remoteCode != null) {
      yield r'remote_code';
      yield serializers.serialize(
        object.remoteCode,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    RecruitUploadedSerializers object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required RecruitUploadedSerializersBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'recruit_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.recruitId = valueDes;
          break;
        case r'title':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.title = valueDes;
          break;
        case r'catch_copy':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.catchCopy = valueDes;
          break;
        case r'payroll_price_from':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.payrollPriceFrom = valueDes;
          break;
        case r'payroll_price_to':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.payrollPriceTo = valueDes;
          break;
        case r'payroll_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.payrollCode = valueDes;
          break;
        case r'start_date':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.startDate = valueDes;
          break;
        case r'end_date':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.endDate = valueDes;
          break;
        case r'job_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.jobCode = valueDes;
          break;
        case r'host_company':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.hostCompany = valueDes;
          break;
        case r'skill_job_code1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.skillJobCode1 = valueDes;
          break;
        case r'skill_job_code2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.skillJobCode2 = valueDes;
          break;
        case r'skill_job_code3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.skillJobCode3 = valueDes;
          break;
        case r'host_agent':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.hostAgent = valueDes;
          break;
        case r'company_user_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.companyUserId = valueDes;
          break;
        case r'total_apply':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.totalApply = valueDes;
          break;
        case r'job_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.jobName = valueDes;
          break;
        case r'applied':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(bool),
          ) as bool;
          result.applied = valueDes;
          break;
        case r'recruit_image_path':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.recruitImagePath = valueDes;
          break;
        case r'is_expired':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(bool),
          ) as bool;
          result.isExpired = valueDes;
          break;
        case r'place_code1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.placeCode1 = valueDes;
          break;
        case r'place_code2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.placeCode2 = valueDes;
          break;
        case r'place_code3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.placeCode3 = valueDes;
          break;
        case r'total_request':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.totalRequest = valueDes;
          break;
        case r'total_under_selection':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.totalUnderSelection = valueDes;
          break;
        case r'display_flag':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.displayFlag = valueDes;
          break;
        case r'remote_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.remoteCode = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  RecruitUploadedSerializers deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = RecruitUploadedSerializersBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

