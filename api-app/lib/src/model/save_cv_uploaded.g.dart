// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'save_cv_uploaded.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$SaveCVUploaded extends SaveCVUploaded {
  @override
  final String cvId;

  factory _$SaveCVUploaded([void Function(SaveCVUploadedBuilder)? updates]) =>
      (new SaveCVUploadedBuilder()..update(updates))._build();

  _$SaveCVUploaded._({required this.cvId}) : super._() {
    BuiltValueNullFieldError.checkNotNull(cvId, r'SaveCVUploaded', 'cvId');
  }

  @override
  SaveCVUploaded rebuild(void Function(SaveCVUploadedBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SaveCVUploadedBuilder toBuilder() =>
      new SaveCVUploadedBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SaveCVUploaded && cvId == other.cvId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, cvId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SaveCVUploaded')..add('cvId', cvId))
        .toString();
  }
}

class SaveCVUploadedBuilder
    implements Builder<SaveCVUploaded, SaveCVUploadedBuilder> {
  _$SaveCVUploaded? _$v;

  String? _cvId;
  String? get cvId => _$this._cvId;
  set cvId(String? cvId) => _$this._cvId = cvId;

  SaveCVUploadedBuilder() {
    SaveCVUploaded._defaults(this);
  }

  SaveCVUploadedBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _cvId = $v.cvId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SaveCVUploaded other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$SaveCVUploaded;
  }

  @override
  void update(void Function(SaveCVUploadedBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SaveCVUploaded build() => _build();

  _$SaveCVUploaded _build() {
    final _$result = _$v ??
        new _$SaveCVUploaded._(
          cvId: BuiltValueNullFieldError.checkNotNull(
              cvId, r'SaveCVUploaded', 'cvId'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
