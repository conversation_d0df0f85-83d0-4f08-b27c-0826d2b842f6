// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'host_company_subscribe_support_company.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$HostCompanySubscribeSupportCompany
    extends HostCompanySubscribeSupportCompany {
  @override
  final String supportCompanyId;

  factory _$HostCompanySubscribeSupportCompany(
          [void Function(HostCompanySubscribeSupportCompanyBuilder)?
              updates]) =>
      (new HostCompanySubscribeSupportCompanyBuilder()..update(updates))
          ._build();

  _$HostCompanySubscribeSupportCompany._({required this.supportCompanyId})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(supportCompanyId,
        r'HostCompanySubscribeSupportCompany', 'supportCompanyId');
  }

  @override
  HostCompanySubscribeSupportCompany rebuild(
          void Function(HostCompanySubscribeSupportCompanyBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  HostCompanySubscribeSupportCompanyBuilder toBuilder() =>
      new HostCompanySubscribeSupportCompanyBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is HostCompanySubscribeSupportCompany &&
        supportCompanyId == other.supportCompanyId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, supportCompanyId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'HostCompanySubscribeSupportCompany')
          ..add('supportCompanyId', supportCompanyId))
        .toString();
  }
}

class HostCompanySubscribeSupportCompanyBuilder
    implements
        Builder<HostCompanySubscribeSupportCompany,
            HostCompanySubscribeSupportCompanyBuilder> {
  _$HostCompanySubscribeSupportCompany? _$v;

  String? _supportCompanyId;
  String? get supportCompanyId => _$this._supportCompanyId;
  set supportCompanyId(String? supportCompanyId) =>
      _$this._supportCompanyId = supportCompanyId;

  HostCompanySubscribeSupportCompanyBuilder() {
    HostCompanySubscribeSupportCompany._defaults(this);
  }

  HostCompanySubscribeSupportCompanyBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _supportCompanyId = $v.supportCompanyId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(HostCompanySubscribeSupportCompany other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$HostCompanySubscribeSupportCompany;
  }

  @override
  void update(
      void Function(HostCompanySubscribeSupportCompanyBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  HostCompanySubscribeSupportCompany build() => _build();

  _$HostCompanySubscribeSupportCompany _build() {
    final _$result = _$v ??
        new _$HostCompanySubscribeSupportCompany._(
          supportCompanyId: BuiltValueNullFieldError.checkNotNull(
              supportCompanyId,
              r'HostCompanySubscribeSupportCompany',
              'supportCompanyId'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
