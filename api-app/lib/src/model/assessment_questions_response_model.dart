//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/assessment_question_data.dart';
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'assessment_questions_response_model.g.dart';

/// AssessmentQuestionsResponseModel
///
/// Properties:
/// * [message] 
/// * [data] 
/// * [errors] 
@BuiltValue()
abstract class AssessmentQuestionsResponseModel implements Built<AssessmentQuestionsResponseModel, AssessmentQuestionsResponseModelBuilder> {
  @BuiltValueField(wireName: r'message')
  String? get message;

  @BuiltValueField(wireName: r'data')
  BuiltList<AssessmentQuestionData> get data;

  @BuiltValueField(wireName: r'errors')
  BuiltList<String>? get errors;

  AssessmentQuestionsResponseModel._();

  factory AssessmentQuestionsResponseModel([void updates(AssessmentQuestionsResponseModelBuilder b)]) = _$AssessmentQuestionsResponseModel;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AssessmentQuestionsResponseModelBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AssessmentQuestionsResponseModel> get serializer => _$AssessmentQuestionsResponseModelSerializer();
}

class _$AssessmentQuestionsResponseModelSerializer implements PrimitiveSerializer<AssessmentQuestionsResponseModel> {
  @override
  final Iterable<Type> types = const [AssessmentQuestionsResponseModel, _$AssessmentQuestionsResponseModel];

  @override
  final String wireName = r'AssessmentQuestionsResponseModel';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AssessmentQuestionsResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'message';
    yield object.message == null ? null : serializers.serialize(
      object.message,
      specifiedType: const FullType.nullable(String),
    );
    yield r'data';
    yield serializers.serialize(
      object.data,
      specifiedType: const FullType(BuiltList, [FullType(AssessmentQuestionData)]),
    );
    yield r'errors';
    yield object.errors == null ? null : serializers.serialize(
      object.errors,
      specifiedType: const FullType.nullable(BuiltList, [FullType(String)]),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    AssessmentQuestionsResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AssessmentQuestionsResponseModelBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'message':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.message = valueDes;
          break;
        case r'data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(AssessmentQuestionData)]),
          ) as BuiltList<AssessmentQuestionData>;
          result.data.replace(valueDes);
          break;
        case r'errors':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(BuiltList, [FullType(String)]),
          ) as BuiltList<String>?;
          if (valueDes == null) continue;
          result.errors.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AssessmentQuestionsResponseModel deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AssessmentQuestionsResponseModelBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

