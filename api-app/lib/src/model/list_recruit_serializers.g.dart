// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'list_recruit_serializers.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ListRecruitSerializers extends ListRecruitSerializers {
  @override
  final String? next;
  @override
  final String? previous;
  @override
  final BuiltList<RecruitUploadedSerializers> results;
  @override
  final int totalCount;

  factory _$ListRecruitSerializers(
          [void Function(ListRecruitSerializersBuilder)? updates]) =>
      (new ListRecruitSerializersBuilder()..update(updates))._build();

  _$ListRecruitSerializers._(
      {this.next,
      this.previous,
      required this.results,
      required this.totalCount})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        results, r'ListRecruitSerializers', 'results');
    BuiltValueNullFieldError.checkNotNull(
        totalCount, r'ListRecruitSerializers', 'totalCount');
  }

  @override
  ListRecruitSerializers rebuild(
          void Function(ListRecruitSerializersBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ListRecruitSerializersBuilder toBuilder() =>
      new ListRecruitSerializersBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ListRecruitSerializers &&
        next == other.next &&
        previous == other.previous &&
        results == other.results &&
        totalCount == other.totalCount;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, next.hashCode);
    _$hash = $jc(_$hash, previous.hashCode);
    _$hash = $jc(_$hash, results.hashCode);
    _$hash = $jc(_$hash, totalCount.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ListRecruitSerializers')
          ..add('next', next)
          ..add('previous', previous)
          ..add('results', results)
          ..add('totalCount', totalCount))
        .toString();
  }
}

class ListRecruitSerializersBuilder
    implements Builder<ListRecruitSerializers, ListRecruitSerializersBuilder> {
  _$ListRecruitSerializers? _$v;

  String? _next;
  String? get next => _$this._next;
  set next(String? next) => _$this._next = next;

  String? _previous;
  String? get previous => _$this._previous;
  set previous(String? previous) => _$this._previous = previous;

  ListBuilder<RecruitUploadedSerializers>? _results;
  ListBuilder<RecruitUploadedSerializers> get results =>
      _$this._results ??= new ListBuilder<RecruitUploadedSerializers>();
  set results(ListBuilder<RecruitUploadedSerializers>? results) =>
      _$this._results = results;

  int? _totalCount;
  int? get totalCount => _$this._totalCount;
  set totalCount(int? totalCount) => _$this._totalCount = totalCount;

  ListRecruitSerializersBuilder() {
    ListRecruitSerializers._defaults(this);
  }

  ListRecruitSerializersBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _next = $v.next;
      _previous = $v.previous;
      _results = $v.results.toBuilder();
      _totalCount = $v.totalCount;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ListRecruitSerializers other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$ListRecruitSerializers;
  }

  @override
  void update(void Function(ListRecruitSerializersBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ListRecruitSerializers build() => _build();

  _$ListRecruitSerializers _build() {
    _$ListRecruitSerializers _$result;
    try {
      _$result = _$v ??
          new _$ListRecruitSerializers._(
            next: next,
            previous: previous,
            results: results.build(),
            totalCount: BuiltValueNullFieldError.checkNotNull(
                totalCount, r'ListRecruitSerializers', 'totalCount'),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'results';
        results.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'ListRecruitSerializers', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
