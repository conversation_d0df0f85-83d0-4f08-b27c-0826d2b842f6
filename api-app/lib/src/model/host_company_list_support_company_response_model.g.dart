// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'host_company_list_support_company_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$HostCompanyListSupportCompanyResponseModel
    extends HostCompanyListSupportCompanyResponseModel {
  @override
  final String? message;
  @override
  final BuiltList<ErrorDetail>? errors;
  @override
  final BuiltList<HostCompanyGetSupportCompany> data;

  factory _$HostCompanyListSupportCompanyResponseModel(
          [void Function(HostCompanyListSupportCompanyResponseModelBuilder)?
              updates]) =>
      (new HostCompanyListSupportCompanyResponseModelBuilder()..update(updates))
          ._build();

  _$HostCompanyListSupportCompanyResponseModel._(
      {this.message, this.errors, required this.data})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'HostCompanyListSupportCompanyResponseModel', 'data');
  }

  @override
  HostCompanyListSupportCompanyResponseModel rebuild(
          void Function(HostCompanyListSupportCompanyResponseModelBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  HostCompanyListSupportCompanyResponseModelBuilder toBuilder() =>
      new HostCompanyListSupportCompanyResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is HostCompanyListSupportCompanyResponseModel &&
        message == other.message &&
        errors == other.errors &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'HostCompanyListSupportCompanyResponseModel')
          ..add('message', message)
          ..add('errors', errors)
          ..add('data', data))
        .toString();
  }
}

class HostCompanyListSupportCompanyResponseModelBuilder
    implements
        Builder<HostCompanyListSupportCompanyResponseModel,
            HostCompanyListSupportCompanyResponseModelBuilder> {
  _$HostCompanyListSupportCompanyResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  ListBuilder<HostCompanyGetSupportCompany>? _data;
  ListBuilder<HostCompanyGetSupportCompany> get data =>
      _$this._data ??= new ListBuilder<HostCompanyGetSupportCompany>();
  set data(ListBuilder<HostCompanyGetSupportCompany>? data) =>
      _$this._data = data;

  HostCompanyListSupportCompanyResponseModelBuilder() {
    HostCompanyListSupportCompanyResponseModel._defaults(this);
  }

  HostCompanyListSupportCompanyResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _errors = $v.errors?.toBuilder();
      _data = $v.data.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(HostCompanyListSupportCompanyResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$HostCompanyListSupportCompanyResponseModel;
  }

  @override
  void update(
      void Function(HostCompanyListSupportCompanyResponseModelBuilder)?
          updates) {
    if (updates != null) updates(this);
  }

  @override
  HostCompanyListSupportCompanyResponseModel build() => _build();

  _$HostCompanyListSupportCompanyResponseModel _build() {
    _$HostCompanyListSupportCompanyResponseModel _$result;
    try {
      _$result = _$v ??
          new _$HostCompanyListSupportCompanyResponseModel._(
            message: message,
            errors: _errors?.build(),
            data: data.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'errors';
        _errors?.build();
        _$failedField = 'data';
        data.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'HostCompanyListSupportCompanyResponseModel',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
