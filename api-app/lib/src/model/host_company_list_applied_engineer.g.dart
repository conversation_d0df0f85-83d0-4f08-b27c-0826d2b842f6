// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'host_company_list_applied_engineer.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$HostCompanyListAppliedEngineer extends HostCompanyListAppliedEngineer {
  @override
  final BuiltList<HostCompanyParamListUserAppliedCompany> data;
  @override
  final int totalEngineers;

  factory _$HostCompanyListAppliedEngineer(
          [void Function(HostCompanyListAppliedEngineerBuilder)? updates]) =>
      (new HostCompanyListAppliedEngineerBuilder()..update(updates))._build();

  _$HostCompanyListAppliedEngineer._(
      {required this.data, required this.totalEngineers})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'HostCompanyListAppliedEngineer', 'data');
    BuiltValueNullFieldError.checkNotNull(
        totalEngineers, r'HostCompanyListAppliedEngineer', 'totalEngineers');
  }

  @override
  HostCompanyListAppliedEngineer rebuild(
          void Function(HostCompanyListAppliedEngineerBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  HostCompanyListAppliedEngineerBuilder toBuilder() =>
      new HostCompanyListAppliedEngineerBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is HostCompanyListAppliedEngineer &&
        data == other.data &&
        totalEngineers == other.totalEngineers;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, totalEngineers.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'HostCompanyListAppliedEngineer')
          ..add('data', data)
          ..add('totalEngineers', totalEngineers))
        .toString();
  }
}

class HostCompanyListAppliedEngineerBuilder
    implements
        Builder<HostCompanyListAppliedEngineer,
            HostCompanyListAppliedEngineerBuilder> {
  _$HostCompanyListAppliedEngineer? _$v;

  ListBuilder<HostCompanyParamListUserAppliedCompany>? _data;
  ListBuilder<HostCompanyParamListUserAppliedCompany> get data =>
      _$this._data ??=
          new ListBuilder<HostCompanyParamListUserAppliedCompany>();
  set data(ListBuilder<HostCompanyParamListUserAppliedCompany>? data) =>
      _$this._data = data;

  int? _totalEngineers;
  int? get totalEngineers => _$this._totalEngineers;
  set totalEngineers(int? totalEngineers) =>
      _$this._totalEngineers = totalEngineers;

  HostCompanyListAppliedEngineerBuilder() {
    HostCompanyListAppliedEngineer._defaults(this);
  }

  HostCompanyListAppliedEngineerBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _data = $v.data.toBuilder();
      _totalEngineers = $v.totalEngineers;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(HostCompanyListAppliedEngineer other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$HostCompanyListAppliedEngineer;
  }

  @override
  void update(void Function(HostCompanyListAppliedEngineerBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  HostCompanyListAppliedEngineer build() => _build();

  _$HostCompanyListAppliedEngineer _build() {
    _$HostCompanyListAppliedEngineer _$result;
    try {
      _$result = _$v ??
          new _$HostCompanyListAppliedEngineer._(
            data: data.build(),
            totalEngineers: BuiltValueNullFieldError.checkNotNull(
                totalEngineers,
                r'HostCompanyListAppliedEngineer',
                'totalEngineers'),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        data.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'HostCompanyListAppliedEngineer', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
