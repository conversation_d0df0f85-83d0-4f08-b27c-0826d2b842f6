//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'request_interview_success.g.dart';

/// RequestInterviewSuccess
///
/// Properties:
/// * [applyId] 
@BuiltValue()
abstract class RequestInterviewSuccess implements Built<RequestInterviewSuccess, RequestInterviewSuccessBuilder> {
  @BuiltValueField(wireName: r'apply_id')
  int get applyId;

  RequestInterviewSuccess._();

  factory RequestInterviewSuccess([void updates(RequestInterviewSuccessBuilder b)]) = _$RequestInterviewSuccess;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(RequestInterviewSuccessBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<RequestInterviewSuccess> get serializer => _$RequestInterviewSuccessSerializer();
}

class _$RequestInterviewSuccessSerializer implements PrimitiveSerializer<RequestInterviewSuccess> {
  @override
  final Iterable<Type> types = const [RequestInterviewSuccess, _$RequestInterviewSuccess];

  @override
  final String wireName = r'RequestInterviewSuccess';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    RequestInterviewSuccess object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'apply_id';
    yield serializers.serialize(
      object.applyId,
      specifiedType: const FullType(int),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    RequestInterviewSuccess object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required RequestInterviewSuccessBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'apply_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.applyId = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  RequestInterviewSuccess deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = RequestInterviewSuccessBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

