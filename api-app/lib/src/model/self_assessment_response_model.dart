//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/self_assessment_data.dart';
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'self_assessment_response_model.g.dart';

/// SelfAssessmentResponseModel
///
/// Properties:
/// * [message] 
/// * [data] 
/// * [errors] 
@BuiltValue()
abstract class SelfAssessmentResponseModel implements Built<SelfAssessmentResponseModel, SelfAssessmentResponseModelBuilder> {
  @BuiltValueField(wireName: r'message')
  String? get message;

  @BuiltValueField(wireName: r'data')
  SelfAssessmentData get data;

  @BuiltValueField(wireName: r'errors')
  BuiltList<String>? get errors;

  SelfAssessmentResponseModel._();

  factory SelfAssessmentResponseModel([void updates(SelfAssessmentResponseModelBuilder b)]) = _$SelfAssessmentResponseModel;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(SelfAssessmentResponseModelBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<SelfAssessmentResponseModel> get serializer => _$SelfAssessmentResponseModelSerializer();
}

class _$SelfAssessmentResponseModelSerializer implements PrimitiveSerializer<SelfAssessmentResponseModel> {
  @override
  final Iterable<Type> types = const [SelfAssessmentResponseModel, _$SelfAssessmentResponseModel];

  @override
  final String wireName = r'SelfAssessmentResponseModel';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    SelfAssessmentResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'message';
    yield object.message == null ? null : serializers.serialize(
      object.message,
      specifiedType: const FullType.nullable(String),
    );
    yield r'data';
    yield serializers.serialize(
      object.data,
      specifiedType: const FullType(SelfAssessmentData),
    );
    yield r'errors';
    yield object.errors == null ? null : serializers.serialize(
      object.errors,
      specifiedType: const FullType.nullable(BuiltList, [FullType(String)]),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    SelfAssessmentResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required SelfAssessmentResponseModelBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'message':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.message = valueDes;
          break;
        case r'data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(SelfAssessmentData),
          ) as SelfAssessmentData;
          result.data.replace(valueDes);
          break;
        case r'errors':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(BuiltList, [FullType(String)]),
          ) as BuiltList<String>?;
          if (valueDes == null) continue;
          result.errors.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  SelfAssessmentResponseModel deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = SelfAssessmentResponseModelBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

