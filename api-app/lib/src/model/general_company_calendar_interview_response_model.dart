//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/error_detail.dart';
import 'package:openapi/src/model/general_company_interview_data.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'general_company_calendar_interview_response_model.g.dart';

/// GeneralCompanyCalendarInterviewResponseModel
///
/// Properties:
/// * [message] 
/// * [errors] 
/// * [data] 
@BuiltValue()
abstract class GeneralCompanyCalendarInterviewResponseModel implements Built<GeneralCompanyCalendarInterviewResponseModel, GeneralCompanyCalendarInterviewResponseModelBuilder> {
  @BuiltValueField(wireName: r'message')
  String? get message;

  @BuiltValueField(wireName: r'errors')
  BuiltList<ErrorDetail>? get errors;

  @BuiltValueField(wireName: r'data')
  BuiltList<GeneralCompanyInterviewData> get data;

  GeneralCompanyCalendarInterviewResponseModel._();

  factory GeneralCompanyCalendarInterviewResponseModel([void updates(GeneralCompanyCalendarInterviewResponseModelBuilder b)]) = _$GeneralCompanyCalendarInterviewResponseModel;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(GeneralCompanyCalendarInterviewResponseModelBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<GeneralCompanyCalendarInterviewResponseModel> get serializer => _$GeneralCompanyCalendarInterviewResponseModelSerializer();
}

class _$GeneralCompanyCalendarInterviewResponseModelSerializer implements PrimitiveSerializer<GeneralCompanyCalendarInterviewResponseModel> {
  @override
  final Iterable<Type> types = const [GeneralCompanyCalendarInterviewResponseModel, _$GeneralCompanyCalendarInterviewResponseModel];

  @override
  final String wireName = r'GeneralCompanyCalendarInterviewResponseModel';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    GeneralCompanyCalendarInterviewResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'message';
    yield object.message == null ? null : serializers.serialize(
      object.message,
      specifiedType: const FullType.nullable(String),
    );
    yield r'errors';
    yield object.errors == null ? null : serializers.serialize(
      object.errors,
      specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
    );
    yield r'data';
    yield serializers.serialize(
      object.data,
      specifiedType: const FullType(BuiltList, [FullType(GeneralCompanyInterviewData)]),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    GeneralCompanyCalendarInterviewResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required GeneralCompanyCalendarInterviewResponseModelBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'message':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.message = valueDes;
          break;
        case r'errors':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
          ) as BuiltList<ErrorDetail>?;
          if (valueDes == null) continue;
          result.errors.replace(valueDes);
          break;
        case r'data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(GeneralCompanyInterviewData)]),
          ) as BuiltList<GeneralCompanyInterviewData>;
          result.data.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  GeneralCompanyCalendarInterviewResponseModel deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = GeneralCompanyCalendarInterviewResponseModelBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

