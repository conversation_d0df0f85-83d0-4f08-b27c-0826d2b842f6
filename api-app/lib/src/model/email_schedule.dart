//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'email_schedule.g.dart';

/// EmailSchedule
///
/// Properties:
/// * [id] 
/// * [type] 
/// * [subject] 
/// * [body] 
/// * [weekday] 
/// * [sendTime] 
/// * [sendDatetime] 
/// * [isValid] 
/// * [isDeleted] 
/// * [isRepeat] 
/// * [created] 
@BuiltValue()
abstract class EmailSchedule implements Built<EmailSchedule, EmailScheduleBuilder> {
  @BuiltValueField(wireName: r'id')
  int? get id;

  @BuiltValueField(wireName: r'type')
  int? get type;

  @BuiltValueField(wireName: r'subject')
  String? get subject;

  @BuiltValueField(wireName: r'body')
  String? get body;

  @BuiltValueField(wireName: r'weekday')
  String? get weekday;

  @BuiltValueField(wireName: r'send_time')
  String? get sendTime;

  @BuiltValueField(wireName: r'send_datetime')
  DateTime? get sendDatetime;

  @BuiltValueField(wireName: r'is_valid')
  int? get isValid;

  @BuiltValueField(wireName: r'is_deleted')
  int? get isDeleted;

  @BuiltValueField(wireName: r'is_repeat')
  int? get isRepeat;

  @BuiltValueField(wireName: r'created')
  DateTime? get created;

  EmailSchedule._();

  factory EmailSchedule([void updates(EmailScheduleBuilder b)]) = _$EmailSchedule;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(EmailScheduleBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<EmailSchedule> get serializer => _$EmailScheduleSerializer();
}

class _$EmailScheduleSerializer implements PrimitiveSerializer<EmailSchedule> {
  @override
  final Iterable<Type> types = const [EmailSchedule, _$EmailSchedule];

  @override
  final String wireName = r'EmailSchedule';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    EmailSchedule object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.id != null) {
      yield r'id';
      yield serializers.serialize(
        object.id,
        specifiedType: const FullType(int),
      );
    }
    if (object.type != null) {
      yield r'type';
      yield serializers.serialize(
        object.type,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.subject != null) {
      yield r'subject';
      yield serializers.serialize(
        object.subject,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.body != null) {
      yield r'body';
      yield serializers.serialize(
        object.body,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.weekday != null) {
      yield r'weekday';
      yield serializers.serialize(
        object.weekday,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.sendTime != null) {
      yield r'send_time';
      yield serializers.serialize(
        object.sendTime,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.sendDatetime != null) {
      yield r'send_datetime';
      yield serializers.serialize(
        object.sendDatetime,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.isValid != null) {
      yield r'is_valid';
      yield serializers.serialize(
        object.isValid,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.isDeleted != null) {
      yield r'is_deleted';
      yield serializers.serialize(
        object.isDeleted,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.isRepeat != null) {
      yield r'is_repeat';
      yield serializers.serialize(
        object.isRepeat,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.created != null) {
      yield r'created';
      yield serializers.serialize(
        object.created,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    EmailSchedule object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required EmailScheduleBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.id = valueDes;
          break;
        case r'type':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.type = valueDes;
          break;
        case r'subject':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.subject = valueDes;
          break;
        case r'body':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.body = valueDes;
          break;
        case r'weekday':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.weekday = valueDes;
          break;
        case r'send_time':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.sendTime = valueDes;
          break;
        case r'send_datetime':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.sendDatetime = valueDes;
          break;
        case r'is_valid':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.isValid = valueDes;
          break;
        case r'is_deleted':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.isDeleted = valueDes;
          break;
        case r'is_repeat':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.isRepeat = valueDes;
          break;
        case r'created':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.created = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  EmailSchedule deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = EmailScheduleBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

