//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'general_company_interview_data.g.dart';

/// GeneralCompanyInterviewData
///
/// Properties:
/// * [applyId] 
/// * [interviewDatetime] 
/// * [hostCompany] 
/// * [hostAgent] 
/// * [engineerFirstName] 
/// * [engineerLastName] 
/// * [engineerNickName] 
@BuiltValue()
abstract class GeneralCompanyInterviewData implements Built<GeneralCompanyInterviewData, GeneralCompanyInterviewDataBuilder> {
  @BuiltValueField(wireName: r'apply_id')
  int? get applyId;

  @BuiltValueField(wireName: r'interview_datetime')
  DateTime? get interviewDatetime;

  @BuiltValueField(wireName: r'host_company')
  int? get hostCompany;

  @BuiltValueField(wireName: r'host_agent')
  int? get hostAgent;

  @BuiltValueField(wireName: r'engineer_first_name')
  String? get engineerFirstName;

  @BuiltValueField(wireName: r'engineer_last_name')
  String? get engineerLastName;

  @BuiltValueField(wireName: r'engineer_nick_name')
  String? get engineerNickName;

  GeneralCompanyInterviewData._();

  factory GeneralCompanyInterviewData([void updates(GeneralCompanyInterviewDataBuilder b)]) = _$GeneralCompanyInterviewData;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(GeneralCompanyInterviewDataBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<GeneralCompanyInterviewData> get serializer => _$GeneralCompanyInterviewDataSerializer();
}

class _$GeneralCompanyInterviewDataSerializer implements PrimitiveSerializer<GeneralCompanyInterviewData> {
  @override
  final Iterable<Type> types = const [GeneralCompanyInterviewData, _$GeneralCompanyInterviewData];

  @override
  final String wireName = r'GeneralCompanyInterviewData';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    GeneralCompanyInterviewData object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.applyId != null) {
      yield r'apply_id';
      yield serializers.serialize(
        object.applyId,
        specifiedType: const FullType(int),
      );
    }
    if (object.interviewDatetime != null) {
      yield r'interview_datetime';
      yield serializers.serialize(
        object.interviewDatetime,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.hostCompany != null) {
      yield r'host_company';
      yield serializers.serialize(
        object.hostCompany,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.hostAgent != null) {
      yield r'host_agent';
      yield serializers.serialize(
        object.hostAgent,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.engineerFirstName != null) {
      yield r'engineer_first_name';
      yield serializers.serialize(
        object.engineerFirstName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.engineerLastName != null) {
      yield r'engineer_last_name';
      yield serializers.serialize(
        object.engineerLastName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.engineerNickName != null) {
      yield r'engineer_nick_name';
      yield serializers.serialize(
        object.engineerNickName,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    GeneralCompanyInterviewData object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required GeneralCompanyInterviewDataBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'apply_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.applyId = valueDes;
          break;
        case r'interview_datetime':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.interviewDatetime = valueDes;
          break;
        case r'host_company':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.hostCompany = valueDes;
          break;
        case r'host_agent':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.hostAgent = valueDes;
          break;
        case r'engineer_first_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.engineerFirstName = valueDes;
          break;
        case r'engineer_last_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.engineerLastName = valueDes;
          break;
        case r'engineer_nick_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.engineerNickName = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  GeneralCompanyInterviewData deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = GeneralCompanyInterviewDataBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

