//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'login_with_sns_response_data.g.dart';

/// LoginWithSNSResponseData
///
/// Properties:
/// * [accessToken] 
/// * [refreshToken] 
/// * [userType] 
/// * [email] 
@BuiltValue()
abstract class LoginWithSNSResponseData implements Built<LoginWithSNSResponseData, LoginWithSNSResponseDataBuilder> {
  @BuiltValueField(wireName: r'access_token')
  String? get accessToken;

  @BuiltValueField(wireName: r'refresh_token')
  String? get refreshToken;

  @BuiltValueField(wireName: r'user_type')
  String? get userType;

  @BuiltValueField(wireName: r'email')
  String? get email;

  LoginWithSNSResponseData._();

  factory LoginWithSNSResponseData([void updates(LoginWithSNSResponseDataBuilder b)]) = _$LoginWithSNSResponseData;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(LoginWithSNSResponseDataBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<LoginWithSNSResponseData> get serializer => _$LoginWithSNSResponseDataSerializer();
}

class _$LoginWithSNSResponseDataSerializer implements PrimitiveSerializer<LoginWithSNSResponseData> {
  @override
  final Iterable<Type> types = const [LoginWithSNSResponseData, _$LoginWithSNSResponseData];

  @override
  final String wireName = r'LoginWithSNSResponseData';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    LoginWithSNSResponseData object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'access_token';
    yield object.accessToken == null ? null : serializers.serialize(
      object.accessToken,
      specifiedType: const FullType.nullable(String),
    );
    yield r'refresh_token';
    yield object.refreshToken == null ? null : serializers.serialize(
      object.refreshToken,
      specifiedType: const FullType.nullable(String),
    );
    yield r'user_type';
    yield object.userType == null ? null : serializers.serialize(
      object.userType,
      specifiedType: const FullType.nullable(String),
    );
    yield r'email';
    yield object.email == null ? null : serializers.serialize(
      object.email,
      specifiedType: const FullType.nullable(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    LoginWithSNSResponseData object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required LoginWithSNSResponseDataBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'access_token':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.accessToken = valueDes;
          break;
        case r'refresh_token':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.refreshToken = valueDes;
          break;
        case r'user_type':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.userType = valueDes;
          break;
        case r'email':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.email = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  LoginWithSNSResponseData deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = LoginWithSNSResponseDataBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

