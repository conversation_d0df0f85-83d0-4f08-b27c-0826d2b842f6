// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recruit_count_filter_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$RecruitCountFilterResponseModel
    extends RecruitCountFilterResponseModel {
  @override
  final String? message;
  @override
  final int data;
  @override
  final BuiltList<ErrorDetail>? errors;

  factory _$RecruitCountFilterResponseModel(
          [void Function(RecruitCountFilterResponseModelBuilder)? updates]) =>
      (new RecruitCountFilterResponseModelBuilder()..update(updates))._build();

  _$RecruitCountFilterResponseModel._(
      {this.message, required this.data, this.errors})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'RecruitCountFilterResponseModel', 'data');
  }

  @override
  RecruitCountFilterResponseModel rebuild(
          void Function(RecruitCountFilterResponseModelBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  RecruitCountFilterResponseModelBuilder toBuilder() =>
      new RecruitCountFilterResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is RecruitCountFilterResponseModel &&
        message == other.message &&
        data == other.data &&
        errors == other.errors;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'RecruitCountFilterResponseModel')
          ..add('message', message)
          ..add('data', data)
          ..add('errors', errors))
        .toString();
  }
}

class RecruitCountFilterResponseModelBuilder
    implements
        Builder<RecruitCountFilterResponseModel,
            RecruitCountFilterResponseModelBuilder> {
  _$RecruitCountFilterResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  int? _data;
  int? get data => _$this._data;
  set data(int? data) => _$this._data = data;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  RecruitCountFilterResponseModelBuilder() {
    RecruitCountFilterResponseModel._defaults(this);
  }

  RecruitCountFilterResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _data = $v.data;
      _errors = $v.errors?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(RecruitCountFilterResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$RecruitCountFilterResponseModel;
  }

  @override
  void update(void Function(RecruitCountFilterResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  RecruitCountFilterResponseModel build() => _build();

  _$RecruitCountFilterResponseModel _build() {
    _$RecruitCountFilterResponseModel _$result;
    try {
      _$result = _$v ??
          new _$RecruitCountFilterResponseModel._(
            message: message,
            data: BuiltValueNullFieldError.checkNotNull(
                data, r'RecruitCountFilterResponseModel', 'data'),
            errors: _errors?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'errors';
        _errors?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'RecruitCountFilterResponseModel', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
