//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/date.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'update_eng_high_light_project.g.dart';

/// UpdateEngHighLightProject
///
/// Properties:
/// * [name] 
/// * [description] 
/// * [size] 
/// * [roleName] 
/// * [responsibilities] 
/// * [technologyUsed] 
/// * [fromDate] 
/// * [toDate] 
@BuiltValue()
abstract class UpdateEngHighLightProject implements Built<UpdateEngHighLightProject, UpdateEngHighLightProjectBuilder> {
  @BuiltValueField(wireName: r'name')
  String? get name;

  @BuiltValueField(wireName: r'description')
  String? get description;

  @BuiltValueField(wireName: r'size')
  String? get size;

  @BuiltValueField(wireName: r'role_name')
  String? get roleName;

  @BuiltValueField(wireName: r'responsibilities')
  String? get responsibilities;

  @BuiltValueField(wireName: r'technology_used')
  String? get technologyUsed;

  @BuiltValueField(wireName: r'from_date')
  Date? get fromDate;

  @BuiltValueField(wireName: r'to_date')
  Date? get toDate;

  UpdateEngHighLightProject._();

  factory UpdateEngHighLightProject([void updates(UpdateEngHighLightProjectBuilder b)]) = _$UpdateEngHighLightProject;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(UpdateEngHighLightProjectBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<UpdateEngHighLightProject> get serializer => _$UpdateEngHighLightProjectSerializer();
}

class _$UpdateEngHighLightProjectSerializer implements PrimitiveSerializer<UpdateEngHighLightProject> {
  @override
  final Iterable<Type> types = const [UpdateEngHighLightProject, _$UpdateEngHighLightProject];

  @override
  final String wireName = r'UpdateEngHighLightProject';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    UpdateEngHighLightProject object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.name != null) {
      yield r'name';
      yield serializers.serialize(
        object.name,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.description != null) {
      yield r'description';
      yield serializers.serialize(
        object.description,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.size != null) {
      yield r'size';
      yield serializers.serialize(
        object.size,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.roleName != null) {
      yield r'role_name';
      yield serializers.serialize(
        object.roleName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.responsibilities != null) {
      yield r'responsibilities';
      yield serializers.serialize(
        object.responsibilities,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.technologyUsed != null) {
      yield r'technology_used';
      yield serializers.serialize(
        object.technologyUsed,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.fromDate != null) {
      yield r'from_date';
      yield serializers.serialize(
        object.fromDate,
        specifiedType: const FullType.nullable(Date),
      );
    }
    if (object.toDate != null) {
      yield r'to_date';
      yield serializers.serialize(
        object.toDate,
        specifiedType: const FullType.nullable(Date),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    UpdateEngHighLightProject object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required UpdateEngHighLightProjectBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.name = valueDes;
          break;
        case r'description':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.description = valueDes;
          break;
        case r'size':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.size = valueDes;
          break;
        case r'role_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.roleName = valueDes;
          break;
        case r'responsibilities':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.responsibilities = valueDes;
          break;
        case r'technology_used':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.technologyUsed = valueDes;
          break;
        case r'from_date':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(Date),
          ) as Date?;
          if (valueDes == null) continue;
          result.fromDate = valueDes;
          break;
        case r'to_date':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(Date),
          ) as Date?;
          if (valueDes == null) continue;
          result.toDate = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  UpdateEngHighLightProject deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = UpdateEngHighLightProjectBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

