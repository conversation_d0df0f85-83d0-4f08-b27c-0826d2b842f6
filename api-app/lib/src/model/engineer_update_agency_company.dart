//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'engineer_update_agency_company.g.dart';

/// EngineerUpdateAgencyCompany
///
/// Properties:
/// * [optionalEngineerId] 
/// * [contactMail] 
@BuiltValue()
abstract class EngineerUpdateAgencyCompany implements Built<EngineerUpdateAgencyCompany, EngineerUpdateAgencyCompanyBuilder> {
  @BuiltValueField(wireName: r'optional_engineer_id')
  int? get optionalEngineerId;

  @BuiltValueField(wireName: r'contact_mail')
  String get contactMail;

  EngineerUpdateAgencyCompany._();

  factory EngineerUpdateAgencyCompany([void updates(EngineerUpdateAgencyCompanyBuilder b)]) = _$EngineerUpdateAgencyCompany;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(EngineerUpdateAgencyCompanyBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<EngineerUpdateAgencyCompany> get serializer => _$EngineerUpdateAgencyCompanySerializer();
}

class _$EngineerUpdateAgencyCompanySerializer implements PrimitiveSerializer<EngineerUpdateAgencyCompany> {
  @override
  final Iterable<Type> types = const [EngineerUpdateAgencyCompany, _$EngineerUpdateAgencyCompany];

  @override
  final String wireName = r'EngineerUpdateAgencyCompany';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    EngineerUpdateAgencyCompany object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.optionalEngineerId != null) {
      yield r'optional_engineer_id';
      yield serializers.serialize(
        object.optionalEngineerId,
        specifiedType: const FullType(int),
      );
    }
    yield r'contact_mail';
    yield serializers.serialize(
      object.contactMail,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    EngineerUpdateAgencyCompany object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required EngineerUpdateAgencyCompanyBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'optional_engineer_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.optionalEngineerId = valueDes;
          break;
        case r'contact_mail':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.contactMail = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  EngineerUpdateAgencyCompany deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = EngineerUpdateAgencyCompanyBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

