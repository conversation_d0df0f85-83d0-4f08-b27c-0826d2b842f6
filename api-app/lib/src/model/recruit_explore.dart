//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/recruit_company.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'recruit_explore.g.dart';

/// RecruitExplore
///
/// Properties:
/// * [recruitId] 
/// * [title] 
/// * [catchCopy] 
/// * [payrollPriceFrom] 
/// * [payrollPriceTo] 
/// * [payrollCode] 
/// * [startDate] 
/// * [endDate] 
/// * [jobCode] 
/// * [hostCompany] 
/// * [skillJobCode1] 
/// * [skillJobCode2] 
/// * [skillJobCode3] 
/// * [waitingFlag] 
/// * [created] 
@BuiltValue()
abstract class RecruitExplore implements Built<RecruitExplore, RecruitExploreBuilder> {
  @BuiltValueField(wireName: r'recruit_id')
  int? get recruitId;

  @BuiltValueField(wireName: r'title')
  String? get title;

  @BuiltValueField(wireName: r'catch_copy')
  String? get catchCopy;

  @BuiltValueField(wireName: r'payroll_price_from')
  String get payrollPriceFrom;

  @BuiltValueField(wireName: r'payroll_price_to')
  String get payrollPriceTo;

  @BuiltValueField(wireName: r'payroll_code')
  String? get payrollCode;

  @BuiltValueField(wireName: r'start_date')
  DateTime? get startDate;

  @BuiltValueField(wireName: r'end_date')
  DateTime? get endDate;

  @BuiltValueField(wireName: r'job_code')
  String? get jobCode;

  @BuiltValueField(wireName: r'host_company')
  RecruitCompany? get hostCompany;

  @BuiltValueField(wireName: r'skill_job_code1')
  String? get skillJobCode1;

  @BuiltValueField(wireName: r'skill_job_code2')
  String? get skillJobCode2;

  @BuiltValueField(wireName: r'skill_job_code3')
  String? get skillJobCode3;

  @BuiltValueField(wireName: r'waiting_flag')
  String? get waitingFlag;

  @BuiltValueField(wireName: r'created')
  DateTime? get created;

  RecruitExplore._();

  factory RecruitExplore([void updates(RecruitExploreBuilder b)]) = _$RecruitExplore;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(RecruitExploreBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<RecruitExplore> get serializer => _$RecruitExploreSerializer();
}

class _$RecruitExploreSerializer implements PrimitiveSerializer<RecruitExplore> {
  @override
  final Iterable<Type> types = const [RecruitExplore, _$RecruitExplore];

  @override
  final String wireName = r'RecruitExplore';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    RecruitExplore object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.recruitId != null) {
      yield r'recruit_id';
      yield serializers.serialize(
        object.recruitId,
        specifiedType: const FullType(int),
      );
    }
    if (object.title != null) {
      yield r'title';
      yield serializers.serialize(
        object.title,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.catchCopy != null) {
      yield r'catch_copy';
      yield serializers.serialize(
        object.catchCopy,
        specifiedType: const FullType.nullable(String),
      );
    }
    yield r'payroll_price_from';
    yield serializers.serialize(
      object.payrollPriceFrom,
      specifiedType: const FullType(String),
    );
    yield r'payroll_price_to';
    yield serializers.serialize(
      object.payrollPriceTo,
      specifiedType: const FullType(String),
    );
    if (object.payrollCode != null) {
      yield r'payroll_code';
      yield serializers.serialize(
        object.payrollCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.startDate != null) {
      yield r'start_date';
      yield serializers.serialize(
        object.startDate,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.endDate != null) {
      yield r'end_date';
      yield serializers.serialize(
        object.endDate,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.jobCode != null) {
      yield r'job_code';
      yield serializers.serialize(
        object.jobCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.hostCompany != null) {
      yield r'host_company';
      yield serializers.serialize(
        object.hostCompany,
        specifiedType: const FullType(RecruitCompany),
      );
    }
    if (object.skillJobCode1 != null) {
      yield r'skill_job_code1';
      yield serializers.serialize(
        object.skillJobCode1,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.skillJobCode2 != null) {
      yield r'skill_job_code2';
      yield serializers.serialize(
        object.skillJobCode2,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.skillJobCode3 != null) {
      yield r'skill_job_code3';
      yield serializers.serialize(
        object.skillJobCode3,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.waitingFlag != null) {
      yield r'waiting_flag';
      yield serializers.serialize(
        object.waitingFlag,
        specifiedType: const FullType(String),
      );
    }
    if (object.created != null) {
      yield r'created';
      yield serializers.serialize(
        object.created,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    RecruitExplore object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required RecruitExploreBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'recruit_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.recruitId = valueDes;
          break;
        case r'title':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.title = valueDes;
          break;
        case r'catch_copy':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.catchCopy = valueDes;
          break;
        case r'payroll_price_from':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.payrollPriceFrom = valueDes;
          break;
        case r'payroll_price_to':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.payrollPriceTo = valueDes;
          break;
        case r'payroll_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.payrollCode = valueDes;
          break;
        case r'start_date':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.startDate = valueDes;
          break;
        case r'end_date':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.endDate = valueDes;
          break;
        case r'job_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.jobCode = valueDes;
          break;
        case r'host_company':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(RecruitCompany),
          ) as RecruitCompany;
          result.hostCompany.replace(valueDes);
          break;
        case r'skill_job_code1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.skillJobCode1 = valueDes;
          break;
        case r'skill_job_code2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.skillJobCode2 = valueDes;
          break;
        case r'skill_job_code3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.skillJobCode3 = valueDes;
          break;
        case r'waiting_flag':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.waitingFlag = valueDes;
          break;
        case r'created':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.created = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  RecruitExplore deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = RecruitExploreBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

