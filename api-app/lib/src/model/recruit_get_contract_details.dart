//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'recruit_get_contract_details.g.dart';

/// RecruitGetContractDetails
///
/// Properties:
/// * [applyId] 
/// * [hostCompanyId] 
/// * [hostCompanyName] 
/// * [hostAgentFirstName] 
/// * [hostAgentLastName] 
/// * [hostAgentAcceptSignPath] 
/// * [supportCompanyId] 
/// * [supportCompanyName] 
/// * [supportAgentFirstName] 
/// * [supportAgentLastName] 
/// * [supportAgentAcceptSignPath] 
/// * [agencyCompanyId] 
/// * [agencyCompanyName] 
/// * [agencyAgentFirstName] 
/// * [agencyAgentLastName] 
/// * [agencyAgentAcceptSignPath] 
/// * [engineerId] 
/// * [engineerFirstName] 
/// * [engineerLastName] 
/// * [engineerAcceptSignPath] 
@BuiltValue()
abstract class RecruitGetContractDetails implements Built<RecruitGetContractDetails, RecruitGetContractDetailsBuilder> {
  @BuiltValueField(wireName: r'apply_id')
  int? get applyId;

  @BuiltValueField(wireName: r'host_company_id')
  int? get hostCompanyId;

  @BuiltValueField(wireName: r'host_company_name')
  String? get hostCompanyName;

  @BuiltValueField(wireName: r'host_agent_first_name')
  String? get hostAgentFirstName;

  @BuiltValueField(wireName: r'host_agent_last_name')
  String? get hostAgentLastName;

  @BuiltValueField(wireName: r'host_agent_accept_sign_path')
  String? get hostAgentAcceptSignPath;

  @BuiltValueField(wireName: r'support_company_id')
  int? get supportCompanyId;

  @BuiltValueField(wireName: r'support_company_name')
  String? get supportCompanyName;

  @BuiltValueField(wireName: r'support_agent_first_name')
  String? get supportAgentFirstName;

  @BuiltValueField(wireName: r'support_agent_last_name')
  String? get supportAgentLastName;

  @BuiltValueField(wireName: r'support_agent_accept_sign_path')
  String? get supportAgentAcceptSignPath;

  @BuiltValueField(wireName: r'agency_company_id')
  int? get agencyCompanyId;

  @BuiltValueField(wireName: r'agency_company_name')
  String? get agencyCompanyName;

  @BuiltValueField(wireName: r'agency_agent_first_name')
  String? get agencyAgentFirstName;

  @BuiltValueField(wireName: r'agency_agent_last_name')
  String? get agencyAgentLastName;

  @BuiltValueField(wireName: r'agency_agent_accept_sign_path')
  String? get agencyAgentAcceptSignPath;

  @BuiltValueField(wireName: r'engineer_id')
  int? get engineerId;

  @BuiltValueField(wireName: r'engineer_first_name')
  String? get engineerFirstName;

  @BuiltValueField(wireName: r'engineer_last_name')
  String? get engineerLastName;

  @BuiltValueField(wireName: r'engineer_accept_sign_path')
  String? get engineerAcceptSignPath;

  RecruitGetContractDetails._();

  factory RecruitGetContractDetails([void updates(RecruitGetContractDetailsBuilder b)]) = _$RecruitGetContractDetails;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(RecruitGetContractDetailsBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<RecruitGetContractDetails> get serializer => _$RecruitGetContractDetailsSerializer();
}

class _$RecruitGetContractDetailsSerializer implements PrimitiveSerializer<RecruitGetContractDetails> {
  @override
  final Iterable<Type> types = const [RecruitGetContractDetails, _$RecruitGetContractDetails];

  @override
  final String wireName = r'RecruitGetContractDetails';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    RecruitGetContractDetails object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.applyId != null) {
      yield r'apply_id';
      yield serializers.serialize(
        object.applyId,
        specifiedType: const FullType(int),
      );
    }
    if (object.hostCompanyId != null) {
      yield r'host_company_id';
      yield serializers.serialize(
        object.hostCompanyId,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.hostCompanyName != null) {
      yield r'host_company_name';
      yield serializers.serialize(
        object.hostCompanyName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.hostAgentFirstName != null) {
      yield r'host_agent_first_name';
      yield serializers.serialize(
        object.hostAgentFirstName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.hostAgentLastName != null) {
      yield r'host_agent_last_name';
      yield serializers.serialize(
        object.hostAgentLastName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.hostAgentAcceptSignPath != null) {
      yield r'host_agent_accept_sign_path';
      yield serializers.serialize(
        object.hostAgentAcceptSignPath,
        specifiedType: const FullType(String),
      );
    }
    if (object.supportCompanyId != null) {
      yield r'support_company_id';
      yield serializers.serialize(
        object.supportCompanyId,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.supportCompanyName != null) {
      yield r'support_company_name';
      yield serializers.serialize(
        object.supportCompanyName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.supportAgentFirstName != null) {
      yield r'support_agent_first_name';
      yield serializers.serialize(
        object.supportAgentFirstName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.supportAgentLastName != null) {
      yield r'support_agent_last_name';
      yield serializers.serialize(
        object.supportAgentLastName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.supportAgentAcceptSignPath != null) {
      yield r'support_agent_accept_sign_path';
      yield serializers.serialize(
        object.supportAgentAcceptSignPath,
        specifiedType: const FullType(String),
      );
    }
    if (object.agencyCompanyId != null) {
      yield r'agency_company_id';
      yield serializers.serialize(
        object.agencyCompanyId,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.agencyCompanyName != null) {
      yield r'agency_company_name';
      yield serializers.serialize(
        object.agencyCompanyName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.agencyAgentFirstName != null) {
      yield r'agency_agent_first_name';
      yield serializers.serialize(
        object.agencyAgentFirstName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.agencyAgentLastName != null) {
      yield r'agency_agent_last_name';
      yield serializers.serialize(
        object.agencyAgentLastName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.agencyAgentAcceptSignPath != null) {
      yield r'agency_agent_accept_sign_path';
      yield serializers.serialize(
        object.agencyAgentAcceptSignPath,
        specifiedType: const FullType(String),
      );
    }
    if (object.engineerId != null) {
      yield r'engineer_id';
      yield serializers.serialize(
        object.engineerId,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.engineerFirstName != null) {
      yield r'engineer_first_name';
      yield serializers.serialize(
        object.engineerFirstName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.engineerLastName != null) {
      yield r'engineer_last_name';
      yield serializers.serialize(
        object.engineerLastName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.engineerAcceptSignPath != null) {
      yield r'engineer_accept_sign_path';
      yield serializers.serialize(
        object.engineerAcceptSignPath,
        specifiedType: const FullType(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    RecruitGetContractDetails object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required RecruitGetContractDetailsBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'apply_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.applyId = valueDes;
          break;
        case r'host_company_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.hostCompanyId = valueDes;
          break;
        case r'host_company_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.hostCompanyName = valueDes;
          break;
        case r'host_agent_first_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.hostAgentFirstName = valueDes;
          break;
        case r'host_agent_last_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.hostAgentLastName = valueDes;
          break;
        case r'host_agent_accept_sign_path':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.hostAgentAcceptSignPath = valueDes;
          break;
        case r'support_company_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.supportCompanyId = valueDes;
          break;
        case r'support_company_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.supportCompanyName = valueDes;
          break;
        case r'support_agent_first_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.supportAgentFirstName = valueDes;
          break;
        case r'support_agent_last_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.supportAgentLastName = valueDes;
          break;
        case r'support_agent_accept_sign_path':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.supportAgentAcceptSignPath = valueDes;
          break;
        case r'agency_company_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.agencyCompanyId = valueDes;
          break;
        case r'agency_company_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.agencyCompanyName = valueDes;
          break;
        case r'agency_agent_first_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.agencyAgentFirstName = valueDes;
          break;
        case r'agency_agent_last_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.agencyAgentLastName = valueDes;
          break;
        case r'agency_agent_accept_sign_path':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.agencyAgentAcceptSignPath = valueDes;
          break;
        case r'engineer_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.engineerId = valueDes;
          break;
        case r'engineer_first_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.engineerFirstName = valueDes;
          break;
        case r'engineer_last_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.engineerLastName = valueDes;
          break;
        case r'engineer_accept_sign_path':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.engineerAcceptSignPath = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  RecruitGetContractDetails deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = RecruitGetContractDetailsBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

