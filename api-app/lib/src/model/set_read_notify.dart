//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'set_read_notify.g.dart';

/// SetReadNotify
///
/// Properties:
/// * [notifyIds] 
@BuiltValue()
abstract class SetReadNotify implements Built<SetReadNotify, SetReadNotifyBuilder> {
  @BuiltValueField(wireName: r'notify_ids')
  BuiltList<int>? get notifyIds;

  SetReadNotify._();

  factory SetReadNotify([void updates(SetReadNotifyBuilder b)]) = _$SetReadNotify;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(SetReadNotifyBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<SetReadNotify> get serializer => _$SetReadNotifySerializer();
}

class _$SetReadNotifySerializer implements PrimitiveSerializer<SetReadNotify> {
  @override
  final Iterable<Type> types = const [SetReadNotify, _$SetReadNotify];

  @override
  final String wireName = r'SetReadNotify';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    SetReadNotify object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'notify_ids';
    yield object.notifyIds == null ? null : serializers.serialize(
      object.notifyIds,
      specifiedType: const FullType.nullable(BuiltList, [FullType(int)]),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    SetReadNotify object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required SetReadNotifyBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'notify_ids':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(BuiltList, [FullType(int)]),
          ) as BuiltList<int>?;
          if (valueDes == null) continue;
          result.notifyIds.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  SetReadNotify deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = SetReadNotifyBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

