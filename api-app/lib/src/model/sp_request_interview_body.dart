//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'sp_request_interview_body.g.dart';

/// SPRequestInterviewBody
///
/// Properties:
/// * [hostCompanyId] 
/// * [hostCompanyRecruitId] 
/// * [engineerId] 
/// * [message] 
@BuiltValue()
abstract class SPRequestInterviewBody implements Built<SPRequestInterviewBody, SPRequestInterviewBodyBuilder> {
  @BuiltValueField(wireName: r'host_company_id')
  int get hostCompanyId;

  @BuiltValueField(wireName: r'host_company_recruit_id')
  int get hostCompanyRecruitId;

  @BuiltValueField(wireName: r'engineer_id')
  int get engineerId;

  @BuiltValueField(wireName: r'message')
  String get message;

  SPRequestInterviewBody._();

  factory SPRequestInterviewBody([void updates(SPRequestInterviewBodyBuilder b)]) = _$SPRequestInterviewBody;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(SPRequestInterviewBodyBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<SPRequestInterviewBody> get serializer => _$SPRequestInterviewBodySerializer();
}

class _$SPRequestInterviewBodySerializer implements PrimitiveSerializer<SPRequestInterviewBody> {
  @override
  final Iterable<Type> types = const [SPRequestInterviewBody, _$SPRequestInterviewBody];

  @override
  final String wireName = r'SPRequestInterviewBody';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    SPRequestInterviewBody object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'host_company_id';
    yield serializers.serialize(
      object.hostCompanyId,
      specifiedType: const FullType(int),
    );
    yield r'host_company_recruit_id';
    yield serializers.serialize(
      object.hostCompanyRecruitId,
      specifiedType: const FullType(int),
    );
    yield r'engineer_id';
    yield serializers.serialize(
      object.engineerId,
      specifiedType: const FullType(int),
    );
    yield r'message';
    yield serializers.serialize(
      object.message,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    SPRequestInterviewBody object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required SPRequestInterviewBodyBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'host_company_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.hostCompanyId = valueDes;
          break;
        case r'host_company_recruit_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.hostCompanyRecruitId = valueDes;
          break;
        case r'engineer_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.engineerId = valueDes;
          break;
        case r'message':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.message = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  SPRequestInterviewBody deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = SPRequestInterviewBodyBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

