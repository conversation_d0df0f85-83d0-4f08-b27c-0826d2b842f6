// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_company_get_user_explore_count_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GeneralCompanyGetUserExploreCountResponseModel
    extends GeneralCompanyGetUserExploreCountResponseModel {
  @override
  final String? message;
  @override
  final int data;
  @override
  final BuiltList<ErrorDetail>? errors;

  factory _$GeneralCompanyGetUserExploreCountResponseModel(
          [void Function(GeneralCompanyGetUserExploreCountResponseModelBuilder)?
              updates]) =>
      (new GeneralCompanyGetUserExploreCountResponseModelBuilder()
            ..update(updates))
          ._build();

  _$GeneralCompanyGetUserExploreCountResponseModel._(
      {this.message, required this.data, this.errors})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'GeneralCompanyGetUserExploreCountResponseModel', 'data');
  }

  @override
  GeneralCompanyGetUserExploreCountResponseModel rebuild(
          void Function(GeneralCompanyGetUserExploreCountResponseModelBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GeneralCompanyGetUserExploreCountResponseModelBuilder toBuilder() =>
      new GeneralCompanyGetUserExploreCountResponseModelBuilder()
        ..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GeneralCompanyGetUserExploreCountResponseModel &&
        message == other.message &&
        data == other.data &&
        errors == other.errors;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'GeneralCompanyGetUserExploreCountResponseModel')
          ..add('message', message)
          ..add('data', data)
          ..add('errors', errors))
        .toString();
  }
}

class GeneralCompanyGetUserExploreCountResponseModelBuilder
    implements
        Builder<GeneralCompanyGetUserExploreCountResponseModel,
            GeneralCompanyGetUserExploreCountResponseModelBuilder> {
  _$GeneralCompanyGetUserExploreCountResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  int? _data;
  int? get data => _$this._data;
  set data(int? data) => _$this._data = data;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  GeneralCompanyGetUserExploreCountResponseModelBuilder() {
    GeneralCompanyGetUserExploreCountResponseModel._defaults(this);
  }

  GeneralCompanyGetUserExploreCountResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _data = $v.data;
      _errors = $v.errors?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GeneralCompanyGetUserExploreCountResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GeneralCompanyGetUserExploreCountResponseModel;
  }

  @override
  void update(
      void Function(GeneralCompanyGetUserExploreCountResponseModelBuilder)?
          updates) {
    if (updates != null) updates(this);
  }

  @override
  GeneralCompanyGetUserExploreCountResponseModel build() => _build();

  _$GeneralCompanyGetUserExploreCountResponseModel _build() {
    _$GeneralCompanyGetUserExploreCountResponseModel _$result;
    try {
      _$result = _$v ??
          new _$GeneralCompanyGetUserExploreCountResponseModel._(
            message: message,
            data: BuiltValueNullFieldError.checkNotNull(data,
                r'GeneralCompanyGetUserExploreCountResponseModel', 'data'),
            errors: _errors?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'errors';
        _errors?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'GeneralCompanyGetUserExploreCountResponseModel',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
