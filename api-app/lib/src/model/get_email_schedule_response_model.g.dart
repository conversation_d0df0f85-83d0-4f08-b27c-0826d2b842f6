// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_email_schedule_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GetEmailScheduleResponseModel extends GetEmailScheduleResponseModel {
  @override
  final String? message;
  @override
  final PagingEmailSchedule data;
  @override
  final BuiltList<ErrorDetail>? errors;

  factory _$GetEmailScheduleResponseModel(
          [void Function(GetEmailScheduleResponseModelBuilder)? updates]) =>
      (new GetEmailScheduleResponseModelBuilder()..update(updates))._build();

  _$GetEmailScheduleResponseModel._(
      {this.message, required this.data, this.errors})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'GetEmailScheduleResponseModel', 'data');
  }

  @override
  GetEmailScheduleResponseModel rebuild(
          void Function(GetEmailScheduleResponseModelBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GetEmailScheduleResponseModelBuilder toBuilder() =>
      new GetEmailScheduleResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GetEmailScheduleResponseModel &&
        message == other.message &&
        data == other.data &&
        errors == other.errors;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GetEmailScheduleResponseModel')
          ..add('message', message)
          ..add('data', data)
          ..add('errors', errors))
        .toString();
  }
}

class GetEmailScheduleResponseModelBuilder
    implements
        Builder<GetEmailScheduleResponseModel,
            GetEmailScheduleResponseModelBuilder> {
  _$GetEmailScheduleResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  PagingEmailScheduleBuilder? _data;
  PagingEmailScheduleBuilder get data =>
      _$this._data ??= new PagingEmailScheduleBuilder();
  set data(PagingEmailScheduleBuilder? data) => _$this._data = data;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  GetEmailScheduleResponseModelBuilder() {
    GetEmailScheduleResponseModel._defaults(this);
  }

  GetEmailScheduleResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _data = $v.data.toBuilder();
      _errors = $v.errors?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GetEmailScheduleResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GetEmailScheduleResponseModel;
  }

  @override
  void update(void Function(GetEmailScheduleResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GetEmailScheduleResponseModel build() => _build();

  _$GetEmailScheduleResponseModel _build() {
    _$GetEmailScheduleResponseModel _$result;
    try {
      _$result = _$v ??
          new _$GetEmailScheduleResponseModel._(
            message: message,
            data: data.build(),
            errors: _errors?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        data.build();
        _$failedField = 'errors';
        _errors?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'GetEmailScheduleResponseModel', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
