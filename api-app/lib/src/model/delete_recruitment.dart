//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'delete_recruitment.g.dart';

/// DeleteRecruitment
///
/// Properties:
/// * [recruitId] 
@BuiltValue()
abstract class DeleteRecruitment implements Built<DeleteRecruitment, DeleteRecruitmentBuilder> {
  @BuiltValueField(wireName: r'recruit_id')
  String get recruitId;

  DeleteRecruitment._();

  factory DeleteRecruitment([void updates(DeleteRecruitmentBuilder b)]) = _$DeleteRecruitment;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(DeleteRecruitmentBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<DeleteRecruitment> get serializer => _$DeleteRecruitmentSerializer();
}

class _$DeleteRecruitmentSerializer implements PrimitiveSerializer<DeleteRecruitment> {
  @override
  final Iterable<Type> types = const [DeleteRecruitment, _$DeleteRecruitment];

  @override
  final String wireName = r'DeleteRecruitment';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    DeleteRecruitment object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'recruit_id';
    yield serializers.serialize(
      object.recruitId,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    DeleteRecruitment object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required DeleteRecruitmentBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'recruit_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.recruitId = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  DeleteRecruitment deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = DeleteRecruitmentBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

