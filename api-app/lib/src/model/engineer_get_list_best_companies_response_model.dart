//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/error_detail.dart';
import 'package:openapi/src/model/engineer_list_best_company_pagination.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'engineer_get_list_best_companies_response_model.g.dart';

/// EngineerGetListBestCompaniesResponseModel
///
/// Properties:
/// * [message] 
/// * [errors] 
/// * [data] 
@BuiltValue()
abstract class EngineerGetListBestCompaniesResponseModel implements Built<EngineerGetListBestCompaniesResponseModel, EngineerGetListBestCompaniesResponseModelBuilder> {
  @BuiltValueField(wireName: r'message')
  String? get message;

  @BuiltValueField(wireName: r'errors')
  BuiltList<ErrorDetail>? get errors;

  @BuiltValueField(wireName: r'data')
  EngineerListBestCompanyPagination get data;

  EngineerGetListBestCompaniesResponseModel._();

  factory EngineerGetListBestCompaniesResponseModel([void updates(EngineerGetListBestCompaniesResponseModelBuilder b)]) = _$EngineerGetListBestCompaniesResponseModel;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(EngineerGetListBestCompaniesResponseModelBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<EngineerGetListBestCompaniesResponseModel> get serializer => _$EngineerGetListBestCompaniesResponseModelSerializer();
}

class _$EngineerGetListBestCompaniesResponseModelSerializer implements PrimitiveSerializer<EngineerGetListBestCompaniesResponseModel> {
  @override
  final Iterable<Type> types = const [EngineerGetListBestCompaniesResponseModel, _$EngineerGetListBestCompaniesResponseModel];

  @override
  final String wireName = r'EngineerGetListBestCompaniesResponseModel';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    EngineerGetListBestCompaniesResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'message';
    yield object.message == null ? null : serializers.serialize(
      object.message,
      specifiedType: const FullType.nullable(String),
    );
    yield r'errors';
    yield object.errors == null ? null : serializers.serialize(
      object.errors,
      specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
    );
    yield r'data';
    yield serializers.serialize(
      object.data,
      specifiedType: const FullType(EngineerListBestCompanyPagination),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    EngineerGetListBestCompaniesResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required EngineerGetListBestCompaniesResponseModelBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'message':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.message = valueDes;
          break;
        case r'errors':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
          ) as BuiltList<ErrorDetail>?;
          if (valueDes == null) continue;
          result.errors.replace(valueDes);
          break;
        case r'data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(EngineerListBestCompanyPagination),
          ) as EngineerListBestCompanyPagination;
          result.data.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  EngineerGetListBestCompaniesResponseModel deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = EngineerGetListBestCompaniesResponseModelBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

