// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_details_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UserDetailsResponseModel extends UserDetailsResponseModel {
  @override
  final String? message;
  @override
  final UserDetailsSerializers? data;
  @override
  final BuiltList<ErrorDetail>? errors;

  factory _$UserDetailsResponseModel(
          [void Function(UserDetailsResponseModelBuilder)? updates]) =>
      (new UserDetailsResponseModelBuilder()..update(updates))._build();

  _$UserDetailsResponseModel._({this.message, this.data, this.errors})
      : super._();

  @override
  UserDetailsResponseModel rebuild(
          void Function(UserDetailsResponseModelBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UserDetailsResponseModelBuilder toBuilder() =>
      new UserDetailsResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UserDetailsResponseModel &&
        message == other.message &&
        data == other.data &&
        errors == other.errors;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UserDetailsResponseModel')
          ..add('message', message)
          ..add('data', data)
          ..add('errors', errors))
        .toString();
  }
}

class UserDetailsResponseModelBuilder
    implements
        Builder<UserDetailsResponseModel, UserDetailsResponseModelBuilder> {
  _$UserDetailsResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  UserDetailsSerializersBuilder? _data;
  UserDetailsSerializersBuilder get data =>
      _$this._data ??= new UserDetailsSerializersBuilder();
  set data(UserDetailsSerializersBuilder? data) => _$this._data = data;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  UserDetailsResponseModelBuilder() {
    UserDetailsResponseModel._defaults(this);
  }

  UserDetailsResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _data = $v.data?.toBuilder();
      _errors = $v.errors?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UserDetailsResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$UserDetailsResponseModel;
  }

  @override
  void update(void Function(UserDetailsResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UserDetailsResponseModel build() => _build();

  _$UserDetailsResponseModel _build() {
    _$UserDetailsResponseModel _$result;
    try {
      _$result = _$v ??
          new _$UserDetailsResponseModel._(
            message: message,
            data: _data?.build(),
            errors: _errors?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
        _$failedField = 'errors';
        _errors?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'UserDetailsResponseModel', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
