// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login_with_sns_response_data.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$LoginWithSNSResponseData extends LoginWithSNSResponseData {
  @override
  final String? accessToken;
  @override
  final String? refreshToken;
  @override
  final String? userType;
  @override
  final String? email;

  factory _$LoginWithSNSResponseData(
          [void Function(LoginWithSNSResponseDataBuilder)? updates]) =>
      (new LoginWithSNSResponseDataBuilder()..update(updates))._build();

  _$LoginWithSNSResponseData._(
      {this.accessToken, this.refreshToken, this.userType, this.email})
      : super._();

  @override
  LoginWithSNSResponseData rebuild(
          void Function(LoginWithSNSResponseDataBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  LoginWithSNSResponseDataBuilder toBuilder() =>
      new LoginWithSNSResponseDataBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is LoginWithSNSResponseData &&
        accessToken == other.accessToken &&
        refreshToken == other.refreshToken &&
        userType == other.userType &&
        email == other.email;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, accessToken.hashCode);
    _$hash = $jc(_$hash, refreshToken.hashCode);
    _$hash = $jc(_$hash, userType.hashCode);
    _$hash = $jc(_$hash, email.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'LoginWithSNSResponseData')
          ..add('accessToken', accessToken)
          ..add('refreshToken', refreshToken)
          ..add('userType', userType)
          ..add('email', email))
        .toString();
  }
}

class LoginWithSNSResponseDataBuilder
    implements
        Builder<LoginWithSNSResponseData, LoginWithSNSResponseDataBuilder> {
  _$LoginWithSNSResponseData? _$v;

  String? _accessToken;
  String? get accessToken => _$this._accessToken;
  set accessToken(String? accessToken) => _$this._accessToken = accessToken;

  String? _refreshToken;
  String? get refreshToken => _$this._refreshToken;
  set refreshToken(String? refreshToken) => _$this._refreshToken = refreshToken;

  String? _userType;
  String? get userType => _$this._userType;
  set userType(String? userType) => _$this._userType = userType;

  String? _email;
  String? get email => _$this._email;
  set email(String? email) => _$this._email = email;

  LoginWithSNSResponseDataBuilder() {
    LoginWithSNSResponseData._defaults(this);
  }

  LoginWithSNSResponseDataBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _accessToken = $v.accessToken;
      _refreshToken = $v.refreshToken;
      _userType = $v.userType;
      _email = $v.email;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(LoginWithSNSResponseData other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$LoginWithSNSResponseData;
  }

  @override
  void update(void Function(LoginWithSNSResponseDataBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  LoginWithSNSResponseData build() => _build();

  _$LoginWithSNSResponseData _build() {
    final _$result = _$v ??
        new _$LoginWithSNSResponseData._(
          accessToken: accessToken,
          refreshToken: refreshToken,
          userType: userType,
          email: email,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
