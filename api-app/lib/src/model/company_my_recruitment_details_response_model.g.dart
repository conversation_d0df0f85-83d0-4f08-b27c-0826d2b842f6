// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'company_my_recruitment_details_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$CompanyMyRecruitmentDetailsResponseModel
    extends CompanyMyRecruitmentDetailsResponseModel {
  @override
  final String? message;
  @override
  final CompanyRecruitDetail data;
  @override
  final BuiltList<ErrorDetail>? errors;

  factory _$CompanyMyRecruitmentDetailsResponseModel(
          [void Function(CompanyMyRecruitmentDetailsResponseModelBuilder)?
              updates]) =>
      (new CompanyMyRecruitmentDetailsResponseModelBuilder()..update(updates))
          ._build();

  _$CompanyMyRecruitmentDetailsResponseModel._(
      {this.message, required this.data, this.errors})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'CompanyMyRecruitmentDetailsResponseModel', 'data');
  }

  @override
  CompanyMyRecruitmentDetailsResponseModel rebuild(
          void Function(CompanyMyRecruitmentDetailsResponseModelBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  CompanyMyRecruitmentDetailsResponseModelBuilder toBuilder() =>
      new CompanyMyRecruitmentDetailsResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is CompanyMyRecruitmentDetailsResponseModel &&
        message == other.message &&
        data == other.data &&
        errors == other.errors;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'CompanyMyRecruitmentDetailsResponseModel')
          ..add('message', message)
          ..add('data', data)
          ..add('errors', errors))
        .toString();
  }
}

class CompanyMyRecruitmentDetailsResponseModelBuilder
    implements
        Builder<CompanyMyRecruitmentDetailsResponseModel,
            CompanyMyRecruitmentDetailsResponseModelBuilder> {
  _$CompanyMyRecruitmentDetailsResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  CompanyRecruitDetailBuilder? _data;
  CompanyRecruitDetailBuilder get data =>
      _$this._data ??= new CompanyRecruitDetailBuilder();
  set data(CompanyRecruitDetailBuilder? data) => _$this._data = data;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  CompanyMyRecruitmentDetailsResponseModelBuilder() {
    CompanyMyRecruitmentDetailsResponseModel._defaults(this);
  }

  CompanyMyRecruitmentDetailsResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _data = $v.data.toBuilder();
      _errors = $v.errors?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(CompanyMyRecruitmentDetailsResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$CompanyMyRecruitmentDetailsResponseModel;
  }

  @override
  void update(
      void Function(CompanyMyRecruitmentDetailsResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  CompanyMyRecruitmentDetailsResponseModel build() => _build();

  _$CompanyMyRecruitmentDetailsResponseModel _build() {
    _$CompanyMyRecruitmentDetailsResponseModel _$result;
    try {
      _$result = _$v ??
          new _$CompanyMyRecruitmentDetailsResponseModel._(
            message: message,
            data: data.build(),
            errors: _errors?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        data.build();
        _$failedField = 'errors';
        _errors?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'CompanyMyRecruitmentDetailsResponseModel',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
