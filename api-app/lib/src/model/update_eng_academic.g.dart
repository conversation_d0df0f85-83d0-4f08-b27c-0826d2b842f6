// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_eng_academic.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UpdateEngAcademic extends UpdateEngAcademic {
  @override
  final String? school;
  @override
  final int? type;
  @override
  final Date? outDate;
  @override
  final String? faculty;

  factory _$UpdateEngAcademic(
          [void Function(UpdateEngAcademicBuilder)? updates]) =>
      (new UpdateEngAcademicBuilder()..update(updates))._build();

  _$UpdateEngAcademic._({this.school, this.type, this.outDate, this.faculty})
      : super._();

  @override
  UpdateEngAcademic rebuild(void Function(UpdateEngAcademicBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UpdateEngAcademicBuilder toBuilder() =>
      new UpdateEngAcademicBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UpdateEngAcademic &&
        school == other.school &&
        type == other.type &&
        outDate == other.outDate &&
        faculty == other.faculty;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, school.hashCode);
    _$hash = $jc(_$hash, type.hashCode);
    _$hash = $jc(_$hash, outDate.hashCode);
    _$hash = $jc(_$hash, faculty.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UpdateEngAcademic')
          ..add('school', school)
          ..add('type', type)
          ..add('outDate', outDate)
          ..add('faculty', faculty))
        .toString();
  }
}

class UpdateEngAcademicBuilder
    implements Builder<UpdateEngAcademic, UpdateEngAcademicBuilder> {
  _$UpdateEngAcademic? _$v;

  String? _school;
  String? get school => _$this._school;
  set school(String? school) => _$this._school = school;

  int? _type;
  int? get type => _$this._type;
  set type(int? type) => _$this._type = type;

  Date? _outDate;
  Date? get outDate => _$this._outDate;
  set outDate(Date? outDate) => _$this._outDate = outDate;

  String? _faculty;
  String? get faculty => _$this._faculty;
  set faculty(String? faculty) => _$this._faculty = faculty;

  UpdateEngAcademicBuilder() {
    UpdateEngAcademic._defaults(this);
  }

  UpdateEngAcademicBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _school = $v.school;
      _type = $v.type;
      _outDate = $v.outDate;
      _faculty = $v.faculty;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UpdateEngAcademic other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$UpdateEngAcademic;
  }

  @override
  void update(void Function(UpdateEngAcademicBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UpdateEngAcademic build() => _build();

  _$UpdateEngAcademic _build() {
    final _$result = _$v ??
        new _$UpdateEngAcademic._(
          school: school,
          type: type,
          outDate: outDate,
          faculty: faculty,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
