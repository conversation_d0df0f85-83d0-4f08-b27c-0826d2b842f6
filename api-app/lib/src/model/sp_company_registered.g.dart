// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sp_company_registered.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$SPCompanyRegistered extends SPCompanyRegistered {
  @override
  final int? companyId;
  @override
  final String? name;
  @override
  final String? logoImagePath;
  @override
  final String? aboutUs;
  @override
  final String? businessDetails;
  @override
  final String? countryCode;
  @override
  final String? addressCode;

  factory _$SPCompanyRegistered(
          [void Function(SPCompanyRegisteredBuilder)? updates]) =>
      (new SPCompanyRegisteredBuilder()..update(updates))._build();

  _$SPCompanyRegistered._(
      {this.companyId,
      this.name,
      this.logoImagePath,
      this.aboutUs,
      this.businessDetails,
      this.countryCode,
      this.addressCode})
      : super._();

  @override
  SPCompanyRegistered rebuild(
          void Function(SPCompanyRegisteredBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SPCompanyRegisteredBuilder toBuilder() =>
      new SPCompanyRegisteredBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SPCompanyRegistered &&
        companyId == other.companyId &&
        name == other.name &&
        logoImagePath == other.logoImagePath &&
        aboutUs == other.aboutUs &&
        businessDetails == other.businessDetails &&
        countryCode == other.countryCode &&
        addressCode == other.addressCode;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, companyId.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, logoImagePath.hashCode);
    _$hash = $jc(_$hash, aboutUs.hashCode);
    _$hash = $jc(_$hash, businessDetails.hashCode);
    _$hash = $jc(_$hash, countryCode.hashCode);
    _$hash = $jc(_$hash, addressCode.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SPCompanyRegistered')
          ..add('companyId', companyId)
          ..add('name', name)
          ..add('logoImagePath', logoImagePath)
          ..add('aboutUs', aboutUs)
          ..add('businessDetails', businessDetails)
          ..add('countryCode', countryCode)
          ..add('addressCode', addressCode))
        .toString();
  }
}

class SPCompanyRegisteredBuilder
    implements Builder<SPCompanyRegistered, SPCompanyRegisteredBuilder> {
  _$SPCompanyRegistered? _$v;

  int? _companyId;
  int? get companyId => _$this._companyId;
  set companyId(int? companyId) => _$this._companyId = companyId;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _logoImagePath;
  String? get logoImagePath => _$this._logoImagePath;
  set logoImagePath(String? logoImagePath) =>
      _$this._logoImagePath = logoImagePath;

  String? _aboutUs;
  String? get aboutUs => _$this._aboutUs;
  set aboutUs(String? aboutUs) => _$this._aboutUs = aboutUs;

  String? _businessDetails;
  String? get businessDetails => _$this._businessDetails;
  set businessDetails(String? businessDetails) =>
      _$this._businessDetails = businessDetails;

  String? _countryCode;
  String? get countryCode => _$this._countryCode;
  set countryCode(String? countryCode) => _$this._countryCode = countryCode;

  String? _addressCode;
  String? get addressCode => _$this._addressCode;
  set addressCode(String? addressCode) => _$this._addressCode = addressCode;

  SPCompanyRegisteredBuilder() {
    SPCompanyRegistered._defaults(this);
  }

  SPCompanyRegisteredBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _companyId = $v.companyId;
      _name = $v.name;
      _logoImagePath = $v.logoImagePath;
      _aboutUs = $v.aboutUs;
      _businessDetails = $v.businessDetails;
      _countryCode = $v.countryCode;
      _addressCode = $v.addressCode;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SPCompanyRegistered other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$SPCompanyRegistered;
  }

  @override
  void update(void Function(SPCompanyRegisteredBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SPCompanyRegistered build() => _build();

  _$SPCompanyRegistered _build() {
    final _$result = _$v ??
        new _$SPCompanyRegistered._(
          companyId: companyId,
          name: name,
          logoImagePath: logoImagePath,
          aboutUs: aboutUs,
          businessDetails: businessDetails,
          countryCode: countryCode,
          addressCode: addressCode,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
