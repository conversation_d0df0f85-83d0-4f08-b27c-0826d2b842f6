// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ResponseModel extends ResponseModel {
  @override
  final String? message;
  @override
  final BuiltMap<String, String?>? data;
  @override
  final BuiltList<ErrorDetail>? errors;

  factory _$ResponseModel([void Function(ResponseModelBuilder)? updates]) =>
      (new ResponseModelBuilder()..update(updates))._build();

  _$ResponseModel._({this.message, this.data, this.errors}) : super._();

  @override
  ResponseModel rebuild(void Function(ResponseModelBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ResponseModelBuilder toBuilder() => new ResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ResponseModel &&
        message == other.message &&
        data == other.data &&
        errors == other.errors;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ResponseModel')
          ..add('message', message)
          ..add('data', data)
          ..add('errors', errors))
        .toString();
  }
}

class ResponseModelBuilder
    implements Builder<ResponseModel, ResponseModelBuilder> {
  _$ResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  MapBuilder<String, String?>? _data;
  MapBuilder<String, String?> get data =>
      _$this._data ??= new MapBuilder<String, String?>();
  set data(MapBuilder<String, String?>? data) => _$this._data = data;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  ResponseModelBuilder() {
    ResponseModel._defaults(this);
  }

  ResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _data = $v.data?.toBuilder();
      _errors = $v.errors?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$ResponseModel;
  }

  @override
  void update(void Function(ResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ResponseModel build() => _build();

  _$ResponseModel _build() {
    _$ResponseModel _$result;
    try {
      _$result = _$v ??
          new _$ResponseModel._(
            message: message,
            data: _data?.build(),
            errors: _errors?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
        _$failedField = 'errors';
        _errors?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'ResponseModel', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
