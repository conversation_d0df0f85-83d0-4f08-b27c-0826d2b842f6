// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'questions.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$Questions extends Questions {
  @override
  final Question question;
  @override
  final BuiltList<Option> options;

  factory _$Questions([void Function(QuestionsBuilder)? updates]) =>
      (new QuestionsBuilder()..update(updates))._build();

  _$Questions._({required this.question, required this.options}) : super._() {
    BuiltValueNullFieldError.checkNotNull(question, r'Questions', 'question');
    BuiltValueNullFieldError.checkNotNull(options, r'Questions', 'options');
  }

  @override
  Questions rebuild(void Function(QuestionsBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  QuestionsBuilder toBuilder() => new QuestionsBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is Questions &&
        question == other.question &&
        options == other.options;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, question.hashCode);
    _$hash = $jc(_$hash, options.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'Questions')
          ..add('question', question)
          ..add('options', options))
        .toString();
  }
}

class QuestionsBuilder implements Builder<Questions, QuestionsBuilder> {
  _$Questions? _$v;

  QuestionBuilder? _question;
  QuestionBuilder get question => _$this._question ??= new QuestionBuilder();
  set question(QuestionBuilder? question) => _$this._question = question;

  ListBuilder<Option>? _options;
  ListBuilder<Option> get options =>
      _$this._options ??= new ListBuilder<Option>();
  set options(ListBuilder<Option>? options) => _$this._options = options;

  QuestionsBuilder() {
    Questions._defaults(this);
  }

  QuestionsBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _question = $v.question.toBuilder();
      _options = $v.options.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(Questions other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$Questions;
  }

  @override
  void update(void Function(QuestionsBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  Questions build() => _build();

  _$Questions _build() {
    _$Questions _$result;
    try {
      _$result = _$v ??
          new _$Questions._(
            question: question.build(),
            options: options.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'question';
        question.build();
        _$failedField = 'options';
        options.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'Questions', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
