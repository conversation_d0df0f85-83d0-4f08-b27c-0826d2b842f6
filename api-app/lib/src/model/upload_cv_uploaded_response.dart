//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/error_detail.dart';
import 'package:openapi/src/model/upload_cv_uploaded.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'upload_cv_uploaded_response.g.dart';

/// UploadCVUploadedResponse
///
/// Properties:
/// * [message] 
/// * [errors] 
/// * [data] 
@BuiltValue()
abstract class UploadCVUploadedResponse implements Built<UploadCVUploadedResponse, UploadCVUploadedResponseBuilder> {
  @BuiltValueField(wireName: r'message')
  String? get message;

  @BuiltValueField(wireName: r'errors')
  BuiltList<ErrorDetail>? get errors;

  @BuiltValueField(wireName: r'data')
  UploadCVUploaded? get data;

  UploadCVUploadedResponse._();

  factory UploadCVUploadedResponse([void updates(UploadCVUploadedResponseBuilder b)]) = _$UploadCVUploadedResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(UploadCVUploadedResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<UploadCVUploadedResponse> get serializer => _$UploadCVUploadedResponseSerializer();
}

class _$UploadCVUploadedResponseSerializer implements PrimitiveSerializer<UploadCVUploadedResponse> {
  @override
  final Iterable<Type> types = const [UploadCVUploadedResponse, _$UploadCVUploadedResponse];

  @override
  final String wireName = r'UploadCVUploadedResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    UploadCVUploadedResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'message';
    yield object.message == null ? null : serializers.serialize(
      object.message,
      specifiedType: const FullType.nullable(String),
    );
    yield r'errors';
    yield object.errors == null ? null : serializers.serialize(
      object.errors,
      specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
    );
    yield r'data';
    yield object.data == null ? null : serializers.serialize(
      object.data,
      specifiedType: const FullType.nullable(UploadCVUploaded),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    UploadCVUploadedResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required UploadCVUploadedResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'message':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.message = valueDes;
          break;
        case r'errors':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
          ) as BuiltList<ErrorDetail>?;
          if (valueDes == null) continue;
          result.errors.replace(valueDes);
          break;
        case r'data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(UploadCVUploaded),
          ) as UploadCVUploaded?;
          if (valueDes == null) continue;
          result.data.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  UploadCVUploadedResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = UploadCVUploadedResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

