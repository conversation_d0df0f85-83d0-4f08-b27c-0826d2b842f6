//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/option.dart';
import 'package:openapi/src/model/question.dart';
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'questions.g.dart';

/// Questions
///
/// Properties:
/// * [question] 
/// * [options] 
@BuiltValue()
abstract class Questions implements Built<Questions, QuestionsBuilder> {
  @BuiltValueField(wireName: r'question')
  Question get question;

  @BuiltValueField(wireName: r'options')
  BuiltList<Option> get options;

  Questions._();

  factory Questions([void updates(QuestionsBuilder b)]) = _$Questions;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(QuestionsBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<Questions> get serializer => _$QuestionsSerializer();
}

class _$QuestionsSerializer implements PrimitiveSerializer<Questions> {
  @override
  final Iterable<Type> types = const [Questions, _$Questions];

  @override
  final String wireName = r'Questions';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    Questions object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'question';
    yield serializers.serialize(
      object.question,
      specifiedType: const FullType(Question),
    );
    yield r'options';
    yield serializers.serialize(
      object.options,
      specifiedType: const FullType(BuiltList, [FullType(Option)]),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    Questions object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required QuestionsBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'question':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(Question),
          ) as Question;
          result.question.replace(valueDes);
          break;
        case r'options':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(Option)]),
          ) as BuiltList<Option>;
          result.options.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  Questions deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = QuestionsBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

