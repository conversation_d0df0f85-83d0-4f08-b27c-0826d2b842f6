// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'set_read_notify.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$SetReadNotify extends SetReadNotify {
  @override
  final BuiltList<int>? notifyIds;

  factory _$SetReadNotify([void Function(SetReadNotifyBuilder)? updates]) =>
      (new SetReadNotifyBuilder()..update(updates))._build();

  _$SetReadNotify._({this.notifyIds}) : super._();

  @override
  SetReadNotify rebuild(void Function(SetReadNotifyBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SetReadNotifyBuilder toBuilder() => new SetReadNotifyBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SetReadNotify && notifyIds == other.notifyIds;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, notifyIds.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SetReadNotify')
          ..add('notifyIds', notifyIds))
        .toString();
  }
}

class SetReadNotifyBuilder
    implements Builder<SetReadNotify, SetReadNotifyBuilder> {
  _$SetReadNotify? _$v;

  ListBuilder<int>? _notifyIds;
  ListBuilder<int> get notifyIds =>
      _$this._notifyIds ??= new ListBuilder<int>();
  set notifyIds(ListBuilder<int>? notifyIds) => _$this._notifyIds = notifyIds;

  SetReadNotifyBuilder() {
    SetReadNotify._defaults(this);
  }

  SetReadNotifyBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _notifyIds = $v.notifyIds?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SetReadNotify other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$SetReadNotify;
  }

  @override
  void update(void Function(SetReadNotifyBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SetReadNotify build() => _build();

  _$SetReadNotify _build() {
    _$SetReadNotify _$result;
    try {
      _$result = _$v ??
          new _$SetReadNotify._(
            notifyIds: _notifyIds?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'notifyIds';
        _notifyIds?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'SetReadNotify', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
