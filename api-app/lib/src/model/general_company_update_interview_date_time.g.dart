// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_company_update_interview_date_time.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GeneralCompanyUpdateInterviewDateTime
    extends GeneralCompanyUpdateInterviewDateTime {
  @override
  final int applyId;
  @override
  final DateTime interviewDatetime;
  @override
  final int? hostCompanyId;

  factory _$GeneralCompanyUpdateInterviewDateTime(
          [void Function(GeneralCompanyUpdateInterviewDateTimeBuilder)?
              updates]) =>
      (new GeneralCompanyUpdateInterviewDateTimeBuilder()..update(updates))
          ._build();

  _$GeneralCompanyUpdateInterviewDateTime._(
      {required this.applyId,
      required this.interviewDatetime,
      this.hostCompanyId})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        applyId, r'GeneralCompanyUpdateInterviewDateTime', 'applyId');
    BuiltValueNullFieldError.checkNotNull(interviewDatetime,
        r'GeneralCompanyUpdateInterviewDateTime', 'interviewDatetime');
  }

  @override
  GeneralCompanyUpdateInterviewDateTime rebuild(
          void Function(GeneralCompanyUpdateInterviewDateTimeBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GeneralCompanyUpdateInterviewDateTimeBuilder toBuilder() =>
      new GeneralCompanyUpdateInterviewDateTimeBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GeneralCompanyUpdateInterviewDateTime &&
        applyId == other.applyId &&
        interviewDatetime == other.interviewDatetime &&
        hostCompanyId == other.hostCompanyId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, applyId.hashCode);
    _$hash = $jc(_$hash, interviewDatetime.hashCode);
    _$hash = $jc(_$hash, hostCompanyId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'GeneralCompanyUpdateInterviewDateTime')
          ..add('applyId', applyId)
          ..add('interviewDatetime', interviewDatetime)
          ..add('hostCompanyId', hostCompanyId))
        .toString();
  }
}

class GeneralCompanyUpdateInterviewDateTimeBuilder
    implements
        Builder<GeneralCompanyUpdateInterviewDateTime,
            GeneralCompanyUpdateInterviewDateTimeBuilder> {
  _$GeneralCompanyUpdateInterviewDateTime? _$v;

  int? _applyId;
  int? get applyId => _$this._applyId;
  set applyId(int? applyId) => _$this._applyId = applyId;

  DateTime? _interviewDatetime;
  DateTime? get interviewDatetime => _$this._interviewDatetime;
  set interviewDatetime(DateTime? interviewDatetime) =>
      _$this._interviewDatetime = interviewDatetime;

  int? _hostCompanyId;
  int? get hostCompanyId => _$this._hostCompanyId;
  set hostCompanyId(int? hostCompanyId) =>
      _$this._hostCompanyId = hostCompanyId;

  GeneralCompanyUpdateInterviewDateTimeBuilder() {
    GeneralCompanyUpdateInterviewDateTime._defaults(this);
  }

  GeneralCompanyUpdateInterviewDateTimeBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _applyId = $v.applyId;
      _interviewDatetime = $v.interviewDatetime;
      _hostCompanyId = $v.hostCompanyId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GeneralCompanyUpdateInterviewDateTime other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GeneralCompanyUpdateInterviewDateTime;
  }

  @override
  void update(
      void Function(GeneralCompanyUpdateInterviewDateTimeBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GeneralCompanyUpdateInterviewDateTime build() => _build();

  _$GeneralCompanyUpdateInterviewDateTime _build() {
    final _$result = _$v ??
        new _$GeneralCompanyUpdateInterviewDateTime._(
          applyId: BuiltValueNullFieldError.checkNotNull(
              applyId, r'GeneralCompanyUpdateInterviewDateTime', 'applyId'),
          interviewDatetime: BuiltValueNullFieldError.checkNotNull(
              interviewDatetime,
              r'GeneralCompanyUpdateInterviewDateTime',
              'interviewDatetime'),
          hostCompanyId: hostCompanyId,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
