// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_company_update.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GeneralCompanyUpdate extends GeneralCompanyUpdate {
  @override
  final int? companyId;
  @override
  final String? name;
  @override
  final String? introductionUrl;
  @override
  final int? employeesType;
  @override
  final String? countryCode;
  @override
  final String? addressCode;
  @override
  final String? address;
  @override
  final String? tel;
  @override
  final String? contactMail;
  @override
  final String? aboutUs;
  @override
  final String? businessDetails;
  @override
  final String? webUrl;
  @override
  final String? capitalStock;
  @override
  final String? capitalStockCurrCode;
  @override
  final String? acceptingFee;
  @override
  final String? acceptingFeeCurrCode;
  @override
  final String? supportOutsourcingFee;
  @override
  final String? supportOutsourcingFeeCurrCode;
  @override
  final String? support;
  @override
  final String? benefits;
  @override
  final String? internationalTel;
  @override
  final String? workingHoursFrom;
  @override
  final String? workingHoursTo;

  factory _$GeneralCompanyUpdate(
          [void Function(GeneralCompanyUpdateBuilder)? updates]) =>
      (new GeneralCompanyUpdateBuilder()..update(updates))._build();

  _$GeneralCompanyUpdate._(
      {this.companyId,
      this.name,
      this.introductionUrl,
      this.employeesType,
      this.countryCode,
      this.addressCode,
      this.address,
      this.tel,
      this.contactMail,
      this.aboutUs,
      this.businessDetails,
      this.webUrl,
      this.capitalStock,
      this.capitalStockCurrCode,
      this.acceptingFee,
      this.acceptingFeeCurrCode,
      this.supportOutsourcingFee,
      this.supportOutsourcingFeeCurrCode,
      this.support,
      this.benefits,
      this.internationalTel,
      this.workingHoursFrom,
      this.workingHoursTo})
      : super._();

  @override
  GeneralCompanyUpdate rebuild(
          void Function(GeneralCompanyUpdateBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GeneralCompanyUpdateBuilder toBuilder() =>
      new GeneralCompanyUpdateBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GeneralCompanyUpdate &&
        companyId == other.companyId &&
        name == other.name &&
        introductionUrl == other.introductionUrl &&
        employeesType == other.employeesType &&
        countryCode == other.countryCode &&
        addressCode == other.addressCode &&
        address == other.address &&
        tel == other.tel &&
        contactMail == other.contactMail &&
        aboutUs == other.aboutUs &&
        businessDetails == other.businessDetails &&
        webUrl == other.webUrl &&
        capitalStock == other.capitalStock &&
        capitalStockCurrCode == other.capitalStockCurrCode &&
        acceptingFee == other.acceptingFee &&
        acceptingFeeCurrCode == other.acceptingFeeCurrCode &&
        supportOutsourcingFee == other.supportOutsourcingFee &&
        supportOutsourcingFeeCurrCode == other.supportOutsourcingFeeCurrCode &&
        support == other.support &&
        benefits == other.benefits &&
        internationalTel == other.internationalTel &&
        workingHoursFrom == other.workingHoursFrom &&
        workingHoursTo == other.workingHoursTo;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, companyId.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, introductionUrl.hashCode);
    _$hash = $jc(_$hash, employeesType.hashCode);
    _$hash = $jc(_$hash, countryCode.hashCode);
    _$hash = $jc(_$hash, addressCode.hashCode);
    _$hash = $jc(_$hash, address.hashCode);
    _$hash = $jc(_$hash, tel.hashCode);
    _$hash = $jc(_$hash, contactMail.hashCode);
    _$hash = $jc(_$hash, aboutUs.hashCode);
    _$hash = $jc(_$hash, businessDetails.hashCode);
    _$hash = $jc(_$hash, webUrl.hashCode);
    _$hash = $jc(_$hash, capitalStock.hashCode);
    _$hash = $jc(_$hash, capitalStockCurrCode.hashCode);
    _$hash = $jc(_$hash, acceptingFee.hashCode);
    _$hash = $jc(_$hash, acceptingFeeCurrCode.hashCode);
    _$hash = $jc(_$hash, supportOutsourcingFee.hashCode);
    _$hash = $jc(_$hash, supportOutsourcingFeeCurrCode.hashCode);
    _$hash = $jc(_$hash, support.hashCode);
    _$hash = $jc(_$hash, benefits.hashCode);
    _$hash = $jc(_$hash, internationalTel.hashCode);
    _$hash = $jc(_$hash, workingHoursFrom.hashCode);
    _$hash = $jc(_$hash, workingHoursTo.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GeneralCompanyUpdate')
          ..add('companyId', companyId)
          ..add('name', name)
          ..add('introductionUrl', introductionUrl)
          ..add('employeesType', employeesType)
          ..add('countryCode', countryCode)
          ..add('addressCode', addressCode)
          ..add('address', address)
          ..add('tel', tel)
          ..add('contactMail', contactMail)
          ..add('aboutUs', aboutUs)
          ..add('businessDetails', businessDetails)
          ..add('webUrl', webUrl)
          ..add('capitalStock', capitalStock)
          ..add('capitalStockCurrCode', capitalStockCurrCode)
          ..add('acceptingFee', acceptingFee)
          ..add('acceptingFeeCurrCode', acceptingFeeCurrCode)
          ..add('supportOutsourcingFee', supportOutsourcingFee)
          ..add('supportOutsourcingFeeCurrCode', supportOutsourcingFeeCurrCode)
          ..add('support', support)
          ..add('benefits', benefits)
          ..add('internationalTel', internationalTel)
          ..add('workingHoursFrom', workingHoursFrom)
          ..add('workingHoursTo', workingHoursTo))
        .toString();
  }
}

class GeneralCompanyUpdateBuilder
    implements Builder<GeneralCompanyUpdate, GeneralCompanyUpdateBuilder> {
  _$GeneralCompanyUpdate? _$v;

  int? _companyId;
  int? get companyId => _$this._companyId;
  set companyId(int? companyId) => _$this._companyId = companyId;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _introductionUrl;
  String? get introductionUrl => _$this._introductionUrl;
  set introductionUrl(String? introductionUrl) =>
      _$this._introductionUrl = introductionUrl;

  int? _employeesType;
  int? get employeesType => _$this._employeesType;
  set employeesType(int? employeesType) =>
      _$this._employeesType = employeesType;

  String? _countryCode;
  String? get countryCode => _$this._countryCode;
  set countryCode(String? countryCode) => _$this._countryCode = countryCode;

  String? _addressCode;
  String? get addressCode => _$this._addressCode;
  set addressCode(String? addressCode) => _$this._addressCode = addressCode;

  String? _address;
  String? get address => _$this._address;
  set address(String? address) => _$this._address = address;

  String? _tel;
  String? get tel => _$this._tel;
  set tel(String? tel) => _$this._tel = tel;

  String? _contactMail;
  String? get contactMail => _$this._contactMail;
  set contactMail(String? contactMail) => _$this._contactMail = contactMail;

  String? _aboutUs;
  String? get aboutUs => _$this._aboutUs;
  set aboutUs(String? aboutUs) => _$this._aboutUs = aboutUs;

  String? _businessDetails;
  String? get businessDetails => _$this._businessDetails;
  set businessDetails(String? businessDetails) =>
      _$this._businessDetails = businessDetails;

  String? _webUrl;
  String? get webUrl => _$this._webUrl;
  set webUrl(String? webUrl) => _$this._webUrl = webUrl;

  String? _capitalStock;
  String? get capitalStock => _$this._capitalStock;
  set capitalStock(String? capitalStock) => _$this._capitalStock = capitalStock;

  String? _capitalStockCurrCode;
  String? get capitalStockCurrCode => _$this._capitalStockCurrCode;
  set capitalStockCurrCode(String? capitalStockCurrCode) =>
      _$this._capitalStockCurrCode = capitalStockCurrCode;

  String? _acceptingFee;
  String? get acceptingFee => _$this._acceptingFee;
  set acceptingFee(String? acceptingFee) => _$this._acceptingFee = acceptingFee;

  String? _acceptingFeeCurrCode;
  String? get acceptingFeeCurrCode => _$this._acceptingFeeCurrCode;
  set acceptingFeeCurrCode(String? acceptingFeeCurrCode) =>
      _$this._acceptingFeeCurrCode = acceptingFeeCurrCode;

  String? _supportOutsourcingFee;
  String? get supportOutsourcingFee => _$this._supportOutsourcingFee;
  set supportOutsourcingFee(String? supportOutsourcingFee) =>
      _$this._supportOutsourcingFee = supportOutsourcingFee;

  String? _supportOutsourcingFeeCurrCode;
  String? get supportOutsourcingFeeCurrCode =>
      _$this._supportOutsourcingFeeCurrCode;
  set supportOutsourcingFeeCurrCode(String? supportOutsourcingFeeCurrCode) =>
      _$this._supportOutsourcingFeeCurrCode = supportOutsourcingFeeCurrCode;

  String? _support;
  String? get support => _$this._support;
  set support(String? support) => _$this._support = support;

  String? _benefits;
  String? get benefits => _$this._benefits;
  set benefits(String? benefits) => _$this._benefits = benefits;

  String? _internationalTel;
  String? get internationalTel => _$this._internationalTel;
  set internationalTel(String? internationalTel) =>
      _$this._internationalTel = internationalTel;

  String? _workingHoursFrom;
  String? get workingHoursFrom => _$this._workingHoursFrom;
  set workingHoursFrom(String? workingHoursFrom) =>
      _$this._workingHoursFrom = workingHoursFrom;

  String? _workingHoursTo;
  String? get workingHoursTo => _$this._workingHoursTo;
  set workingHoursTo(String? workingHoursTo) =>
      _$this._workingHoursTo = workingHoursTo;

  GeneralCompanyUpdateBuilder() {
    GeneralCompanyUpdate._defaults(this);
  }

  GeneralCompanyUpdateBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _companyId = $v.companyId;
      _name = $v.name;
      _introductionUrl = $v.introductionUrl;
      _employeesType = $v.employeesType;
      _countryCode = $v.countryCode;
      _addressCode = $v.addressCode;
      _address = $v.address;
      _tel = $v.tel;
      _contactMail = $v.contactMail;
      _aboutUs = $v.aboutUs;
      _businessDetails = $v.businessDetails;
      _webUrl = $v.webUrl;
      _capitalStock = $v.capitalStock;
      _capitalStockCurrCode = $v.capitalStockCurrCode;
      _acceptingFee = $v.acceptingFee;
      _acceptingFeeCurrCode = $v.acceptingFeeCurrCode;
      _supportOutsourcingFee = $v.supportOutsourcingFee;
      _supportOutsourcingFeeCurrCode = $v.supportOutsourcingFeeCurrCode;
      _support = $v.support;
      _benefits = $v.benefits;
      _internationalTel = $v.internationalTel;
      _workingHoursFrom = $v.workingHoursFrom;
      _workingHoursTo = $v.workingHoursTo;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GeneralCompanyUpdate other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GeneralCompanyUpdate;
  }

  @override
  void update(void Function(GeneralCompanyUpdateBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GeneralCompanyUpdate build() => _build();

  _$GeneralCompanyUpdate _build() {
    final _$result = _$v ??
        new _$GeneralCompanyUpdate._(
          companyId: companyId,
          name: name,
          introductionUrl: introductionUrl,
          employeesType: employeesType,
          countryCode: countryCode,
          addressCode: addressCode,
          address: address,
          tel: tel,
          contactMail: contactMail,
          aboutUs: aboutUs,
          businessDetails: businessDetails,
          webUrl: webUrl,
          capitalStock: capitalStock,
          capitalStockCurrCode: capitalStockCurrCode,
          acceptingFee: acceptingFee,
          acceptingFeeCurrCode: acceptingFeeCurrCode,
          supportOutsourcingFee: supportOutsourcingFee,
          supportOutsourcingFeeCurrCode: supportOutsourcingFeeCurrCode,
          support: support,
          benefits: benefits,
          internationalTel: internationalTel,
          workingHoursFrom: workingHoursFrom,
          workingHoursTo: workingHoursTo,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
