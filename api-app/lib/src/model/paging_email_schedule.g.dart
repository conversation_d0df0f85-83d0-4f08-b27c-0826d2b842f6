// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'paging_email_schedule.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$PagingEmailSchedule extends PagingEmailSchedule {
  @override
  final String? next;
  @override
  final String? previous;
  @override
  final BuiltList<EmailSchedule> results;
  @override
  final int totalCount;

  factory _$PagingEmailSchedule(
          [void Function(PagingEmailScheduleBuilder)? updates]) =>
      (new PagingEmailScheduleBuilder()..update(updates))._build();

  _$PagingEmailSchedule._(
      {this.next,
      this.previous,
      required this.results,
      required this.totalCount})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        results, r'PagingEmailSchedule', 'results');
    BuiltValueNullFieldError.checkNotNull(
        totalCount, r'PagingEmailSchedule', 'totalCount');
  }

  @override
  PagingEmailSchedule rebuild(
          void Function(PagingEmailScheduleBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PagingEmailScheduleBuilder toBuilder() =>
      new PagingEmailScheduleBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PagingEmailSchedule &&
        next == other.next &&
        previous == other.previous &&
        results == other.results &&
        totalCount == other.totalCount;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, next.hashCode);
    _$hash = $jc(_$hash, previous.hashCode);
    _$hash = $jc(_$hash, results.hashCode);
    _$hash = $jc(_$hash, totalCount.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PagingEmailSchedule')
          ..add('next', next)
          ..add('previous', previous)
          ..add('results', results)
          ..add('totalCount', totalCount))
        .toString();
  }
}

class PagingEmailScheduleBuilder
    implements Builder<PagingEmailSchedule, PagingEmailScheduleBuilder> {
  _$PagingEmailSchedule? _$v;

  String? _next;
  String? get next => _$this._next;
  set next(String? next) => _$this._next = next;

  String? _previous;
  String? get previous => _$this._previous;
  set previous(String? previous) => _$this._previous = previous;

  ListBuilder<EmailSchedule>? _results;
  ListBuilder<EmailSchedule> get results =>
      _$this._results ??= new ListBuilder<EmailSchedule>();
  set results(ListBuilder<EmailSchedule>? results) => _$this._results = results;

  int? _totalCount;
  int? get totalCount => _$this._totalCount;
  set totalCount(int? totalCount) => _$this._totalCount = totalCount;

  PagingEmailScheduleBuilder() {
    PagingEmailSchedule._defaults(this);
  }

  PagingEmailScheduleBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _next = $v.next;
      _previous = $v.previous;
      _results = $v.results.toBuilder();
      _totalCount = $v.totalCount;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PagingEmailSchedule other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$PagingEmailSchedule;
  }

  @override
  void update(void Function(PagingEmailScheduleBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PagingEmailSchedule build() => _build();

  _$PagingEmailSchedule _build() {
    _$PagingEmailSchedule _$result;
    try {
      _$result = _$v ??
          new _$PagingEmailSchedule._(
            next: next,
            previous: previous,
            results: results.build(),
            totalCount: BuiltValueNullFieldError.checkNotNull(
                totalCount, r'PagingEmailSchedule', 'totalCount'),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'results';
        results.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'PagingEmailSchedule', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
