//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'general_company_request_interview_apply.g.dart';

/// GeneralCompanyRequestInterviewApply
///
/// Properties:
/// * [applyId] 
/// * [hostCompanyId] 
@BuiltValue()
abstract class GeneralCompanyRequestInterviewApply implements Built<GeneralCompanyRequestInterviewApply, GeneralCompanyRequestInterviewApplyBuilder> {
  @BuiltValueField(wireName: r'apply_id')
  int get applyId;

  @BuiltValueField(wireName: r'host_company_id')
  int? get hostCompanyId;

  GeneralCompanyRequestInterviewApply._();

  factory GeneralCompanyRequestInterviewApply([void updates(GeneralCompanyRequestInterviewApplyBuilder b)]) = _$GeneralCompanyRequestInterviewApply;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(GeneralCompanyRequestInterviewApplyBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<GeneralCompanyRequestInterviewApply> get serializer => _$GeneralCompanyRequestInterviewApplySerializer();
}

class _$GeneralCompanyRequestInterviewApplySerializer implements PrimitiveSerializer<GeneralCompanyRequestInterviewApply> {
  @override
  final Iterable<Type> types = const [GeneralCompanyRequestInterviewApply, _$GeneralCompanyRequestInterviewApply];

  @override
  final String wireName = r'GeneralCompanyRequestInterviewApply';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    GeneralCompanyRequestInterviewApply object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'apply_id';
    yield serializers.serialize(
      object.applyId,
      specifiedType: const FullType(int),
    );
    if (object.hostCompanyId != null) {
      yield r'host_company_id';
      yield serializers.serialize(
        object.hostCompanyId,
        specifiedType: const FullType.nullable(int),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    GeneralCompanyRequestInterviewApply object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required GeneralCompanyRequestInterviewApplyBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'apply_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.applyId = valueDes;
          break;
        case r'host_company_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.hostCompanyId = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  GeneralCompanyRequestInterviewApply deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = GeneralCompanyRequestInterviewApplyBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

