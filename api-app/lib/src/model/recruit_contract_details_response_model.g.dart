// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recruit_contract_details_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$RecruitContractDetailsResponseModel
    extends RecruitContractDetailsResponseModel {
  @override
  final String? message;
  @override
  final BuiltList<ErrorDetail>? errors;
  @override
  final RecruitGetContractDetails data;

  factory _$RecruitContractDetailsResponseModel(
          [void Function(RecruitContractDetailsResponseModelBuilder)?
              updates]) =>
      (new RecruitContractDetailsResponseModelBuilder()..update(updates))
          ._build();

  _$RecruitContractDetailsResponseModel._(
      {this.message, this.errors, required this.data})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'RecruitContractDetailsResponseModel', 'data');
  }

  @override
  RecruitContractDetailsResponseModel rebuild(
          void Function(RecruitContractDetailsResponseModelBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  RecruitContractDetailsResponseModelBuilder toBuilder() =>
      new RecruitContractDetailsResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is RecruitContractDetailsResponseModel &&
        message == other.message &&
        errors == other.errors &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'RecruitContractDetailsResponseModel')
          ..add('message', message)
          ..add('errors', errors)
          ..add('data', data))
        .toString();
  }
}

class RecruitContractDetailsResponseModelBuilder
    implements
        Builder<RecruitContractDetailsResponseModel,
            RecruitContractDetailsResponseModelBuilder> {
  _$RecruitContractDetailsResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  RecruitGetContractDetailsBuilder? _data;
  RecruitGetContractDetailsBuilder get data =>
      _$this._data ??= new RecruitGetContractDetailsBuilder();
  set data(RecruitGetContractDetailsBuilder? data) => _$this._data = data;

  RecruitContractDetailsResponseModelBuilder() {
    RecruitContractDetailsResponseModel._defaults(this);
  }

  RecruitContractDetailsResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _errors = $v.errors?.toBuilder();
      _data = $v.data.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(RecruitContractDetailsResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$RecruitContractDetailsResponseModel;
  }

  @override
  void update(
      void Function(RecruitContractDetailsResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  RecruitContractDetailsResponseModel build() => _build();

  _$RecruitContractDetailsResponseModel _build() {
    _$RecruitContractDetailsResponseModel _$result;
    try {
      _$result = _$v ??
          new _$RecruitContractDetailsResponseModel._(
            message: message,
            errors: _errors?.build(),
            data: data.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'errors';
        _errors?.build();
        _$failedField = 'data';
        data.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'RecruitContractDetailsResponseModel',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
