//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/date.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'general_company_apply_details.g.dart';

/// GeneralCompanyApplyDetails
///
/// Properties:
/// * [applyId] 
/// * [payrollPrice] 
/// * [payrollCode] 
/// * [recruitProgressCode] 
/// * [progressUpdateDatetime] 
/// * [interviewDatetime] 
/// * [joingDate] 
/// * [jobCode] 
/// * [employCode] 
/// * [placeCode] 
/// * [benefits] 
/// * [offerContractDate] 
/// * [offerPdfPath] 
/// * [expiryDate] 
/// * [isFromSupport] 
/// * [created] 
/// * [updated] 
/// * [recruit] 
/// * [group] 
/// * [engineer] 
/// * [agencyCompany] 
/// * [agencyAgent] 
/// * [hostCompany] 
/// * [hostAgent] 
/// * [supportCompany] 
/// * [supportAgent] 
/// * [engineerAcceptSign] 
@BuiltValue()
abstract class GeneralCompanyApplyDetails implements Built<GeneralCompanyApplyDetails, GeneralCompanyApplyDetailsBuilder> {
  @BuiltValueField(wireName: r'apply_id')
  int? get applyId;

  @BuiltValueField(wireName: r'payroll_price')
  String? get payrollPrice;

  @BuiltValueField(wireName: r'payroll_code')
  String? get payrollCode;

  @BuiltValueField(wireName: r'recruit_progress_code')
  int? get recruitProgressCode;

  @BuiltValueField(wireName: r'progress_update_datetime')
  DateTime? get progressUpdateDatetime;

  @BuiltValueField(wireName: r'interview_datetime')
  DateTime? get interviewDatetime;

  @BuiltValueField(wireName: r'joing_date')
  Date? get joingDate;

  @BuiltValueField(wireName: r'job_code')
  String? get jobCode;

  @BuiltValueField(wireName: r'employ_code')
  String? get employCode;

  @BuiltValueField(wireName: r'place_code')
  String? get placeCode;

  @BuiltValueField(wireName: r'benefits')
  String? get benefits;

  @BuiltValueField(wireName: r'offer_contract_date')
  Date? get offerContractDate;

  @BuiltValueField(wireName: r'offer_pdf_path')
  String? get offerPdfPath;

  @BuiltValueField(wireName: r'expiry_date')
  Date? get expiryDate;

  @BuiltValueField(wireName: r'is_from_support')
  int? get isFromSupport;

  @BuiltValueField(wireName: r'created')
  DateTime? get created;

  @BuiltValueField(wireName: r'updated')
  DateTime? get updated;

  @BuiltValueField(wireName: r'recruit')
  int? get recruit;

  @BuiltValueField(wireName: r'group')
  int? get group;

  @BuiltValueField(wireName: r'engineer')
  int get engineer;

  @BuiltValueField(wireName: r'agency_company')
  int? get agencyCompany;

  @BuiltValueField(wireName: r'agency_agent')
  int? get agencyAgent;

  @BuiltValueField(wireName: r'host_company')
  int? get hostCompany;

  @BuiltValueField(wireName: r'host_agent')
  int? get hostAgent;

  @BuiltValueField(wireName: r'support_company')
  int? get supportCompany;

  @BuiltValueField(wireName: r'support_agent')
  int? get supportAgent;

  @BuiltValueField(wireName: r'engineer_accept_sign')
  int? get engineerAcceptSign;

  GeneralCompanyApplyDetails._();

  factory GeneralCompanyApplyDetails([void updates(GeneralCompanyApplyDetailsBuilder b)]) = _$GeneralCompanyApplyDetails;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(GeneralCompanyApplyDetailsBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<GeneralCompanyApplyDetails> get serializer => _$GeneralCompanyApplyDetailsSerializer();
}

class _$GeneralCompanyApplyDetailsSerializer implements PrimitiveSerializer<GeneralCompanyApplyDetails> {
  @override
  final Iterable<Type> types = const [GeneralCompanyApplyDetails, _$GeneralCompanyApplyDetails];

  @override
  final String wireName = r'GeneralCompanyApplyDetails';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    GeneralCompanyApplyDetails object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.applyId != null) {
      yield r'apply_id';
      yield serializers.serialize(
        object.applyId,
        specifiedType: const FullType(int),
      );
    }
    if (object.payrollPrice != null) {
      yield r'payroll_price';
      yield serializers.serialize(
        object.payrollPrice,
        specifiedType: const FullType(String),
      );
    }
    if (object.payrollCode != null) {
      yield r'payroll_code';
      yield serializers.serialize(
        object.payrollCode,
        specifiedType: const FullType(String),
      );
    }
    if (object.recruitProgressCode != null) {
      yield r'recruit_progress_code';
      yield serializers.serialize(
        object.recruitProgressCode,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.progressUpdateDatetime != null) {
      yield r'progress_update_datetime';
      yield serializers.serialize(
        object.progressUpdateDatetime,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.interviewDatetime != null) {
      yield r'interview_datetime';
      yield serializers.serialize(
        object.interviewDatetime,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.joingDate != null) {
      yield r'joing_date';
      yield serializers.serialize(
        object.joingDate,
        specifiedType: const FullType.nullable(Date),
      );
    }
    if (object.jobCode != null) {
      yield r'job_code';
      yield serializers.serialize(
        object.jobCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.employCode != null) {
      yield r'employ_code';
      yield serializers.serialize(
        object.employCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.placeCode != null) {
      yield r'place_code';
      yield serializers.serialize(
        object.placeCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.benefits != null) {
      yield r'benefits';
      yield serializers.serialize(
        object.benefits,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.offerContractDate != null) {
      yield r'offer_contract_date';
      yield serializers.serialize(
        object.offerContractDate,
        specifiedType: const FullType.nullable(Date),
      );
    }
    if (object.offerPdfPath != null) {
      yield r'offer_pdf_path';
      yield serializers.serialize(
        object.offerPdfPath,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.expiryDate != null) {
      yield r'expiry_date';
      yield serializers.serialize(
        object.expiryDate,
        specifiedType: const FullType.nullable(Date),
      );
    }
    if (object.isFromSupport != null) {
      yield r'is_from_support';
      yield serializers.serialize(
        object.isFromSupport,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.created != null) {
      yield r'created';
      yield serializers.serialize(
        object.created,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.updated != null) {
      yield r'updated';
      yield serializers.serialize(
        object.updated,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.recruit != null) {
      yield r'recruit';
      yield serializers.serialize(
        object.recruit,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.group != null) {
      yield r'group';
      yield serializers.serialize(
        object.group,
        specifiedType: const FullType.nullable(int),
      );
    }
    yield r'engineer';
    yield serializers.serialize(
      object.engineer,
      specifiedType: const FullType(int),
    );
    if (object.agencyCompany != null) {
      yield r'agency_company';
      yield serializers.serialize(
        object.agencyCompany,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.agencyAgent != null) {
      yield r'agency_agent';
      yield serializers.serialize(
        object.agencyAgent,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.hostCompany != null) {
      yield r'host_company';
      yield serializers.serialize(
        object.hostCompany,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.hostAgent != null) {
      yield r'host_agent';
      yield serializers.serialize(
        object.hostAgent,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.supportCompany != null) {
      yield r'support_company';
      yield serializers.serialize(
        object.supportCompany,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.supportAgent != null) {
      yield r'support_agent';
      yield serializers.serialize(
        object.supportAgent,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.engineerAcceptSign != null) {
      yield r'engineer_accept_sign';
      yield serializers.serialize(
        object.engineerAcceptSign,
        specifiedType: const FullType.nullable(int),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    GeneralCompanyApplyDetails object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required GeneralCompanyApplyDetailsBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'apply_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.applyId = valueDes;
          break;
        case r'payroll_price':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.payrollPrice = valueDes;
          break;
        case r'payroll_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.payrollCode = valueDes;
          break;
        case r'recruit_progress_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.recruitProgressCode = valueDes;
          break;
        case r'progress_update_datetime':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.progressUpdateDatetime = valueDes;
          break;
        case r'interview_datetime':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.interviewDatetime = valueDes;
          break;
        case r'joing_date':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(Date),
          ) as Date?;
          if (valueDes == null) continue;
          result.joingDate = valueDes;
          break;
        case r'job_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.jobCode = valueDes;
          break;
        case r'employ_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.employCode = valueDes;
          break;
        case r'place_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.placeCode = valueDes;
          break;
        case r'benefits':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.benefits = valueDes;
          break;
        case r'offer_contract_date':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(Date),
          ) as Date?;
          if (valueDes == null) continue;
          result.offerContractDate = valueDes;
          break;
        case r'offer_pdf_path':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.offerPdfPath = valueDes;
          break;
        case r'expiry_date':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(Date),
          ) as Date?;
          if (valueDes == null) continue;
          result.expiryDate = valueDes;
          break;
        case r'is_from_support':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.isFromSupport = valueDes;
          break;
        case r'created':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.created = valueDes;
          break;
        case r'updated':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.updated = valueDes;
          break;
        case r'recruit':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.recruit = valueDes;
          break;
        case r'group':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.group = valueDes;
          break;
        case r'engineer':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.engineer = valueDes;
          break;
        case r'agency_company':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.agencyCompany = valueDes;
          break;
        case r'agency_agent':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.agencyAgent = valueDes;
          break;
        case r'host_company':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.hostCompany = valueDes;
          break;
        case r'host_agent':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.hostAgent = valueDes;
          break;
        case r'support_company':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.supportCompany = valueDes;
          break;
        case r'support_agent':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.supportAgent = valueDes;
          break;
        case r'engineer_accept_sign':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.engineerAcceptSign = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  GeneralCompanyApplyDetails deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = GeneralCompanyApplyDetailsBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

