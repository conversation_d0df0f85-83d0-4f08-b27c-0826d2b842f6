// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login_success.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$LoginSuccess extends LoginSuccess {
  @override
  final String? captchaKey;
  @override
  final String? captchaImageUrl;
  @override
  final bool isEmailVerified;
  @override
  final String? refresh;
  @override
  final String? access;

  factory _$LoginSuccess([void Function(LoginSuccessBuilder)? updates]) =>
      (new LoginSuccessBuilder()..update(updates))._build();

  _$LoginSuccess._(
      {this.captcha<PERSON>ey,
      this.captchaImageUrl,
      required this.isEmailVerified,
      this.refresh,
      this.access})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        isEmailVerified, r'LoginSuccess', 'isEmailVerified');
  }

  @override
  LoginSuccess rebuild(void Function(LoginSuccessBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  LoginSuccessBuilder toBuilder() => new LoginSuccessBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is LoginSuccess &&
        captchaKey == other.captchaKey &&
        captchaImageUrl == other.captchaImageUrl &&
        isEmailVerified == other.isEmailVerified &&
        refresh == other.refresh &&
        access == other.access;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, captchaKey.hashCode);
    _$hash = $jc(_$hash, captchaImageUrl.hashCode);
    _$hash = $jc(_$hash, isEmailVerified.hashCode);
    _$hash = $jc(_$hash, refresh.hashCode);
    _$hash = $jc(_$hash, access.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'LoginSuccess')
          ..add('captchaKey', captchaKey)
          ..add('captchaImageUrl', captchaImageUrl)
          ..add('isEmailVerified', isEmailVerified)
          ..add('refresh', refresh)
          ..add('access', access))
        .toString();
  }
}

class LoginSuccessBuilder
    implements Builder<LoginSuccess, LoginSuccessBuilder> {
  _$LoginSuccess? _$v;

  String? _captchaKey;
  String? get captchaKey => _$this._captchaKey;
  set captchaKey(String? captchaKey) => _$this._captchaKey = captchaKey;

  String? _captchaImageUrl;
  String? get captchaImageUrl => _$this._captchaImageUrl;
  set captchaImageUrl(String? captchaImageUrl) =>
      _$this._captchaImageUrl = captchaImageUrl;

  bool? _isEmailVerified;
  bool? get isEmailVerified => _$this._isEmailVerified;
  set isEmailVerified(bool? isEmailVerified) =>
      _$this._isEmailVerified = isEmailVerified;

  String? _refresh;
  String? get refresh => _$this._refresh;
  set refresh(String? refresh) => _$this._refresh = refresh;

  String? _access;
  String? get access => _$this._access;
  set access(String? access) => _$this._access = access;

  LoginSuccessBuilder() {
    LoginSuccess._defaults(this);
  }

  LoginSuccessBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _captchaKey = $v.captchaKey;
      _captchaImageUrl = $v.captchaImageUrl;
      _isEmailVerified = $v.isEmailVerified;
      _refresh = $v.refresh;
      _access = $v.access;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(LoginSuccess other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$LoginSuccess;
  }

  @override
  void update(void Function(LoginSuccessBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  LoginSuccess build() => _build();

  _$LoginSuccess _build() {
    final _$result = _$v ??
        new _$LoginSuccess._(
          captchaKey: captchaKey,
          captchaImageUrl: captchaImageUrl,
          isEmailVerified: BuiltValueNullFieldError.checkNotNull(
              isEmailVerified, r'LoginSuccess', 'isEmailVerified'),
          refresh: refresh,
          access: access,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
