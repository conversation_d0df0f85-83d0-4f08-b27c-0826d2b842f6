//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'save_cv_uploaded.g.dart';

/// SaveCVUploaded
///
/// Properties:
/// * [cvId] 
@BuiltValue()
abstract class SaveCVUploaded implements Built<SaveCVUploaded, SaveCVUploadedBuilder> {
  @BuiltValueField(wireName: r'cv_id')
  String get cvId;

  SaveCVUploaded._();

  factory SaveCVUploaded([void updates(SaveCVUploadedBuilder b)]) = _$SaveCVUploaded;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(SaveCVUploadedBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<SaveCVUploaded> get serializer => _$SaveCVUploadedSerializer();
}

class _$SaveCVUploadedSerializer implements PrimitiveSerializer<SaveCVUploaded> {
  @override
  final Iterable<Type> types = const [SaveCVUploaded, _$SaveCVUploaded];

  @override
  final String wireName = r'SaveCVUploaded';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    SaveCVUploaded object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'cv_id';
    yield serializers.serialize(
      object.cvId,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    SaveCVUploaded object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required SaveCVUploadedBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'cv_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.cvId = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  SaveCVUploaded deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = SaveCVUploadedBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

