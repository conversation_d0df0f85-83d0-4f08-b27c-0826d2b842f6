//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/general_company_explore_user.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'paging_general_company_explore_user_response_model.g.dart';

/// PagingGeneralCompanyExploreUserResponseModel
///
/// Properties:
/// * [count] 
/// * [next] 
/// * [previous] 
/// * [aiSummary] 
/// * [aiSummaryJa] 
/// * [aiSummaryVi] 
/// * [recruitId] 
/// * [results] 
@BuiltValue()
abstract class PagingGeneralCompanyExploreUserResponseModel implements Built<PagingGeneralCompanyExploreUserResponseModel, PagingGeneralCompanyExploreUserResponseModelBuilder> {
  @BuiltValueField(wireName: r'count')
  int get count;

  @BuiltValueField(wireName: r'next')
  String? get next;

  @BuiltValueField(wireName: r'previous')
  String? get previous;

  @BuiltValueField(wireName: r'ai_summary')
  String? get aiSummary;

  @BuiltValueField(wireName: r'ai_summary_ja')
  String? get aiSummaryJa;

  @BuiltValueField(wireName: r'ai_summary_vi')
  String? get aiSummaryVi;

  @BuiltValueField(wireName: r'recruit_id')
  int? get recruitId;

  @BuiltValueField(wireName: r'results')
  BuiltList<GeneralCompanyExploreUser> get results;

  PagingGeneralCompanyExploreUserResponseModel._();

  factory PagingGeneralCompanyExploreUserResponseModel([void updates(PagingGeneralCompanyExploreUserResponseModelBuilder b)]) = _$PagingGeneralCompanyExploreUserResponseModel;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(PagingGeneralCompanyExploreUserResponseModelBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<PagingGeneralCompanyExploreUserResponseModel> get serializer => _$PagingGeneralCompanyExploreUserResponseModelSerializer();
}

class _$PagingGeneralCompanyExploreUserResponseModelSerializer implements PrimitiveSerializer<PagingGeneralCompanyExploreUserResponseModel> {
  @override
  final Iterable<Type> types = const [PagingGeneralCompanyExploreUserResponseModel, _$PagingGeneralCompanyExploreUserResponseModel];

  @override
  final String wireName = r'PagingGeneralCompanyExploreUserResponseModel';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    PagingGeneralCompanyExploreUserResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'count';
    yield serializers.serialize(
      object.count,
      specifiedType: const FullType(int),
    );
    yield r'next';
    yield object.next == null ? null : serializers.serialize(
      object.next,
      specifiedType: const FullType.nullable(String),
    );
    yield r'previous';
    yield object.previous == null ? null : serializers.serialize(
      object.previous,
      specifiedType: const FullType.nullable(String),
    );
    yield r'ai_summary';
    yield object.aiSummary == null ? null : serializers.serialize(
      object.aiSummary,
      specifiedType: const FullType.nullable(String),
    );
    yield r'ai_summary_ja';
    yield object.aiSummaryJa == null ? null : serializers.serialize(
      object.aiSummaryJa,
      specifiedType: const FullType.nullable(String),
    );
    yield r'ai_summary_vi';
    yield object.aiSummaryVi == null ? null : serializers.serialize(
      object.aiSummaryVi,
      specifiedType: const FullType.nullable(String),
    );
    yield r'recruit_id';
    yield object.recruitId == null ? null : serializers.serialize(
      object.recruitId,
      specifiedType: const FullType.nullable(int),
    );
    yield r'results';
    yield serializers.serialize(
      object.results,
      specifiedType: const FullType(BuiltList, [FullType(GeneralCompanyExploreUser)]),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    PagingGeneralCompanyExploreUserResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required PagingGeneralCompanyExploreUserResponseModelBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'count':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.count = valueDes;
          break;
        case r'next':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.next = valueDes;
          break;
        case r'previous':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.previous = valueDes;
          break;
        case r'ai_summary':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.aiSummary = valueDes;
          break;
        case r'ai_summary_ja':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.aiSummaryJa = valueDes;
          break;
        case r'ai_summary_vi':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.aiSummaryVi = valueDes;
          break;
        case r'recruit_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.recruitId = valueDes;
          break;
        case r'results':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(GeneralCompanyExploreUser)]),
          ) as BuiltList<GeneralCompanyExploreUser>;
          result.results.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  PagingGeneralCompanyExploreUserResponseModel deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = PagingGeneralCompanyExploreUserResponseModelBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

