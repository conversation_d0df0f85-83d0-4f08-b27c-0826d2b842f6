// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_chat.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UserChat extends UserChat {
  @override
  final int userId;
  @override
  final int userType;
  @override
  final String? email;
  @override
  final String? profileImagePath;
  @override
  final String? firstName;
  @override
  final String? lastName;
  @override
  final HostCompany? company;
  @override
  final int? chatId;
  @override
  final int? age;
  @override
  final String? nickname;
  @override
  final String? countryCode;

  factory _$UserChat([void Function(UserChatBuilder)? updates]) =>
      (new UserChatBuilder()..update(updates))._build();

  _$UserChat._(
      {required this.userId,
      required this.userType,
      this.email,
      this.profileImagePath,
      this.firstName,
      this.lastName,
      this.company,
      this.chatId,
      this.age,
      this.nickname,
      this.countryCode})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(userId, r'UserChat', 'userId');
    BuiltValueNullFieldError.checkNotNull(userType, r'UserChat', 'userType');
  }

  @override
  UserChat rebuild(void Function(UserChatBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UserChatBuilder toBuilder() => new UserChatBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UserChat &&
        userId == other.userId &&
        userType == other.userType &&
        email == other.email &&
        profileImagePath == other.profileImagePath &&
        firstName == other.firstName &&
        lastName == other.lastName &&
        company == other.company &&
        chatId == other.chatId &&
        age == other.age &&
        nickname == other.nickname &&
        countryCode == other.countryCode;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, userId.hashCode);
    _$hash = $jc(_$hash, userType.hashCode);
    _$hash = $jc(_$hash, email.hashCode);
    _$hash = $jc(_$hash, profileImagePath.hashCode);
    _$hash = $jc(_$hash, firstName.hashCode);
    _$hash = $jc(_$hash, lastName.hashCode);
    _$hash = $jc(_$hash, company.hashCode);
    _$hash = $jc(_$hash, chatId.hashCode);
    _$hash = $jc(_$hash, age.hashCode);
    _$hash = $jc(_$hash, nickname.hashCode);
    _$hash = $jc(_$hash, countryCode.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UserChat')
          ..add('userId', userId)
          ..add('userType', userType)
          ..add('email', email)
          ..add('profileImagePath', profileImagePath)
          ..add('firstName', firstName)
          ..add('lastName', lastName)
          ..add('company', company)
          ..add('chatId', chatId)
          ..add('age', age)
          ..add('nickname', nickname)
          ..add('countryCode', countryCode))
        .toString();
  }
}

class UserChatBuilder implements Builder<UserChat, UserChatBuilder> {
  _$UserChat? _$v;

  int? _userId;
  int? get userId => _$this._userId;
  set userId(int? userId) => _$this._userId = userId;

  int? _userType;
  int? get userType => _$this._userType;
  set userType(int? userType) => _$this._userType = userType;

  String? _email;
  String? get email => _$this._email;
  set email(String? email) => _$this._email = email;

  String? _profileImagePath;
  String? get profileImagePath => _$this._profileImagePath;
  set profileImagePath(String? profileImagePath) =>
      _$this._profileImagePath = profileImagePath;

  String? _firstName;
  String? get firstName => _$this._firstName;
  set firstName(String? firstName) => _$this._firstName = firstName;

  String? _lastName;
  String? get lastName => _$this._lastName;
  set lastName(String? lastName) => _$this._lastName = lastName;

  HostCompanyBuilder? _company;
  HostCompanyBuilder get company =>
      _$this._company ??= new HostCompanyBuilder();
  set company(HostCompanyBuilder? company) => _$this._company = company;

  int? _chatId;
  int? get chatId => _$this._chatId;
  set chatId(int? chatId) => _$this._chatId = chatId;

  int? _age;
  int? get age => _$this._age;
  set age(int? age) => _$this._age = age;

  String? _nickname;
  String? get nickname => _$this._nickname;
  set nickname(String? nickname) => _$this._nickname = nickname;

  String? _countryCode;
  String? get countryCode => _$this._countryCode;
  set countryCode(String? countryCode) => _$this._countryCode = countryCode;

  UserChatBuilder() {
    UserChat._defaults(this);
  }

  UserChatBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _userId = $v.userId;
      _userType = $v.userType;
      _email = $v.email;
      _profileImagePath = $v.profileImagePath;
      _firstName = $v.firstName;
      _lastName = $v.lastName;
      _company = $v.company?.toBuilder();
      _chatId = $v.chatId;
      _age = $v.age;
      _nickname = $v.nickname;
      _countryCode = $v.countryCode;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UserChat other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$UserChat;
  }

  @override
  void update(void Function(UserChatBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UserChat build() => _build();

  _$UserChat _build() {
    _$UserChat _$result;
    try {
      _$result = _$v ??
          new _$UserChat._(
            userId: BuiltValueNullFieldError.checkNotNull(
                userId, r'UserChat', 'userId'),
            userType: BuiltValueNullFieldError.checkNotNull(
                userType, r'UserChat', 'userType'),
            email: email,
            profileImagePath: profileImagePath,
            firstName: firstName,
            lastName: lastName,
            company: _company?.build(),
            chatId: chatId,
            age: age,
            nickname: nickname,
            countryCode: countryCode,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'company';
        _company?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'UserChat', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
