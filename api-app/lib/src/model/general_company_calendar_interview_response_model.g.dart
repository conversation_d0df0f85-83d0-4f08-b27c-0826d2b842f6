// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_company_calendar_interview_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GeneralCompanyCalendarInterviewResponseModel
    extends GeneralCompanyCalendarInterviewResponseModel {
  @override
  final String? message;
  @override
  final BuiltList<ErrorDetail>? errors;
  @override
  final BuiltList<GeneralCompanyInterviewData> data;

  factory _$GeneralCompanyCalendarInterviewResponseModel(
          [void Function(GeneralCompanyCalendarInterviewResponseModelBuilder)?
              updates]) =>
      (new GeneralCompanyCalendarInterviewResponseModelBuilder()
            ..update(updates))
          ._build();

  _$GeneralCompanyCalendarInterviewResponseModel._(
      {this.message, this.errors, required this.data})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'GeneralCompanyCalendarInterviewResponseModel', 'data');
  }

  @override
  GeneralCompanyCalendarInterviewResponseModel rebuild(
          void Function(GeneralCompanyCalendarInterviewResponseModelBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GeneralCompanyCalendarInterviewResponseModelBuilder toBuilder() =>
      new GeneralCompanyCalendarInterviewResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GeneralCompanyCalendarInterviewResponseModel &&
        message == other.message &&
        errors == other.errors &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'GeneralCompanyCalendarInterviewResponseModel')
          ..add('message', message)
          ..add('errors', errors)
          ..add('data', data))
        .toString();
  }
}

class GeneralCompanyCalendarInterviewResponseModelBuilder
    implements
        Builder<GeneralCompanyCalendarInterviewResponseModel,
            GeneralCompanyCalendarInterviewResponseModelBuilder> {
  _$GeneralCompanyCalendarInterviewResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  ListBuilder<GeneralCompanyInterviewData>? _data;
  ListBuilder<GeneralCompanyInterviewData> get data =>
      _$this._data ??= new ListBuilder<GeneralCompanyInterviewData>();
  set data(ListBuilder<GeneralCompanyInterviewData>? data) =>
      _$this._data = data;

  GeneralCompanyCalendarInterviewResponseModelBuilder() {
    GeneralCompanyCalendarInterviewResponseModel._defaults(this);
  }

  GeneralCompanyCalendarInterviewResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _errors = $v.errors?.toBuilder();
      _data = $v.data.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GeneralCompanyCalendarInterviewResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GeneralCompanyCalendarInterviewResponseModel;
  }

  @override
  void update(
      void Function(GeneralCompanyCalendarInterviewResponseModelBuilder)?
          updates) {
    if (updates != null) updates(this);
  }

  @override
  GeneralCompanyCalendarInterviewResponseModel build() => _build();

  _$GeneralCompanyCalendarInterviewResponseModel _build() {
    _$GeneralCompanyCalendarInterviewResponseModel _$result;
    try {
      _$result = _$v ??
          new _$GeneralCompanyCalendarInterviewResponseModel._(
            message: message,
            errors: _errors?.build(),
            data: data.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'errors';
        _errors?.build();
        _$failedField = 'data';
        data.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'GeneralCompanyCalendarInterviewResponseModel',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
