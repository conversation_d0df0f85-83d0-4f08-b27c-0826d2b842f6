// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_company_register.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GeneralCompanyRegister extends GeneralCompanyRegister {
  @override
  final GeneralCompanyUser user;
  @override
  final GeneralCompany company;
  @override
  final int userType;

  factory _$GeneralCompanyRegister(
          [void Function(GeneralCompanyRegisterBuilder)? updates]) =>
      (new GeneralCompanyRegisterBuilder()..update(updates))._build();

  _$GeneralCompanyRegister._(
      {required this.user, required this.company, required this.userType})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        user, r'GeneralCompanyRegister', 'user');
    BuiltValueNullFieldError.checkNotNull(
        company, r'GeneralCompanyRegister', 'company');
    BuiltValueNullFieldError.checkNotNull(
        userType, r'GeneralCompanyRegister', 'userType');
  }

  @override
  GeneralCompanyRegister rebuild(
          void Function(GeneralCompanyRegisterBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GeneralCompanyRegisterBuilder toBuilder() =>
      new GeneralCompanyRegisterBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GeneralCompanyRegister &&
        user == other.user &&
        company == other.company &&
        userType == other.userType;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, user.hashCode);
    _$hash = $jc(_$hash, company.hashCode);
    _$hash = $jc(_$hash, userType.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GeneralCompanyRegister')
          ..add('user', user)
          ..add('company', company)
          ..add('userType', userType))
        .toString();
  }
}

class GeneralCompanyRegisterBuilder
    implements Builder<GeneralCompanyRegister, GeneralCompanyRegisterBuilder> {
  _$GeneralCompanyRegister? _$v;

  GeneralCompanyUserBuilder? _user;
  GeneralCompanyUserBuilder get user =>
      _$this._user ??= new GeneralCompanyUserBuilder();
  set user(GeneralCompanyUserBuilder? user) => _$this._user = user;

  GeneralCompanyBuilder? _company;
  GeneralCompanyBuilder get company =>
      _$this._company ??= new GeneralCompanyBuilder();
  set company(GeneralCompanyBuilder? company) => _$this._company = company;

  int? _userType;
  int? get userType => _$this._userType;
  set userType(int? userType) => _$this._userType = userType;

  GeneralCompanyRegisterBuilder() {
    GeneralCompanyRegister._defaults(this);
  }

  GeneralCompanyRegisterBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _user = $v.user.toBuilder();
      _company = $v.company.toBuilder();
      _userType = $v.userType;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GeneralCompanyRegister other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GeneralCompanyRegister;
  }

  @override
  void update(void Function(GeneralCompanyRegisterBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GeneralCompanyRegister build() => _build();

  _$GeneralCompanyRegister _build() {
    _$GeneralCompanyRegister _$result;
    try {
      _$result = _$v ??
          new _$GeneralCompanyRegister._(
            user: user.build(),
            company: company.build(),
            userType: BuiltValueNullFieldError.checkNotNull(
                userType, r'GeneralCompanyRegister', 'userType'),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'user';
        user.build();
        _$failedField = 'company';
        company.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'GeneralCompanyRegister', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
