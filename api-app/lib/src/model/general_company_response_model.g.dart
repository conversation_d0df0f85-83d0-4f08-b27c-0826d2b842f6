// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_company_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GeneralCompanyResponseModel extends GeneralCompanyResponseModel {
  @override
  final String? message;
  @override
  final BuiltList<ErrorDetail>? errors;
  @override
  final GeneralCompanyDetails data;

  factory _$GeneralCompanyResponseModel(
          [void Function(GeneralCompanyResponseModelBuilder)? updates]) =>
      (new GeneralCompanyResponseModelBuilder()..update(updates))._build();

  _$GeneralCompanyResponseModel._(
      {this.message, this.errors, required this.data})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'GeneralCompanyResponseModel', 'data');
  }

  @override
  GeneralCompanyResponseModel rebuild(
          void Function(GeneralCompanyResponseModelBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GeneralCompanyResponseModelBuilder toBuilder() =>
      new GeneralCompanyResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GeneralCompanyResponseModel &&
        message == other.message &&
        errors == other.errors &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GeneralCompanyResponseModel')
          ..add('message', message)
          ..add('errors', errors)
          ..add('data', data))
        .toString();
  }
}

class GeneralCompanyResponseModelBuilder
    implements
        Builder<GeneralCompanyResponseModel,
            GeneralCompanyResponseModelBuilder> {
  _$GeneralCompanyResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  GeneralCompanyDetailsBuilder? _data;
  GeneralCompanyDetailsBuilder get data =>
      _$this._data ??= new GeneralCompanyDetailsBuilder();
  set data(GeneralCompanyDetailsBuilder? data) => _$this._data = data;

  GeneralCompanyResponseModelBuilder() {
    GeneralCompanyResponseModel._defaults(this);
  }

  GeneralCompanyResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _errors = $v.errors?.toBuilder();
      _data = $v.data.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GeneralCompanyResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GeneralCompanyResponseModel;
  }

  @override
  void update(void Function(GeneralCompanyResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GeneralCompanyResponseModel build() => _build();

  _$GeneralCompanyResponseModel _build() {
    _$GeneralCompanyResponseModel _$result;
    try {
      _$result = _$v ??
          new _$GeneralCompanyResponseModel._(
            message: message,
            errors: _errors?.build(),
            data: data.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'errors';
        _errors?.build();
        _$failedField = 'data';
        data.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'GeneralCompanyResponseModel', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
