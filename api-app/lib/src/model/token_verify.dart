//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'token_verify.g.dart';

/// TokenVerify
///
/// Properties:
/// * [token] 
@BuiltValue()
abstract class TokenVerify implements Built<TokenVerify, TokenVerifyBuilder> {
  @BuiltValueField(wireName: r'token')
  String get token;

  TokenVerify._();

  factory TokenVerify([void updates(TokenVerifyBuilder b)]) = _$TokenVerify;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(TokenVerifyBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<TokenVerify> get serializer => _$TokenVerifySerializer();
}

class _$TokenVerifySerializer implements PrimitiveSerializer<TokenVerify> {
  @override
  final Iterable<Type> types = const [TokenVerify, _$TokenVerify];

  @override
  final String wireName = r'TokenVerify';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    TokenVerify object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'token';
    yield serializers.serialize(
      object.token,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    TokenVerify object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required TokenVerifyBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'token':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.token = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  TokenVerify deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = TokenVerifyBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

