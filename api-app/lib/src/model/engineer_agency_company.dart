//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'engineer_agency_company.g.dart';

/// EngineerAgencyCompany
///
/// Properties:
/// * [companyId] 
/// * [logoImagePath] 
/// * [name] 
/// * [agentFee] 
/// * [agentFeeCurrCode] 
@BuiltValue()
abstract class EngineerAgencyCompany implements Built<EngineerAgencyCompany, EngineerAgencyCompanyBuilder> {
  @BuiltValueField(wireName: r'company_id')
  int? get companyId;

  @BuiltValueField(wireName: r'logo_image_path')
  String? get logoImagePath;

  @BuiltValueField(wireName: r'name')
  String? get name;

  @BuiltValueField(wireName: r'agent_fee')
  double? get agentFee;

  @BuiltValueField(wireName: r'agent_fee_curr_code')
  String? get agentFeeCurrCode;

  EngineerAgencyCompany._();

  factory EngineerAgencyCompany([void updates(EngineerAgencyCompanyBuilder b)]) = _$EngineerAgencyCompany;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(EngineerAgencyCompanyBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<EngineerAgencyCompany> get serializer => _$EngineerAgencyCompanySerializer();
}

class _$EngineerAgencyCompanySerializer implements PrimitiveSerializer<EngineerAgencyCompany> {
  @override
  final Iterable<Type> types = const [EngineerAgencyCompany, _$EngineerAgencyCompany];

  @override
  final String wireName = r'EngineerAgencyCompany';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    EngineerAgencyCompany object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.companyId != null) {
      yield r'company_id';
      yield serializers.serialize(
        object.companyId,
        specifiedType: const FullType(int),
      );
    }
    if (object.logoImagePath != null) {
      yield r'logo_image_path';
      yield serializers.serialize(
        object.logoImagePath,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.name != null) {
      yield r'name';
      yield serializers.serialize(
        object.name,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.agentFee != null) {
      yield r'agent_fee';
      yield serializers.serialize(
        object.agentFee,
        specifiedType: const FullType.nullable(double),
      );
    }
    if (object.agentFeeCurrCode != null) {
      yield r'agent_fee_curr_code';
      yield serializers.serialize(
        object.agentFeeCurrCode,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    EngineerAgencyCompany object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required EngineerAgencyCompanyBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'company_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.companyId = valueDes;
          break;
        case r'logo_image_path':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.logoImagePath = valueDes;
          break;
        case r'name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.name = valueDes;
          break;
        case r'agent_fee':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(double),
          ) as double?;
          if (valueDes == null) continue;
          result.agentFee = valueDes;
          break;
        case r'agent_fee_curr_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.agentFeeCurrCode = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  EngineerAgencyCompany deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = EngineerAgencyCompanyBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

