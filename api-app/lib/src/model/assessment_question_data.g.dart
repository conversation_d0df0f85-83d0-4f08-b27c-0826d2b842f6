// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'assessment_question_data.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AssessmentQuestionData extends AssessmentQuestionData {
  @override
  final AssessmentQuestion question;
  @override
  final BuiltList<AssessmentAnswer> options;

  factory _$AssessmentQuestionData(
          [void Function(AssessmentQuestionDataBuilder)? updates]) =>
      (new AssessmentQuestionDataBuilder()..update(updates))._build();

  _$AssessmentQuestionData._({required this.question, required this.options})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        question, r'AssessmentQuestionData', 'question');
    BuiltValueNullFieldError.checkNotNull(
        options, r'AssessmentQuestionData', 'options');
  }

  @override
  AssessmentQuestionData rebuild(
          void Function(AssessmentQuestionDataBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AssessmentQuestionDataBuilder toBuilder() =>
      new AssessmentQuestionDataBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AssessmentQuestionData &&
        question == other.question &&
        options == other.options;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, question.hashCode);
    _$hash = $jc(_$hash, options.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AssessmentQuestionData')
          ..add('question', question)
          ..add('options', options))
        .toString();
  }
}

class AssessmentQuestionDataBuilder
    implements Builder<AssessmentQuestionData, AssessmentQuestionDataBuilder> {
  _$AssessmentQuestionData? _$v;

  AssessmentQuestionBuilder? _question;
  AssessmentQuestionBuilder get question =>
      _$this._question ??= new AssessmentQuestionBuilder();
  set question(AssessmentQuestionBuilder? question) =>
      _$this._question = question;

  ListBuilder<AssessmentAnswer>? _options;
  ListBuilder<AssessmentAnswer> get options =>
      _$this._options ??= new ListBuilder<AssessmentAnswer>();
  set options(ListBuilder<AssessmentAnswer>? options) =>
      _$this._options = options;

  AssessmentQuestionDataBuilder() {
    AssessmentQuestionData._defaults(this);
  }

  AssessmentQuestionDataBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _question = $v.question.toBuilder();
      _options = $v.options.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AssessmentQuestionData other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$AssessmentQuestionData;
  }

  @override
  void update(void Function(AssessmentQuestionDataBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AssessmentQuestionData build() => _build();

  _$AssessmentQuestionData _build() {
    _$AssessmentQuestionData _$result;
    try {
      _$result = _$v ??
          new _$AssessmentQuestionData._(
            question: question.build(),
            options: options.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'question';
        question.build();
        _$failedField = 'options';
        options.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'AssessmentQuestionData', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
