//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'eng_license.g.dart';

/// EngLicense
///
/// Properties:
/// * [engineerId] 
/// * [licenceCode] 
/// * [getDate] 
/// * [licencePoint] 
/// * [licenceName] 
/// * [licenseCodeName] 
@BuiltValue()
abstract class EngLicense implements Built<EngLicense, EngLicenseBuilder> {
  @BuiltValueField(wireName: r'engineer_id')
  int get engineerId;

  @BuiltValueField(wireName: r'licence_code')
  String? get licenceCode;

  @BuiltValueField(wireName: r'get_date')
  String? get getDate;

  @BuiltValueField(wireName: r'licence_point')
  String? get licencePoint;

  @BuiltValueField(wireName: r'licence_name')
  String? get licenceName;

  @BuiltValueField(wireName: r'license_code_name')
  String? get licenseCodeName;

  EngLicense._();

  factory EngLicense([void updates(EngLicenseBuilder b)]) = _$EngLicense;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(EngLicenseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<EngLicense> get serializer => _$EngLicenseSerializer();
}

class _$EngLicenseSerializer implements PrimitiveSerializer<EngLicense> {
  @override
  final Iterable<Type> types = const [EngLicense, _$EngLicense];

  @override
  final String wireName = r'EngLicense';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    EngLicense object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'engineer_id';
    yield serializers.serialize(
      object.engineerId,
      specifiedType: const FullType(int),
    );
    if (object.licenceCode != null) {
      yield r'licence_code';
      yield serializers.serialize(
        object.licenceCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.getDate != null) {
      yield r'get_date';
      yield serializers.serialize(
        object.getDate,
        specifiedType: const FullType.nullable(String),
      );
    }
    yield r'licence_point';
    yield object.licencePoint == null ? null : serializers.serialize(
      object.licencePoint,
      specifiedType: const FullType.nullable(String),
    );
    if (object.licenceName != null) {
      yield r'licence_name';
      yield serializers.serialize(
        object.licenceName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.licenseCodeName != null) {
      yield r'license_code_name';
      yield serializers.serialize(
        object.licenseCodeName,
        specifiedType: const FullType(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    EngLicense object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required EngLicenseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'engineer_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.engineerId = valueDes;
          break;
        case r'licence_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.licenceCode = valueDes;
          break;
        case r'get_date':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.getDate = valueDes;
          break;
        case r'licence_point':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.licencePoint = valueDes;
          break;
        case r'licence_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.licenceName = valueDes;
          break;
        case r'license_code_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.licenseCodeName = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  EngLicense deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = EngLicenseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

