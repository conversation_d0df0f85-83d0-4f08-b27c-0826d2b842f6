// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recruit_uploaded_serializers.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$RecruitUploadedSerializers extends RecruitUploadedSerializers {
  @override
  final int? recruitId;
  @override
  final String? title;
  @override
  final String? catchCopy;
  @override
  final String? payrollPriceFrom;
  @override
  final String? payrollPriceTo;
  @override
  final String? payrollCode;
  @override
  final DateTime? startDate;
  @override
  final DateTime? endDate;
  @override
  final String? jobCode;
  @override
  final int hostCompany;
  @override
  final String? skillJobCode1;
  @override
  final String? skillJobCode2;
  @override
  final String? skillJobCode3;
  @override
  final int hostAgent;
  @override
  final int? companyUserId;
  @override
  final int? totalApply;
  @override
  final String? jobName;
  @override
  final bool? applied;
  @override
  final String? recruitImagePath;
  @override
  final bool? isExpired;
  @override
  final String? placeCode1;
  @override
  final String? placeCode2;
  @override
  final String? placeCode3;
  @override
  final int? totalRequest;
  @override
  final int? totalUnderSelection;
  @override
  final int? displayFlag;
  @override
  final String? remoteCode;

  factory _$RecruitUploadedSerializers(
          [void Function(RecruitUploadedSerializersBuilder)? updates]) =>
      (new RecruitUploadedSerializersBuilder()..update(updates))._build();

  _$RecruitUploadedSerializers._(
      {this.recruitId,
      this.title,
      this.catchCopy,
      this.payrollPriceFrom,
      this.payrollPriceTo,
      this.payrollCode,
      this.startDate,
      this.endDate,
      this.jobCode,
      required this.hostCompany,
      this.skillJobCode1,
      this.skillJobCode2,
      this.skillJobCode3,
      required this.hostAgent,
      this.companyUserId,
      this.totalApply,
      this.jobName,
      this.applied,
      this.recruitImagePath,
      this.isExpired,
      this.placeCode1,
      this.placeCode2,
      this.placeCode3,
      this.totalRequest,
      this.totalUnderSelection,
      this.displayFlag,
      this.remoteCode})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        hostCompany, r'RecruitUploadedSerializers', 'hostCompany');
    BuiltValueNullFieldError.checkNotNull(
        hostAgent, r'RecruitUploadedSerializers', 'hostAgent');
  }

  @override
  RecruitUploadedSerializers rebuild(
          void Function(RecruitUploadedSerializersBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  RecruitUploadedSerializersBuilder toBuilder() =>
      new RecruitUploadedSerializersBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is RecruitUploadedSerializers &&
        recruitId == other.recruitId &&
        title == other.title &&
        catchCopy == other.catchCopy &&
        payrollPriceFrom == other.payrollPriceFrom &&
        payrollPriceTo == other.payrollPriceTo &&
        payrollCode == other.payrollCode &&
        startDate == other.startDate &&
        endDate == other.endDate &&
        jobCode == other.jobCode &&
        hostCompany == other.hostCompany &&
        skillJobCode1 == other.skillJobCode1 &&
        skillJobCode2 == other.skillJobCode2 &&
        skillJobCode3 == other.skillJobCode3 &&
        hostAgent == other.hostAgent &&
        companyUserId == other.companyUserId &&
        totalApply == other.totalApply &&
        jobName == other.jobName &&
        applied == other.applied &&
        recruitImagePath == other.recruitImagePath &&
        isExpired == other.isExpired &&
        placeCode1 == other.placeCode1 &&
        placeCode2 == other.placeCode2 &&
        placeCode3 == other.placeCode3 &&
        totalRequest == other.totalRequest &&
        totalUnderSelection == other.totalUnderSelection &&
        displayFlag == other.displayFlag &&
        remoteCode == other.remoteCode;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, recruitId.hashCode);
    _$hash = $jc(_$hash, title.hashCode);
    _$hash = $jc(_$hash, catchCopy.hashCode);
    _$hash = $jc(_$hash, payrollPriceFrom.hashCode);
    _$hash = $jc(_$hash, payrollPriceTo.hashCode);
    _$hash = $jc(_$hash, payrollCode.hashCode);
    _$hash = $jc(_$hash, startDate.hashCode);
    _$hash = $jc(_$hash, endDate.hashCode);
    _$hash = $jc(_$hash, jobCode.hashCode);
    _$hash = $jc(_$hash, hostCompany.hashCode);
    _$hash = $jc(_$hash, skillJobCode1.hashCode);
    _$hash = $jc(_$hash, skillJobCode2.hashCode);
    _$hash = $jc(_$hash, skillJobCode3.hashCode);
    _$hash = $jc(_$hash, hostAgent.hashCode);
    _$hash = $jc(_$hash, companyUserId.hashCode);
    _$hash = $jc(_$hash, totalApply.hashCode);
    _$hash = $jc(_$hash, jobName.hashCode);
    _$hash = $jc(_$hash, applied.hashCode);
    _$hash = $jc(_$hash, recruitImagePath.hashCode);
    _$hash = $jc(_$hash, isExpired.hashCode);
    _$hash = $jc(_$hash, placeCode1.hashCode);
    _$hash = $jc(_$hash, placeCode2.hashCode);
    _$hash = $jc(_$hash, placeCode3.hashCode);
    _$hash = $jc(_$hash, totalRequest.hashCode);
    _$hash = $jc(_$hash, totalUnderSelection.hashCode);
    _$hash = $jc(_$hash, displayFlag.hashCode);
    _$hash = $jc(_$hash, remoteCode.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'RecruitUploadedSerializers')
          ..add('recruitId', recruitId)
          ..add('title', title)
          ..add('catchCopy', catchCopy)
          ..add('payrollPriceFrom', payrollPriceFrom)
          ..add('payrollPriceTo', payrollPriceTo)
          ..add('payrollCode', payrollCode)
          ..add('startDate', startDate)
          ..add('endDate', endDate)
          ..add('jobCode', jobCode)
          ..add('hostCompany', hostCompany)
          ..add('skillJobCode1', skillJobCode1)
          ..add('skillJobCode2', skillJobCode2)
          ..add('skillJobCode3', skillJobCode3)
          ..add('hostAgent', hostAgent)
          ..add('companyUserId', companyUserId)
          ..add('totalApply', totalApply)
          ..add('jobName', jobName)
          ..add('applied', applied)
          ..add('recruitImagePath', recruitImagePath)
          ..add('isExpired', isExpired)
          ..add('placeCode1', placeCode1)
          ..add('placeCode2', placeCode2)
          ..add('placeCode3', placeCode3)
          ..add('totalRequest', totalRequest)
          ..add('totalUnderSelection', totalUnderSelection)
          ..add('displayFlag', displayFlag)
          ..add('remoteCode', remoteCode))
        .toString();
  }
}

class RecruitUploadedSerializersBuilder
    implements
        Builder<RecruitUploadedSerializers, RecruitUploadedSerializersBuilder> {
  _$RecruitUploadedSerializers? _$v;

  int? _recruitId;
  int? get recruitId => _$this._recruitId;
  set recruitId(int? recruitId) => _$this._recruitId = recruitId;

  String? _title;
  String? get title => _$this._title;
  set title(String? title) => _$this._title = title;

  String? _catchCopy;
  String? get catchCopy => _$this._catchCopy;
  set catchCopy(String? catchCopy) => _$this._catchCopy = catchCopy;

  String? _payrollPriceFrom;
  String? get payrollPriceFrom => _$this._payrollPriceFrom;
  set payrollPriceFrom(String? payrollPriceFrom) =>
      _$this._payrollPriceFrom = payrollPriceFrom;

  String? _payrollPriceTo;
  String? get payrollPriceTo => _$this._payrollPriceTo;
  set payrollPriceTo(String? payrollPriceTo) =>
      _$this._payrollPriceTo = payrollPriceTo;

  String? _payrollCode;
  String? get payrollCode => _$this._payrollCode;
  set payrollCode(String? payrollCode) => _$this._payrollCode = payrollCode;

  DateTime? _startDate;
  DateTime? get startDate => _$this._startDate;
  set startDate(DateTime? startDate) => _$this._startDate = startDate;

  DateTime? _endDate;
  DateTime? get endDate => _$this._endDate;
  set endDate(DateTime? endDate) => _$this._endDate = endDate;

  String? _jobCode;
  String? get jobCode => _$this._jobCode;
  set jobCode(String? jobCode) => _$this._jobCode = jobCode;

  int? _hostCompany;
  int? get hostCompany => _$this._hostCompany;
  set hostCompany(int? hostCompany) => _$this._hostCompany = hostCompany;

  String? _skillJobCode1;
  String? get skillJobCode1 => _$this._skillJobCode1;
  set skillJobCode1(String? skillJobCode1) =>
      _$this._skillJobCode1 = skillJobCode1;

  String? _skillJobCode2;
  String? get skillJobCode2 => _$this._skillJobCode2;
  set skillJobCode2(String? skillJobCode2) =>
      _$this._skillJobCode2 = skillJobCode2;

  String? _skillJobCode3;
  String? get skillJobCode3 => _$this._skillJobCode3;
  set skillJobCode3(String? skillJobCode3) =>
      _$this._skillJobCode3 = skillJobCode3;

  int? _hostAgent;
  int? get hostAgent => _$this._hostAgent;
  set hostAgent(int? hostAgent) => _$this._hostAgent = hostAgent;

  int? _companyUserId;
  int? get companyUserId => _$this._companyUserId;
  set companyUserId(int? companyUserId) =>
      _$this._companyUserId = companyUserId;

  int? _totalApply;
  int? get totalApply => _$this._totalApply;
  set totalApply(int? totalApply) => _$this._totalApply = totalApply;

  String? _jobName;
  String? get jobName => _$this._jobName;
  set jobName(String? jobName) => _$this._jobName = jobName;

  bool? _applied;
  bool? get applied => _$this._applied;
  set applied(bool? applied) => _$this._applied = applied;

  String? _recruitImagePath;
  String? get recruitImagePath => _$this._recruitImagePath;
  set recruitImagePath(String? recruitImagePath) =>
      _$this._recruitImagePath = recruitImagePath;

  bool? _isExpired;
  bool? get isExpired => _$this._isExpired;
  set isExpired(bool? isExpired) => _$this._isExpired = isExpired;

  String? _placeCode1;
  String? get placeCode1 => _$this._placeCode1;
  set placeCode1(String? placeCode1) => _$this._placeCode1 = placeCode1;

  String? _placeCode2;
  String? get placeCode2 => _$this._placeCode2;
  set placeCode2(String? placeCode2) => _$this._placeCode2 = placeCode2;

  String? _placeCode3;
  String? get placeCode3 => _$this._placeCode3;
  set placeCode3(String? placeCode3) => _$this._placeCode3 = placeCode3;

  int? _totalRequest;
  int? get totalRequest => _$this._totalRequest;
  set totalRequest(int? totalRequest) => _$this._totalRequest = totalRequest;

  int? _totalUnderSelection;
  int? get totalUnderSelection => _$this._totalUnderSelection;
  set totalUnderSelection(int? totalUnderSelection) =>
      _$this._totalUnderSelection = totalUnderSelection;

  int? _displayFlag;
  int? get displayFlag => _$this._displayFlag;
  set displayFlag(int? displayFlag) => _$this._displayFlag = displayFlag;

  String? _remoteCode;
  String? get remoteCode => _$this._remoteCode;
  set remoteCode(String? remoteCode) => _$this._remoteCode = remoteCode;

  RecruitUploadedSerializersBuilder() {
    RecruitUploadedSerializers._defaults(this);
  }

  RecruitUploadedSerializersBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _recruitId = $v.recruitId;
      _title = $v.title;
      _catchCopy = $v.catchCopy;
      _payrollPriceFrom = $v.payrollPriceFrom;
      _payrollPriceTo = $v.payrollPriceTo;
      _payrollCode = $v.payrollCode;
      _startDate = $v.startDate;
      _endDate = $v.endDate;
      _jobCode = $v.jobCode;
      _hostCompany = $v.hostCompany;
      _skillJobCode1 = $v.skillJobCode1;
      _skillJobCode2 = $v.skillJobCode2;
      _skillJobCode3 = $v.skillJobCode3;
      _hostAgent = $v.hostAgent;
      _companyUserId = $v.companyUserId;
      _totalApply = $v.totalApply;
      _jobName = $v.jobName;
      _applied = $v.applied;
      _recruitImagePath = $v.recruitImagePath;
      _isExpired = $v.isExpired;
      _placeCode1 = $v.placeCode1;
      _placeCode2 = $v.placeCode2;
      _placeCode3 = $v.placeCode3;
      _totalRequest = $v.totalRequest;
      _totalUnderSelection = $v.totalUnderSelection;
      _displayFlag = $v.displayFlag;
      _remoteCode = $v.remoteCode;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(RecruitUploadedSerializers other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$RecruitUploadedSerializers;
  }

  @override
  void update(void Function(RecruitUploadedSerializersBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  RecruitUploadedSerializers build() => _build();

  _$RecruitUploadedSerializers _build() {
    final _$result = _$v ??
        new _$RecruitUploadedSerializers._(
          recruitId: recruitId,
          title: title,
          catchCopy: catchCopy,
          payrollPriceFrom: payrollPriceFrom,
          payrollPriceTo: payrollPriceTo,
          payrollCode: payrollCode,
          startDate: startDate,
          endDate: endDate,
          jobCode: jobCode,
          hostCompany: BuiltValueNullFieldError.checkNotNull(
              hostCompany, r'RecruitUploadedSerializers', 'hostCompany'),
          skillJobCode1: skillJobCode1,
          skillJobCode2: skillJobCode2,
          skillJobCode3: skillJobCode3,
          hostAgent: BuiltValueNullFieldError.checkNotNull(
              hostAgent, r'RecruitUploadedSerializers', 'hostAgent'),
          companyUserId: companyUserId,
          totalApply: totalApply,
          jobName: jobName,
          applied: applied,
          recruitImagePath: recruitImagePath,
          isExpired: isExpired,
          placeCode1: placeCode1,
          placeCode2: placeCode2,
          placeCode3: placeCode3,
          totalRequest: totalRequest,
          totalUnderSelection: totalUnderSelection,
          displayFlag: displayFlag,
          remoteCode: remoteCode,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
