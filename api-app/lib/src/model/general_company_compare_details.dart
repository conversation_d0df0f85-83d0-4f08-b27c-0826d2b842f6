//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'general_company_compare_details.g.dart';

/// GeneralCompanyCompareDetails
///
/// Properties:
/// * [description] 
/// * [point] 
/// * [salaryMatched] 
/// * [workLocationMatched] 
/// * [employCodeMatched] 
/// * [jobSkillsMatched] 
@BuiltValue()
abstract class GeneralCompanyCompareDetails implements Built<GeneralCompanyCompareDetails, GeneralCompanyCompareDetailsBuilder> {
  @BuiltValueField(wireName: r'description')
  String? get description;

  @BuiltValueField(wireName: r'point')
  int? get point;

  @BuiltValueField(wireName: r'salary_matched')
  bool? get salaryMatched;

  @BuiltValueField(wireName: r'work_location_matched')
  bool? get workLocationMatched;

  @BuiltValueField(wireName: r'employ_code_matched')
  bool? get employCodeMatched;

  @BuiltValueField(wireName: r'job_skills_matched')
  bool? get jobSkillsMatched;

  GeneralCompanyCompareDetails._();

  factory GeneralCompanyCompareDetails([void updates(GeneralCompanyCompareDetailsBuilder b)]) = _$GeneralCompanyCompareDetails;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(GeneralCompanyCompareDetailsBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<GeneralCompanyCompareDetails> get serializer => _$GeneralCompanyCompareDetailsSerializer();
}

class _$GeneralCompanyCompareDetailsSerializer implements PrimitiveSerializer<GeneralCompanyCompareDetails> {
  @override
  final Iterable<Type> types = const [GeneralCompanyCompareDetails, _$GeneralCompanyCompareDetails];

  @override
  final String wireName = r'GeneralCompanyCompareDetails';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    GeneralCompanyCompareDetails object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.description != null) {
      yield r'description';
      yield serializers.serialize(
        object.description,
        specifiedType: const FullType(String),
      );
    }
    if (object.point != null) {
      yield r'point';
      yield serializers.serialize(
        object.point,
        specifiedType: const FullType(int),
      );
    }
    if (object.salaryMatched != null) {
      yield r'salary_matched';
      yield serializers.serialize(
        object.salaryMatched,
        specifiedType: const FullType(bool),
      );
    }
    if (object.workLocationMatched != null) {
      yield r'work_location_matched';
      yield serializers.serialize(
        object.workLocationMatched,
        specifiedType: const FullType(bool),
      );
    }
    if (object.employCodeMatched != null) {
      yield r'employ_code_matched';
      yield serializers.serialize(
        object.employCodeMatched,
        specifiedType: const FullType(bool),
      );
    }
    if (object.jobSkillsMatched != null) {
      yield r'job_skills_matched';
      yield serializers.serialize(
        object.jobSkillsMatched,
        specifiedType: const FullType(bool),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    GeneralCompanyCompareDetails object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required GeneralCompanyCompareDetailsBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'description':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.description = valueDes;
          break;
        case r'point':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.point = valueDes;
          break;
        case r'salary_matched':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(bool),
          ) as bool;
          result.salaryMatched = valueDes;
          break;
        case r'work_location_matched':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(bool),
          ) as bool;
          result.workLocationMatched = valueDes;
          break;
        case r'employ_code_matched':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(bool),
          ) as bool;
          result.employCodeMatched = valueDes;
          break;
        case r'job_skills_matched':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(bool),
          ) as bool;
          result.jobSkillsMatched = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  GeneralCompanyCompareDetails deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = GeneralCompanyCompareDetailsBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

