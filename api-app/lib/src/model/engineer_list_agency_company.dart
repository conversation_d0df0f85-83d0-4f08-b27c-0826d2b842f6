//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'engineer_list_agency_company.g.dart';

/// EngineerListAgencyCompany
///
/// Properties:
/// * [companyId] 
/// * [userType] 
/// * [name] 
/// * [tel] 
/// * [logoImagePath] 
/// * [contactMail] 
/// * [agentFee] 
/// * [agentFeeCurrCode] 
@BuiltValue()
abstract class EngineerListAgencyCompany implements Built<EngineerListAgencyCompany, EngineerListAgencyCompanyBuilder> {
  @BuiltValueField(wireName: r'company_id')
  int? get companyId;

  @BuiltValueField(wireName: r'user_type')
  int get userType;

  @BuiltValueField(wireName: r'name')
  String? get name;

  @BuiltValueField(wireName: r'tel')
  String? get tel;

  @BuiltValueField(wireName: r'logo_image_path')
  String? get logoImagePath;

  @BuiltValueField(wireName: r'contact_mail')
  String? get contactMail;

  @BuiltValueField(wireName: r'agent_fee')
  double? get agentFee;

  @BuiltValueField(wireName: r'agent_fee_curr_code')
  String? get agentFeeCurrCode;

  EngineerListAgencyCompany._();

  factory EngineerListAgencyCompany([void updates(EngineerListAgencyCompanyBuilder b)]) = _$EngineerListAgencyCompany;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(EngineerListAgencyCompanyBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<EngineerListAgencyCompany> get serializer => _$EngineerListAgencyCompanySerializer();
}

class _$EngineerListAgencyCompanySerializer implements PrimitiveSerializer<EngineerListAgencyCompany> {
  @override
  final Iterable<Type> types = const [EngineerListAgencyCompany, _$EngineerListAgencyCompany];

  @override
  final String wireName = r'EngineerListAgencyCompany';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    EngineerListAgencyCompany object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.companyId != null) {
      yield r'company_id';
      yield serializers.serialize(
        object.companyId,
        specifiedType: const FullType(int),
      );
    }
    yield r'user_type';
    yield serializers.serialize(
      object.userType,
      specifiedType: const FullType(int),
    );
    if (object.name != null) {
      yield r'name';
      yield serializers.serialize(
        object.name,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.tel != null) {
      yield r'tel';
      yield serializers.serialize(
        object.tel,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.logoImagePath != null) {
      yield r'logo_image_path';
      yield serializers.serialize(
        object.logoImagePath,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.contactMail != null) {
      yield r'contact_mail';
      yield serializers.serialize(
        object.contactMail,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.agentFee != null) {
      yield r'agent_fee';
      yield serializers.serialize(
        object.agentFee,
        specifiedType: const FullType.nullable(double),
      );
    }
    if (object.agentFeeCurrCode != null) {
      yield r'agent_fee_curr_code';
      yield serializers.serialize(
        object.agentFeeCurrCode,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    EngineerListAgencyCompany object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required EngineerListAgencyCompanyBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'company_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.companyId = valueDes;
          break;
        case r'user_type':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.userType = valueDes;
          break;
        case r'name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.name = valueDes;
          break;
        case r'tel':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.tel = valueDes;
          break;
        case r'logo_image_path':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.logoImagePath = valueDes;
          break;
        case r'contact_mail':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.contactMail = valueDes;
          break;
        case r'agent_fee':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(double),
          ) as double?;
          if (valueDes == null) continue;
          result.agentFee = valueDes;
          break;
        case r'agent_fee_curr_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.agentFeeCurrCode = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  EngineerListAgencyCompany deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = EngineerListAgencyCompanyBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

