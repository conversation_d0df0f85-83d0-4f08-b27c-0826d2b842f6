// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_company_list_group_chat_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GeneralCompanyListGroupChatResponseModel
    extends GeneralCompanyListGroupChatResponseModel {
  @override
  final String? message;
  @override
  final ListGroupChat data;
  @override
  final BuiltList<ErrorDetail>? errors;

  factory _$GeneralCompanyListGroupChatResponseModel(
          [void Function(GeneralCompanyListGroupChatResponseModelBuilder)?
              updates]) =>
      (new GeneralCompanyListGroupChatResponseModelBuilder()..update(updates))
          ._build();

  _$GeneralCompanyListGroupChatResponseModel._(
      {this.message, required this.data, this.errors})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'GeneralCompanyListGroupChatResponseModel', 'data');
  }

  @override
  GeneralCompanyListGroupChatResponseModel rebuild(
          void Function(GeneralCompanyListGroupChatResponseModelBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GeneralCompanyListGroupChatResponseModelBuilder toBuilder() =>
      new GeneralCompanyListGroupChatResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GeneralCompanyListGroupChatResponseModel &&
        message == other.message &&
        data == other.data &&
        errors == other.errors;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'GeneralCompanyListGroupChatResponseModel')
          ..add('message', message)
          ..add('data', data)
          ..add('errors', errors))
        .toString();
  }
}

class GeneralCompanyListGroupChatResponseModelBuilder
    implements
        Builder<GeneralCompanyListGroupChatResponseModel,
            GeneralCompanyListGroupChatResponseModelBuilder> {
  _$GeneralCompanyListGroupChatResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  ListGroupChatBuilder? _data;
  ListGroupChatBuilder get data => _$this._data ??= new ListGroupChatBuilder();
  set data(ListGroupChatBuilder? data) => _$this._data = data;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  GeneralCompanyListGroupChatResponseModelBuilder() {
    GeneralCompanyListGroupChatResponseModel._defaults(this);
  }

  GeneralCompanyListGroupChatResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _data = $v.data.toBuilder();
      _errors = $v.errors?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GeneralCompanyListGroupChatResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GeneralCompanyListGroupChatResponseModel;
  }

  @override
  void update(
      void Function(GeneralCompanyListGroupChatResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GeneralCompanyListGroupChatResponseModel build() => _build();

  _$GeneralCompanyListGroupChatResponseModel _build() {
    _$GeneralCompanyListGroupChatResponseModel _$result;
    try {
      _$result = _$v ??
          new _$GeneralCompanyListGroupChatResponseModel._(
            message: message,
            data: data.build(),
            errors: _errors?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        data.build();
        _$failedField = 'errors';
        _errors?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'GeneralCompanyListGroupChatResponseModel',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
