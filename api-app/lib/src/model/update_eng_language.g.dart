// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_eng_language.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UpdateEngLanguage extends UpdateEngLanguage {
  @override
  final int? languageLevelType;
  @override
  final String? languageCode;

  factory _$UpdateEngLanguage(
          [void Function(UpdateEngLanguageBuilder)? updates]) =>
      (new UpdateEngLanguageBuilder()..update(updates))._build();

  _$UpdateEngLanguage._({this.languageLevelType, this.languageCode})
      : super._();

  @override
  UpdateEngLanguage rebuild(void Function(UpdateEngLanguageBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UpdateEngLanguageBuilder toBuilder() =>
      new UpdateEngLanguageBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UpdateEngLanguage &&
        languageLevelType == other.languageLevelType &&
        languageCode == other.languageCode;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, languageLevelType.hashCode);
    _$hash = $jc(_$hash, languageCode.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UpdateEngLanguage')
          ..add('languageLevelType', languageLevelType)
          ..add('languageCode', languageCode))
        .toString();
  }
}

class UpdateEngLanguageBuilder
    implements Builder<UpdateEngLanguage, UpdateEngLanguageBuilder> {
  _$UpdateEngLanguage? _$v;

  int? _languageLevelType;
  int? get languageLevelType => _$this._languageLevelType;
  set languageLevelType(int? languageLevelType) =>
      _$this._languageLevelType = languageLevelType;

  String? _languageCode;
  String? get languageCode => _$this._languageCode;
  set languageCode(String? languageCode) => _$this._languageCode = languageCode;

  UpdateEngLanguageBuilder() {
    UpdateEngLanguage._defaults(this);
  }

  UpdateEngLanguageBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _languageLevelType = $v.languageLevelType;
      _languageCode = $v.languageCode;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UpdateEngLanguage other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$UpdateEngLanguage;
  }

  @override
  void update(void Function(UpdateEngLanguageBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UpdateEngLanguage build() => _build();

  _$UpdateEngLanguage _build() {
    final _$result = _$v ??
        new _$UpdateEngLanguage._(
          languageLevelType: languageLevelType,
          languageCode: languageCode,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
