//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/general_company_applied_engineers.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'paging_general_company_applied_engineers_response_model.g.dart';

/// PagingGeneralCompanyAppliedEngineersResponseModel
///
/// Properties:
/// * [count] 
/// * [next] 
/// * [previous] 
/// * [results] 
@BuiltValue()
abstract class PagingGeneralCompanyAppliedEngineersResponseModel implements Built<PagingGeneralCompanyAppliedEngineersResponseModel, PagingGeneralCompanyAppliedEngineersResponseModelBuilder> {
  @BuiltValueField(wireName: r'count')
  int get count;

  @BuiltValueField(wireName: r'next')
  String? get next;

  @BuiltValueField(wireName: r'previous')
  String? get previous;

  @BuiltValueField(wireName: r'results')
  BuiltList<GeneralCompanyAppliedEngineers> get results;

  PagingGeneralCompanyAppliedEngineersResponseModel._();

  factory PagingGeneralCompanyAppliedEngineersResponseModel([void updates(PagingGeneralCompanyAppliedEngineersResponseModelBuilder b)]) = _$PagingGeneralCompanyAppliedEngineersResponseModel;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(PagingGeneralCompanyAppliedEngineersResponseModelBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<PagingGeneralCompanyAppliedEngineersResponseModel> get serializer => _$PagingGeneralCompanyAppliedEngineersResponseModelSerializer();
}

class _$PagingGeneralCompanyAppliedEngineersResponseModelSerializer implements PrimitiveSerializer<PagingGeneralCompanyAppliedEngineersResponseModel> {
  @override
  final Iterable<Type> types = const [PagingGeneralCompanyAppliedEngineersResponseModel, _$PagingGeneralCompanyAppliedEngineersResponseModel];

  @override
  final String wireName = r'PagingGeneralCompanyAppliedEngineersResponseModel';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    PagingGeneralCompanyAppliedEngineersResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'count';
    yield serializers.serialize(
      object.count,
      specifiedType: const FullType(int),
    );
    yield r'next';
    yield object.next == null ? null : serializers.serialize(
      object.next,
      specifiedType: const FullType.nullable(String),
    );
    yield r'previous';
    yield object.previous == null ? null : serializers.serialize(
      object.previous,
      specifiedType: const FullType.nullable(String),
    );
    yield r'results';
    yield serializers.serialize(
      object.results,
      specifiedType: const FullType(BuiltList, [FullType(GeneralCompanyAppliedEngineers)]),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    PagingGeneralCompanyAppliedEngineersResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required PagingGeneralCompanyAppliedEngineersResponseModelBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'count':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.count = valueDes;
          break;
        case r'next':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.next = valueDes;
          break;
        case r'previous':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.previous = valueDes;
          break;
        case r'results':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(GeneralCompanyAppliedEngineers)]),
          ) as BuiltList<GeneralCompanyAppliedEngineers>;
          result.results.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  PagingGeneralCompanyAppliedEngineersResponseModel deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = PagingGeneralCompanyAppliedEngineersResponseModelBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

