//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'host_company_subscribe_support_company.g.dart';

/// HostCompanySubscribeSupportCompany
///
/// Properties:
/// * [supportCompanyId] 
@BuiltValue()
abstract class HostCompanySubscribeSupportCompany implements Built<HostCompanySubscribeSupportCompany, HostCompanySubscribeSupportCompanyBuilder> {
  @BuiltValueField(wireName: r'support_company_id')
  String get supportCompanyId;

  HostCompanySubscribeSupportCompany._();

  factory HostCompanySubscribeSupportCompany([void updates(HostCompanySubscribeSupportCompanyBuilder b)]) = _$HostCompanySubscribeSupportCompany;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(HostCompanySubscribeSupportCompanyBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<HostCompanySubscribeSupportCompany> get serializer => _$HostCompanySubscribeSupportCompanySerializer();
}

class _$HostCompanySubscribeSupportCompanySerializer implements PrimitiveSerializer<HostCompanySubscribeSupportCompany> {
  @override
  final Iterable<Type> types = const [HostCompanySubscribeSupportCompany, _$HostCompanySubscribeSupportCompany];

  @override
  final String wireName = r'HostCompanySubscribeSupportCompany';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    HostCompanySubscribeSupportCompany object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'support_company_id';
    yield serializers.serialize(
      object.supportCompanyId,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    HostCompanySubscribeSupportCompany object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required HostCompanySubscribeSupportCompanyBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'support_company_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.supportCompanyId = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  HostCompanySubscribeSupportCompany deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = HostCompanySubscribeSupportCompanyBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

