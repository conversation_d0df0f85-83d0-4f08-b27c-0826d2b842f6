// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'evaluation_method.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$EvaluationMethod extends EvaluationMethod {
  @override
  final String textEn;
  @override
  final String textVi;
  @override
  final String text;

  factory _$EvaluationMethod(
          [void Function(EvaluationMethodBuilder)? updates]) =>
      (new EvaluationMethodBuilder()..update(updates))._build();

  _$EvaluationMethod._(
      {required this.textEn, required this.textVi, required this.text})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        textEn, r'EvaluationMethod', 'textEn');
    BuiltValueNullFieldError.checkNotNull(
        textVi, r'EvaluationMethod', 'textVi');
    BuiltValueNullFieldError.checkNotNull(text, r'EvaluationMethod', 'text');
  }

  @override
  EvaluationMethod rebuild(void Function(EvaluationMethodBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  EvaluationMethodBuilder toBuilder() =>
      new EvaluationMethodBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is EvaluationMethod &&
        textEn == other.textEn &&
        textVi == other.textVi &&
        text == other.text;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, textEn.hashCode);
    _$hash = $jc(_$hash, textVi.hashCode);
    _$hash = $jc(_$hash, text.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'EvaluationMethod')
          ..add('textEn', textEn)
          ..add('textVi', textVi)
          ..add('text', text))
        .toString();
  }
}

class EvaluationMethodBuilder
    implements Builder<EvaluationMethod, EvaluationMethodBuilder> {
  _$EvaluationMethod? _$v;

  String? _textEn;
  String? get textEn => _$this._textEn;
  set textEn(String? textEn) => _$this._textEn = textEn;

  String? _textVi;
  String? get textVi => _$this._textVi;
  set textVi(String? textVi) => _$this._textVi = textVi;

  String? _text;
  String? get text => _$this._text;
  set text(String? text) => _$this._text = text;

  EvaluationMethodBuilder() {
    EvaluationMethod._defaults(this);
  }

  EvaluationMethodBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _textEn = $v.textEn;
      _textVi = $v.textVi;
      _text = $v.text;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(EvaluationMethod other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$EvaluationMethod;
  }

  @override
  void update(void Function(EvaluationMethodBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  EvaluationMethod build() => _build();

  _$EvaluationMethod _build() {
    final _$result = _$v ??
        new _$EvaluationMethod._(
          textEn: BuiltValueNullFieldError.checkNotNull(
              textEn, r'EvaluationMethod', 'textEn'),
          textVi: BuiltValueNullFieldError.checkNotNull(
              textVi, r'EvaluationMethod', 'textVi'),
          text: BuiltValueNullFieldError.checkNotNull(
              text, r'EvaluationMethod', 'text'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
