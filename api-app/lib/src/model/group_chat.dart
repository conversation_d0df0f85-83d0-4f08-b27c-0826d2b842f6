//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/user_chat.dart';
import 'package:openapi/src/model/general_company_user_applied_company.dart';
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/chat.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'group_chat.g.dart';

/// GroupChat
///
/// Properties:
/// * [id] 
/// * [group] 
/// * [chat] 
/// * [updated] 
/// * [created] 
/// * [lastedMessage] 
/// * [users] 
/// * [progressCode] 
/// * [engineer] 
@BuiltValue()
abstract class GroupChat implements Built<GroupChat, GroupChatBuilder> {
  @BuiltValueField(wireName: r'id')
  int get id;

  @BuiltValueField(wireName: r'group')
  int get group;

  @BuiltValueField(wireName: r'chat')
  int? get chat;

  @BuiltValueField(wireName: r'updated')
  DateTime? get updated;

  @BuiltValueField(wireName: r'created')
  DateTime? get created;

  @BuiltValueField(wireName: r'lasted_message')
  Chat? get lastedMessage;

  @BuiltValueField(wireName: r'users')
  BuiltList<UserChat>? get users;

  @BuiltValueField(wireName: r'progress_code')
  int? get progressCode;

  @BuiltValueField(wireName: r'engineer')
  GeneralCompanyUserAppliedCompany? get engineer;

  GroupChat._();

  factory GroupChat([void updates(GroupChatBuilder b)]) = _$GroupChat;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(GroupChatBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<GroupChat> get serializer => _$GroupChatSerializer();
}

class _$GroupChatSerializer implements PrimitiveSerializer<GroupChat> {
  @override
  final Iterable<Type> types = const [GroupChat, _$GroupChat];

  @override
  final String wireName = r'GroupChat';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    GroupChat object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'id';
    yield serializers.serialize(
      object.id,
      specifiedType: const FullType(int),
    );
    yield r'group';
    yield serializers.serialize(
      object.group,
      specifiedType: const FullType(int),
    );
    if (object.chat != null) {
      yield r'chat';
      yield serializers.serialize(
        object.chat,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.updated != null) {
      yield r'updated';
      yield serializers.serialize(
        object.updated,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.created != null) {
      yield r'created';
      yield serializers.serialize(
        object.created,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.lastedMessage != null) {
      yield r'lasted_message';
      yield serializers.serialize(
        object.lastedMessage,
        specifiedType: const FullType(Chat),
      );
    }
    if (object.users != null) {
      yield r'users';
      yield serializers.serialize(
        object.users,
        specifiedType: const FullType(BuiltList, [FullType(UserChat)]),
      );
    }
    if (object.progressCode != null) {
      yield r'progress_code';
      yield serializers.serialize(
        object.progressCode,
        specifiedType: const FullType(int),
      );
    }
    if (object.engineer != null) {
      yield r'engineer';
      yield serializers.serialize(
        object.engineer,
        specifiedType: const FullType(GeneralCompanyUserAppliedCompany),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    GroupChat object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required GroupChatBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.id = valueDes;
          break;
        case r'group':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.group = valueDes;
          break;
        case r'chat':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.chat = valueDes;
          break;
        case r'updated':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.updated = valueDes;
          break;
        case r'created':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.created = valueDes;
          break;
        case r'lasted_message':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(Chat),
          ) as Chat;
          result.lastedMessage.replace(valueDes);
          break;
        case r'users':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(UserChat)]),
          ) as BuiltList<UserChat>;
          result.users.replace(valueDes);
          break;
        case r'progress_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.progressCode = valueDes;
          break;
        case r'engineer':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(GeneralCompanyUserAppliedCompany),
          ) as GeneralCompanyUserAppliedCompany;
          result.engineer.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  GroupChat deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = GroupChatBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

