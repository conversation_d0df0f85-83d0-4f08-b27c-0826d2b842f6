// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'assessment_questions_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AssessmentQuestionsResponseModel
    extends AssessmentQuestionsResponseModel {
  @override
  final String? message;
  @override
  final BuiltList<AssessmentQuestionData> data;
  @override
  final BuiltList<String>? errors;

  factory _$AssessmentQuestionsResponseModel(
          [void Function(AssessmentQuestionsResponseModelBuilder)? updates]) =>
      (new AssessmentQuestionsResponseModelBuilder()..update(updates))._build();

  _$AssessmentQuestionsResponseModel._(
      {this.message, required this.data, this.errors})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'AssessmentQuestionsResponseModel', 'data');
  }

  @override
  AssessmentQuestionsResponseModel rebuild(
          void Function(AssessmentQuestionsResponseModelBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AssessmentQuestionsResponseModelBuilder toBuilder() =>
      new AssessmentQuestionsResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AssessmentQuestionsResponseModel &&
        message == other.message &&
        data == other.data &&
        errors == other.errors;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AssessmentQuestionsResponseModel')
          ..add('message', message)
          ..add('data', data)
          ..add('errors', errors))
        .toString();
  }
}

class AssessmentQuestionsResponseModelBuilder
    implements
        Builder<AssessmentQuestionsResponseModel,
            AssessmentQuestionsResponseModelBuilder> {
  _$AssessmentQuestionsResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  ListBuilder<AssessmentQuestionData>? _data;
  ListBuilder<AssessmentQuestionData> get data =>
      _$this._data ??= new ListBuilder<AssessmentQuestionData>();
  set data(ListBuilder<AssessmentQuestionData>? data) => _$this._data = data;

  ListBuilder<String>? _errors;
  ListBuilder<String> get errors =>
      _$this._errors ??= new ListBuilder<String>();
  set errors(ListBuilder<String>? errors) => _$this._errors = errors;

  AssessmentQuestionsResponseModelBuilder() {
    AssessmentQuestionsResponseModel._defaults(this);
  }

  AssessmentQuestionsResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _data = $v.data.toBuilder();
      _errors = $v.errors?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AssessmentQuestionsResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$AssessmentQuestionsResponseModel;
  }

  @override
  void update(void Function(AssessmentQuestionsResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AssessmentQuestionsResponseModel build() => _build();

  _$AssessmentQuestionsResponseModel _build() {
    _$AssessmentQuestionsResponseModel _$result;
    try {
      _$result = _$v ??
          new _$AssessmentQuestionsResponseModel._(
            message: message,
            data: data.build(),
            errors: _errors?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        data.build();
        _$failedField = 'errors';
        _errors?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'AssessmentQuestionsResponseModel', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
