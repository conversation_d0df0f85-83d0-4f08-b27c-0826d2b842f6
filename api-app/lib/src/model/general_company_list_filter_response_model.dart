//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/general_company_filter.dart';
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/error_detail.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'general_company_list_filter_response_model.g.dart';

/// GeneralCompanyListFilterResponseModel
///
/// Properties:
/// * [message] 
/// * [data] 
/// * [errors] 
@BuiltValue()
abstract class GeneralCompanyListFilterResponseModel implements Built<GeneralCompanyListFilterResponseModel, GeneralCompanyListFilterResponseModelBuilder> {
  @BuiltValueField(wireName: r'message')
  String? get message;

  @BuiltValueField(wireName: r'data')
  BuiltList<GeneralCompanyFilter> get data;

  @BuiltValueField(wireName: r'errors')
  BuiltList<ErrorDetail>? get errors;

  GeneralCompanyListFilterResponseModel._();

  factory GeneralCompanyListFilterResponseModel([void updates(GeneralCompanyListFilterResponseModelBuilder b)]) = _$GeneralCompanyListFilterResponseModel;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(GeneralCompanyListFilterResponseModelBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<GeneralCompanyListFilterResponseModel> get serializer => _$GeneralCompanyListFilterResponseModelSerializer();
}

class _$GeneralCompanyListFilterResponseModelSerializer implements PrimitiveSerializer<GeneralCompanyListFilterResponseModel> {
  @override
  final Iterable<Type> types = const [GeneralCompanyListFilterResponseModel, _$GeneralCompanyListFilterResponseModel];

  @override
  final String wireName = r'GeneralCompanyListFilterResponseModel';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    GeneralCompanyListFilterResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'message';
    yield object.message == null ? null : serializers.serialize(
      object.message,
      specifiedType: const FullType.nullable(String),
    );
    yield r'data';
    yield serializers.serialize(
      object.data,
      specifiedType: const FullType(BuiltList, [FullType(GeneralCompanyFilter)]),
    );
    yield r'errors';
    yield object.errors == null ? null : serializers.serialize(
      object.errors,
      specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    GeneralCompanyListFilterResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required GeneralCompanyListFilterResponseModelBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'message':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.message = valueDes;
          break;
        case r'data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(GeneralCompanyFilter)]),
          ) as BuiltList<GeneralCompanyFilter>;
          result.data.replace(valueDes);
          break;
        case r'errors':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
          ) as BuiltList<ErrorDetail>?;
          if (valueDes == null) continue;
          result.errors.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  GeneralCompanyListFilterResponseModel deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = GeneralCompanyListFilterResponseModelBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

