//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'host_company_request_interview.g.dart';

/// HostCompanyRequestInterview
///
/// Properties:
/// * [recruitId] 
/// * [userId] 
/// * [message] 
@BuiltValue()
abstract class HostCompanyRequestInterview implements Built<HostCompanyRequestInterview, HostCompanyRequestInterviewBuilder> {
  @BuiltValueField(wireName: r'recruit_id')
  int get recruitId;

  @BuiltValueField(wireName: r'user_id')
  int get userId;

  @BuiltValueField(wireName: r'message')
  String get message;

  HostCompanyRequestInterview._();

  factory HostCompanyRequestInterview([void updates(HostCompanyRequestInterviewBuilder b)]) = _$HostCompanyRequestInterview;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(HostCompanyRequestInterviewBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<HostCompanyRequestInterview> get serializer => _$HostCompanyRequestInterviewSerializer();
}

class _$HostCompanyRequestInterviewSerializer implements PrimitiveSerializer<HostCompanyRequestInterview> {
  @override
  final Iterable<Type> types = const [HostCompanyRequestInterview, _$HostCompanyRequestInterview];

  @override
  final String wireName = r'HostCompanyRequestInterview';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    HostCompanyRequestInterview object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'recruit_id';
    yield serializers.serialize(
      object.recruitId,
      specifiedType: const FullType(int),
    );
    yield r'user_id';
    yield serializers.serialize(
      object.userId,
      specifiedType: const FullType(int),
    );
    yield r'message';
    yield serializers.serialize(
      object.message,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    HostCompanyRequestInterview object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required HostCompanyRequestInterviewBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'recruit_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.recruitId = valueDes;
          break;
        case r'user_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.userId = valueDes;
          break;
        case r'message':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.message = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  HostCompanyRequestInterview deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = HostCompanyRequestInterviewBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

