// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'delete_engineers.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$DeleteEngineers extends DeleteEngineers {
  @override
  final BuiltList<int> engineerIds;

  factory _$DeleteEngineers([void Function(DeleteEngineersBuilder)? updates]) =>
      (new DeleteEngineersBuilder()..update(updates))._build();

  _$DeleteEngineers._({required this.engineerIds}) : super._() {
    BuiltValueNullFieldError.checkNotNull(
        engineerIds, r'DeleteEngineers', 'engineerIds');
  }

  @override
  DeleteEngineers rebuild(void Function(DeleteEngineersBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  DeleteEngineersBuilder toBuilder() =>
      new DeleteEngineersBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is DeleteEngineers && engineerIds == other.engineerIds;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, engineerIds.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'DeleteEngineers')
          ..add('engineerIds', engineerIds))
        .toString();
  }
}

class DeleteEngineersBuilder
    implements Builder<DeleteEngineers, DeleteEngineersBuilder> {
  _$DeleteEngineers? _$v;

  ListBuilder<int>? _engineerIds;
  ListBuilder<int> get engineerIds =>
      _$this._engineerIds ??= new ListBuilder<int>();
  set engineerIds(ListBuilder<int>? engineerIds) =>
      _$this._engineerIds = engineerIds;

  DeleteEngineersBuilder() {
    DeleteEngineers._defaults(this);
  }

  DeleteEngineersBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _engineerIds = $v.engineerIds.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(DeleteEngineers other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$DeleteEngineers;
  }

  @override
  void update(void Function(DeleteEngineersBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  DeleteEngineers build() => _build();

  _$DeleteEngineers _build() {
    _$DeleteEngineers _$result;
    try {
      _$result = _$v ??
          new _$DeleteEngineers._(
            engineerIds: engineerIds.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'engineerIds';
        engineerIds.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'DeleteEngineers', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
