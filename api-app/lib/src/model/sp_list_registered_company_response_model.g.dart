// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sp_list_registered_company_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$SPListRegisteredCompanyResponseModel
    extends SPListRegisteredCompanyResponseModel {
  @override
  final String? message;
  @override
  final BuiltList<ErrorDetail>? errors;
  @override
  final BuiltList<SPCompanyRegistered> data;

  factory _$SPListRegisteredCompanyResponseModel(
          [void Function(SPListRegisteredCompanyResponseModelBuilder)?
              updates]) =>
      (new SPListRegisteredCompanyResponseModelBuilder()..update(updates))
          ._build();

  _$SPListRegisteredCompanyResponseModel._(
      {this.message, this.errors, required this.data})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'SPListRegisteredCompanyResponseModel', 'data');
  }

  @override
  SPListRegisteredCompanyResponseModel rebuild(
          void Function(SPListRegisteredCompanyResponseModelBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SPListRegisteredCompanyResponseModelBuilder toBuilder() =>
      new SPListRegisteredCompanyResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SPListRegisteredCompanyResponseModel &&
        message == other.message &&
        errors == other.errors &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SPListRegisteredCompanyResponseModel')
          ..add('message', message)
          ..add('errors', errors)
          ..add('data', data))
        .toString();
  }
}

class SPListRegisteredCompanyResponseModelBuilder
    implements
        Builder<SPListRegisteredCompanyResponseModel,
            SPListRegisteredCompanyResponseModelBuilder> {
  _$SPListRegisteredCompanyResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  ListBuilder<SPCompanyRegistered>? _data;
  ListBuilder<SPCompanyRegistered> get data =>
      _$this._data ??= new ListBuilder<SPCompanyRegistered>();
  set data(ListBuilder<SPCompanyRegistered>? data) => _$this._data = data;

  SPListRegisteredCompanyResponseModelBuilder() {
    SPListRegisteredCompanyResponseModel._defaults(this);
  }

  SPListRegisteredCompanyResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _errors = $v.errors?.toBuilder();
      _data = $v.data.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SPListRegisteredCompanyResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$SPListRegisteredCompanyResponseModel;
  }

  @override
  void update(
      void Function(SPListRegisteredCompanyResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SPListRegisteredCompanyResponseModel build() => _build();

  _$SPListRegisteredCompanyResponseModel _build() {
    _$SPListRegisteredCompanyResponseModel _$result;
    try {
      _$result = _$v ??
          new _$SPListRegisteredCompanyResponseModel._(
            message: message,
            errors: _errors?.build(),
            data: data.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'errors';
        _errors?.build();
        _$failedField = 'data';
        data.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'SPListRegisteredCompanyResponseModel',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
