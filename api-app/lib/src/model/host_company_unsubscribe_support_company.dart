//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'host_company_unsubscribe_support_company.g.dart';

/// HostCompanyUnsubscribeSupportCompany
///
/// Properties:
/// * [supportCompanyId] 
@BuiltValue()
abstract class HostCompanyUnsubscribeSupportCompany implements Built<HostCompanyUnsubscribeSupportCompany, HostCompanyUnsubscribeSupportCompanyBuilder> {
  @BuiltValueField(wireName: r'support_company_id')
  String get supportCompanyId;

  HostCompanyUnsubscribeSupportCompany._();

  factory HostCompanyUnsubscribeSupportCompany([void updates(HostCompanyUnsubscribeSupportCompanyBuilder b)]) = _$HostCompanyUnsubscribeSupportCompany;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(HostCompanyUnsubscribeSupportCompanyBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<HostCompanyUnsubscribeSupportCompany> get serializer => _$HostCompanyUnsubscribeSupportCompanySerializer();
}

class _$HostCompanyUnsubscribeSupportCompanySerializer implements PrimitiveSerializer<HostCompanyUnsubscribeSupportCompany> {
  @override
  final Iterable<Type> types = const [HostCompanyUnsubscribeSupportCompany, _$HostCompanyUnsubscribeSupportCompany];

  @override
  final String wireName = r'HostCompanyUnsubscribeSupportCompany';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    HostCompanyUnsubscribeSupportCompany object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'support_company_id';
    yield serializers.serialize(
      object.supportCompanyId,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    HostCompanyUnsubscribeSupportCompany object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required HostCompanyUnsubscribeSupportCompanyBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'support_company_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.supportCompanyId = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  HostCompanyUnsubscribeSupportCompany deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = HostCompanyUnsubscribeSupportCompanyBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

