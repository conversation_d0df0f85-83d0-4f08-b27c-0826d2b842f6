// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'self_assessment_data.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$SelfAssessmentData extends SelfAssessmentData {
  @override
  final SelfAssessmentSheet? remoteWorkSkills;
  @override
  final SelfAssessmentSheet? communicationSkillsSelfAssessmentSheet;
  @override
  final SelfAssessmentSheet? globalResponsivenessSkills;
  @override
  final SelfAssessmentSheet? reportingConsultationSkillsSelfEvaluation;
  @override
  final SelfAssessmentSheet? projectManagementSkillsSelfEvaluation;
  @override
  final BuiltList<EvaluationMethod> evaluationMethods;
  @override
  final BuiltList<Questions> questions;

  factory _$SelfAssessmentData(
          [void Function(SelfAssessmentDataBuilder)? updates]) =>
      (new SelfAssessmentDataBuilder()..update(updates))._build();

  _$SelfAssessmentData._(
      {this.remoteWorkSkills,
      this.communicationSkillsSelfAssessmentSheet,
      this.globalResponsivenessSkills,
      this.reportingConsultationSkillsSelfEvaluation,
      this.projectManagementSkillsSelfEvaluation,
      required this.evaluationMethods,
      required this.questions})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        evaluationMethods, r'SelfAssessmentData', 'evaluationMethods');
    BuiltValueNullFieldError.checkNotNull(
        questions, r'SelfAssessmentData', 'questions');
  }

  @override
  SelfAssessmentData rebuild(
          void Function(SelfAssessmentDataBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SelfAssessmentDataBuilder toBuilder() =>
      new SelfAssessmentDataBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SelfAssessmentData &&
        remoteWorkSkills == other.remoteWorkSkills &&
        communicationSkillsSelfAssessmentSheet ==
            other.communicationSkillsSelfAssessmentSheet &&
        globalResponsivenessSkills == other.globalResponsivenessSkills &&
        reportingConsultationSkillsSelfEvaluation ==
            other.reportingConsultationSkillsSelfEvaluation &&
        projectManagementSkillsSelfEvaluation ==
            other.projectManagementSkillsSelfEvaluation &&
        evaluationMethods == other.evaluationMethods &&
        questions == other.questions;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, remoteWorkSkills.hashCode);
    _$hash = $jc(_$hash, communicationSkillsSelfAssessmentSheet.hashCode);
    _$hash = $jc(_$hash, globalResponsivenessSkills.hashCode);
    _$hash = $jc(_$hash, reportingConsultationSkillsSelfEvaluation.hashCode);
    _$hash = $jc(_$hash, projectManagementSkillsSelfEvaluation.hashCode);
    _$hash = $jc(_$hash, evaluationMethods.hashCode);
    _$hash = $jc(_$hash, questions.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SelfAssessmentData')
          ..add('remoteWorkSkills', remoteWorkSkills)
          ..add('communicationSkillsSelfAssessmentSheet',
              communicationSkillsSelfAssessmentSheet)
          ..add('globalResponsivenessSkills', globalResponsivenessSkills)
          ..add('reportingConsultationSkillsSelfEvaluation',
              reportingConsultationSkillsSelfEvaluation)
          ..add('projectManagementSkillsSelfEvaluation',
              projectManagementSkillsSelfEvaluation)
          ..add('evaluationMethods', evaluationMethods)
          ..add('questions', questions))
        .toString();
  }
}

class SelfAssessmentDataBuilder
    implements Builder<SelfAssessmentData, SelfAssessmentDataBuilder> {
  _$SelfAssessmentData? _$v;

  SelfAssessmentSheetBuilder? _remoteWorkSkills;
  SelfAssessmentSheetBuilder get remoteWorkSkills =>
      _$this._remoteWorkSkills ??= new SelfAssessmentSheetBuilder();
  set remoteWorkSkills(SelfAssessmentSheetBuilder? remoteWorkSkills) =>
      _$this._remoteWorkSkills = remoteWorkSkills;

  SelfAssessmentSheetBuilder? _communicationSkillsSelfAssessmentSheet;
  SelfAssessmentSheetBuilder get communicationSkillsSelfAssessmentSheet =>
      _$this._communicationSkillsSelfAssessmentSheet ??=
          new SelfAssessmentSheetBuilder();
  set communicationSkillsSelfAssessmentSheet(
          SelfAssessmentSheetBuilder? communicationSkillsSelfAssessmentSheet) =>
      _$this._communicationSkillsSelfAssessmentSheet =
          communicationSkillsSelfAssessmentSheet;

  SelfAssessmentSheetBuilder? _globalResponsivenessSkills;
  SelfAssessmentSheetBuilder get globalResponsivenessSkills =>
      _$this._globalResponsivenessSkills ??= new SelfAssessmentSheetBuilder();
  set globalResponsivenessSkills(
          SelfAssessmentSheetBuilder? globalResponsivenessSkills) =>
      _$this._globalResponsivenessSkills = globalResponsivenessSkills;

  SelfAssessmentSheetBuilder? _reportingConsultationSkillsSelfEvaluation;
  SelfAssessmentSheetBuilder get reportingConsultationSkillsSelfEvaluation =>
      _$this._reportingConsultationSkillsSelfEvaluation ??=
          new SelfAssessmentSheetBuilder();
  set reportingConsultationSkillsSelfEvaluation(
          SelfAssessmentSheetBuilder?
              reportingConsultationSkillsSelfEvaluation) =>
      _$this._reportingConsultationSkillsSelfEvaluation =
          reportingConsultationSkillsSelfEvaluation;

  SelfAssessmentSheetBuilder? _projectManagementSkillsSelfEvaluation;
  SelfAssessmentSheetBuilder get projectManagementSkillsSelfEvaluation =>
      _$this._projectManagementSkillsSelfEvaluation ??=
          new SelfAssessmentSheetBuilder();
  set projectManagementSkillsSelfEvaluation(
          SelfAssessmentSheetBuilder? projectManagementSkillsSelfEvaluation) =>
      _$this._projectManagementSkillsSelfEvaluation =
          projectManagementSkillsSelfEvaluation;

  ListBuilder<EvaluationMethod>? _evaluationMethods;
  ListBuilder<EvaluationMethod> get evaluationMethods =>
      _$this._evaluationMethods ??= new ListBuilder<EvaluationMethod>();
  set evaluationMethods(ListBuilder<EvaluationMethod>? evaluationMethods) =>
      _$this._evaluationMethods = evaluationMethods;

  ListBuilder<Questions>? _questions;
  ListBuilder<Questions> get questions =>
      _$this._questions ??= new ListBuilder<Questions>();
  set questions(ListBuilder<Questions>? questions) =>
      _$this._questions = questions;

  SelfAssessmentDataBuilder() {
    SelfAssessmentData._defaults(this);
  }

  SelfAssessmentDataBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _remoteWorkSkills = $v.remoteWorkSkills?.toBuilder();
      _communicationSkillsSelfAssessmentSheet =
          $v.communicationSkillsSelfAssessmentSheet?.toBuilder();
      _globalResponsivenessSkills = $v.globalResponsivenessSkills?.toBuilder();
      _reportingConsultationSkillsSelfEvaluation =
          $v.reportingConsultationSkillsSelfEvaluation?.toBuilder();
      _projectManagementSkillsSelfEvaluation =
          $v.projectManagementSkillsSelfEvaluation?.toBuilder();
      _evaluationMethods = $v.evaluationMethods.toBuilder();
      _questions = $v.questions.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SelfAssessmentData other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$SelfAssessmentData;
  }

  @override
  void update(void Function(SelfAssessmentDataBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SelfAssessmentData build() => _build();

  _$SelfAssessmentData _build() {
    _$SelfAssessmentData _$result;
    try {
      _$result = _$v ??
          new _$SelfAssessmentData._(
            remoteWorkSkills: _remoteWorkSkills?.build(),
            communicationSkillsSelfAssessmentSheet:
                _communicationSkillsSelfAssessmentSheet?.build(),
            globalResponsivenessSkills: _globalResponsivenessSkills?.build(),
            reportingConsultationSkillsSelfEvaluation:
                _reportingConsultationSkillsSelfEvaluation?.build(),
            projectManagementSkillsSelfEvaluation:
                _projectManagementSkillsSelfEvaluation?.build(),
            evaluationMethods: evaluationMethods.build(),
            questions: questions.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'remoteWorkSkills';
        _remoteWorkSkills?.build();
        _$failedField = 'communicationSkillsSelfAssessmentSheet';
        _communicationSkillsSelfAssessmentSheet?.build();
        _$failedField = 'globalResponsivenessSkills';
        _globalResponsivenessSkills?.build();
        _$failedField = 'reportingConsultationSkillsSelfEvaluation';
        _reportingConsultationSkillsSelfEvaluation?.build();
        _$failedField = 'projectManagementSkillsSelfEvaluation';
        _projectManagementSkillsSelfEvaluation?.build();
        _$failedField = 'evaluationMethods';
        evaluationMethods.build();
        _$failedField = 'questions';
        questions.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'SelfAssessmentData', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
