// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'send_whatsapp_code.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$SendWhatsappCode extends SendWhatsappCode {
  @override
  final String phoneNumber;

  factory _$SendWhatsappCode(
          [void Function(SendWhatsappCodeBuilder)? updates]) =>
      (new SendWhatsappCodeBuilder()..update(updates))._build();

  _$SendWhatsappCode._({required this.phoneNumber}) : super._() {
    BuiltValueNullFieldError.checkNotNull(
        phoneNumber, r'SendWhatsappCode', 'phoneNumber');
  }

  @override
  SendWhatsappCode rebuild(void Function(SendWhatsappCodeBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SendWhatsappCodeBuilder toBuilder() =>
      new SendWhatsappCodeBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SendWhatsappCode && phoneNumber == other.phoneNumber;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, phoneNumber.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SendWhatsappCode')
          ..add('phoneNumber', phoneNumber))
        .toString();
  }
}

class SendWhatsappCodeBuilder
    implements Builder<SendWhatsappCode, SendWhatsappCodeBuilder> {
  _$SendWhatsappCode? _$v;

  String? _phoneNumber;
  String? get phoneNumber => _$this._phoneNumber;
  set phoneNumber(String? phoneNumber) => _$this._phoneNumber = phoneNumber;

  SendWhatsappCodeBuilder() {
    SendWhatsappCode._defaults(this);
  }

  SendWhatsappCodeBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _phoneNumber = $v.phoneNumber;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SendWhatsappCode other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$SendWhatsappCode;
  }

  @override
  void update(void Function(SendWhatsappCodeBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SendWhatsappCode build() => _build();

  _$SendWhatsappCode _build() {
    final _$result = _$v ??
        new _$SendWhatsappCode._(
          phoneNumber: BuiltValueNullFieldError.checkNotNull(
              phoneNumber, r'SendWhatsappCode', 'phoneNumber'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
