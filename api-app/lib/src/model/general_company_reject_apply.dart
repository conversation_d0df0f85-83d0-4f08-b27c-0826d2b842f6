//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'general_company_reject_apply.g.dart';

/// GeneralCompanyRejectApply
///
/// Properties:
/// * [applyId] 
/// * [hostCompanyId] 
@BuiltValue()
abstract class GeneralCompanyRejectApply implements Built<GeneralCompanyRejectApply, GeneralCompanyRejectApplyBuilder> {
  @BuiltValueField(wireName: r'apply_id')
  int get applyId;

  @BuiltValueField(wireName: r'host_company_id')
  int? get hostCompanyId;

  GeneralCompanyRejectApply._();

  factory GeneralCompanyRejectApply([void updates(GeneralCompanyRejectApplyBuilder b)]) = _$GeneralCompanyRejectApply;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(GeneralCompanyRejectApplyBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<GeneralCompanyRejectApply> get serializer => _$GeneralCompanyRejectApplySerializer();
}

class _$GeneralCompanyRejectApplySerializer implements PrimitiveSerializer<GeneralCompanyRejectApply> {
  @override
  final Iterable<Type> types = const [GeneralCompanyRejectApply, _$GeneralCompanyRejectApply];

  @override
  final String wireName = r'GeneralCompanyRejectApply';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    GeneralCompanyRejectApply object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'apply_id';
    yield serializers.serialize(
      object.applyId,
      specifiedType: const FullType(int),
    );
    if (object.hostCompanyId != null) {
      yield r'host_company_id';
      yield serializers.serialize(
        object.hostCompanyId,
        specifiedType: const FullType.nullable(int),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    GeneralCompanyRejectApply object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required GeneralCompanyRejectApplyBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'apply_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.applyId = valueDes;
          break;
        case r'host_company_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.hostCompanyId = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  GeneralCompanyRejectApply deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = GeneralCompanyRejectApplyBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

