//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/update_hope_job_skill.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'update_eng_hope.g.dart';

/// UpdateEngHope
///
/// Properties:
/// * [placeCode1] 
/// * [placeCode2] 
/// * [placeCode3] 
/// * [employCode] 
/// * [payrollPrice] 
/// * [payrollCode] 
/// * [jobSkills] 
/// * [remoteCode] 
@BuiltValue()
abstract class UpdateEngHope implements Built<UpdateEngHope, UpdateEngHopeBuilder> {
  @BuiltValueField(wireName: r'place_code1')
  String? get placeCode1;

  @BuiltValueField(wireName: r'place_code2')
  String? get placeCode2;

  @BuiltValueField(wireName: r'place_code3')
  String? get placeCode3;

  @BuiltValueField(wireName: r'employ_code')
  String? get employCode;

  @BuiltValueField(wireName: r'payroll_price')
  String? get payrollPrice;

  @BuiltValueField(wireName: r'payroll_code')
  String? get payrollCode;

  @BuiltValueField(wireName: r'job_skills')
  BuiltList<UpdateHopeJobSkill> get jobSkills;

  @BuiltValueField(wireName: r'remote_code')
  String? get remoteCode;

  UpdateEngHope._();

  factory UpdateEngHope([void updates(UpdateEngHopeBuilder b)]) = _$UpdateEngHope;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(UpdateEngHopeBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<UpdateEngHope> get serializer => _$UpdateEngHopeSerializer();
}

class _$UpdateEngHopeSerializer implements PrimitiveSerializer<UpdateEngHope> {
  @override
  final Iterable<Type> types = const [UpdateEngHope, _$UpdateEngHope];

  @override
  final String wireName = r'UpdateEngHope';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    UpdateEngHope object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.placeCode1 != null) {
      yield r'place_code1';
      yield serializers.serialize(
        object.placeCode1,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.placeCode2 != null) {
      yield r'place_code2';
      yield serializers.serialize(
        object.placeCode2,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.placeCode3 != null) {
      yield r'place_code3';
      yield serializers.serialize(
        object.placeCode3,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.employCode != null) {
      yield r'employ_code';
      yield serializers.serialize(
        object.employCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    yield r'payroll_price';
    yield object.payrollPrice == null ? null : serializers.serialize(
      object.payrollPrice,
      specifiedType: const FullType.nullable(String),
    );
    if (object.payrollCode != null) {
      yield r'payroll_code';
      yield serializers.serialize(
        object.payrollCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    yield r'job_skills';
    yield serializers.serialize(
      object.jobSkills,
      specifiedType: const FullType(BuiltList, [FullType(UpdateHopeJobSkill)]),
    );
    if (object.remoteCode != null) {
      yield r'remote_code';
      yield serializers.serialize(
        object.remoteCode,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    UpdateEngHope object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required UpdateEngHopeBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'place_code1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.placeCode1 = valueDes;
          break;
        case r'place_code2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.placeCode2 = valueDes;
          break;
        case r'place_code3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.placeCode3 = valueDes;
          break;
        case r'employ_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.employCode = valueDes;
          break;
        case r'payroll_price':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.payrollPrice = valueDes;
          break;
        case r'payroll_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.payrollCode = valueDes;
          break;
        case r'job_skills':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(UpdateHopeJobSkill)]),
          ) as BuiltList<UpdateHopeJobSkill>;
          result.jobSkills.replace(valueDes);
          break;
        case r'remote_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.remoteCode = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  UpdateEngHope deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = UpdateEngHopeBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

