// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'set_read_notify_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$SetReadNotifyResponseModel extends SetReadNotifyResponseModel {
  @override
  final String? message;
  @override
  final bool data;
  @override
  final BuiltList<String>? errors;

  factory _$SetReadNotifyResponseModel(
          [void Function(SetReadNotifyResponseModelBuilder)? updates]) =>
      (new SetReadNotifyResponseModelBuilder()..update(updates))._build();

  _$SetReadNotifyResponseModel._(
      {this.message, required this.data, this.errors})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'SetReadNotifyResponseModel', 'data');
  }

  @override
  SetReadNotifyResponseModel rebuild(
          void Function(SetReadNotifyResponseModelBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SetReadNotifyResponseModelBuilder toBuilder() =>
      new SetReadNotifyResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SetReadNotifyResponseModel &&
        message == other.message &&
        data == other.data &&
        errors == other.errors;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SetReadNotifyResponseModel')
          ..add('message', message)
          ..add('data', data)
          ..add('errors', errors))
        .toString();
  }
}

class SetReadNotifyResponseModelBuilder
    implements
        Builder<SetReadNotifyResponseModel, SetReadNotifyResponseModelBuilder> {
  _$SetReadNotifyResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  bool? _data;
  bool? get data => _$this._data;
  set data(bool? data) => _$this._data = data;

  ListBuilder<String>? _errors;
  ListBuilder<String> get errors =>
      _$this._errors ??= new ListBuilder<String>();
  set errors(ListBuilder<String>? errors) => _$this._errors = errors;

  SetReadNotifyResponseModelBuilder() {
    SetReadNotifyResponseModel._defaults(this);
  }

  SetReadNotifyResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _data = $v.data;
      _errors = $v.errors?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SetReadNotifyResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$SetReadNotifyResponseModel;
  }

  @override
  void update(void Function(SetReadNotifyResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SetReadNotifyResponseModel build() => _build();

  _$SetReadNotifyResponseModel _build() {
    _$SetReadNotifyResponseModel _$result;
    try {
      _$result = _$v ??
          new _$SetReadNotifyResponseModel._(
            message: message,
            data: BuiltValueNullFieldError.checkNotNull(
                data, r'SetReadNotifyResponseModel', 'data'),
            errors: _errors?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'errors';
        _errors?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'SetReadNotifyResponseModel', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
