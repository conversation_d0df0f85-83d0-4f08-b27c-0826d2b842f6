//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'support_language200_response.g.dart';

/// SupportLanguage200Response
///
/// Properties:
/// * [support] 
@BuiltValue()
abstract class SupportLanguage200Response implements Built<SupportLanguage200Response, SupportLanguage200ResponseBuilder> {
  @BuiltValueField(wireName: r'support')
  BuiltList<String>? get support;

  SupportLanguage200Response._();

  factory SupportLanguage200Response([void updates(SupportLanguage200ResponseBuilder b)]) = _$SupportLanguage200Response;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(SupportLanguage200ResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<SupportLanguage200Response> get serializer => _$SupportLanguage200ResponseSerializer();
}

class _$SupportLanguage200ResponseSerializer implements PrimitiveSerializer<SupportLanguage200Response> {
  @override
  final Iterable<Type> types = const [SupportLanguage200Response, _$SupportLanguage200Response];

  @override
  final String wireName = r'SupportLanguage200Response';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    SupportLanguage200Response object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.support != null) {
      yield r'support';
      yield serializers.serialize(
        object.support,
        specifiedType: const FullType(BuiltList, [FullType(String)]),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    SupportLanguage200Response object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required SupportLanguage200ResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'support':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(String)]),
          ) as BuiltList<String>;
          result.support.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  SupportLanguage200Response deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = SupportLanguage200ResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

