// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'engineer_self_assesment_details.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$EngineerSelfAssesmentDetails extends EngineerSelfAssesmentDetails {
  @override
  final int? remoteExpYears;
  @override
  final String? remoteJobDescription;
  @override
  final int? totalRemoteSkill;
  @override
  final int? totalGlobalSkill;
  @override
  final int? totalCommunicationSkill;
  @override
  final int? totalReportSkill;
  @override
  final int? totalManagementSkill;
  @override
  final int? socialStyle;
  @override
  final int? durabilityScore;
  @override
  final int? globalWorkExp;

  factory _$EngineerSelfAssesmentDetails(
          [void Function(EngineerSelfAssesmentDetailsBuilder)? updates]) =>
      (new EngineerSelfAssesmentDetailsBuilder()..update(updates))._build();

  _$EngineerSelfAssesmentDetails._(
      {this.remoteExpYears,
      this.remoteJobDescription,
      this.totalRemoteSkill,
      this.totalGlobalSkill,
      this.totalCommunicationSkill,
      this.totalReportSkill,
      this.totalManagementSkill,
      this.socialStyle,
      this.durabilityScore,
      this.globalWorkExp})
      : super._();

  @override
  EngineerSelfAssesmentDetails rebuild(
          void Function(EngineerSelfAssesmentDetailsBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  EngineerSelfAssesmentDetailsBuilder toBuilder() =>
      new EngineerSelfAssesmentDetailsBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is EngineerSelfAssesmentDetails &&
        remoteExpYears == other.remoteExpYears &&
        remoteJobDescription == other.remoteJobDescription &&
        totalRemoteSkill == other.totalRemoteSkill &&
        totalGlobalSkill == other.totalGlobalSkill &&
        totalCommunicationSkill == other.totalCommunicationSkill &&
        totalReportSkill == other.totalReportSkill &&
        totalManagementSkill == other.totalManagementSkill &&
        socialStyle == other.socialStyle &&
        durabilityScore == other.durabilityScore &&
        globalWorkExp == other.globalWorkExp;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, remoteExpYears.hashCode);
    _$hash = $jc(_$hash, remoteJobDescription.hashCode);
    _$hash = $jc(_$hash, totalRemoteSkill.hashCode);
    _$hash = $jc(_$hash, totalGlobalSkill.hashCode);
    _$hash = $jc(_$hash, totalCommunicationSkill.hashCode);
    _$hash = $jc(_$hash, totalReportSkill.hashCode);
    _$hash = $jc(_$hash, totalManagementSkill.hashCode);
    _$hash = $jc(_$hash, socialStyle.hashCode);
    _$hash = $jc(_$hash, durabilityScore.hashCode);
    _$hash = $jc(_$hash, globalWorkExp.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'EngineerSelfAssesmentDetails')
          ..add('remoteExpYears', remoteExpYears)
          ..add('remoteJobDescription', remoteJobDescription)
          ..add('totalRemoteSkill', totalRemoteSkill)
          ..add('totalGlobalSkill', totalGlobalSkill)
          ..add('totalCommunicationSkill', totalCommunicationSkill)
          ..add('totalReportSkill', totalReportSkill)
          ..add('totalManagementSkill', totalManagementSkill)
          ..add('socialStyle', socialStyle)
          ..add('durabilityScore', durabilityScore)
          ..add('globalWorkExp', globalWorkExp))
        .toString();
  }
}

class EngineerSelfAssesmentDetailsBuilder
    implements
        Builder<EngineerSelfAssesmentDetails,
            EngineerSelfAssesmentDetailsBuilder> {
  _$EngineerSelfAssesmentDetails? _$v;

  int? _remoteExpYears;
  int? get remoteExpYears => _$this._remoteExpYears;
  set remoteExpYears(int? remoteExpYears) =>
      _$this._remoteExpYears = remoteExpYears;

  String? _remoteJobDescription;
  String? get remoteJobDescription => _$this._remoteJobDescription;
  set remoteJobDescription(String? remoteJobDescription) =>
      _$this._remoteJobDescription = remoteJobDescription;

  int? _totalRemoteSkill;
  int? get totalRemoteSkill => _$this._totalRemoteSkill;
  set totalRemoteSkill(int? totalRemoteSkill) =>
      _$this._totalRemoteSkill = totalRemoteSkill;

  int? _totalGlobalSkill;
  int? get totalGlobalSkill => _$this._totalGlobalSkill;
  set totalGlobalSkill(int? totalGlobalSkill) =>
      _$this._totalGlobalSkill = totalGlobalSkill;

  int? _totalCommunicationSkill;
  int? get totalCommunicationSkill => _$this._totalCommunicationSkill;
  set totalCommunicationSkill(int? totalCommunicationSkill) =>
      _$this._totalCommunicationSkill = totalCommunicationSkill;

  int? _totalReportSkill;
  int? get totalReportSkill => _$this._totalReportSkill;
  set totalReportSkill(int? totalReportSkill) =>
      _$this._totalReportSkill = totalReportSkill;

  int? _totalManagementSkill;
  int? get totalManagementSkill => _$this._totalManagementSkill;
  set totalManagementSkill(int? totalManagementSkill) =>
      _$this._totalManagementSkill = totalManagementSkill;

  int? _socialStyle;
  int? get socialStyle => _$this._socialStyle;
  set socialStyle(int? socialStyle) => _$this._socialStyle = socialStyle;

  int? _durabilityScore;
  int? get durabilityScore => _$this._durabilityScore;
  set durabilityScore(int? durabilityScore) =>
      _$this._durabilityScore = durabilityScore;

  int? _globalWorkExp;
  int? get globalWorkExp => _$this._globalWorkExp;
  set globalWorkExp(int? globalWorkExp) =>
      _$this._globalWorkExp = globalWorkExp;

  EngineerSelfAssesmentDetailsBuilder() {
    EngineerSelfAssesmentDetails._defaults(this);
  }

  EngineerSelfAssesmentDetailsBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _remoteExpYears = $v.remoteExpYears;
      _remoteJobDescription = $v.remoteJobDescription;
      _totalRemoteSkill = $v.totalRemoteSkill;
      _totalGlobalSkill = $v.totalGlobalSkill;
      _totalCommunicationSkill = $v.totalCommunicationSkill;
      _totalReportSkill = $v.totalReportSkill;
      _totalManagementSkill = $v.totalManagementSkill;
      _socialStyle = $v.socialStyle;
      _durabilityScore = $v.durabilityScore;
      _globalWorkExp = $v.globalWorkExp;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(EngineerSelfAssesmentDetails other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$EngineerSelfAssesmentDetails;
  }

  @override
  void update(void Function(EngineerSelfAssesmentDetailsBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  EngineerSelfAssesmentDetails build() => _build();

  _$EngineerSelfAssesmentDetails _build() {
    final _$result = _$v ??
        new _$EngineerSelfAssesmentDetails._(
          remoteExpYears: remoteExpYears,
          remoteJobDescription: remoteJobDescription,
          totalRemoteSkill: totalRemoteSkill,
          totalGlobalSkill: totalGlobalSkill,
          totalCommunicationSkill: totalCommunicationSkill,
          totalReportSkill: totalReportSkill,
          totalManagementSkill: totalManagementSkill,
          socialStyle: socialStyle,
          durabilityScore: durabilityScore,
          globalWorkExp: globalWorkExp,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
