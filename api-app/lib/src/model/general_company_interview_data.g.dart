// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_company_interview_data.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GeneralCompanyInterviewData extends GeneralCompanyInterviewData {
  @override
  final int? applyId;
  @override
  final DateTime? interviewDatetime;
  @override
  final int? hostCompany;
  @override
  final int? hostAgent;
  @override
  final String? engineerFirstName;
  @override
  final String? engineerLastName;
  @override
  final String? engineerNickName;

  factory _$GeneralCompanyInterviewData(
          [void Function(GeneralCompanyInterviewDataBuilder)? updates]) =>
      (new GeneralCompanyInterviewDataBuilder()..update(updates))._build();

  _$GeneralCompanyInterviewData._(
      {this.applyId,
      this.interviewDatetime,
      this.hostCompany,
      this.hostAgent,
      this.engineerFirstName,
      this.engineerLastName,
      this.engineerNickName})
      : super._();

  @override
  GeneralCompanyInterviewData rebuild(
          void Function(GeneralCompanyInterviewDataBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GeneralCompanyInterviewDataBuilder toBuilder() =>
      new GeneralCompanyInterviewDataBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GeneralCompanyInterviewData &&
        applyId == other.applyId &&
        interviewDatetime == other.interviewDatetime &&
        hostCompany == other.hostCompany &&
        hostAgent == other.hostAgent &&
        engineerFirstName == other.engineerFirstName &&
        engineerLastName == other.engineerLastName &&
        engineerNickName == other.engineerNickName;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, applyId.hashCode);
    _$hash = $jc(_$hash, interviewDatetime.hashCode);
    _$hash = $jc(_$hash, hostCompany.hashCode);
    _$hash = $jc(_$hash, hostAgent.hashCode);
    _$hash = $jc(_$hash, engineerFirstName.hashCode);
    _$hash = $jc(_$hash, engineerLastName.hashCode);
    _$hash = $jc(_$hash, engineerNickName.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GeneralCompanyInterviewData')
          ..add('applyId', applyId)
          ..add('interviewDatetime', interviewDatetime)
          ..add('hostCompany', hostCompany)
          ..add('hostAgent', hostAgent)
          ..add('engineerFirstName', engineerFirstName)
          ..add('engineerLastName', engineerLastName)
          ..add('engineerNickName', engineerNickName))
        .toString();
  }
}

class GeneralCompanyInterviewDataBuilder
    implements
        Builder<GeneralCompanyInterviewData,
            GeneralCompanyInterviewDataBuilder> {
  _$GeneralCompanyInterviewData? _$v;

  int? _applyId;
  int? get applyId => _$this._applyId;
  set applyId(int? applyId) => _$this._applyId = applyId;

  DateTime? _interviewDatetime;
  DateTime? get interviewDatetime => _$this._interviewDatetime;
  set interviewDatetime(DateTime? interviewDatetime) =>
      _$this._interviewDatetime = interviewDatetime;

  int? _hostCompany;
  int? get hostCompany => _$this._hostCompany;
  set hostCompany(int? hostCompany) => _$this._hostCompany = hostCompany;

  int? _hostAgent;
  int? get hostAgent => _$this._hostAgent;
  set hostAgent(int? hostAgent) => _$this._hostAgent = hostAgent;

  String? _engineerFirstName;
  String? get engineerFirstName => _$this._engineerFirstName;
  set engineerFirstName(String? engineerFirstName) =>
      _$this._engineerFirstName = engineerFirstName;

  String? _engineerLastName;
  String? get engineerLastName => _$this._engineerLastName;
  set engineerLastName(String? engineerLastName) =>
      _$this._engineerLastName = engineerLastName;

  String? _engineerNickName;
  String? get engineerNickName => _$this._engineerNickName;
  set engineerNickName(String? engineerNickName) =>
      _$this._engineerNickName = engineerNickName;

  GeneralCompanyInterviewDataBuilder() {
    GeneralCompanyInterviewData._defaults(this);
  }

  GeneralCompanyInterviewDataBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _applyId = $v.applyId;
      _interviewDatetime = $v.interviewDatetime;
      _hostCompany = $v.hostCompany;
      _hostAgent = $v.hostAgent;
      _engineerFirstName = $v.engineerFirstName;
      _engineerLastName = $v.engineerLastName;
      _engineerNickName = $v.engineerNickName;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GeneralCompanyInterviewData other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GeneralCompanyInterviewData;
  }

  @override
  void update(void Function(GeneralCompanyInterviewDataBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GeneralCompanyInterviewData build() => _build();

  _$GeneralCompanyInterviewData _build() {
    final _$result = _$v ??
        new _$GeneralCompanyInterviewData._(
          applyId: applyId,
          interviewDatetime: interviewDatetime,
          hostCompany: hostCompany,
          hostAgent: hostAgent,
          engineerFirstName: engineerFirstName,
          engineerLastName: engineerLastName,
          engineerNickName: engineerNickName,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
