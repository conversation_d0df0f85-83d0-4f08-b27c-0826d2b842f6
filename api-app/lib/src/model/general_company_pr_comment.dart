//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'general_company_pr_comment.g.dart';

/// GeneralCompanyPRComment
///
/// Properties:
/// * [introductionPr] 
/// * [createDatetime] 
/// * [mapId] 
/// * [agencyCompany] 
/// * [agencyAgent] 
/// * [updateDatetime] 
@BuiltValue()
abstract class GeneralCompanyPRComment implements Built<GeneralCompanyPRComment, GeneralCompanyPRCommentBuilder> {
  @BuiltValueField(wireName: r'introduction_pr')
  String? get introductionPr;

  @BuiltValueField(wireName: r'create_datetime')
  DateTime? get createDatetime;

  @BuiltValueField(wireName: r'map_id')
  int? get mapId;

  @BuiltValueField(wireName: r'agency_company')
  int get agencyCompany;

  @BuiltValueField(wireName: r'agency_agent')
  int? get agencyAgent;

  @BuiltValueField(wireName: r'update_datetime')
  DateTime? get updateDatetime;

  GeneralCompanyPRComment._();

  factory GeneralCompanyPRComment([void updates(GeneralCompanyPRCommentBuilder b)]) = _$GeneralCompanyPRComment;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(GeneralCompanyPRCommentBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<GeneralCompanyPRComment> get serializer => _$GeneralCompanyPRCommentSerializer();
}

class _$GeneralCompanyPRCommentSerializer implements PrimitiveSerializer<GeneralCompanyPRComment> {
  @override
  final Iterable<Type> types = const [GeneralCompanyPRComment, _$GeneralCompanyPRComment];

  @override
  final String wireName = r'GeneralCompanyPRComment';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    GeneralCompanyPRComment object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.introductionPr != null) {
      yield r'introduction_pr';
      yield serializers.serialize(
        object.introductionPr,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.createDatetime != null) {
      yield r'create_datetime';
      yield serializers.serialize(
        object.createDatetime,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.mapId != null) {
      yield r'map_id';
      yield serializers.serialize(
        object.mapId,
        specifiedType: const FullType(int),
      );
    }
    yield r'agency_company';
    yield serializers.serialize(
      object.agencyCompany,
      specifiedType: const FullType(int),
    );
    if (object.agencyAgent != null) {
      yield r'agency_agent';
      yield serializers.serialize(
        object.agencyAgent,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.updateDatetime != null) {
      yield r'update_datetime';
      yield serializers.serialize(
        object.updateDatetime,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    GeneralCompanyPRComment object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required GeneralCompanyPRCommentBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'introduction_pr':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.introductionPr = valueDes;
          break;
        case r'create_datetime':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.createDatetime = valueDes;
          break;
        case r'map_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.mapId = valueDes;
          break;
        case r'agency_company':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.agencyCompany = valueDes;
          break;
        case r'agency_agent':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.agencyAgent = valueDes;
          break;
        case r'update_datetime':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.updateDatetime = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  GeneralCompanyPRComment deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = GeneralCompanyPRCommentBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

