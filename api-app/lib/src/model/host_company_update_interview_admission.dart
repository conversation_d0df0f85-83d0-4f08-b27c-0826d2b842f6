//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/date.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'host_company_update_interview_admission.g.dart';

/// HostCompanyUpdateInterviewAdmission
///
/// Properties:
/// * [applyId] 
/// * [jobCode] 
/// * [employCode] 
/// * [placeCode] 
/// * [payrollCode] 
/// * [payrollPrice] 
/// * [joiningDate] 
@BuiltValue()
abstract class HostCompanyUpdateInterviewAdmission implements Built<HostCompanyUpdateInterviewAdmission, HostCompanyUpdateInterviewAdmissionBuilder> {
  @BuiltValueField(wireName: r'apply_id')
  int get applyId;

  @BuiltValueField(wireName: r'job_code')
  String get jobCode;

  @BuiltValueField(wireName: r'employ_code')
  String get employCode;

  @BuiltValueField(wireName: r'place_code')
  String get placeCode;

  @BuiltValueField(wireName: r'payroll_code')
  String get payrollCode;

  @BuiltValueField(wireName: r'payroll_price')
  double get payrollPrice;

  @BuiltValueField(wireName: r'joining_date')
  Date get joiningDate;

  HostCompanyUpdateInterviewAdmission._();

  factory HostCompanyUpdateInterviewAdmission([void updates(HostCompanyUpdateInterviewAdmissionBuilder b)]) = _$HostCompanyUpdateInterviewAdmission;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(HostCompanyUpdateInterviewAdmissionBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<HostCompanyUpdateInterviewAdmission> get serializer => _$HostCompanyUpdateInterviewAdmissionSerializer();
}

class _$HostCompanyUpdateInterviewAdmissionSerializer implements PrimitiveSerializer<HostCompanyUpdateInterviewAdmission> {
  @override
  final Iterable<Type> types = const [HostCompanyUpdateInterviewAdmission, _$HostCompanyUpdateInterviewAdmission];

  @override
  final String wireName = r'HostCompanyUpdateInterviewAdmission';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    HostCompanyUpdateInterviewAdmission object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'apply_id';
    yield serializers.serialize(
      object.applyId,
      specifiedType: const FullType(int),
    );
    yield r'job_code';
    yield serializers.serialize(
      object.jobCode,
      specifiedType: const FullType(String),
    );
    yield r'employ_code';
    yield serializers.serialize(
      object.employCode,
      specifiedType: const FullType(String),
    );
    yield r'place_code';
    yield serializers.serialize(
      object.placeCode,
      specifiedType: const FullType(String),
    );
    yield r'payroll_code';
    yield serializers.serialize(
      object.payrollCode,
      specifiedType: const FullType(String),
    );
    yield r'payroll_price';
    yield serializers.serialize(
      object.payrollPrice,
      specifiedType: const FullType(double),
    );
    yield r'joining_date';
    yield serializers.serialize(
      object.joiningDate,
      specifiedType: const FullType(Date),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    HostCompanyUpdateInterviewAdmission object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required HostCompanyUpdateInterviewAdmissionBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'apply_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.applyId = valueDes;
          break;
        case r'job_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.jobCode = valueDes;
          break;
        case r'employ_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.employCode = valueDes;
          break;
        case r'place_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.placeCode = valueDes;
          break;
        case r'payroll_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.payrollCode = valueDes;
          break;
        case r'payroll_price':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(double),
          ) as double;
          result.payrollPrice = valueDes;
          break;
        case r'joining_date':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(Date),
          ) as Date;
          result.joiningDate = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  HostCompanyUpdateInterviewAdmission deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = HostCompanyUpdateInterviewAdmissionBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

