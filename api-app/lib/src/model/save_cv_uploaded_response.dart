//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/error_detail.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'save_cv_uploaded_response.g.dart';

/// SaveCVUploadedResponse
///
/// Properties:
/// * [message] 
/// * [errors] 
/// * [data] 
@BuiltValue()
abstract class SaveCVUploadedResponse implements Built<SaveCVUploadedResponse, SaveCVUploadedResponseBuilder> {
  @BuiltValueField(wireName: r'message')
  String? get message;

  @BuiltValueField(wireName: r'errors')
  BuiltList<ErrorDetail>? get errors;

  @BuiltValueField(wireName: r'data')
  String? get data;

  SaveCVUploadedResponse._();

  factory SaveCVUploadedResponse([void updates(SaveCVUploadedResponseBuilder b)]) = _$SaveCVUploadedResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(SaveCVUploadedResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<SaveCVUploadedResponse> get serializer => _$SaveCVUploadedResponseSerializer();
}

class _$SaveCVUploadedResponseSerializer implements PrimitiveSerializer<SaveCVUploadedResponse> {
  @override
  final Iterable<Type> types = const [SaveCVUploadedResponse, _$SaveCVUploadedResponse];

  @override
  final String wireName = r'SaveCVUploadedResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    SaveCVUploadedResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'message';
    yield object.message == null ? null : serializers.serialize(
      object.message,
      specifiedType: const FullType.nullable(String),
    );
    yield r'errors';
    yield object.errors == null ? null : serializers.serialize(
      object.errors,
      specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
    );
    yield r'data';
    yield object.data == null ? null : serializers.serialize(
      object.data,
      specifiedType: const FullType.nullable(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    SaveCVUploadedResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required SaveCVUploadedResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'message':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.message = valueDes;
          break;
        case r'errors':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
          ) as BuiltList<ErrorDetail>?;
          if (valueDes == null) continue;
          result.errors.replace(valueDes);
          break;
        case r'data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.data = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  SaveCVUploadedResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = SaveCVUploadedResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

