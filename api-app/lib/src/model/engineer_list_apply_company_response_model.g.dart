// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'engineer_list_apply_company_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$EngineerListApplyCompanyResponseModel
    extends EngineerListApplyCompanyResponseModel {
  @override
  final String? message;
  @override
  final BuiltList<ErrorDetail>? errors;
  @override
  final EngineerListApplyCompanyPagination data;

  factory _$EngineerListApplyCompanyResponseModel(
          [void Function(EngineerListApplyCompanyResponseModelBuilder)?
              updates]) =>
      (new EngineerListApplyCompanyResponseModelBuilder()..update(updates))
          ._build();

  _$EngineerListApplyCompanyResponseModel._(
      {this.message, this.errors, required this.data})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'EngineerListApplyCompanyResponseModel', 'data');
  }

  @override
  EngineerListApplyCompanyResponseModel rebuild(
          void Function(EngineerListApplyCompanyResponseModelBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  EngineerListApplyCompanyResponseModelBuilder toBuilder() =>
      new EngineerListApplyCompanyResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is EngineerListApplyCompanyResponseModel &&
        message == other.message &&
        errors == other.errors &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'EngineerListApplyCompanyResponseModel')
          ..add('message', message)
          ..add('errors', errors)
          ..add('data', data))
        .toString();
  }
}

class EngineerListApplyCompanyResponseModelBuilder
    implements
        Builder<EngineerListApplyCompanyResponseModel,
            EngineerListApplyCompanyResponseModelBuilder> {
  _$EngineerListApplyCompanyResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  EngineerListApplyCompanyPaginationBuilder? _data;
  EngineerListApplyCompanyPaginationBuilder get data =>
      _$this._data ??= new EngineerListApplyCompanyPaginationBuilder();
  set data(EngineerListApplyCompanyPaginationBuilder? data) =>
      _$this._data = data;

  EngineerListApplyCompanyResponseModelBuilder() {
    EngineerListApplyCompanyResponseModel._defaults(this);
  }

  EngineerListApplyCompanyResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _errors = $v.errors?.toBuilder();
      _data = $v.data.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(EngineerListApplyCompanyResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$EngineerListApplyCompanyResponseModel;
  }

  @override
  void update(
      void Function(EngineerListApplyCompanyResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  EngineerListApplyCompanyResponseModel build() => _build();

  _$EngineerListApplyCompanyResponseModel _build() {
    _$EngineerListApplyCompanyResponseModel _$result;
    try {
      _$result = _$v ??
          new _$EngineerListApplyCompanyResponseModel._(
            message: message,
            errors: _errors?.build(),
            data: data.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'errors';
        _errors?.build();
        _$failedField = 'data';
        data.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'EngineerListApplyCompanyResponseModel',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
