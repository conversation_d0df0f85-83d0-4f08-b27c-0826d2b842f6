//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'general_company_save_filter.g.dart';

/// GeneralCompanySaveFilter
///
/// Properties:
/// * [filterName] 
/// * [ageFrom] 
/// * [ageTo] 
/// * [sexType] 
/// * [countryCode] 
/// * [addressCode1] 
/// * [addressCode2] 
/// * [lastAcademicCode] 
/// * [languageCode1] 
/// * [languageLevelType1] 
/// * [languageCode2] 
/// * [languageLevelType2] 
/// * [experiencedJobCode1] 
/// * [yearsOfExperience1] 
/// * [experiencedJobCode2] 
/// * [yearsOfExperience2] 
/// * [experiencedJobCode3] 
/// * [yearsOfExperience3] 
/// * [skillCode11] 
/// * [skillLevelType11] 
/// * [skillCode12] 
/// * [skillLevelType12] 
/// * [skillCode13] 
/// * [skillLevelType13] 
/// * [skillCode21] 
/// * [skillLevelType21] 
/// * [skillCode22] 
/// * [skillLevelType22] 
/// * [skillCode23] 
/// * [skillLevelType23] 
/// * [skillCode31] 
/// * [skillLevelType31] 
/// * [skillCode32] 
/// * [skillLevelType32] 
/// * [skillCode33] 
/// * [skillLevelType33] 
/// * [licenceCode1] 
/// * [licencePoint1] 
/// * [licenceCode2] 
/// * [licencePoint2] 
/// * [licenceCode3] 
/// * [licencePoint3] 
/// * [recruitingJobCode] 
/// * [recruitingEmployCode] 
/// * [workPlaceCode1] 
/// * [workPlaceCode2] 
/// * [workPlaceCode3] 
/// * [payrollCode] 
/// * [payrollPriceFrom] 
/// * [payrollPriceTo] 
/// * [agentFee] 
/// * [agentFeeCurrCode] 
/// * [hostAgent] 
@BuiltValue()
abstract class GeneralCompanySaveFilter implements Built<GeneralCompanySaveFilter, GeneralCompanySaveFilterBuilder> {
  @BuiltValueField(wireName: r'filter_name')
  String get filterName;

  @BuiltValueField(wireName: r'age_from')
  int? get ageFrom;

  @BuiltValueField(wireName: r'age_to')
  int? get ageTo;

  @BuiltValueField(wireName: r'sex_type')
  String? get sexType;

  @BuiltValueField(wireName: r'country_code')
  String? get countryCode;

  @BuiltValueField(wireName: r'address_code1')
  String? get addressCode1;

  @BuiltValueField(wireName: r'address_code2')
  String? get addressCode2;

  @BuiltValueField(wireName: r'last_academic_code')
  String? get lastAcademicCode;

  @BuiltValueField(wireName: r'language_code1')
  String? get languageCode1;

  @BuiltValueField(wireName: r'language_level_type1')
  int? get languageLevelType1;

  @BuiltValueField(wireName: r'language_code2')
  String? get languageCode2;

  @BuiltValueField(wireName: r'language_level_type2')
  int? get languageLevelType2;

  @BuiltValueField(wireName: r'experienced_job_code1')
  String? get experiencedJobCode1;

  @BuiltValueField(wireName: r'years_of_experience1')
  int? get yearsOfExperience1;

  @BuiltValueField(wireName: r'experienced_job_code2')
  String? get experiencedJobCode2;

  @BuiltValueField(wireName: r'years_of_experience2')
  int? get yearsOfExperience2;

  @BuiltValueField(wireName: r'experienced_job_code3')
  String? get experiencedJobCode3;

  @BuiltValueField(wireName: r'years_of_experience3')
  int? get yearsOfExperience3;

  @BuiltValueField(wireName: r'skill_code1_1')
  int? get skillCode11;

  @BuiltValueField(wireName: r'skill_level_type1_1')
  int? get skillLevelType11;

  @BuiltValueField(wireName: r'skill_code1_2')
  int? get skillCode12;

  @BuiltValueField(wireName: r'skill_level_type1_2')
  int? get skillLevelType12;

  @BuiltValueField(wireName: r'skill_code1_3')
  int? get skillCode13;

  @BuiltValueField(wireName: r'skill_level_type1_3')
  int? get skillLevelType13;

  @BuiltValueField(wireName: r'skill_code2_1')
  int? get skillCode21;

  @BuiltValueField(wireName: r'skill_level_type2_1')
  int? get skillLevelType21;

  @BuiltValueField(wireName: r'skill_code2_2')
  int? get skillCode22;

  @BuiltValueField(wireName: r'skill_level_type2_2')
  int? get skillLevelType22;

  @BuiltValueField(wireName: r'skill_code2_3')
  int? get skillCode23;

  @BuiltValueField(wireName: r'skill_level_type2_3')
  int? get skillLevelType23;

  @BuiltValueField(wireName: r'skill_code3_1')
  int? get skillCode31;

  @BuiltValueField(wireName: r'skill_level_type3_1')
  int? get skillLevelType31;

  @BuiltValueField(wireName: r'skill_code3_2')
  int? get skillCode32;

  @BuiltValueField(wireName: r'skill_level_type3_2')
  int? get skillLevelType32;

  @BuiltValueField(wireName: r'skill_code3_3')
  int? get skillCode33;

  @BuiltValueField(wireName: r'skill_level_type3_3')
  int? get skillLevelType33;

  @BuiltValueField(wireName: r'licence_code1')
  String? get licenceCode1;

  @BuiltValueField(wireName: r'licence_point1')
  int? get licencePoint1;

  @BuiltValueField(wireName: r'licence_code2')
  String? get licenceCode2;

  @BuiltValueField(wireName: r'licence_point2')
  int? get licencePoint2;

  @BuiltValueField(wireName: r'licence_code3')
  String? get licenceCode3;

  @BuiltValueField(wireName: r'licence_point3')
  int? get licencePoint3;

  @BuiltValueField(wireName: r'recruiting_job_code')
  String? get recruitingJobCode;

  @BuiltValueField(wireName: r'recruiting_employ_code')
  String? get recruitingEmployCode;

  @BuiltValueField(wireName: r'work_place_code1')
  String? get workPlaceCode1;

  @BuiltValueField(wireName: r'work_place_code2')
  String? get workPlaceCode2;

  @BuiltValueField(wireName: r'work_place_code3')
  String? get workPlaceCode3;

  @BuiltValueField(wireName: r'payroll_code')
  String? get payrollCode;

  @BuiltValueField(wireName: r'payroll_price_from')
  double? get payrollPriceFrom;

  @BuiltValueField(wireName: r'payroll_price_to')
  double? get payrollPriceTo;

  @BuiltValueField(wireName: r'agent_fee')
  double? get agentFee;

  @BuiltValueField(wireName: r'agent_fee_curr_code')
  String? get agentFeeCurrCode;

  @BuiltValueField(wireName: r'host_agent')
  int get hostAgent;

  GeneralCompanySaveFilter._();

  factory GeneralCompanySaveFilter([void updates(GeneralCompanySaveFilterBuilder b)]) = _$GeneralCompanySaveFilter;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(GeneralCompanySaveFilterBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<GeneralCompanySaveFilter> get serializer => _$GeneralCompanySaveFilterSerializer();
}

class _$GeneralCompanySaveFilterSerializer implements PrimitiveSerializer<GeneralCompanySaveFilter> {
  @override
  final Iterable<Type> types = const [GeneralCompanySaveFilter, _$GeneralCompanySaveFilter];

  @override
  final String wireName = r'GeneralCompanySaveFilter';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    GeneralCompanySaveFilter object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'filter_name';
    yield serializers.serialize(
      object.filterName,
      specifiedType: const FullType(String),
    );
    if (object.ageFrom != null) {
      yield r'age_from';
      yield serializers.serialize(
        object.ageFrom,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.ageTo != null) {
      yield r'age_to';
      yield serializers.serialize(
        object.ageTo,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.sexType != null) {
      yield r'sex_type';
      yield serializers.serialize(
        object.sexType,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.countryCode != null) {
      yield r'country_code';
      yield serializers.serialize(
        object.countryCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.addressCode1 != null) {
      yield r'address_code1';
      yield serializers.serialize(
        object.addressCode1,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.addressCode2 != null) {
      yield r'address_code2';
      yield serializers.serialize(
        object.addressCode2,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.lastAcademicCode != null) {
      yield r'last_academic_code';
      yield serializers.serialize(
        object.lastAcademicCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.languageCode1 != null) {
      yield r'language_code1';
      yield serializers.serialize(
        object.languageCode1,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.languageLevelType1 != null) {
      yield r'language_level_type1';
      yield serializers.serialize(
        object.languageLevelType1,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.languageCode2 != null) {
      yield r'language_code2';
      yield serializers.serialize(
        object.languageCode2,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.languageLevelType2 != null) {
      yield r'language_level_type2';
      yield serializers.serialize(
        object.languageLevelType2,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.experiencedJobCode1 != null) {
      yield r'experienced_job_code1';
      yield serializers.serialize(
        object.experiencedJobCode1,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.yearsOfExperience1 != null) {
      yield r'years_of_experience1';
      yield serializers.serialize(
        object.yearsOfExperience1,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.experiencedJobCode2 != null) {
      yield r'experienced_job_code2';
      yield serializers.serialize(
        object.experiencedJobCode2,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.yearsOfExperience2 != null) {
      yield r'years_of_experience2';
      yield serializers.serialize(
        object.yearsOfExperience2,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.experiencedJobCode3 != null) {
      yield r'experienced_job_code3';
      yield serializers.serialize(
        object.experiencedJobCode3,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.yearsOfExperience3 != null) {
      yield r'years_of_experience3';
      yield serializers.serialize(
        object.yearsOfExperience3,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.skillCode11 != null) {
      yield r'skill_code1_1';
      yield serializers.serialize(
        object.skillCode11,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.skillLevelType11 != null) {
      yield r'skill_level_type1_1';
      yield serializers.serialize(
        object.skillLevelType11,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.skillCode12 != null) {
      yield r'skill_code1_2';
      yield serializers.serialize(
        object.skillCode12,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.skillLevelType12 != null) {
      yield r'skill_level_type1_2';
      yield serializers.serialize(
        object.skillLevelType12,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.skillCode13 != null) {
      yield r'skill_code1_3';
      yield serializers.serialize(
        object.skillCode13,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.skillLevelType13 != null) {
      yield r'skill_level_type1_3';
      yield serializers.serialize(
        object.skillLevelType13,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.skillCode21 != null) {
      yield r'skill_code2_1';
      yield serializers.serialize(
        object.skillCode21,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.skillLevelType21 != null) {
      yield r'skill_level_type2_1';
      yield serializers.serialize(
        object.skillLevelType21,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.skillCode22 != null) {
      yield r'skill_code2_2';
      yield serializers.serialize(
        object.skillCode22,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.skillLevelType22 != null) {
      yield r'skill_level_type2_2';
      yield serializers.serialize(
        object.skillLevelType22,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.skillCode23 != null) {
      yield r'skill_code2_3';
      yield serializers.serialize(
        object.skillCode23,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.skillLevelType23 != null) {
      yield r'skill_level_type2_3';
      yield serializers.serialize(
        object.skillLevelType23,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.skillCode31 != null) {
      yield r'skill_code3_1';
      yield serializers.serialize(
        object.skillCode31,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.skillLevelType31 != null) {
      yield r'skill_level_type3_1';
      yield serializers.serialize(
        object.skillLevelType31,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.skillCode32 != null) {
      yield r'skill_code3_2';
      yield serializers.serialize(
        object.skillCode32,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.skillLevelType32 != null) {
      yield r'skill_level_type3_2';
      yield serializers.serialize(
        object.skillLevelType32,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.skillCode33 != null) {
      yield r'skill_code3_3';
      yield serializers.serialize(
        object.skillCode33,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.skillLevelType33 != null) {
      yield r'skill_level_type3_3';
      yield serializers.serialize(
        object.skillLevelType33,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.licenceCode1 != null) {
      yield r'licence_code1';
      yield serializers.serialize(
        object.licenceCode1,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.licencePoint1 != null) {
      yield r'licence_point1';
      yield serializers.serialize(
        object.licencePoint1,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.licenceCode2 != null) {
      yield r'licence_code2';
      yield serializers.serialize(
        object.licenceCode2,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.licencePoint2 != null) {
      yield r'licence_point2';
      yield serializers.serialize(
        object.licencePoint2,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.licenceCode3 != null) {
      yield r'licence_code3';
      yield serializers.serialize(
        object.licenceCode3,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.licencePoint3 != null) {
      yield r'licence_point3';
      yield serializers.serialize(
        object.licencePoint3,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.recruitingJobCode != null) {
      yield r'recruiting_job_code';
      yield serializers.serialize(
        object.recruitingJobCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.recruitingEmployCode != null) {
      yield r'recruiting_employ_code';
      yield serializers.serialize(
        object.recruitingEmployCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.workPlaceCode1 != null) {
      yield r'work_place_code1';
      yield serializers.serialize(
        object.workPlaceCode1,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.workPlaceCode2 != null) {
      yield r'work_place_code2';
      yield serializers.serialize(
        object.workPlaceCode2,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.workPlaceCode3 != null) {
      yield r'work_place_code3';
      yield serializers.serialize(
        object.workPlaceCode3,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.payrollCode != null) {
      yield r'payroll_code';
      yield serializers.serialize(
        object.payrollCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.payrollPriceFrom != null) {
      yield r'payroll_price_from';
      yield serializers.serialize(
        object.payrollPriceFrom,
        specifiedType: const FullType.nullable(double),
      );
    }
    if (object.payrollPriceTo != null) {
      yield r'payroll_price_to';
      yield serializers.serialize(
        object.payrollPriceTo,
        specifiedType: const FullType.nullable(double),
      );
    }
    if (object.agentFee != null) {
      yield r'agent_fee';
      yield serializers.serialize(
        object.agentFee,
        specifiedType: const FullType.nullable(double),
      );
    }
    if (object.agentFeeCurrCode != null) {
      yield r'agent_fee_curr_code';
      yield serializers.serialize(
        object.agentFeeCurrCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    yield r'host_agent';
    yield serializers.serialize(
      object.hostAgent,
      specifiedType: const FullType(int),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    GeneralCompanySaveFilter object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required GeneralCompanySaveFilterBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'filter_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.filterName = valueDes;
          break;
        case r'age_from':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.ageFrom = valueDes;
          break;
        case r'age_to':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.ageTo = valueDes;
          break;
        case r'sex_type':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.sexType = valueDes;
          break;
        case r'country_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.countryCode = valueDes;
          break;
        case r'address_code1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.addressCode1 = valueDes;
          break;
        case r'address_code2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.addressCode2 = valueDes;
          break;
        case r'last_academic_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.lastAcademicCode = valueDes;
          break;
        case r'language_code1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.languageCode1 = valueDes;
          break;
        case r'language_level_type1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.languageLevelType1 = valueDes;
          break;
        case r'language_code2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.languageCode2 = valueDes;
          break;
        case r'language_level_type2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.languageLevelType2 = valueDes;
          break;
        case r'experienced_job_code1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.experiencedJobCode1 = valueDes;
          break;
        case r'years_of_experience1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.yearsOfExperience1 = valueDes;
          break;
        case r'experienced_job_code2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.experiencedJobCode2 = valueDes;
          break;
        case r'years_of_experience2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.yearsOfExperience2 = valueDes;
          break;
        case r'experienced_job_code3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.experiencedJobCode3 = valueDes;
          break;
        case r'years_of_experience3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.yearsOfExperience3 = valueDes;
          break;
        case r'skill_code1_1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.skillCode11 = valueDes;
          break;
        case r'skill_level_type1_1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.skillLevelType11 = valueDes;
          break;
        case r'skill_code1_2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.skillCode12 = valueDes;
          break;
        case r'skill_level_type1_2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.skillLevelType12 = valueDes;
          break;
        case r'skill_code1_3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.skillCode13 = valueDes;
          break;
        case r'skill_level_type1_3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.skillLevelType13 = valueDes;
          break;
        case r'skill_code2_1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.skillCode21 = valueDes;
          break;
        case r'skill_level_type2_1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.skillLevelType21 = valueDes;
          break;
        case r'skill_code2_2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.skillCode22 = valueDes;
          break;
        case r'skill_level_type2_2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.skillLevelType22 = valueDes;
          break;
        case r'skill_code2_3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.skillCode23 = valueDes;
          break;
        case r'skill_level_type2_3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.skillLevelType23 = valueDes;
          break;
        case r'skill_code3_1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.skillCode31 = valueDes;
          break;
        case r'skill_level_type3_1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.skillLevelType31 = valueDes;
          break;
        case r'skill_code3_2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.skillCode32 = valueDes;
          break;
        case r'skill_level_type3_2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.skillLevelType32 = valueDes;
          break;
        case r'skill_code3_3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.skillCode33 = valueDes;
          break;
        case r'skill_level_type3_3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.skillLevelType33 = valueDes;
          break;
        case r'licence_code1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.licenceCode1 = valueDes;
          break;
        case r'licence_point1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.licencePoint1 = valueDes;
          break;
        case r'licence_code2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.licenceCode2 = valueDes;
          break;
        case r'licence_point2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.licencePoint2 = valueDes;
          break;
        case r'licence_code3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.licenceCode3 = valueDes;
          break;
        case r'licence_point3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.licencePoint3 = valueDes;
          break;
        case r'recruiting_job_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.recruitingJobCode = valueDes;
          break;
        case r'recruiting_employ_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.recruitingEmployCode = valueDes;
          break;
        case r'work_place_code1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.workPlaceCode1 = valueDes;
          break;
        case r'work_place_code2':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.workPlaceCode2 = valueDes;
          break;
        case r'work_place_code3':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.workPlaceCode3 = valueDes;
          break;
        case r'payroll_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.payrollCode = valueDes;
          break;
        case r'payroll_price_from':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(double),
          ) as double?;
          if (valueDes == null) continue;
          result.payrollPriceFrom = valueDes;
          break;
        case r'payroll_price_to':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(double),
          ) as double?;
          if (valueDes == null) continue;
          result.payrollPriceTo = valueDes;
          break;
        case r'agent_fee':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(double),
          ) as double?;
          if (valueDes == null) continue;
          result.agentFee = valueDes;
          break;
        case r'agent_fee_curr_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.agentFeeCurrCode = valueDes;
          break;
        case r'host_agent':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.hostAgent = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  GeneralCompanySaveFilter deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = GeneralCompanySaveFilterBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

