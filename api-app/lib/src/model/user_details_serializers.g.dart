// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_details_serializers.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UserDetailsSerializers extends UserDetailsSerializers {
  @override
  final String? profileImagePath;
  @override
  final String email;
  @override
  final String? firstName;
  @override
  final String? lastName;
  @override
  final String? nickname;
  @override
  final int? sexType;
  @override
  final Date? birthDate;
  @override
  final String? countryCode;
  @override
  final String? tel;
  @override
  final String? addressCode;
  @override
  final String? cityName;
  @override
  final String? passportNumber;
  @override
  final String? passportImagePath;
  @override
  final BuiltList<EngAcademic>? educations;
  @override
  final BuiltList<EngLanguage>? languages;
  @override
  final BuiltList<EngLicense>? qualifications;
  @override
  final BuiltList<EngSkill>? skills;
  @override
  final BuiltList<EngCareer>? experiences;
  @override
  final EngHope? requirements;
  @override
  final String? lastAcademicCode;
  @override
  final String? pr;
  @override
  final String? selfIntroductionUrl;
  @override
  final String? internationalTel;
  @override
  final DateTime? updated;
  @override
  final int userType;
  @override
  final String? facebookUrl;
  @override
  final String? linkedinUrl;
  @override
  final String? whatsappUrl;
  @override
  final String? lastAcademicName;
  @override
  final EngSelfAssesment? selfAssesment;
  @override
  final String? emailTemp;
  @override
  final BuiltList<CategorySkill>? categorySkills;
  @override
  final String? skillsForCvDisplay;
  @override
  final String? professionalSummary;
  @override
  final BuiltList<EngHighLightProject>? highlightProjects;

  factory _$UserDetailsSerializers(
          [void Function(UserDetailsSerializersBuilder)? updates]) =>
      (new UserDetailsSerializersBuilder()..update(updates))._build();

  _$UserDetailsSerializers._(
      {this.profileImagePath,
      required this.email,
      this.firstName,
      this.lastName,
      this.nickname,
      this.sexType,
      this.birthDate,
      this.countryCode,
      this.tel,
      this.addressCode,
      this.cityName,
      this.passportNumber,
      this.passportImagePath,
      this.educations,
      this.languages,
      this.qualifications,
      this.skills,
      this.experiences,
      this.requirements,
      this.lastAcademicCode,
      this.pr,
      this.selfIntroductionUrl,
      this.internationalTel,
      this.updated,
      required this.userType,
      this.facebookUrl,
      this.linkedinUrl,
      this.whatsappUrl,
      this.lastAcademicName,
      this.selfAssesment,
      this.emailTemp,
      this.categorySkills,
      this.skillsForCvDisplay,
      this.professionalSummary,
      this.highlightProjects})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        email, r'UserDetailsSerializers', 'email');
    BuiltValueNullFieldError.checkNotNull(
        userType, r'UserDetailsSerializers', 'userType');
  }

  @override
  UserDetailsSerializers rebuild(
          void Function(UserDetailsSerializersBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UserDetailsSerializersBuilder toBuilder() =>
      new UserDetailsSerializersBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UserDetailsSerializers &&
        profileImagePath == other.profileImagePath &&
        email == other.email &&
        firstName == other.firstName &&
        lastName == other.lastName &&
        nickname == other.nickname &&
        sexType == other.sexType &&
        birthDate == other.birthDate &&
        countryCode == other.countryCode &&
        tel == other.tel &&
        addressCode == other.addressCode &&
        cityName == other.cityName &&
        passportNumber == other.passportNumber &&
        passportImagePath == other.passportImagePath &&
        educations == other.educations &&
        languages == other.languages &&
        qualifications == other.qualifications &&
        skills == other.skills &&
        experiences == other.experiences &&
        requirements == other.requirements &&
        lastAcademicCode == other.lastAcademicCode &&
        pr == other.pr &&
        selfIntroductionUrl == other.selfIntroductionUrl &&
        internationalTel == other.internationalTel &&
        updated == other.updated &&
        userType == other.userType &&
        facebookUrl == other.facebookUrl &&
        linkedinUrl == other.linkedinUrl &&
        whatsappUrl == other.whatsappUrl &&
        lastAcademicName == other.lastAcademicName &&
        selfAssesment == other.selfAssesment &&
        emailTemp == other.emailTemp &&
        categorySkills == other.categorySkills &&
        skillsForCvDisplay == other.skillsForCvDisplay &&
        professionalSummary == other.professionalSummary &&
        highlightProjects == other.highlightProjects;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, profileImagePath.hashCode);
    _$hash = $jc(_$hash, email.hashCode);
    _$hash = $jc(_$hash, firstName.hashCode);
    _$hash = $jc(_$hash, lastName.hashCode);
    _$hash = $jc(_$hash, nickname.hashCode);
    _$hash = $jc(_$hash, sexType.hashCode);
    _$hash = $jc(_$hash, birthDate.hashCode);
    _$hash = $jc(_$hash, countryCode.hashCode);
    _$hash = $jc(_$hash, tel.hashCode);
    _$hash = $jc(_$hash, addressCode.hashCode);
    _$hash = $jc(_$hash, cityName.hashCode);
    _$hash = $jc(_$hash, passportNumber.hashCode);
    _$hash = $jc(_$hash, passportImagePath.hashCode);
    _$hash = $jc(_$hash, educations.hashCode);
    _$hash = $jc(_$hash, languages.hashCode);
    _$hash = $jc(_$hash, qualifications.hashCode);
    _$hash = $jc(_$hash, skills.hashCode);
    _$hash = $jc(_$hash, experiences.hashCode);
    _$hash = $jc(_$hash, requirements.hashCode);
    _$hash = $jc(_$hash, lastAcademicCode.hashCode);
    _$hash = $jc(_$hash, pr.hashCode);
    _$hash = $jc(_$hash, selfIntroductionUrl.hashCode);
    _$hash = $jc(_$hash, internationalTel.hashCode);
    _$hash = $jc(_$hash, updated.hashCode);
    _$hash = $jc(_$hash, userType.hashCode);
    _$hash = $jc(_$hash, facebookUrl.hashCode);
    _$hash = $jc(_$hash, linkedinUrl.hashCode);
    _$hash = $jc(_$hash, whatsappUrl.hashCode);
    _$hash = $jc(_$hash, lastAcademicName.hashCode);
    _$hash = $jc(_$hash, selfAssesment.hashCode);
    _$hash = $jc(_$hash, emailTemp.hashCode);
    _$hash = $jc(_$hash, categorySkills.hashCode);
    _$hash = $jc(_$hash, skillsForCvDisplay.hashCode);
    _$hash = $jc(_$hash, professionalSummary.hashCode);
    _$hash = $jc(_$hash, highlightProjects.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UserDetailsSerializers')
          ..add('profileImagePath', profileImagePath)
          ..add('email', email)
          ..add('firstName', firstName)
          ..add('lastName', lastName)
          ..add('nickname', nickname)
          ..add('sexType', sexType)
          ..add('birthDate', birthDate)
          ..add('countryCode', countryCode)
          ..add('tel', tel)
          ..add('addressCode', addressCode)
          ..add('cityName', cityName)
          ..add('passportNumber', passportNumber)
          ..add('passportImagePath', passportImagePath)
          ..add('educations', educations)
          ..add('languages', languages)
          ..add('qualifications', qualifications)
          ..add('skills', skills)
          ..add('experiences', experiences)
          ..add('requirements', requirements)
          ..add('lastAcademicCode', lastAcademicCode)
          ..add('pr', pr)
          ..add('selfIntroductionUrl', selfIntroductionUrl)
          ..add('internationalTel', internationalTel)
          ..add('updated', updated)
          ..add('userType', userType)
          ..add('facebookUrl', facebookUrl)
          ..add('linkedinUrl', linkedinUrl)
          ..add('whatsappUrl', whatsappUrl)
          ..add('lastAcademicName', lastAcademicName)
          ..add('selfAssesment', selfAssesment)
          ..add('emailTemp', emailTemp)
          ..add('categorySkills', categorySkills)
          ..add('skillsForCvDisplay', skillsForCvDisplay)
          ..add('professionalSummary', professionalSummary)
          ..add('highlightProjects', highlightProjects))
        .toString();
  }
}

class UserDetailsSerializersBuilder
    implements Builder<UserDetailsSerializers, UserDetailsSerializersBuilder> {
  _$UserDetailsSerializers? _$v;

  String? _profileImagePath;
  String? get profileImagePath => _$this._profileImagePath;
  set profileImagePath(String? profileImagePath) =>
      _$this._profileImagePath = profileImagePath;

  String? _email;
  String? get email => _$this._email;
  set email(String? email) => _$this._email = email;

  String? _firstName;
  String? get firstName => _$this._firstName;
  set firstName(String? firstName) => _$this._firstName = firstName;

  String? _lastName;
  String? get lastName => _$this._lastName;
  set lastName(String? lastName) => _$this._lastName = lastName;

  String? _nickname;
  String? get nickname => _$this._nickname;
  set nickname(String? nickname) => _$this._nickname = nickname;

  int? _sexType;
  int? get sexType => _$this._sexType;
  set sexType(int? sexType) => _$this._sexType = sexType;

  Date? _birthDate;
  Date? get birthDate => _$this._birthDate;
  set birthDate(Date? birthDate) => _$this._birthDate = birthDate;

  String? _countryCode;
  String? get countryCode => _$this._countryCode;
  set countryCode(String? countryCode) => _$this._countryCode = countryCode;

  String? _tel;
  String? get tel => _$this._tel;
  set tel(String? tel) => _$this._tel = tel;

  String? _addressCode;
  String? get addressCode => _$this._addressCode;
  set addressCode(String? addressCode) => _$this._addressCode = addressCode;

  String? _cityName;
  String? get cityName => _$this._cityName;
  set cityName(String? cityName) => _$this._cityName = cityName;

  String? _passportNumber;
  String? get passportNumber => _$this._passportNumber;
  set passportNumber(String? passportNumber) =>
      _$this._passportNumber = passportNumber;

  String? _passportImagePath;
  String? get passportImagePath => _$this._passportImagePath;
  set passportImagePath(String? passportImagePath) =>
      _$this._passportImagePath = passportImagePath;

  ListBuilder<EngAcademic>? _educations;
  ListBuilder<EngAcademic> get educations =>
      _$this._educations ??= new ListBuilder<EngAcademic>();
  set educations(ListBuilder<EngAcademic>? educations) =>
      _$this._educations = educations;

  ListBuilder<EngLanguage>? _languages;
  ListBuilder<EngLanguage> get languages =>
      _$this._languages ??= new ListBuilder<EngLanguage>();
  set languages(ListBuilder<EngLanguage>? languages) =>
      _$this._languages = languages;

  ListBuilder<EngLicense>? _qualifications;
  ListBuilder<EngLicense> get qualifications =>
      _$this._qualifications ??= new ListBuilder<EngLicense>();
  set qualifications(ListBuilder<EngLicense>? qualifications) =>
      _$this._qualifications = qualifications;

  ListBuilder<EngSkill>? _skills;
  ListBuilder<EngSkill> get skills =>
      _$this._skills ??= new ListBuilder<EngSkill>();
  set skills(ListBuilder<EngSkill>? skills) => _$this._skills = skills;

  ListBuilder<EngCareer>? _experiences;
  ListBuilder<EngCareer> get experiences =>
      _$this._experiences ??= new ListBuilder<EngCareer>();
  set experiences(ListBuilder<EngCareer>? experiences) =>
      _$this._experiences = experiences;

  EngHopeBuilder? _requirements;
  EngHopeBuilder get requirements =>
      _$this._requirements ??= new EngHopeBuilder();
  set requirements(EngHopeBuilder? requirements) =>
      _$this._requirements = requirements;

  String? _lastAcademicCode;
  String? get lastAcademicCode => _$this._lastAcademicCode;
  set lastAcademicCode(String? lastAcademicCode) =>
      _$this._lastAcademicCode = lastAcademicCode;

  String? _pr;
  String? get pr => _$this._pr;
  set pr(String? pr) => _$this._pr = pr;

  String? _selfIntroductionUrl;
  String? get selfIntroductionUrl => _$this._selfIntroductionUrl;
  set selfIntroductionUrl(String? selfIntroductionUrl) =>
      _$this._selfIntroductionUrl = selfIntroductionUrl;

  String? _internationalTel;
  String? get internationalTel => _$this._internationalTel;
  set internationalTel(String? internationalTel) =>
      _$this._internationalTel = internationalTel;

  DateTime? _updated;
  DateTime? get updated => _$this._updated;
  set updated(DateTime? updated) => _$this._updated = updated;

  int? _userType;
  int? get userType => _$this._userType;
  set userType(int? userType) => _$this._userType = userType;

  String? _facebookUrl;
  String? get facebookUrl => _$this._facebookUrl;
  set facebookUrl(String? facebookUrl) => _$this._facebookUrl = facebookUrl;

  String? _linkedinUrl;
  String? get linkedinUrl => _$this._linkedinUrl;
  set linkedinUrl(String? linkedinUrl) => _$this._linkedinUrl = linkedinUrl;

  String? _whatsappUrl;
  String? get whatsappUrl => _$this._whatsappUrl;
  set whatsappUrl(String? whatsappUrl) => _$this._whatsappUrl = whatsappUrl;

  String? _lastAcademicName;
  String? get lastAcademicName => _$this._lastAcademicName;
  set lastAcademicName(String? lastAcademicName) =>
      _$this._lastAcademicName = lastAcademicName;

  EngSelfAssesmentBuilder? _selfAssesment;
  EngSelfAssesmentBuilder get selfAssesment =>
      _$this._selfAssesment ??= new EngSelfAssesmentBuilder();
  set selfAssesment(EngSelfAssesmentBuilder? selfAssesment) =>
      _$this._selfAssesment = selfAssesment;

  String? _emailTemp;
  String? get emailTemp => _$this._emailTemp;
  set emailTemp(String? emailTemp) => _$this._emailTemp = emailTemp;

  ListBuilder<CategorySkill>? _categorySkills;
  ListBuilder<CategorySkill> get categorySkills =>
      _$this._categorySkills ??= new ListBuilder<CategorySkill>();
  set categorySkills(ListBuilder<CategorySkill>? categorySkills) =>
      _$this._categorySkills = categorySkills;

  String? _skillsForCvDisplay;
  String? get skillsForCvDisplay => _$this._skillsForCvDisplay;
  set skillsForCvDisplay(String? skillsForCvDisplay) =>
      _$this._skillsForCvDisplay = skillsForCvDisplay;

  String? _professionalSummary;
  String? get professionalSummary => _$this._professionalSummary;
  set professionalSummary(String? professionalSummary) =>
      _$this._professionalSummary = professionalSummary;

  ListBuilder<EngHighLightProject>? _highlightProjects;
  ListBuilder<EngHighLightProject> get highlightProjects =>
      _$this._highlightProjects ??= new ListBuilder<EngHighLightProject>();
  set highlightProjects(ListBuilder<EngHighLightProject>? highlightProjects) =>
      _$this._highlightProjects = highlightProjects;

  UserDetailsSerializersBuilder() {
    UserDetailsSerializers._defaults(this);
  }

  UserDetailsSerializersBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _profileImagePath = $v.profileImagePath;
      _email = $v.email;
      _firstName = $v.firstName;
      _lastName = $v.lastName;
      _nickname = $v.nickname;
      _sexType = $v.sexType;
      _birthDate = $v.birthDate;
      _countryCode = $v.countryCode;
      _tel = $v.tel;
      _addressCode = $v.addressCode;
      _cityName = $v.cityName;
      _passportNumber = $v.passportNumber;
      _passportImagePath = $v.passportImagePath;
      _educations = $v.educations?.toBuilder();
      _languages = $v.languages?.toBuilder();
      _qualifications = $v.qualifications?.toBuilder();
      _skills = $v.skills?.toBuilder();
      _experiences = $v.experiences?.toBuilder();
      _requirements = $v.requirements?.toBuilder();
      _lastAcademicCode = $v.lastAcademicCode;
      _pr = $v.pr;
      _selfIntroductionUrl = $v.selfIntroductionUrl;
      _internationalTel = $v.internationalTel;
      _updated = $v.updated;
      _userType = $v.userType;
      _facebookUrl = $v.facebookUrl;
      _linkedinUrl = $v.linkedinUrl;
      _whatsappUrl = $v.whatsappUrl;
      _lastAcademicName = $v.lastAcademicName;
      _selfAssesment = $v.selfAssesment?.toBuilder();
      _emailTemp = $v.emailTemp;
      _categorySkills = $v.categorySkills?.toBuilder();
      _skillsForCvDisplay = $v.skillsForCvDisplay;
      _professionalSummary = $v.professionalSummary;
      _highlightProjects = $v.highlightProjects?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UserDetailsSerializers other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$UserDetailsSerializers;
  }

  @override
  void update(void Function(UserDetailsSerializersBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UserDetailsSerializers build() => _build();

  _$UserDetailsSerializers _build() {
    _$UserDetailsSerializers _$result;
    try {
      _$result = _$v ??
          new _$UserDetailsSerializers._(
            profileImagePath: profileImagePath,
            email: BuiltValueNullFieldError.checkNotNull(
                email, r'UserDetailsSerializers', 'email'),
            firstName: firstName,
            lastName: lastName,
            nickname: nickname,
            sexType: sexType,
            birthDate: birthDate,
            countryCode: countryCode,
            tel: tel,
            addressCode: addressCode,
            cityName: cityName,
            passportNumber: passportNumber,
            passportImagePath: passportImagePath,
            educations: _educations?.build(),
            languages: _languages?.build(),
            qualifications: _qualifications?.build(),
            skills: _skills?.build(),
            experiences: _experiences?.build(),
            requirements: _requirements?.build(),
            lastAcademicCode: lastAcademicCode,
            pr: pr,
            selfIntroductionUrl: selfIntroductionUrl,
            internationalTel: internationalTel,
            updated: updated,
            userType: BuiltValueNullFieldError.checkNotNull(
                userType, r'UserDetailsSerializers', 'userType'),
            facebookUrl: facebookUrl,
            linkedinUrl: linkedinUrl,
            whatsappUrl: whatsappUrl,
            lastAcademicName: lastAcademicName,
            selfAssesment: _selfAssesment?.build(),
            emailTemp: emailTemp,
            categorySkills: _categorySkills?.build(),
            skillsForCvDisplay: skillsForCvDisplay,
            professionalSummary: professionalSummary,
            highlightProjects: _highlightProjects?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'educations';
        _educations?.build();
        _$failedField = 'languages';
        _languages?.build();
        _$failedField = 'qualifications';
        _qualifications?.build();
        _$failedField = 'skills';
        _skills?.build();
        _$failedField = 'experiences';
        _experiences?.build();
        _$failedField = 'requirements';
        _requirements?.build();

        _$failedField = 'selfAssesment';
        _selfAssesment?.build();

        _$failedField = 'categorySkills';
        _categorySkills?.build();

        _$failedField = 'highlightProjects';
        _highlightProjects?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'UserDetailsSerializers', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
