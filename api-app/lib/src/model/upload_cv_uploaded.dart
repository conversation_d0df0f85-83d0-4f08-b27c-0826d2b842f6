//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/json_object.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'upload_cv_uploaded.g.dart';

/// UploadCVUploaded
///
/// Properties:
/// * [cvId] 
/// * [cvData] 
@BuiltValue()
abstract class UploadCVUploaded implements Built<UploadCVUploaded, UploadCVUploadedBuilder> {
  @BuiltValueField(wireName: r'cv_id')
  String get cvId;

  @BuiltValueField(wireName: r'cv_data')
  JsonObject get cvData;

  UploadCVUploaded._();

  factory UploadCVUploaded([void updates(UploadCVUploadedBuilder b)]) = _$UploadCVUploaded;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(UploadCVUploadedBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<UploadCVUploaded> get serializer => _$UploadCVUploadedSerializer();
}

class _$UploadCVUploadedSerializer implements PrimitiveSerializer<UploadCVUploaded> {
  @override
  final Iterable<Type> types = const [UploadCVUploaded, _$UploadCVUploaded];

  @override
  final String wireName = r'UploadCVUploaded';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    UploadCVUploaded object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'cv_id';
    yield serializers.serialize(
      object.cvId,
      specifiedType: const FullType(String),
    );
    yield r'cv_data';
    yield serializers.serialize(
      object.cvData,
      specifiedType: const FullType(JsonObject),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    UploadCVUploaded object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required UploadCVUploadedBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'cv_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.cvId = valueDes;
          break;
        case r'cv_data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(JsonObject),
          ) as JsonObject;
          result.cvData = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  UploadCVUploaded deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = UploadCVUploadedBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

