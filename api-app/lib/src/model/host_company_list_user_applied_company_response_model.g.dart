// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'host_company_list_user_applied_company_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$HostCompanyListUserAppliedCompanyResponseModel
    extends HostCompanyListUserAppliedCompanyResponseModel {
  @override
  final String? message;
  @override
  final BuiltList<ErrorDetail>? errors;
  @override
  final HostCompanyListAppliedEngineer data;

  factory _$HostCompanyListUserAppliedCompanyResponseModel(
          [void Function(HostCompanyListUserAppliedCompanyResponseModelBuilder)?
              updates]) =>
      (new HostCompanyListUserAppliedCompanyResponseModelBuilder()
            ..update(updates))
          ._build();

  _$HostCompanyListUserAppliedCompanyResponseModel._(
      {this.message, this.errors, required this.data})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'HostCompanyListUserAppliedCompanyResponseModel', 'data');
  }

  @override
  HostCompanyListUserAppliedCompanyResponseModel rebuild(
          void Function(HostCompanyListUserAppliedCompanyResponseModelBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  HostCompanyListUserAppliedCompanyResponseModelBuilder toBuilder() =>
      new HostCompanyListUserAppliedCompanyResponseModelBuilder()
        ..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is HostCompanyListUserAppliedCompanyResponseModel &&
        message == other.message &&
        errors == other.errors &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'HostCompanyListUserAppliedCompanyResponseModel')
          ..add('message', message)
          ..add('errors', errors)
          ..add('data', data))
        .toString();
  }
}

class HostCompanyListUserAppliedCompanyResponseModelBuilder
    implements
        Builder<HostCompanyListUserAppliedCompanyResponseModel,
            HostCompanyListUserAppliedCompanyResponseModelBuilder> {
  _$HostCompanyListUserAppliedCompanyResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  HostCompanyListAppliedEngineerBuilder? _data;
  HostCompanyListAppliedEngineerBuilder get data =>
      _$this._data ??= new HostCompanyListAppliedEngineerBuilder();
  set data(HostCompanyListAppliedEngineerBuilder? data) => _$this._data = data;

  HostCompanyListUserAppliedCompanyResponseModelBuilder() {
    HostCompanyListUserAppliedCompanyResponseModel._defaults(this);
  }

  HostCompanyListUserAppliedCompanyResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _errors = $v.errors?.toBuilder();
      _data = $v.data.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(HostCompanyListUserAppliedCompanyResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$HostCompanyListUserAppliedCompanyResponseModel;
  }

  @override
  void update(
      void Function(HostCompanyListUserAppliedCompanyResponseModelBuilder)?
          updates) {
    if (updates != null) updates(this);
  }

  @override
  HostCompanyListUserAppliedCompanyResponseModel build() => _build();

  _$HostCompanyListUserAppliedCompanyResponseModel _build() {
    _$HostCompanyListUserAppliedCompanyResponseModel _$result;
    try {
      _$result = _$v ??
          new _$HostCompanyListUserAppliedCompanyResponseModel._(
            message: message,
            errors: _errors?.build(),
            data: data.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'errors';
        _errors?.build();
        _$failedField = 'data';
        data.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'HostCompanyListUserAppliedCompanyResponseModel',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
