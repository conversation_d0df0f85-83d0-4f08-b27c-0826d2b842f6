// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'upload_cv_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UploadCVResponse extends UploadCVResponse {
  @override
  final String cvId;
  @override
  final JsonObject cvData;

  factory _$UploadCVResponse(
          [void Function(UploadCVResponseBuilder)? updates]) =>
      (new UploadCVResponseBuilder()..update(updates))._build();

  _$UploadCVResponse._({required this.cvId, required this.cvData}) : super._() {
    BuiltValueNullFieldError.checkNotNull(cvId, r'UploadCVResponse', 'cvId');
    BuiltValueNullFieldError.checkNotNull(
        cvData, r'UploadCVResponse', 'cvData');
  }

  @override
  UploadCVResponse rebuild(void Function(UploadCVResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UploadCVResponseBuilder toBuilder() =>
      new UploadCVResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UploadCVResponse &&
        cvId == other.cvId &&
        cvData == other.cvData;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, cvId.hashCode);
    _$hash = $jc(_$hash, cvData.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UploadCVResponse')
          ..add('cvId', cvId)
          ..add('cvData', cvData))
        .toString();
  }
}

class UploadCVResponseBuilder
    implements Builder<UploadCVResponse, UploadCVResponseBuilder> {
  _$UploadCVResponse? _$v;

  String? _cvId;
  String? get cvId => _$this._cvId;
  set cvId(String? cvId) => _$this._cvId = cvId;

  JsonObject? _cvData;
  JsonObject? get cvData => _$this._cvData;
  set cvData(JsonObject? cvData) => _$this._cvData = cvData;

  UploadCVResponseBuilder() {
    UploadCVResponse._defaults(this);
  }

  UploadCVResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _cvId = $v.cvId;
      _cvData = $v.cvData;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UploadCVResponse other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$UploadCVResponse;
  }

  @override
  void update(void Function(UploadCVResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UploadCVResponse build() => _build();

  _$UploadCVResponse _build() {
    final _$result = _$v ??
        new _$UploadCVResponse._(
          cvId: BuiltValueNullFieldError.checkNotNull(
              cvId, r'UploadCVResponse', 'cvId'),
          cvData: BuiltValueNullFieldError.checkNotNull(
              cvData, r'UploadCVResponse', 'cvData'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
