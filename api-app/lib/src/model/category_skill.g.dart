// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'category_skill.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$CategorySkill extends CategorySkill {
  @override
  final BuiltList<EngSkill> skills;
  @override
  final String categoryId;

  factory _$CategorySkill([void Function(CategorySkillBuilder)? updates]) =>
      (new CategorySkillBuilder()..update(updates))._build();

  _$CategorySkill._({required this.skills, required this.categoryId})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(skills, r'CategorySkill', 'skills');
    BuiltValueNullFieldError.checkNotNull(
        categoryId, r'CategorySkill', 'categoryId');
  }

  @override
  CategorySkill rebuild(void Function(CategorySkillBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  CategorySkillBuilder toBuilder() => new CategorySkillBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is CategorySkill &&
        skills == other.skills &&
        categoryId == other.categoryId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, skills.hashCode);
    _$hash = $jc(_$hash, categoryId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'CategorySkill')
          ..add('skills', skills)
          ..add('categoryId', categoryId))
        .toString();
  }
}

class CategorySkillBuilder
    implements Builder<CategorySkill, CategorySkillBuilder> {
  _$CategorySkill? _$v;

  ListBuilder<EngSkill>? _skills;
  ListBuilder<EngSkill> get skills =>
      _$this._skills ??= new ListBuilder<EngSkill>();
  set skills(ListBuilder<EngSkill>? skills) => _$this._skills = skills;

  String? _categoryId;
  String? get categoryId => _$this._categoryId;
  set categoryId(String? categoryId) => _$this._categoryId = categoryId;

  CategorySkillBuilder() {
    CategorySkill._defaults(this);
  }

  CategorySkillBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _skills = $v.skills.toBuilder();
      _categoryId = $v.categoryId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(CategorySkill other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$CategorySkill;
  }

  @override
  void update(void Function(CategorySkillBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  CategorySkill build() => _build();

  _$CategorySkill _build() {
    _$CategorySkill _$result;
    try {
      _$result = _$v ??
          new _$CategorySkill._(
            skills: skills.build(),
            categoryId: BuiltValueNullFieldError.checkNotNull(
                categoryId, r'CategorySkill', 'categoryId'),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'skills';
        skills.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'CategorySkill', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
