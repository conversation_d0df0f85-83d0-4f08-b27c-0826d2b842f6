//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/error_detail.dart';
import 'package:openapi/src/model/recruit_get_contract_details.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'recruit_contract_details_response_model.g.dart';

/// RecruitContractDetailsResponseModel
///
/// Properties:
/// * [message] 
/// * [errors] 
/// * [data] 
@BuiltValue()
abstract class RecruitContractDetailsResponseModel implements Built<RecruitContractDetailsResponseModel, RecruitContractDetailsResponseModelBuilder> {
  @BuiltValueField(wireName: r'message')
  String? get message;

  @BuiltValueField(wireName: r'errors')
  BuiltList<ErrorDetail>? get errors;

  @BuiltValueField(wireName: r'data')
  RecruitGetContractDetails get data;

  RecruitContractDetailsResponseModel._();

  factory RecruitContractDetailsResponseModel([void updates(RecruitContractDetailsResponseModelBuilder b)]) = _$RecruitContractDetailsResponseModel;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(RecruitContractDetailsResponseModelBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<RecruitContractDetailsResponseModel> get serializer => _$RecruitContractDetailsResponseModelSerializer();
}

class _$RecruitContractDetailsResponseModelSerializer implements PrimitiveSerializer<RecruitContractDetailsResponseModel> {
  @override
  final Iterable<Type> types = const [RecruitContractDetailsResponseModel, _$RecruitContractDetailsResponseModel];

  @override
  final String wireName = r'RecruitContractDetailsResponseModel';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    RecruitContractDetailsResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'message';
    yield object.message == null ? null : serializers.serialize(
      object.message,
      specifiedType: const FullType.nullable(String),
    );
    yield r'errors';
    yield object.errors == null ? null : serializers.serialize(
      object.errors,
      specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
    );
    yield r'data';
    yield serializers.serialize(
      object.data,
      specifiedType: const FullType(RecruitGetContractDetails),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    RecruitContractDetailsResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required RecruitContractDetailsResponseModelBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'message':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.message = valueDes;
          break;
        case r'errors':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
          ) as BuiltList<ErrorDetail>?;
          if (valueDes == null) continue;
          result.errors.replace(valueDes);
          break;
        case r'data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(RecruitGetContractDetails),
          ) as RecruitGetContractDetails;
          result.data.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  RecruitContractDetailsResponseModel deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = RecruitContractDetailsResponseModelBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

