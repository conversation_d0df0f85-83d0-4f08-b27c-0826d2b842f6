// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_email_detail_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GetEmailDetailResponseModel extends GetEmailDetailResponseModel {
  @override
  final String? message;
  @override
  final EmailSchedule data;
  @override
  final BuiltList<ErrorDetail>? errors;

  factory _$GetEmailDetailResponseModel(
          [void Function(GetEmailDetailResponseModelBuilder)? updates]) =>
      (new GetEmailDetailResponseModelBuilder()..update(updates))._build();

  _$GetEmailDetailResponseModel._(
      {this.message, required this.data, this.errors})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'GetEmailDetailResponseModel', 'data');
  }

  @override
  GetEmailDetailResponseModel rebuild(
          void Function(GetEmailDetailResponseModelBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GetEmailDetailResponseModelBuilder toBuilder() =>
      new GetEmailDetailResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GetEmailDetailResponseModel &&
        message == other.message &&
        data == other.data &&
        errors == other.errors;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GetEmailDetailResponseModel')
          ..add('message', message)
          ..add('data', data)
          ..add('errors', errors))
        .toString();
  }
}

class GetEmailDetailResponseModelBuilder
    implements
        Builder<GetEmailDetailResponseModel,
            GetEmailDetailResponseModelBuilder> {
  _$GetEmailDetailResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  EmailScheduleBuilder? _data;
  EmailScheduleBuilder get data => _$this._data ??= new EmailScheduleBuilder();
  set data(EmailScheduleBuilder? data) => _$this._data = data;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  GetEmailDetailResponseModelBuilder() {
    GetEmailDetailResponseModel._defaults(this);
  }

  GetEmailDetailResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _data = $v.data.toBuilder();
      _errors = $v.errors?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GetEmailDetailResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GetEmailDetailResponseModel;
  }

  @override
  void update(void Function(GetEmailDetailResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GetEmailDetailResponseModel build() => _build();

  _$GetEmailDetailResponseModel _build() {
    _$GetEmailDetailResponseModel _$result;
    try {
      _$result = _$v ??
          new _$GetEmailDetailResponseModel._(
            message: message,
            data: data.build(),
            errors: _errors?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        data.build();
        _$failedField = 'errors';
        _errors?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'GetEmailDetailResponseModel', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
