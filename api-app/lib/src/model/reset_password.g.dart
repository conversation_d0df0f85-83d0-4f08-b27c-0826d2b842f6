// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'reset_password.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ResetPassword extends ResetPassword {
  @override
  final String email;

  factory _$ResetPassword([void Function(ResetPasswordBuilder)? updates]) =>
      (new ResetPasswordBuilder()..update(updates))._build();

  _$ResetPassword._({required this.email}) : super._() {
    BuiltValueNullFieldError.checkNotNull(email, r'ResetPassword', 'email');
  }

  @override
  ResetPassword rebuild(void Function(ResetPasswordBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ResetPasswordBuilder toBuilder() => new ResetPasswordBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ResetPassword && email == other.email;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, email.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ResetPassword')..add('email', email))
        .toString();
  }
}

class ResetPasswordBuilder
    implements Builder<ResetPassword, ResetPasswordBuilder> {
  _$ResetPassword? _$v;

  String? _email;
  String? get email => _$this._email;
  set email(String? email) => _$this._email = email;

  ResetPasswordBuilder() {
    ResetPassword._defaults(this);
  }

  ResetPasswordBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _email = $v.email;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ResetPassword other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$ResetPassword;
  }

  @override
  void update(void Function(ResetPasswordBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ResetPassword build() => _build();

  _$ResetPassword _build() {
    final _$result = _$v ??
        new _$ResetPassword._(
          email: BuiltValueNullFieldError.checkNotNull(
              email, r'ResetPassword', 'email'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
