//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/date.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'eng_academic.g.dart';

/// EngAcademic
///
/// Properties:
/// * [engineerId] 
/// * [school] 
/// * [type] 
/// * [outDate] 
/// * [faculty] 
/// * [typeName] 
@BuiltValue()
abstract class EngAcademic implements Built<EngAcademic, EngAcademicBuilder> {
  @BuiltValueField(wireName: r'engineer_id')
  int get engineerId;

  @BuiltValueField(wireName: r'school')
  String? get school;

  @BuiltValueField(wireName: r'type')
  int? get type;

  @BuiltValueField(wireName: r'out_date')
  Date? get outDate;

  @BuiltValueField(wireName: r'faculty')
  String? get faculty;

  @BuiltValueField(wireName: r'type_name')
  String? get typeName;

  EngAcademic._();

  factory EngAcademic([void updates(EngAcademicBuilder b)]) = _$EngAcademic;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(EngAcademicBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<EngAcademic> get serializer => _$EngAcademicSerializer();
}

class _$EngAcademicSerializer implements PrimitiveSerializer<EngAcademic> {
  @override
  final Iterable<Type> types = const [EngAcademic, _$EngAcademic];

  @override
  final String wireName = r'EngAcademic';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    EngAcademic object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'engineer_id';
    yield serializers.serialize(
      object.engineerId,
      specifiedType: const FullType(int),
    );
    if (object.school != null) {
      yield r'school';
      yield serializers.serialize(
        object.school,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.type != null) {
      yield r'type';
      yield serializers.serialize(
        object.type,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.outDate != null) {
      yield r'out_date';
      yield serializers.serialize(
        object.outDate,
        specifiedType: const FullType.nullable(Date),
      );
    }
    if (object.faculty != null) {
      yield r'faculty';
      yield serializers.serialize(
        object.faculty,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.typeName != null) {
      yield r'type_name';
      yield serializers.serialize(
        object.typeName,
        specifiedType: const FullType(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    EngAcademic object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required EngAcademicBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'engineer_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.engineerId = valueDes;
          break;
        case r'school':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.school = valueDes;
          break;
        case r'type':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.type = valueDes;
          break;
        case r'out_date':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(Date),
          ) as Date?;
          if (valueDes == null) continue;
          result.outDate = valueDes;
          break;
        case r'faculty':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.faculty = valueDes;
          break;
        case r'type_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.typeName = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  EngAcademic deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = EngAcademicBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

