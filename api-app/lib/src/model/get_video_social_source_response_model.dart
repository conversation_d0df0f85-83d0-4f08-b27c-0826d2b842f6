//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'get_video_social_source_response_model.g.dart';

/// GetVideoSocialSourceResponseModel
///
/// Properties:
/// * [data] 
@BuiltValue()
abstract class GetVideoSocialSourceResponseModel implements Built<GetVideoSocialSourceResponseModel, GetVideoSocialSourceResponseModelBuilder> {
  @BuiltValueField(wireName: r'data')
  String get data;

  GetVideoSocialSourceResponseModel._();

  factory GetVideoSocialSourceResponseModel([void updates(GetVideoSocialSourceResponseModelBuilder b)]) = _$GetVideoSocialSourceResponseModel;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(GetVideoSocialSourceResponseModelBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<GetVideoSocialSourceResponseModel> get serializer => _$GetVideoSocialSourceResponseModelSerializer();
}

class _$GetVideoSocialSourceResponseModelSerializer implements PrimitiveSerializer<GetVideoSocialSourceResponseModel> {
  @override
  final Iterable<Type> types = const [GetVideoSocialSourceResponseModel, _$GetVideoSocialSourceResponseModel];

  @override
  final String wireName = r'GetVideoSocialSourceResponseModel';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    GetVideoSocialSourceResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'data';
    yield serializers.serialize(
      object.data,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    GetVideoSocialSourceResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required GetVideoSocialSourceResponseModelBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.data = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  GetVideoSocialSourceResponseModel deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = GetVideoSocialSourceResponseModelBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

