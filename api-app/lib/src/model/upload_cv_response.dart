//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/json_object.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'upload_cv_response.g.dart';

/// UploadCVResponse
///
/// Properties:
/// * [cvId] 
/// * [cvData] 
@BuiltValue()
abstract class UploadCVResponse implements Built<UploadCVResponse, UploadCVResponseBuilder> {
  @BuiltValueField(wireName: r'cv_id')
  String get cvId;

  @BuiltValueField(wireName: r'cv_data')
  JsonObject get cvData;

  UploadCVResponse._();

  factory UploadCVResponse([void updates(UploadCVResponseBuilder b)]) = _$UploadCVResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(UploadCVResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<UploadCVResponse> get serializer => _$UploadCVResponseSerializer();
}

class _$UploadCVResponseSerializer implements PrimitiveSerializer<UploadCVResponse> {
  @override
  final Iterable<Type> types = const [UploadCVResponse, _$UploadCVResponse];

  @override
  final String wireName = r'UploadCVResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    UploadCVResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'cv_id';
    yield serializers.serialize(
      object.cvId,
      specifiedType: const FullType(String),
    );
    yield r'cv_data';
    yield serializers.serialize(
      object.cvData,
      specifiedType: const FullType(JsonObject),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    UploadCVResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required UploadCVResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'cv_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.cvId = valueDes;
          break;
        case r'cv_data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(JsonObject),
          ) as JsonObject;
          result.cvData = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  UploadCVResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = UploadCVResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

