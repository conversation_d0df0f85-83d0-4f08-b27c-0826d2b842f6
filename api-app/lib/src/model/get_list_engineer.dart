//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/eng_skill.dart';
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/eng_hope.dart';
import 'package:openapi/src/model/eng_language.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'get_list_engineer.g.dart';

/// GetListEngineer
///
/// Properties:
/// * [profileImagePath] 
/// * [email] 
/// * [firstName] 
/// * [lastName] 
/// * [countryCode] 
/// * [updated] 
/// * [created] 
/// * [createdUser] 
/// * [skills] 
/// * [userId] 
/// * [lastAcademicName] 
/// * [facebookUrl] 
/// * [linkedinUrl] 
/// * [whatsappUrl] 
/// * [zaloId] 
/// * [requirements] 
/// * [languages] 
/// * [employmentStatus] 
/// * [salesMemo] 
/// * [isDataPolicyAccept] 
/// * [tel] 
/// * [internationalTel] 
@BuiltValue()
abstract class GetListEngineer implements Built<GetListEngineer, GetListEngineerBuilder> {
  @BuiltValueField(wireName: r'profile_image_path')
  String? get profileImagePath;

  @BuiltValueField(wireName: r'email')
  String get email;

  @BuiltValueField(wireName: r'first_name')
  String? get firstName;

  @BuiltValueField(wireName: r'last_name')
  String? get lastName;

  @BuiltValueField(wireName: r'country_code')
  String? get countryCode;

  @BuiltValueField(wireName: r'updated')
  DateTime? get updated;

  @BuiltValueField(wireName: r'created')
  DateTime? get created;

  @BuiltValueField(wireName: r'created_user')
  String? get createdUser;

  @BuiltValueField(wireName: r'skills')
  BuiltList<EngSkill>? get skills;

  @BuiltValueField(wireName: r'user_id')
  int? get userId;

  @BuiltValueField(wireName: r'last_academic_name')
  String? get lastAcademicName;

  @BuiltValueField(wireName: r'facebook_url')
  String? get facebookUrl;

  @BuiltValueField(wireName: r'linkedin_url')
  String? get linkedinUrl;

  @BuiltValueField(wireName: r'whatsapp_url')
  String? get whatsappUrl;

  @BuiltValueField(wireName: r'zalo_id')
  String? get zaloId;

  @BuiltValueField(wireName: r'requirements')
  EngHope? get requirements;

  @BuiltValueField(wireName: r'languages')
  BuiltList<EngLanguage>? get languages;

  @BuiltValueField(wireName: r'employment_status')
  int? get employmentStatus;

  @BuiltValueField(wireName: r'sales_memo')
  String? get salesMemo;

  @BuiltValueField(wireName: r'is_data_policy_accept')
  int? get isDataPolicyAccept;

  @BuiltValueField(wireName: r'tel')
  String? get tel;

  @BuiltValueField(wireName: r'international_tel')
  String? get internationalTel;

  GetListEngineer._();

  factory GetListEngineer([void updates(GetListEngineerBuilder b)]) = _$GetListEngineer;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(GetListEngineerBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<GetListEngineer> get serializer => _$GetListEngineerSerializer();
}

class _$GetListEngineerSerializer implements PrimitiveSerializer<GetListEngineer> {
  @override
  final Iterable<Type> types = const [GetListEngineer, _$GetListEngineer];

  @override
  final String wireName = r'GetListEngineer';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    GetListEngineer object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.profileImagePath != null) {
      yield r'profile_image_path';
      yield serializers.serialize(
        object.profileImagePath,
        specifiedType: const FullType.nullable(String),
      );
    }
    yield r'email';
    yield serializers.serialize(
      object.email,
      specifiedType: const FullType(String),
    );
    if (object.firstName != null) {
      yield r'first_name';
      yield serializers.serialize(
        object.firstName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.lastName != null) {
      yield r'last_name';
      yield serializers.serialize(
        object.lastName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.countryCode != null) {
      yield r'country_code';
      yield serializers.serialize(
        object.countryCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.updated != null) {
      yield r'updated';
      yield serializers.serialize(
        object.updated,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.created != null) {
      yield r'created';
      yield serializers.serialize(
        object.created,
        specifiedType: const FullType(DateTime),
      );
    }
    if (object.createdUser != null) {
      yield r'created_user';
      yield serializers.serialize(
        object.createdUser,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.skills != null) {
      yield r'skills';
      yield serializers.serialize(
        object.skills,
        specifiedType: const FullType(BuiltList, [FullType(EngSkill)]),
      );
    }
    if (object.userId != null) {
      yield r'user_id';
      yield serializers.serialize(
        object.userId,
        specifiedType: const FullType(int),
      );
    }
    if (object.lastAcademicName != null) {
      yield r'last_academic_name';
      yield serializers.serialize(
        object.lastAcademicName,
        specifiedType: const FullType(String),
      );
    }
    if (object.facebookUrl != null) {
      yield r'facebook_url';
      yield serializers.serialize(
        object.facebookUrl,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.linkedinUrl != null) {
      yield r'linkedin_url';
      yield serializers.serialize(
        object.linkedinUrl,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.whatsappUrl != null) {
      yield r'whatsapp_url';
      yield serializers.serialize(
        object.whatsappUrl,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.zaloId != null) {
      yield r'zalo_id';
      yield serializers.serialize(
        object.zaloId,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.requirements != null) {
      yield r'requirements';
      yield serializers.serialize(
        object.requirements,
        specifiedType: const FullType(EngHope),
      );
    }
    if (object.languages != null) {
      yield r'languages';
      yield serializers.serialize(
        object.languages,
        specifiedType: const FullType(BuiltList, [FullType(EngLanguage)]),
      );
    }
    yield r'employment_status';
    yield object.employmentStatus == null ? null : serializers.serialize(
      object.employmentStatus,
      specifiedType: const FullType.nullable(int),
    );
    yield r'sales_memo';
    yield object.salesMemo == null ? null : serializers.serialize(
      object.salesMemo,
      specifiedType: const FullType.nullable(String),
    );
    yield r'is_data_policy_accept';
    yield object.isDataPolicyAccept == null ? null : serializers.serialize(
      object.isDataPolicyAccept,
      specifiedType: const FullType.nullable(int),
    );
    if (object.tel != null) {
      yield r'tel';
      yield serializers.serialize(
        object.tel,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.internationalTel != null) {
      yield r'international_tel';
      yield serializers.serialize(
        object.internationalTel,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    GetListEngineer object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required GetListEngineerBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'profile_image_path':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.profileImagePath = valueDes;
          break;
        case r'email':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.email = valueDes;
          break;
        case r'first_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.firstName = valueDes;
          break;
        case r'last_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.lastName = valueDes;
          break;
        case r'country_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.countryCode = valueDes;
          break;
        case r'updated':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.updated = valueDes;
          break;
        case r'created':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(DateTime),
          ) as DateTime;
          result.created = valueDes;
          break;
        case r'created_user':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.createdUser = valueDes;
          break;
        case r'skills':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(EngSkill)]),
          ) as BuiltList<EngSkill>;
          result.skills.replace(valueDes);
          break;
        case r'user_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.userId = valueDes;
          break;
        case r'last_academic_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.lastAcademicName = valueDes;
          break;
        case r'facebook_url':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.facebookUrl = valueDes;
          break;
        case r'linkedin_url':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.linkedinUrl = valueDes;
          break;
        case r'whatsapp_url':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.whatsappUrl = valueDes;
          break;
        case r'zalo_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.zaloId = valueDes;
          break;
        case r'requirements':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(EngHope),
          ) as EngHope;
          result.requirements.replace(valueDes);
          break;
        case r'languages':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(EngLanguage)]),
          ) as BuiltList<EngLanguage>;
          result.languages.replace(valueDes);
          break;
        case r'employment_status':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.employmentStatus = valueDes;
          break;
        case r'sales_memo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.salesMemo = valueDes;
          break;
        case r'is_data_policy_accept':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.isDataPolicyAccept = valueDes;
          break;
        case r'tel':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.tel = valueDes;
          break;
        case r'international_tel':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.internationalTel = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  GetListEngineer deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = GetListEngineerBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

