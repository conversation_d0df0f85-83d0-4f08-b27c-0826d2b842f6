// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_company_list_applied_engineer.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GeneralCompanyListAppliedEngineer
    extends GeneralCompanyListAppliedEngineer {
  @override
  final BuiltList<GeneralCompanyAppliedEngineers> data;
  @override
  final int totalEngineers;
  @override
  final int? hostCompanyId;

  factory _$GeneralCompanyListAppliedEngineer(
          [void Function(GeneralCompanyListAppliedEngineerBuilder)? updates]) =>
      (new GeneralCompanyListAppliedEngineerBuilder()..update(updates))
          ._build();

  _$GeneralCompanyListAppliedEngineer._(
      {required this.data, required this.totalEngineers, this.hostCompanyId})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'GeneralCompanyListAppliedEngineer', 'data');
    BuiltValueNullFieldError.checkNotNull(
        totalEngineers, r'GeneralCompanyListAppliedEngineer', 'totalEngineers');
  }

  @override
  GeneralCompanyListAppliedEngineer rebuild(
          void Function(GeneralCompanyListAppliedEngineerBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GeneralCompanyListAppliedEngineerBuilder toBuilder() =>
      new GeneralCompanyListAppliedEngineerBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GeneralCompanyListAppliedEngineer &&
        data == other.data &&
        totalEngineers == other.totalEngineers &&
        hostCompanyId == other.hostCompanyId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, totalEngineers.hashCode);
    _$hash = $jc(_$hash, hostCompanyId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GeneralCompanyListAppliedEngineer')
          ..add('data', data)
          ..add('totalEngineers', totalEngineers)
          ..add('hostCompanyId', hostCompanyId))
        .toString();
  }
}

class GeneralCompanyListAppliedEngineerBuilder
    implements
        Builder<GeneralCompanyListAppliedEngineer,
            GeneralCompanyListAppliedEngineerBuilder> {
  _$GeneralCompanyListAppliedEngineer? _$v;

  ListBuilder<GeneralCompanyAppliedEngineers>? _data;
  ListBuilder<GeneralCompanyAppliedEngineers> get data =>
      _$this._data ??= new ListBuilder<GeneralCompanyAppliedEngineers>();
  set data(ListBuilder<GeneralCompanyAppliedEngineers>? data) =>
      _$this._data = data;

  int? _totalEngineers;
  int? get totalEngineers => _$this._totalEngineers;
  set totalEngineers(int? totalEngineers) =>
      _$this._totalEngineers = totalEngineers;

  int? _hostCompanyId;
  int? get hostCompanyId => _$this._hostCompanyId;
  set hostCompanyId(int? hostCompanyId) =>
      _$this._hostCompanyId = hostCompanyId;

  GeneralCompanyListAppliedEngineerBuilder() {
    GeneralCompanyListAppliedEngineer._defaults(this);
  }

  GeneralCompanyListAppliedEngineerBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _data = $v.data.toBuilder();
      _totalEngineers = $v.totalEngineers;
      _hostCompanyId = $v.hostCompanyId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GeneralCompanyListAppliedEngineer other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GeneralCompanyListAppliedEngineer;
  }

  @override
  void update(
      void Function(GeneralCompanyListAppliedEngineerBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GeneralCompanyListAppliedEngineer build() => _build();

  _$GeneralCompanyListAppliedEngineer _build() {
    _$GeneralCompanyListAppliedEngineer _$result;
    try {
      _$result = _$v ??
          new _$GeneralCompanyListAppliedEngineer._(
            data: data.build(),
            totalEngineers: BuiltValueNullFieldError.checkNotNull(
                totalEngineers,
                r'GeneralCompanyListAppliedEngineer',
                'totalEngineers'),
            hostCompanyId: hostCompanyId,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        data.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'GeneralCompanyListAppliedEngineer', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
