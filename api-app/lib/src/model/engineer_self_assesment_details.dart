//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'engineer_self_assesment_details.g.dart';

/// EngineerSelfAssesmentDetails
///
/// Properties:
/// * [remoteExpYears] 
/// * [remoteJobDescription] 
/// * [totalRemoteSkill] 
/// * [totalGlobalSkill] 
/// * [totalCommunicationSkill] 
/// * [totalReportSkill] 
/// * [totalManagementSkill] 
/// * [socialStyle] 
/// * [durabilityScore] 
/// * [globalWorkExp] 
@BuiltValue()
abstract class EngineerSelfAssesmentDetails implements Built<EngineerSelfAssesmentDetails, EngineerSelfAssesmentDetailsBuilder> {
  @BuiltValueField(wireName: r'remote_exp_years')
  int? get remoteExpYears;

  @BuiltValueField(wireName: r'remote_job_description')
  String? get remoteJobDescription;

  @BuiltValueField(wireName: r'total_remote_skill')
  int? get totalRemoteSkill;

  @BuiltValueField(wireName: r'total_global_skill')
  int? get totalGlobalSkill;

  @BuiltValueField(wireName: r'total_communication_skill')
  int? get totalCommunicationSkill;

  @BuiltValueField(wireName: r'total_report_skill')
  int? get totalReportSkill;

  @BuiltValueField(wireName: r'total_management_skill')
  int? get totalManagementSkill;

  @BuiltValueField(wireName: r'social_style')
  int? get socialStyle;

  @BuiltValueField(wireName: r'durability_score')
  int? get durabilityScore;

  @BuiltValueField(wireName: r'global_work_exp')
  int? get globalWorkExp;

  EngineerSelfAssesmentDetails._();

  factory EngineerSelfAssesmentDetails([void updates(EngineerSelfAssesmentDetailsBuilder b)]) = _$EngineerSelfAssesmentDetails;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(EngineerSelfAssesmentDetailsBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<EngineerSelfAssesmentDetails> get serializer => _$EngineerSelfAssesmentDetailsSerializer();
}

class _$EngineerSelfAssesmentDetailsSerializer implements PrimitiveSerializer<EngineerSelfAssesmentDetails> {
  @override
  final Iterable<Type> types = const [EngineerSelfAssesmentDetails, _$EngineerSelfAssesmentDetails];

  @override
  final String wireName = r'EngineerSelfAssesmentDetails';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    EngineerSelfAssesmentDetails object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'remote_exp_years';
    yield object.remoteExpYears == null ? null : serializers.serialize(
      object.remoteExpYears,
      specifiedType: const FullType.nullable(int),
    );
    yield r'remote_job_description';
    yield object.remoteJobDescription == null ? null : serializers.serialize(
      object.remoteJobDescription,
      specifiedType: const FullType.nullable(String),
    );
    yield r'total_remote_skill';
    yield object.totalRemoteSkill == null ? null : serializers.serialize(
      object.totalRemoteSkill,
      specifiedType: const FullType.nullable(int),
    );
    yield r'total_global_skill';
    yield object.totalGlobalSkill == null ? null : serializers.serialize(
      object.totalGlobalSkill,
      specifiedType: const FullType.nullable(int),
    );
    yield r'total_communication_skill';
    yield object.totalCommunicationSkill == null ? null : serializers.serialize(
      object.totalCommunicationSkill,
      specifiedType: const FullType.nullable(int),
    );
    yield r'total_report_skill';
    yield object.totalReportSkill == null ? null : serializers.serialize(
      object.totalReportSkill,
      specifiedType: const FullType.nullable(int),
    );
    yield r'total_management_skill';
    yield object.totalManagementSkill == null ? null : serializers.serialize(
      object.totalManagementSkill,
      specifiedType: const FullType.nullable(int),
    );
    yield r'social_style';
    yield object.socialStyle == null ? null : serializers.serialize(
      object.socialStyle,
      specifiedType: const FullType.nullable(int),
    );
    yield r'durability_score';
    yield object.durabilityScore == null ? null : serializers.serialize(
      object.durabilityScore,
      specifiedType: const FullType.nullable(int),
    );
    yield r'global_work_exp';
    yield object.globalWorkExp == null ? null : serializers.serialize(
      object.globalWorkExp,
      specifiedType: const FullType.nullable(int),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    EngineerSelfAssesmentDetails object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required EngineerSelfAssesmentDetailsBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'remote_exp_years':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.remoteExpYears = valueDes;
          break;
        case r'remote_job_description':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.remoteJobDescription = valueDes;
          break;
        case r'total_remote_skill':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.totalRemoteSkill = valueDes;
          break;
        case r'total_global_skill':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.totalGlobalSkill = valueDes;
          break;
        case r'total_communication_skill':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.totalCommunicationSkill = valueDes;
          break;
        case r'total_report_skill':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.totalReportSkill = valueDes;
          break;
        case r'total_management_skill':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.totalManagementSkill = valueDes;
          break;
        case r'social_style':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.socialStyle = valueDes;
          break;
        case r'durability_score':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.durabilityScore = valueDes;
          break;
        case r'global_work_exp':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.globalWorkExp = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  EngineerSelfAssesmentDetails deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = EngineerSelfAssesmentDetailsBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

