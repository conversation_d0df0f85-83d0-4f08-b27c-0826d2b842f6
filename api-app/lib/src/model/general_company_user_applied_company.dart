//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/eng_skill.dart';
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/eng_license.dart';
import 'package:openapi/src/model/eng_hope.dart';
import 'package:openapi/src/model/eng_work_history.dart';
import 'package:openapi/src/model/main_skill.dart';
import 'package:openapi/src/model/eng_language.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'general_company_user_applied_company.g.dart';

/// GeneralCompanyUserAppliedCompany
///
/// Properties:
/// * [userId] 
/// * [profileImagePath] 
/// * [firstName] 
/// * [lastName] 
/// * [age] 
/// * [languages] 
/// * [lastAcademicCode] 
/// * [interestedFlag] 
/// * [skills] 
/// * [countryCode] 
/// * [academicLevelName] 
/// * [nickname] 
/// * [schoolName] 
/// * [existApplyId] 
/// * [updated] 
/// * [qualifications] 
/// * [payrollPriceUsd] 
/// * [lastLogin] 
/// * [totalApplyCount] 
/// * [totalCompanyChangedCount] 
/// * [jobCodeHasWorkedRecently] 
/// * [totalRecruitProgressCodeActive] 
/// * [currentRecruitProgressCodeWithMyCompany] 
/// * [totalMatchSkillCodes] 
/// * [remoteCodeScore] 
/// * [salaryScore] 
/// * [totalScore] 
/// * [sexType] 
/// * [addressCode] 
/// * [mainSkill] 
/// * [hopeJobCodes] 
/// * [workHistories] 
/// * [underSelectionCount] 
/// * [requirements] 
@BuiltValue()
abstract class GeneralCompanyUserAppliedCompany implements Built<GeneralCompanyUserAppliedCompany, GeneralCompanyUserAppliedCompanyBuilder> {
  @BuiltValueField(wireName: r'user_id')
  int? get userId;

  @BuiltValueField(wireName: r'profile_image_path')
  String? get profileImagePath;

  @BuiltValueField(wireName: r'first_name')
  String? get firstName;

  @BuiltValueField(wireName: r'last_name')
  String? get lastName;

  @BuiltValueField(wireName: r'age')
  int? get age;

  @BuiltValueField(wireName: r'languages')
  BuiltList<EngLanguage>? get languages;

  @BuiltValueField(wireName: r'last_academic_code')
  String? get lastAcademicCode;

  @BuiltValueField(wireName: r'interested_flag')
  int? get interestedFlag;

  @BuiltValueField(wireName: r'skills')
  BuiltList<EngSkill>? get skills;

  @BuiltValueField(wireName: r'country_code')
  String? get countryCode;

  @BuiltValueField(wireName: r'academic_level_name')
  String? get academicLevelName;

  @BuiltValueField(wireName: r'nickname')
  String? get nickname;

  @BuiltValueField(wireName: r'school_name')
  String? get schoolName;

  @BuiltValueField(wireName: r'exist_apply_id')
  int? get existApplyId;

  @BuiltValueField(wireName: r'updated')
  DateTime? get updated;

  @BuiltValueField(wireName: r'qualifications')
  BuiltList<EngLicense>? get qualifications;

  @BuiltValueField(wireName: r'payroll_price_usd')
  String? get payrollPriceUsd;

  @BuiltValueField(wireName: r'last_login')
  DateTime? get lastLogin;

  @BuiltValueField(wireName: r'total_apply_count')
  int? get totalApplyCount;

  @BuiltValueField(wireName: r'total_company_changed_count')
  int? get totalCompanyChangedCount;

  @BuiltValueField(wireName: r'job_code_has_worked_recently')
  int? get jobCodeHasWorkedRecently;

  @BuiltValueField(wireName: r'total_recruit_progress_code_active')
  int? get totalRecruitProgressCodeActive;

  @BuiltValueField(wireName: r'current_recruit_progress_code_with_my_company')
  int? get currentRecruitProgressCodeWithMyCompany;

  @BuiltValueField(wireName: r'total_match_skill_codes')
  int? get totalMatchSkillCodes;

  @BuiltValueField(wireName: r'remote_code_score')
  int? get remoteCodeScore;

  @BuiltValueField(wireName: r'salary_score')
  int? get salaryScore;

  @BuiltValueField(wireName: r'total_score')
  int? get totalScore;

  @BuiltValueField(wireName: r'sex_type')
  int? get sexType;

  @BuiltValueField(wireName: r'address_code')
  String? get addressCode;

  @BuiltValueField(wireName: r'main_skill')
  BuiltList<MainSkill>? get mainSkill;

  @BuiltValueField(wireName: r'hope_job_codes')
  BuiltList<String?>? get hopeJobCodes;

  @BuiltValueField(wireName: r'work_histories')
  BuiltList<EngWorkHistory>? get workHistories;

  @BuiltValueField(wireName: r'under_selection_count')
  int? get underSelectionCount;

  @BuiltValueField(wireName: r'requirements')
  EngHope? get requirements;

  GeneralCompanyUserAppliedCompany._();

  factory GeneralCompanyUserAppliedCompany([void updates(GeneralCompanyUserAppliedCompanyBuilder b)]) = _$GeneralCompanyUserAppliedCompany;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(GeneralCompanyUserAppliedCompanyBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<GeneralCompanyUserAppliedCompany> get serializer => _$GeneralCompanyUserAppliedCompanySerializer();
}

class _$GeneralCompanyUserAppliedCompanySerializer implements PrimitiveSerializer<GeneralCompanyUserAppliedCompany> {
  @override
  final Iterable<Type> types = const [GeneralCompanyUserAppliedCompany, _$GeneralCompanyUserAppliedCompany];

  @override
  final String wireName = r'GeneralCompanyUserAppliedCompany';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    GeneralCompanyUserAppliedCompany object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.userId != null) {
      yield r'user_id';
      yield serializers.serialize(
        object.userId,
        specifiedType: const FullType(int),
      );
    }
    if (object.profileImagePath != null) {
      yield r'profile_image_path';
      yield serializers.serialize(
        object.profileImagePath,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.firstName != null) {
      yield r'first_name';
      yield serializers.serialize(
        object.firstName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.lastName != null) {
      yield r'last_name';
      yield serializers.serialize(
        object.lastName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.age != null) {
      yield r'age';
      yield serializers.serialize(
        object.age,
        specifiedType: const FullType(int),
      );
    }
    if (object.languages != null) {
      yield r'languages';
      yield serializers.serialize(
        object.languages,
        specifiedType: const FullType(BuiltList, [FullType(EngLanguage)]),
      );
    }
    if (object.lastAcademicCode != null) {
      yield r'last_academic_code';
      yield serializers.serialize(
        object.lastAcademicCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.interestedFlag != null) {
      yield r'interested_flag';
      yield serializers.serialize(
        object.interestedFlag,
        specifiedType: const FullType(int),
      );
    }
    if (object.skills != null) {
      yield r'skills';
      yield serializers.serialize(
        object.skills,
        specifiedType: const FullType(BuiltList, [FullType(EngSkill)]),
      );
    }
    if (object.countryCode != null) {
      yield r'country_code';
      yield serializers.serialize(
        object.countryCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.academicLevelName != null) {
      yield r'academic_level_name';
      yield serializers.serialize(
        object.academicLevelName,
        specifiedType: const FullType(String),
      );
    }
    if (object.nickname != null) {
      yield r'nickname';
      yield serializers.serialize(
        object.nickname,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.schoolName != null) {
      yield r'school_name';
      yield serializers.serialize(
        object.schoolName,
        specifiedType: const FullType(String),
      );
    }
    if (object.existApplyId != null) {
      yield r'exist_apply_id';
      yield serializers.serialize(
        object.existApplyId,
        specifiedType: const FullType(int),
      );
    }
    if (object.updated != null) {
      yield r'updated';
      yield serializers.serialize(
        object.updated,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.qualifications != null) {
      yield r'qualifications';
      yield serializers.serialize(
        object.qualifications,
        specifiedType: const FullType(BuiltList, [FullType(EngLicense)]),
      );
    }
    if (object.payrollPriceUsd != null) {
      yield r'payroll_price_usd';
      yield serializers.serialize(
        object.payrollPriceUsd,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.lastLogin != null) {
      yield r'last_login';
      yield serializers.serialize(
        object.lastLogin,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.totalApplyCount != null) {
      yield r'total_apply_count';
      yield serializers.serialize(
        object.totalApplyCount,
        specifiedType: const FullType(int),
      );
    }
    if (object.totalCompanyChangedCount != null) {
      yield r'total_company_changed_count';
      yield serializers.serialize(
        object.totalCompanyChangedCount,
        specifiedType: const FullType(int),
      );
    }
    if (object.jobCodeHasWorkedRecently != null) {
      yield r'job_code_has_worked_recently';
      yield serializers.serialize(
        object.jobCodeHasWorkedRecently,
        specifiedType: const FullType(int),
      );
    }
    if (object.totalRecruitProgressCodeActive != null) {
      yield r'total_recruit_progress_code_active';
      yield serializers.serialize(
        object.totalRecruitProgressCodeActive,
        specifiedType: const FullType(int),
      );
    }
    if (object.currentRecruitProgressCodeWithMyCompany != null) {
      yield r'current_recruit_progress_code_with_my_company';
      yield serializers.serialize(
        object.currentRecruitProgressCodeWithMyCompany,
        specifiedType: const FullType(int),
      );
    }
    if (object.totalMatchSkillCodes != null) {
      yield r'total_match_skill_codes';
      yield serializers.serialize(
        object.totalMatchSkillCodes,
        specifiedType: const FullType(int),
      );
    }
    if (object.remoteCodeScore != null) {
      yield r'remote_code_score';
      yield serializers.serialize(
        object.remoteCodeScore,
        specifiedType: const FullType(int),
      );
    }
    if (object.salaryScore != null) {
      yield r'salary_score';
      yield serializers.serialize(
        object.salaryScore,
        specifiedType: const FullType(int),
      );
    }
    if (object.totalScore != null) {
      yield r'total_score';
      yield serializers.serialize(
        object.totalScore,
        specifiedType: const FullType(int),
      );
    }
    if (object.sexType != null) {
      yield r'sex_type';
      yield serializers.serialize(
        object.sexType,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.addressCode != null) {
      yield r'address_code';
      yield serializers.serialize(
        object.addressCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.mainSkill != null) {
      yield r'main_skill';
      yield serializers.serialize(
        object.mainSkill,
        specifiedType: const FullType(BuiltList, [FullType(MainSkill)]),
      );
    }
    if (object.hopeJobCodes != null) {
      yield r'hope_job_codes';
      yield serializers.serialize(
        object.hopeJobCodes,
        specifiedType: const FullType(BuiltList, [FullType.nullable(String)]),
      );
    }
    if (object.workHistories != null) {
      yield r'work_histories';
      yield serializers.serialize(
        object.workHistories,
        specifiedType: const FullType(BuiltList, [FullType(EngWorkHistory)]),
      );
    }
    if (object.underSelectionCount != null) {
      yield r'under_selection_count';
      yield serializers.serialize(
        object.underSelectionCount,
        specifiedType: const FullType(int),
      );
    }
    if (object.requirements != null) {
      yield r'requirements';
      yield serializers.serialize(
        object.requirements,
        specifiedType: const FullType(EngHope),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    GeneralCompanyUserAppliedCompany object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required GeneralCompanyUserAppliedCompanyBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'user_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.userId = valueDes;
          break;
        case r'profile_image_path':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.profileImagePath = valueDes;
          break;
        case r'first_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.firstName = valueDes;
          break;
        case r'last_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.lastName = valueDes;
          break;
        case r'age':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.age = valueDes;
          break;
        case r'languages':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(EngLanguage)]),
          ) as BuiltList<EngLanguage>;
          result.languages.replace(valueDes);
          break;
        case r'last_academic_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.lastAcademicCode = valueDes;
          break;
        case r'interested_flag':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.interestedFlag = valueDes;
          break;
        case r'skills':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(EngSkill)]),
          ) as BuiltList<EngSkill>;
          result.skills.replace(valueDes);
          break;
        case r'country_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.countryCode = valueDes;
          break;
        case r'academic_level_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.academicLevelName = valueDes;
          break;
        case r'nickname':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.nickname = valueDes;
          break;
        case r'school_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.schoolName = valueDes;
          break;
        case r'exist_apply_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.existApplyId = valueDes;
          break;
        case r'updated':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.updated = valueDes;
          break;
        case r'qualifications':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(EngLicense)]),
          ) as BuiltList<EngLicense>;
          result.qualifications.replace(valueDes);
          break;
        case r'payroll_price_usd':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.payrollPriceUsd = valueDes;
          break;
        case r'last_login':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.lastLogin = valueDes;
          break;
        case r'total_apply_count':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.totalApplyCount = valueDes;
          break;
        case r'total_company_changed_count':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.totalCompanyChangedCount = valueDes;
          break;
        case r'job_code_has_worked_recently':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.jobCodeHasWorkedRecently = valueDes;
          break;
        case r'total_recruit_progress_code_active':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.totalRecruitProgressCodeActive = valueDes;
          break;
        case r'current_recruit_progress_code_with_my_company':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.currentRecruitProgressCodeWithMyCompany = valueDes;
          break;
        case r'total_match_skill_codes':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.totalMatchSkillCodes = valueDes;
          break;
        case r'remote_code_score':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.remoteCodeScore = valueDes;
          break;
        case r'salary_score':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.salaryScore = valueDes;
          break;
        case r'total_score':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.totalScore = valueDes;
          break;
        case r'sex_type':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.sexType = valueDes;
          break;
        case r'address_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.addressCode = valueDes;
          break;
        case r'main_skill':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(MainSkill)]),
          ) as BuiltList<MainSkill>;
          result.mainSkill.replace(valueDes);
          break;
        case r'hope_job_codes':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType.nullable(String)]),
          ) as BuiltList<String?>;
          result.hopeJobCodes.replace(valueDes);
          break;
        case r'work_histories':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(EngWorkHistory)]),
          ) as BuiltList<EngWorkHistory>;
          result.workHistories.replace(valueDes);
          break;
        case r'under_selection_count':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.underSelectionCount = valueDes;
          break;
        case r'requirements':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(EngHope),
          ) as EngHope;
          result.requirements.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  GeneralCompanyUserAppliedCompany deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = GeneralCompanyUserAppliedCompanyBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

