// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'eng_license.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$EngLicense extends EngLicense {
  @override
  final int engineerId;
  @override
  final String? licenceCode;
  @override
  final String? getDate;
  @override
  final String? licencePoint;
  @override
  final String? licenceName;
  @override
  final String? licenseCodeName;

  factory _$EngLicense([void Function(EngLicenseBuilder)? updates]) =>
      (new EngLicenseBuilder()..update(updates))._build();

  _$EngLicense._(
      {required this.engineerId,
      this.licenceCode,
      this.getDate,
      this.licencePoint,
      this.licenceName,
      this.licenseCodeName})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        engineerId, r'EngLicense', 'engineerId');
  }

  @override
  EngLicense rebuild(void Function(EngLicenseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  EngLicenseBuilder toBuilder() => new EngLicenseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is EngLicense &&
        engineerId == other.engineerId &&
        licenceCode == other.licenceCode &&
        getDate == other.getDate &&
        licencePoint == other.licencePoint &&
        licenceName == other.licenceName &&
        licenseCodeName == other.licenseCodeName;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, engineerId.hashCode);
    _$hash = $jc(_$hash, licenceCode.hashCode);
    _$hash = $jc(_$hash, getDate.hashCode);
    _$hash = $jc(_$hash, licencePoint.hashCode);
    _$hash = $jc(_$hash, licenceName.hashCode);
    _$hash = $jc(_$hash, licenseCodeName.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'EngLicense')
          ..add('engineerId', engineerId)
          ..add('licenceCode', licenceCode)
          ..add('getDate', getDate)
          ..add('licencePoint', licencePoint)
          ..add('licenceName', licenceName)
          ..add('licenseCodeName', licenseCodeName))
        .toString();
  }
}

class EngLicenseBuilder implements Builder<EngLicense, EngLicenseBuilder> {
  _$EngLicense? _$v;

  int? _engineerId;
  int? get engineerId => _$this._engineerId;
  set engineerId(int? engineerId) => _$this._engineerId = engineerId;

  String? _licenceCode;
  String? get licenceCode => _$this._licenceCode;
  set licenceCode(String? licenceCode) => _$this._licenceCode = licenceCode;

  String? _getDate;
  String? get getDate => _$this._getDate;
  set getDate(String? getDate) => _$this._getDate = getDate;

  String? _licencePoint;
  String? get licencePoint => _$this._licencePoint;
  set licencePoint(String? licencePoint) => _$this._licencePoint = licencePoint;

  String? _licenceName;
  String? get licenceName => _$this._licenceName;
  set licenceName(String? licenceName) => _$this._licenceName = licenceName;

  String? _licenseCodeName;
  String? get licenseCodeName => _$this._licenseCodeName;
  set licenseCodeName(String? licenseCodeName) =>
      _$this._licenseCodeName = licenseCodeName;

  EngLicenseBuilder() {
    EngLicense._defaults(this);
  }

  EngLicenseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _engineerId = $v.engineerId;
      _licenceCode = $v.licenceCode;
      _getDate = $v.getDate;
      _licencePoint = $v.licencePoint;
      _licenceName = $v.licenceName;
      _licenseCodeName = $v.licenseCodeName;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(EngLicense other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$EngLicense;
  }

  @override
  void update(void Function(EngLicenseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  EngLicense build() => _build();

  _$EngLicense _build() {
    final _$result = _$v ??
        new _$EngLicense._(
          engineerId: BuiltValueNullFieldError.checkNotNull(
              engineerId, r'EngLicense', 'engineerId'),
          licenceCode: licenceCode,
          getDate: getDate,
          licencePoint: licencePoint,
          licenceName: licenceName,
          licenseCodeName: licenseCodeName,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
