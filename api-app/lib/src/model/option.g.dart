// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'option.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$Option extends Option {
  @override
  final String uuid;
  @override
  final String textEn;
  @override
  final String textVi;
  @override
  final String text;
  @override
  final bool? isSelected;
  @override
  final Description description;

  factory _$Option([void Function(OptionBuilder)? updates]) =>
      (new OptionBuilder()..update(updates))._build();

  _$Option._(
      {required this.uuid,
      required this.textEn,
      required this.textVi,
      required this.text,
      this.isSelected,
      required this.description})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(uuid, r'Option', 'uuid');
    BuiltValueNullFieldError.checkNotNull(textEn, r'Option', 'textEn');
    BuiltValueNullFieldError.checkNotNull(textVi, r'Option', 'textVi');
    BuiltValueNullFieldError.checkNotNull(text, r'Option', 'text');
    BuiltValueNullFieldError.checkNotNull(
        description, r'Option', 'description');
  }

  @override
  Option rebuild(void Function(OptionBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  OptionBuilder toBuilder() => new OptionBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is Option &&
        uuid == other.uuid &&
        textEn == other.textEn &&
        textVi == other.textVi &&
        text == other.text &&
        isSelected == other.isSelected &&
        description == other.description;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, uuid.hashCode);
    _$hash = $jc(_$hash, textEn.hashCode);
    _$hash = $jc(_$hash, textVi.hashCode);
    _$hash = $jc(_$hash, text.hashCode);
    _$hash = $jc(_$hash, isSelected.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'Option')
          ..add('uuid', uuid)
          ..add('textEn', textEn)
          ..add('textVi', textVi)
          ..add('text', text)
          ..add('isSelected', isSelected)
          ..add('description', description))
        .toString();
  }
}

class OptionBuilder implements Builder<Option, OptionBuilder> {
  _$Option? _$v;

  String? _uuid;
  String? get uuid => _$this._uuid;
  set uuid(String? uuid) => _$this._uuid = uuid;

  String? _textEn;
  String? get textEn => _$this._textEn;
  set textEn(String? textEn) => _$this._textEn = textEn;

  String? _textVi;
  String? get textVi => _$this._textVi;
  set textVi(String? textVi) => _$this._textVi = textVi;

  String? _text;
  String? get text => _$this._text;
  set text(String? text) => _$this._text = text;

  bool? _isSelected;
  bool? get isSelected => _$this._isSelected;
  set isSelected(bool? isSelected) => _$this._isSelected = isSelected;

  DescriptionBuilder? _description;
  DescriptionBuilder get description =>
      _$this._description ??= new DescriptionBuilder();
  set description(DescriptionBuilder? description) =>
      _$this._description = description;

  OptionBuilder() {
    Option._defaults(this);
  }

  OptionBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _uuid = $v.uuid;
      _textEn = $v.textEn;
      _textVi = $v.textVi;
      _text = $v.text;
      _isSelected = $v.isSelected;
      _description = $v.description.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(Option other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$Option;
  }

  @override
  void update(void Function(OptionBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  Option build() => _build();

  _$Option _build() {
    _$Option _$result;
    try {
      _$result = _$v ??
          new _$Option._(
            uuid:
                BuiltValueNullFieldError.checkNotNull(uuid, r'Option', 'uuid'),
            textEn: BuiltValueNullFieldError.checkNotNull(
                textEn, r'Option', 'textEn'),
            textVi: BuiltValueNullFieldError.checkNotNull(
                textVi, r'Option', 'textVi'),
            text:
                BuiltValueNullFieldError.checkNotNull(text, r'Option', 'text'),
            isSelected: isSelected,
            description: description.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'description';
        description.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'Option', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
