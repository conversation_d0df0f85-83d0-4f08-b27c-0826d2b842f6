// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'count_unread_notify_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$CountUnreadNotifyResponseModel extends CountUnreadNotifyResponseModel {
  @override
  final String? message;
  @override
  final CountUnreadNotify data;
  @override
  final BuiltList<String>? errors;

  factory _$CountUnreadNotifyResponseModel(
          [void Function(CountUnreadNotifyResponseModelBuilder)? updates]) =>
      (new CountUnreadNotifyResponseModelBuilder()..update(updates))._build();

  _$CountUnreadNotifyResponseModel._(
      {this.message, required this.data, this.errors})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'CountUnreadNotifyResponseModel', 'data');
  }

  @override
  CountUnreadNotifyResponseModel rebuild(
          void Function(CountUnreadNotifyResponseModelBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  CountUnreadNotifyResponseModelBuilder toBuilder() =>
      new CountUnreadNotifyResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is CountUnreadNotifyResponseModel &&
        message == other.message &&
        data == other.data &&
        errors == other.errors;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'CountUnreadNotifyResponseModel')
          ..add('message', message)
          ..add('data', data)
          ..add('errors', errors))
        .toString();
  }
}

class CountUnreadNotifyResponseModelBuilder
    implements
        Builder<CountUnreadNotifyResponseModel,
            CountUnreadNotifyResponseModelBuilder> {
  _$CountUnreadNotifyResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  CountUnreadNotifyBuilder? _data;
  CountUnreadNotifyBuilder get data =>
      _$this._data ??= new CountUnreadNotifyBuilder();
  set data(CountUnreadNotifyBuilder? data) => _$this._data = data;

  ListBuilder<String>? _errors;
  ListBuilder<String> get errors =>
      _$this._errors ??= new ListBuilder<String>();
  set errors(ListBuilder<String>? errors) => _$this._errors = errors;

  CountUnreadNotifyResponseModelBuilder() {
    CountUnreadNotifyResponseModel._defaults(this);
  }

  CountUnreadNotifyResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _data = $v.data.toBuilder();
      _errors = $v.errors?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(CountUnreadNotifyResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$CountUnreadNotifyResponseModel;
  }

  @override
  void update(void Function(CountUnreadNotifyResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  CountUnreadNotifyResponseModel build() => _build();

  _$CountUnreadNotifyResponseModel _build() {
    _$CountUnreadNotifyResponseModel _$result;
    try {
      _$result = _$v ??
          new _$CountUnreadNotifyResponseModel._(
            message: message,
            data: data.build(),
            errors: _errors?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        data.build();
        _$failedField = 'errors';
        _errors?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'CountUnreadNotifyResponseModel', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
