// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'confirm_whatsapp_code.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ConfirmWhatsappCode extends ConfirmWhatsappCode {
  @override
  final String phoneNumber;
  @override
  final String code;

  factory _$ConfirmWhatsappCode(
          [void Function(ConfirmWhatsappCodeBuilder)? updates]) =>
      (new ConfirmWhatsappCodeBuilder()..update(updates))._build();

  _$ConfirmWhatsappCode._({required this.phoneNumber, required this.code})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        phoneNumber, r'ConfirmWhatsappCode', 'phoneNumber');
    BuiltValueNullFieldError.checkNotNull(code, r'ConfirmWhatsappCode', 'code');
  }

  @override
  ConfirmWhatsappCode rebuild(
          void Function(ConfirmWhatsappCodeBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ConfirmWhatsappCodeBuilder toBuilder() =>
      new ConfirmWhatsappCodeBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ConfirmWhatsappCode &&
        phoneNumber == other.phoneNumber &&
        code == other.code;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, phoneNumber.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ConfirmWhatsappCode')
          ..add('phoneNumber', phoneNumber)
          ..add('code', code))
        .toString();
  }
}

class ConfirmWhatsappCodeBuilder
    implements Builder<ConfirmWhatsappCode, ConfirmWhatsappCodeBuilder> {
  _$ConfirmWhatsappCode? _$v;

  String? _phoneNumber;
  String? get phoneNumber => _$this._phoneNumber;
  set phoneNumber(String? phoneNumber) => _$this._phoneNumber = phoneNumber;

  String? _code;
  String? get code => _$this._code;
  set code(String? code) => _$this._code = code;

  ConfirmWhatsappCodeBuilder() {
    ConfirmWhatsappCode._defaults(this);
  }

  ConfirmWhatsappCodeBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _phoneNumber = $v.phoneNumber;
      _code = $v.code;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ConfirmWhatsappCode other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$ConfirmWhatsappCode;
  }

  @override
  void update(void Function(ConfirmWhatsappCodeBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ConfirmWhatsappCode build() => _build();

  _$ConfirmWhatsappCode _build() {
    final _$result = _$v ??
        new _$ConfirmWhatsappCode._(
          phoneNumber: BuiltValueNullFieldError.checkNotNull(
              phoneNumber, r'ConfirmWhatsappCode', 'phoneNumber'),
          code: BuiltValueNullFieldError.checkNotNull(
              code, r'ConfirmWhatsappCode', 'code'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
