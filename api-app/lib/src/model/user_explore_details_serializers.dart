//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/engineer_self_assesment_details.dart';
import 'package:openapi/src/model/eng_academic.dart';
import 'package:openapi/src/model/eng_skill.dart';
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/eng_license.dart';
import 'package:openapi/src/model/eng_hope.dart';
import 'package:openapi/src/model/eng_career.dart';
import 'package:openapi/src/model/date.dart';
import 'package:openapi/src/model/eng_language.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'user_explore_details_serializers.g.dart';

/// UserExploreDetailsSerializers
///
/// Properties:
/// * [profileImagePath] 
/// * [email] 
/// * [firstName] 
/// * [lastName] 
/// * [sexType] 
/// * [birthDate] 
/// * [countryCode] 
/// * [tel] 
/// * [addressCode] 
/// * [cityName] 
/// * [passportNumber] 
/// * [passportImagePath] 
/// * [educations] 
/// * [languages] 
/// * [qualifications] 
/// * [skills] 
/// * [experiences] 
/// * [requirements] 
/// * [lastAcademicCode] 
/// * [pr] 
/// * [selfIntroductionUrl] 
/// * [internationalTel] 
/// * [updated] 
/// * [userType] 
/// * [facebookUrl] 
/// * [linkedinUrl] 
/// * [whatsappUrl] 
/// * [interestedFlag] 
/// * [userId] 
/// * [lastAcademicName] 
/// * [selfAssesmentDetails] 
/// * [canRequestInterview] 
/// * [nickname] 
@BuiltValue()
abstract class UserExploreDetailsSerializers implements Built<UserExploreDetailsSerializers, UserExploreDetailsSerializersBuilder> {
  @BuiltValueField(wireName: r'profile_image_path')
  String? get profileImagePath;

  @BuiltValueField(wireName: r'email')
  String get email;

  @BuiltValueField(wireName: r'first_name')
  String? get firstName;

  @BuiltValueField(wireName: r'last_name')
  String? get lastName;

  @BuiltValueField(wireName: r'sex_type')
  int? get sexType;

  @BuiltValueField(wireName: r'birth_date')
  Date? get birthDate;

  @BuiltValueField(wireName: r'country_code')
  String? get countryCode;

  @BuiltValueField(wireName: r'tel')
  String? get tel;

  @BuiltValueField(wireName: r'address_code')
  String? get addressCode;

  @BuiltValueField(wireName: r'city_name')
  String? get cityName;

  @BuiltValueField(wireName: r'passport_number')
  String? get passportNumber;

  @BuiltValueField(wireName: r'passport_image_path')
  String? get passportImagePath;

  @BuiltValueField(wireName: r'educations')
  BuiltList<EngAcademic>? get educations;

  @BuiltValueField(wireName: r'languages')
  BuiltList<EngLanguage>? get languages;

  @BuiltValueField(wireName: r'qualifications')
  BuiltList<EngLicense>? get qualifications;

  @BuiltValueField(wireName: r'skills')
  BuiltList<EngSkill>? get skills;

  @BuiltValueField(wireName: r'experiences')
  BuiltList<EngCareer>? get experiences;

  @BuiltValueField(wireName: r'requirements')
  EngHope? get requirements;

  @BuiltValueField(wireName: r'last_academic_code')
  String? get lastAcademicCode;

  @BuiltValueField(wireName: r'pr')
  String? get pr;

  @BuiltValueField(wireName: r'self_introduction_url')
  String? get selfIntroductionUrl;

  @BuiltValueField(wireName: r'international_tel')
  String? get internationalTel;

  @BuiltValueField(wireName: r'updated')
  DateTime? get updated;

  @BuiltValueField(wireName: r'user_type')
  int get userType;

  @BuiltValueField(wireName: r'facebook_url')
  String? get facebookUrl;

  @BuiltValueField(wireName: r'linkedin_url')
  String? get linkedinUrl;

  @BuiltValueField(wireName: r'whatsapp_url')
  String? get whatsappUrl;

  @BuiltValueField(wireName: r'interested_flag')
  int? get interestedFlag;

  @BuiltValueField(wireName: r'user_id')
  int? get userId;

  @BuiltValueField(wireName: r'last_academic_name')
  String? get lastAcademicName;

  @BuiltValueField(wireName: r'self_assesment_details')
  EngineerSelfAssesmentDetails? get selfAssesmentDetails;

  @BuiltValueField(wireName: r'can_request_interview')
  bool? get canRequestInterview;

  @BuiltValueField(wireName: r'nickname')
  String? get nickname;

  UserExploreDetailsSerializers._();

  factory UserExploreDetailsSerializers([void updates(UserExploreDetailsSerializersBuilder b)]) = _$UserExploreDetailsSerializers;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(UserExploreDetailsSerializersBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<UserExploreDetailsSerializers> get serializer => _$UserExploreDetailsSerializersSerializer();
}

class _$UserExploreDetailsSerializersSerializer implements PrimitiveSerializer<UserExploreDetailsSerializers> {
  @override
  final Iterable<Type> types = const [UserExploreDetailsSerializers, _$UserExploreDetailsSerializers];

  @override
  final String wireName = r'UserExploreDetailsSerializers';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    UserExploreDetailsSerializers object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.profileImagePath != null) {
      yield r'profile_image_path';
      yield serializers.serialize(
        object.profileImagePath,
        specifiedType: const FullType.nullable(String),
      );
    }
    yield r'email';
    yield serializers.serialize(
      object.email,
      specifiedType: const FullType(String),
    );
    if (object.firstName != null) {
      yield r'first_name';
      yield serializers.serialize(
        object.firstName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.lastName != null) {
      yield r'last_name';
      yield serializers.serialize(
        object.lastName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.sexType != null) {
      yield r'sex_type';
      yield serializers.serialize(
        object.sexType,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.birthDate != null) {
      yield r'birth_date';
      yield serializers.serialize(
        object.birthDate,
        specifiedType: const FullType.nullable(Date),
      );
    }
    if (object.countryCode != null) {
      yield r'country_code';
      yield serializers.serialize(
        object.countryCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.tel != null) {
      yield r'tel';
      yield serializers.serialize(
        object.tel,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.addressCode != null) {
      yield r'address_code';
      yield serializers.serialize(
        object.addressCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.cityName != null) {
      yield r'city_name';
      yield serializers.serialize(
        object.cityName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.passportNumber != null) {
      yield r'passport_number';
      yield serializers.serialize(
        object.passportNumber,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.passportImagePath != null) {
      yield r'passport_image_path';
      yield serializers.serialize(
        object.passportImagePath,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.educations != null) {
      yield r'educations';
      yield serializers.serialize(
        object.educations,
        specifiedType: const FullType(BuiltList, [FullType(EngAcademic)]),
      );
    }
    if (object.languages != null) {
      yield r'languages';
      yield serializers.serialize(
        object.languages,
        specifiedType: const FullType(BuiltList, [FullType(EngLanguage)]),
      );
    }
    if (object.qualifications != null) {
      yield r'qualifications';
      yield serializers.serialize(
        object.qualifications,
        specifiedType: const FullType(BuiltList, [FullType(EngLicense)]),
      );
    }
    if (object.skills != null) {
      yield r'skills';
      yield serializers.serialize(
        object.skills,
        specifiedType: const FullType(BuiltList, [FullType(EngSkill)]),
      );
    }
    if (object.experiences != null) {
      yield r'experiences';
      yield serializers.serialize(
        object.experiences,
        specifiedType: const FullType(BuiltList, [FullType(EngCareer)]),
      );
    }
    if (object.requirements != null) {
      yield r'requirements';
      yield serializers.serialize(
        object.requirements,
        specifiedType: const FullType(EngHope),
      );
    }
    if (object.lastAcademicCode != null) {
      yield r'last_academic_code';
      yield serializers.serialize(
        object.lastAcademicCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.pr != null) {
      yield r'pr';
      yield serializers.serialize(
        object.pr,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.selfIntroductionUrl != null) {
      yield r'self_introduction_url';
      yield serializers.serialize(
        object.selfIntroductionUrl,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.internationalTel != null) {
      yield r'international_tel';
      yield serializers.serialize(
        object.internationalTel,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.updated != null) {
      yield r'updated';
      yield serializers.serialize(
        object.updated,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    yield r'user_type';
    yield serializers.serialize(
      object.userType,
      specifiedType: const FullType(int),
    );
    if (object.facebookUrl != null) {
      yield r'facebook_url';
      yield serializers.serialize(
        object.facebookUrl,
        specifiedType: const FullType(String),
      );
    }
    if (object.linkedinUrl != null) {
      yield r'linkedin_url';
      yield serializers.serialize(
        object.linkedinUrl,
        specifiedType: const FullType(String),
      );
    }
    if (object.whatsappUrl != null) {
      yield r'whatsapp_url';
      yield serializers.serialize(
        object.whatsappUrl,
        specifiedType: const FullType(String),
      );
    }
    if (object.interestedFlag != null) {
      yield r'interested_flag';
      yield serializers.serialize(
        object.interestedFlag,
        specifiedType: const FullType(int),
      );
    }
    if (object.userId != null) {
      yield r'user_id';
      yield serializers.serialize(
        object.userId,
        specifiedType: const FullType(int),
      );
    }
    if (object.lastAcademicName != null) {
      yield r'last_academic_name';
      yield serializers.serialize(
        object.lastAcademicName,
        specifiedType: const FullType(String),
      );
    }
    if (object.selfAssesmentDetails != null) {
      yield r'self_assesment_details';
      yield serializers.serialize(
        object.selfAssesmentDetails,
        specifiedType: const FullType(EngineerSelfAssesmentDetails),
      );
    }
    if (object.canRequestInterview != null) {
      yield r'can_request_interview';
      yield serializers.serialize(
        object.canRequestInterview,
        specifiedType: const FullType(bool),
      );
    }
    if (object.nickname != null) {
      yield r'nickname';
      yield serializers.serialize(
        object.nickname,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    UserExploreDetailsSerializers object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required UserExploreDetailsSerializersBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'profile_image_path':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.profileImagePath = valueDes;
          break;
        case r'email':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.email = valueDes;
          break;
        case r'first_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.firstName = valueDes;
          break;
        case r'last_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.lastName = valueDes;
          break;
        case r'sex_type':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.sexType = valueDes;
          break;
        case r'birth_date':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(Date),
          ) as Date?;
          if (valueDes == null) continue;
          result.birthDate = valueDes;
          break;
        case r'country_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.countryCode = valueDes;
          break;
        case r'tel':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.tel = valueDes;
          break;
        case r'address_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.addressCode = valueDes;
          break;
        case r'city_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.cityName = valueDes;
          break;
        case r'passport_number':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.passportNumber = valueDes;
          break;
        case r'passport_image_path':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.passportImagePath = valueDes;
          break;
        case r'educations':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(EngAcademic)]),
          ) as BuiltList<EngAcademic>;
          result.educations.replace(valueDes);
          break;
        case r'languages':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(EngLanguage)]),
          ) as BuiltList<EngLanguage>;
          result.languages.replace(valueDes);
          break;
        case r'qualifications':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(EngLicense)]),
          ) as BuiltList<EngLicense>;
          result.qualifications.replace(valueDes);
          break;
        case r'skills':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(EngSkill)]),
          ) as BuiltList<EngSkill>;
          result.skills.replace(valueDes);
          break;
        case r'experiences':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(EngCareer)]),
          ) as BuiltList<EngCareer>;
          result.experiences.replace(valueDes);
          break;
        case r'requirements':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(EngHope),
          ) as EngHope;
          result.requirements.replace(valueDes);
          break;
        case r'last_academic_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.lastAcademicCode = valueDes;
          break;
        case r'pr':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.pr = valueDes;
          break;
        case r'self_introduction_url':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.selfIntroductionUrl = valueDes;
          break;
        case r'international_tel':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.internationalTel = valueDes;
          break;
        case r'updated':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.updated = valueDes;
          break;
        case r'user_type':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.userType = valueDes;
          break;
        case r'facebook_url':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.facebookUrl = valueDes;
          break;
        case r'linkedin_url':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.linkedinUrl = valueDes;
          break;
        case r'whatsapp_url':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.whatsappUrl = valueDes;
          break;
        case r'interested_flag':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.interestedFlag = valueDes;
          break;
        case r'user_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.userId = valueDes;
          break;
        case r'last_academic_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.lastAcademicName = valueDes;
          break;
        case r'self_assesment_details':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(EngineerSelfAssesmentDetails),
          ) as EngineerSelfAssesmentDetails;
          result.selfAssesmentDetails.replace(valueDes);
          break;
        case r'can_request_interview':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(bool),
          ) as bool;
          result.canRequestInterview = valueDes;
          break;
        case r'nickname':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.nickname = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  UserExploreDetailsSerializers deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = UserExploreDetailsSerializersBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

