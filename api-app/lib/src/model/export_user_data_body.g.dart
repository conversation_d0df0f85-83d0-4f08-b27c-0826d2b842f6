// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'export_user_data_body.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ExportUserDataBody extends ExportUserDataBody {
  @override
  final BuiltList<int> engineerIds;
  @override
  final String exportType;

  factory _$ExportUserDataBody(
          [void Function(ExportUserDataBodyBuilder)? updates]) =>
      (new ExportUserDataBodyBuilder()..update(updates))._build();

  _$ExportUserDataBody._({required this.engineerIds, required this.exportType})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        engineerIds, r'ExportUserDataBody', 'engineerIds');
    BuiltValueNullFieldError.checkNotNull(
        exportType, r'ExportUserDataBody', 'exportType');
  }

  @override
  ExportUserDataBody rebuild(
          void Function(ExportUserDataBodyBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ExportUserDataBodyBuilder toBuilder() =>
      new ExportUserDataBodyBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ExportUserDataBody &&
        engineerIds == other.engineerIds &&
        exportType == other.exportType;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, engineerIds.hashCode);
    _$hash = $jc(_$hash, exportType.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ExportUserDataBody')
          ..add('engineerIds', engineerIds)
          ..add('exportType', exportType))
        .toString();
  }
}

class ExportUserDataBodyBuilder
    implements Builder<ExportUserDataBody, ExportUserDataBodyBuilder> {
  _$ExportUserDataBody? _$v;

  ListBuilder<int>? _engineerIds;
  ListBuilder<int> get engineerIds =>
      _$this._engineerIds ??= new ListBuilder<int>();
  set engineerIds(ListBuilder<int>? engineerIds) =>
      _$this._engineerIds = engineerIds;

  String? _exportType;
  String? get exportType => _$this._exportType;
  set exportType(String? exportType) => _$this._exportType = exportType;

  ExportUserDataBodyBuilder() {
    ExportUserDataBody._defaults(this);
  }

  ExportUserDataBodyBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _engineerIds = $v.engineerIds.toBuilder();
      _exportType = $v.exportType;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ExportUserDataBody other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$ExportUserDataBody;
  }

  @override
  void update(void Function(ExportUserDataBodyBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ExportUserDataBody build() => _build();

  _$ExportUserDataBody _build() {
    _$ExportUserDataBody _$result;
    try {
      _$result = _$v ??
          new _$ExportUserDataBody._(
            engineerIds: engineerIds.build(),
            exportType: BuiltValueNullFieldError.checkNotNull(
                exportType, r'ExportUserDataBody', 'exportType'),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'engineerIds';
        engineerIds.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'ExportUserDataBody', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
