//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/request_interview_success.dart';
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/error_detail.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'general_company_request_interview_apply_response_model.g.dart';

/// GeneralCompanyRequestInterviewApplyResponseModel
///
/// Properties:
/// * [message] 
/// * [errors] 
/// * [data] 
@BuiltValue()
abstract class GeneralCompanyRequestInterviewApplyResponseModel implements Built<GeneralCompanyRequestInterviewApplyResponseModel, GeneralCompanyRequestInterviewApplyResponseModelBuilder> {
  @BuiltValueField(wireName: r'message')
  String? get message;

  @BuiltValueField(wireName: r'errors')
  BuiltList<ErrorDetail>? get errors;

  @BuiltValueField(wireName: r'data')
  RequestInterviewSuccess get data;

  GeneralCompanyRequestInterviewApplyResponseModel._();

  factory GeneralCompanyRequestInterviewApplyResponseModel([void updates(GeneralCompanyRequestInterviewApplyResponseModelBuilder b)]) = _$GeneralCompanyRequestInterviewApplyResponseModel;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(GeneralCompanyRequestInterviewApplyResponseModelBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<GeneralCompanyRequestInterviewApplyResponseModel> get serializer => _$GeneralCompanyRequestInterviewApplyResponseModelSerializer();
}

class _$GeneralCompanyRequestInterviewApplyResponseModelSerializer implements PrimitiveSerializer<GeneralCompanyRequestInterviewApplyResponseModel> {
  @override
  final Iterable<Type> types = const [GeneralCompanyRequestInterviewApplyResponseModel, _$GeneralCompanyRequestInterviewApplyResponseModel];

  @override
  final String wireName = r'GeneralCompanyRequestInterviewApplyResponseModel';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    GeneralCompanyRequestInterviewApplyResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'message';
    yield object.message == null ? null : serializers.serialize(
      object.message,
      specifiedType: const FullType.nullable(String),
    );
    yield r'errors';
    yield object.errors == null ? null : serializers.serialize(
      object.errors,
      specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
    );
    yield r'data';
    yield serializers.serialize(
      object.data,
      specifiedType: const FullType(RequestInterviewSuccess),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    GeneralCompanyRequestInterviewApplyResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required GeneralCompanyRequestInterviewApplyResponseModelBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'message':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.message = valueDes;
          break;
        case r'errors':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
          ) as BuiltList<ErrorDetail>?;
          if (valueDes == null) continue;
          result.errors.replace(valueDes);
          break;
        case r'data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(RequestInterviewSuccess),
          ) as RequestInterviewSuccess;
          result.data.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  GeneralCompanyRequestInterviewApplyResponseModel deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = GeneralCompanyRequestInterviewApplyResponseModelBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

