//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'eng_career_job_skill.g.dart';

/// EngCareerJobSkill
///
/// Properties:
/// * [jobCode] 
/// * [skillCode] 
/// * [yearsOfExperience] 
/// * [created] 
/// * [updated] 
/// * [skillCodeName] 
/// * [jobName] 
/// * [levelName] 
@BuiltValue()
abstract class EngCareerJobSkill implements Built<EngCareerJobSkill, EngCareerJobSkillBuilder> {
  @BuiltValueField(wireName: r'job_code')
  String? get jobCode;

  @BuiltValueField(wireName: r'skill_code')
  String? get skillCode;

  @BuiltValueField(wireName: r'years_of_experience')
  int? get yearsOfExperience;

  @BuiltValueField(wireName: r'created')
  DateTime? get created;

  @BuiltValueField(wireName: r'updated')
  DateTime? get updated;

  @BuiltValueField(wireName: r'skill_code_name')
  String? get skillCodeName;

  @BuiltValueField(wireName: r'job_name')
  String? get jobName;

  @BuiltValueField(wireName: r'level_name')
  String? get levelName;

  EngCareerJobSkill._();

  factory EngCareerJobSkill([void updates(EngCareerJobSkillBuilder b)]) = _$EngCareerJobSkill;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(EngCareerJobSkillBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<EngCareerJobSkill> get serializer => _$EngCareerJobSkillSerializer();
}

class _$EngCareerJobSkillSerializer implements PrimitiveSerializer<EngCareerJobSkill> {
  @override
  final Iterable<Type> types = const [EngCareerJobSkill, _$EngCareerJobSkill];

  @override
  final String wireName = r'EngCareerJobSkill';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    EngCareerJobSkill object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.jobCode != null) {
      yield r'job_code';
      yield serializers.serialize(
        object.jobCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.skillCode != null) {
      yield r'skill_code';
      yield serializers.serialize(
        object.skillCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.yearsOfExperience != null) {
      yield r'years_of_experience';
      yield serializers.serialize(
        object.yearsOfExperience,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.created != null) {
      yield r'created';
      yield serializers.serialize(
        object.created,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.updated != null) {
      yield r'updated';
      yield serializers.serialize(
        object.updated,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.skillCodeName != null) {
      yield r'skill_code_name';
      yield serializers.serialize(
        object.skillCodeName,
        specifiedType: const FullType(String),
      );
    }
    if (object.jobName != null) {
      yield r'job_name';
      yield serializers.serialize(
        object.jobName,
        specifiedType: const FullType(String),
      );
    }
    if (object.levelName != null) {
      yield r'level_name';
      yield serializers.serialize(
        object.levelName,
        specifiedType: const FullType(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    EngCareerJobSkill object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required EngCareerJobSkillBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'job_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.jobCode = valueDes;
          break;
        case r'skill_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.skillCode = valueDes;
          break;
        case r'years_of_experience':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.yearsOfExperience = valueDes;
          break;
        case r'created':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.created = valueDes;
          break;
        case r'updated':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.updated = valueDes;
          break;
        case r'skill_code_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.skillCodeName = valueDes;
          break;
        case r'job_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.jobName = valueDes;
          break;
        case r'level_name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.levelName = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  EngCareerJobSkill deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = EngCareerJobSkillBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

