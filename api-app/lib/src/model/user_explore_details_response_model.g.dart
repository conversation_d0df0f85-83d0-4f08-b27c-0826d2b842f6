// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_explore_details_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UserExploreDetailsResponseModel
    extends UserExploreDetailsResponseModel {
  @override
  final String? message;
  @override
  final UserExploreDetailsSerializers data;
  @override
  final BuiltList<ErrorDetail>? errors;

  factory _$UserExploreDetailsResponseModel(
          [void Function(UserExploreDetailsResponseModelBuilder)? updates]) =>
      (new UserExploreDetailsResponseModelBuilder()..update(updates))._build();

  _$UserExploreDetailsResponseModel._(
      {this.message, required this.data, this.errors})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'UserExploreDetailsResponseModel', 'data');
  }

  @override
  UserExploreDetailsResponseModel rebuild(
          void Function(UserExploreDetailsResponseModelBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UserExploreDetailsResponseModelBuilder toBuilder() =>
      new UserExploreDetailsResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UserExploreDetailsResponseModel &&
        message == other.message &&
        data == other.data &&
        errors == other.errors;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UserExploreDetailsResponseModel')
          ..add('message', message)
          ..add('data', data)
          ..add('errors', errors))
        .toString();
  }
}

class UserExploreDetailsResponseModelBuilder
    implements
        Builder<UserExploreDetailsResponseModel,
            UserExploreDetailsResponseModelBuilder> {
  _$UserExploreDetailsResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  UserExploreDetailsSerializersBuilder? _data;
  UserExploreDetailsSerializersBuilder get data =>
      _$this._data ??= new UserExploreDetailsSerializersBuilder();
  set data(UserExploreDetailsSerializersBuilder? data) => _$this._data = data;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  UserExploreDetailsResponseModelBuilder() {
    UserExploreDetailsResponseModel._defaults(this);
  }

  UserExploreDetailsResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _data = $v.data.toBuilder();
      _errors = $v.errors?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UserExploreDetailsResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$UserExploreDetailsResponseModel;
  }

  @override
  void update(void Function(UserExploreDetailsResponseModelBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UserExploreDetailsResponseModel build() => _build();

  _$UserExploreDetailsResponseModel _build() {
    _$UserExploreDetailsResponseModel _$result;
    try {
      _$result = _$v ??
          new _$UserExploreDetailsResponseModel._(
            message: message,
            data: data.build(),
            errors: _errors?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        data.build();
        _$failedField = 'errors';
        _errors?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'UserExploreDetailsResponseModel', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
