//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/description.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'option.g.dart';

/// Option
///
/// Properties:
/// * [uuid] 
/// * [textEn] 
/// * [textVi] 
/// * [text] 
/// * [isSelected] 
/// * [description] 
@BuiltValue()
abstract class Option implements Built<Option, OptionBuilder> {
  @BuiltValueField(wireName: r'uuid')
  String get uuid;

  @BuiltValueField(wireName: r'text_en')
  String get textEn;

  @BuiltValueField(wireName: r'text_vi')
  String get textVi;

  @BuiltValueField(wireName: r'text')
  String get text;

  @BuiltValueField(wireName: r'is_selected')
  bool? get isSelected;

  @BuiltValueField(wireName: r'description')
  Description get description;

  Option._();

  factory Option([void updates(OptionBuilder b)]) = _$Option;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(OptionBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<Option> get serializer => _$OptionSerializer();
}

class _$OptionSerializer implements PrimitiveSerializer<Option> {
  @override
  final Iterable<Type> types = const [Option, _$Option];

  @override
  final String wireName = r'Option';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    Option object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'uuid';
    yield serializers.serialize(
      object.uuid,
      specifiedType: const FullType(String),
    );
    yield r'text_en';
    yield serializers.serialize(
      object.textEn,
      specifiedType: const FullType(String),
    );
    yield r'text_vi';
    yield serializers.serialize(
      object.textVi,
      specifiedType: const FullType(String),
    );
    yield r'text';
    yield serializers.serialize(
      object.text,
      specifiedType: const FullType(String),
    );
    if (object.isSelected != null) {
      yield r'is_selected';
      yield serializers.serialize(
        object.isSelected,
        specifiedType: const FullType(bool),
      );
    }
    yield r'description';
    yield serializers.serialize(
      object.description,
      specifiedType: const FullType(Description),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    Option object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required OptionBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'uuid':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.uuid = valueDes;
          break;
        case r'text_en':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.textEn = valueDes;
          break;
        case r'text_vi':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.textVi = valueDes;
          break;
        case r'text':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.text = valueDes;
          break;
        case r'is_selected':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(bool),
          ) as bool;
          result.isSelected = valueDes;
          break;
        case r'description':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(Description),
          ) as Description;
          result.description.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  Option deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = OptionBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

