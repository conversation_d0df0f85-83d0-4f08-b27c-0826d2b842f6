// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'confirm_login.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ConfirmLogin extends ConfirmLogin {
  @override
  final String email;
  @override
  final String code;
  @override
  final String password;

  factory _$ConfirmLogin([void Function(ConfirmLoginBuilder)? updates]) =>
      (new ConfirmLoginBuilder()..update(updates))._build();

  _$ConfirmLogin._(
      {required this.email, required this.code, required this.password})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(email, r'ConfirmLogin', 'email');
    BuiltValueNullFieldError.checkNotNull(code, r'ConfirmLogin', 'code');
    BuiltValueNullFieldError.checkNotNull(
        password, r'ConfirmLogin', 'password');
  }

  @override
  ConfirmLogin rebuild(void Function(ConfirmLoginBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ConfirmLoginBuilder toBuilder() => new ConfirmLoginBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ConfirmLogin &&
        email == other.email &&
        code == other.code &&
        password == other.password;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, email.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, password.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ConfirmLogin')
          ..add('email', email)
          ..add('code', code)
          ..add('password', password))
        .toString();
  }
}

class ConfirmLoginBuilder
    implements Builder<ConfirmLogin, ConfirmLoginBuilder> {
  _$ConfirmLogin? _$v;

  String? _email;
  String? get email => _$this._email;
  set email(String? email) => _$this._email = email;

  String? _code;
  String? get code => _$this._code;
  set code(String? code) => _$this._code = code;

  String? _password;
  String? get password => _$this._password;
  set password(String? password) => _$this._password = password;

  ConfirmLoginBuilder() {
    ConfirmLogin._defaults(this);
  }

  ConfirmLoginBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _email = $v.email;
      _code = $v.code;
      _password = $v.password;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ConfirmLogin other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$ConfirmLogin;
  }

  @override
  void update(void Function(ConfirmLoginBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ConfirmLogin build() => _build();

  _$ConfirmLogin _build() {
    final _$result = _$v ??
        new _$ConfirmLogin._(
          email: BuiltValueNullFieldError.checkNotNull(
              email, r'ConfirmLogin', 'email'),
          code: BuiltValueNullFieldError.checkNotNull(
              code, r'ConfirmLogin', 'code'),
          password: BuiltValueNullFieldError.checkNotNull(
              password, r'ConfirmLogin', 'password'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
