// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'main_skill.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$MainSkill extends MainSkill {
  @override
  final String skillCode;
  @override
  final String skillName;

  factory _$MainSkill([void Function(MainSkillBuilder)? updates]) =>
      (new MainSkillBuilder()..update(updates))._build();

  _$MainSkill._({required this.skillCode, required this.skillName})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(skillCode, r'MainSkill', 'skillCode');
    BuiltValueNullFieldError.checkNotNull(skillName, r'MainSkill', 'skillName');
  }

  @override
  MainSkill rebuild(void Function(MainSkillBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  MainSkillBuilder toBuilder() => new MainSkillBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is MainSkill &&
        skillCode == other.skillCode &&
        skillName == other.skillName;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, skillCode.hashCode);
    _$hash = $jc(_$hash, skillName.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'MainSkill')
          ..add('skillCode', skillCode)
          ..add('skillName', skillName))
        .toString();
  }
}

class MainSkillBuilder implements Builder<MainSkill, MainSkillBuilder> {
  _$MainSkill? _$v;

  String? _skillCode;
  String? get skillCode => _$this._skillCode;
  set skillCode(String? skillCode) => _$this._skillCode = skillCode;

  String? _skillName;
  String? get skillName => _$this._skillName;
  set skillName(String? skillName) => _$this._skillName = skillName;

  MainSkillBuilder() {
    MainSkill._defaults(this);
  }

  MainSkillBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _skillCode = $v.skillCode;
      _skillName = $v.skillName;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(MainSkill other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$MainSkill;
  }

  @override
  void update(void Function(MainSkillBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  MainSkill build() => _build();

  _$MainSkill _build() {
    final _$result = _$v ??
        new _$MainSkill._(
          skillCode: BuiltValueNullFieldError.checkNotNull(
              skillCode, r'MainSkill', 'skillCode'),
          skillName: BuiltValueNullFieldError.checkNotNull(
              skillName, r'MainSkill', 'skillName'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
