// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'token_verify.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TokenVerify extends TokenVerify {
  @override
  final String token;

  factory _$TokenVerify([void Function(TokenVerifyBuilder)? updates]) =>
      (new TokenVerifyBuilder()..update(updates))._build();

  _$TokenVerify._({required this.token}) : super._() {
    BuiltValueNullFieldError.checkNotNull(token, r'TokenVerify', 'token');
  }

  @override
  TokenVerify rebuild(void Function(TokenVerifyBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TokenVerifyBuilder toBuilder() => new TokenVerifyBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TokenVerify && token == other.token;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, token.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TokenVerify')..add('token', token))
        .toString();
  }
}

class TokenVerifyBuilder implements Builder<TokenVerify, TokenVerifyBuilder> {
  _$TokenVerify? _$v;

  String? _token;
  String? get token => _$this._token;
  set token(String? token) => _$this._token = token;

  TokenVerifyBuilder() {
    TokenVerify._defaults(this);
  }

  TokenVerifyBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _token = $v.token;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TokenVerify other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$TokenVerify;
  }

  @override
  void update(void Function(TokenVerifyBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TokenVerify build() => _build();

  _$TokenVerify _build() {
    final _$result = _$v ??
        new _$TokenVerify._(
          token: BuiltValueNullFieldError.checkNotNull(
              token, r'TokenVerify', 'token'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
