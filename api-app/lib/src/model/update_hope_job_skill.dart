//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'update_hope_job_skill.g.dart';

/// UpdateHopeJobSkill
///
/// Properties:
/// * [jobCode] 
/// * [skillCode] 
@BuiltValue()
abstract class UpdateHopeJobSkill implements Built<UpdateHopeJobSkill, UpdateHopeJobSkillBuilder> {
  @BuiltValueField(wireName: r'job_code')
  String? get jobCode;

  @BuiltValueField(wireName: r'skill_code')
  String? get skillCode;

  UpdateHopeJobSkill._();

  factory UpdateHopeJobSkill([void updates(UpdateHopeJobSkillBuilder b)]) = _$UpdateHopeJobSkill;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(UpdateHopeJobSkillBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<UpdateHopeJobSkill> get serializer => _$UpdateHopeJobSkillSerializer();
}

class _$UpdateHopeJobSkillSerializer implements PrimitiveSerializer<UpdateHopeJobSkill> {
  @override
  final Iterable<Type> types = const [UpdateHopeJobSkill, _$UpdateHopeJobSkill];

  @override
  final String wireName = r'UpdateHopeJobSkill';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    UpdateHopeJobSkill object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.jobCode != null) {
      yield r'job_code';
      yield serializers.serialize(
        object.jobCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.skillCode != null) {
      yield r'skill_code';
      yield serializers.serialize(
        object.skillCode,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    UpdateHopeJobSkill object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required UpdateHopeJobSkillBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'job_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.jobCode = valueDes;
          break;
        case r'skill_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.skillCode = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  UpdateHopeJobSkill deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = UpdateHopeJobSkillBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

