// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hope_category_skill.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$HopeCategorySkill extends HopeCategorySkill {
  @override
  final String categoryId;
  @override
  final BuiltList<HopeJobSkill> skills;

  factory _$HopeCategorySkill(
          [void Function(HopeCategorySkillBuilder)? updates]) =>
      (new HopeCategorySkillBuilder()..update(updates))._build();

  _$HopeCategorySkill._({required this.categoryId, required this.skills})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        categoryId, r'HopeCategorySkill', 'categoryId');
    BuiltValueNullFieldError.checkNotNull(
        skills, r'HopeCategorySkill', 'skills');
  }

  @override
  HopeCategorySkill rebuild(void Function(HopeCategorySkillBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  HopeCategorySkillBuilder toBuilder() =>
      new HopeCategorySkillBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is HopeCategorySkill &&
        categoryId == other.categoryId &&
        skills == other.skills;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, categoryId.hashCode);
    _$hash = $jc(_$hash, skills.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'HopeCategorySkill')
          ..add('categoryId', categoryId)
          ..add('skills', skills))
        .toString();
  }
}

class HopeCategorySkillBuilder
    implements Builder<HopeCategorySkill, HopeCategorySkillBuilder> {
  _$HopeCategorySkill? _$v;

  String? _categoryId;
  String? get categoryId => _$this._categoryId;
  set categoryId(String? categoryId) => _$this._categoryId = categoryId;

  ListBuilder<HopeJobSkill>? _skills;
  ListBuilder<HopeJobSkill> get skills =>
      _$this._skills ??= new ListBuilder<HopeJobSkill>();
  set skills(ListBuilder<HopeJobSkill>? skills) => _$this._skills = skills;

  HopeCategorySkillBuilder() {
    HopeCategorySkill._defaults(this);
  }

  HopeCategorySkillBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _categoryId = $v.categoryId;
      _skills = $v.skills.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(HopeCategorySkill other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$HopeCategorySkill;
  }

  @override
  void update(void Function(HopeCategorySkillBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  HopeCategorySkill build() => _build();

  _$HopeCategorySkill _build() {
    _$HopeCategorySkill _$result;
    try {
      _$result = _$v ??
          new _$HopeCategorySkill._(
            categoryId: BuiltValueNullFieldError.checkNotNull(
                categoryId, r'HopeCategorySkill', 'categoryId'),
            skills: skills.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'skills';
        skills.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'HopeCategorySkill', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
