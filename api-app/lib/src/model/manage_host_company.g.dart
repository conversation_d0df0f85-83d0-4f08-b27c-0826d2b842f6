// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'manage_host_company.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ManageHostCompany extends ManageHostCompany {
  @override
  final int? companyId;
  @override
  final String? name;
  @override
  final String? logoImagePath;
  @override
  final int? messageCount;
  @override
  final int? totalApplicants;
  @override
  final int? newApplicants;
  @override
  final int? unprocessedTask;
  @override
  final DateTime? lastStatusUpdate;
  @override
  final int? activeJobListings;

  factory _$ManageHostCompany(
          [void Function(ManageHostCompanyBuilder)? updates]) =>
      (new ManageHostCompanyBuilder()..update(updates))._build();

  _$ManageHostCompany._(
      {this.companyId,
      this.name,
      this.logoImagePath,
      this.messageCount,
      this.totalApplicants,
      this.newApplicants,
      this.unprocessedTask,
      this.lastStatusUpdate,
      this.activeJobListings})
      : super._();

  @override
  ManageHostCompany rebuild(void Function(ManageHostCompanyBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ManageHostCompanyBuilder toBuilder() =>
      new ManageHostCompanyBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ManageHostCompany &&
        companyId == other.companyId &&
        name == other.name &&
        logoImagePath == other.logoImagePath &&
        messageCount == other.messageCount &&
        totalApplicants == other.totalApplicants &&
        newApplicants == other.newApplicants &&
        unprocessedTask == other.unprocessedTask &&
        lastStatusUpdate == other.lastStatusUpdate &&
        activeJobListings == other.activeJobListings;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, companyId.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, logoImagePath.hashCode);
    _$hash = $jc(_$hash, messageCount.hashCode);
    _$hash = $jc(_$hash, totalApplicants.hashCode);
    _$hash = $jc(_$hash, newApplicants.hashCode);
    _$hash = $jc(_$hash, unprocessedTask.hashCode);
    _$hash = $jc(_$hash, lastStatusUpdate.hashCode);
    _$hash = $jc(_$hash, activeJobListings.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ManageHostCompany')
          ..add('companyId', companyId)
          ..add('name', name)
          ..add('logoImagePath', logoImagePath)
          ..add('messageCount', messageCount)
          ..add('totalApplicants', totalApplicants)
          ..add('newApplicants', newApplicants)
          ..add('unprocessedTask', unprocessedTask)
          ..add('lastStatusUpdate', lastStatusUpdate)
          ..add('activeJobListings', activeJobListings))
        .toString();
  }
}

class ManageHostCompanyBuilder
    implements Builder<ManageHostCompany, ManageHostCompanyBuilder> {
  _$ManageHostCompany? _$v;

  int? _companyId;
  int? get companyId => _$this._companyId;
  set companyId(int? companyId) => _$this._companyId = companyId;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _logoImagePath;
  String? get logoImagePath => _$this._logoImagePath;
  set logoImagePath(String? logoImagePath) =>
      _$this._logoImagePath = logoImagePath;

  int? _messageCount;
  int? get messageCount => _$this._messageCount;
  set messageCount(int? messageCount) => _$this._messageCount = messageCount;

  int? _totalApplicants;
  int? get totalApplicants => _$this._totalApplicants;
  set totalApplicants(int? totalApplicants) =>
      _$this._totalApplicants = totalApplicants;

  int? _newApplicants;
  int? get newApplicants => _$this._newApplicants;
  set newApplicants(int? newApplicants) =>
      _$this._newApplicants = newApplicants;

  int? _unprocessedTask;
  int? get unprocessedTask => _$this._unprocessedTask;
  set unprocessedTask(int? unprocessedTask) =>
      _$this._unprocessedTask = unprocessedTask;

  DateTime? _lastStatusUpdate;
  DateTime? get lastStatusUpdate => _$this._lastStatusUpdate;
  set lastStatusUpdate(DateTime? lastStatusUpdate) =>
      _$this._lastStatusUpdate = lastStatusUpdate;

  int? _activeJobListings;
  int? get activeJobListings => _$this._activeJobListings;
  set activeJobListings(int? activeJobListings) =>
      _$this._activeJobListings = activeJobListings;

  ManageHostCompanyBuilder() {
    ManageHostCompany._defaults(this);
  }

  ManageHostCompanyBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _companyId = $v.companyId;
      _name = $v.name;
      _logoImagePath = $v.logoImagePath;
      _messageCount = $v.messageCount;
      _totalApplicants = $v.totalApplicants;
      _newApplicants = $v.newApplicants;
      _unprocessedTask = $v.unprocessedTask;
      _lastStatusUpdate = $v.lastStatusUpdate;
      _activeJobListings = $v.activeJobListings;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ManageHostCompany other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$ManageHostCompany;
  }

  @override
  void update(void Function(ManageHostCompanyBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ManageHostCompany build() => _build();

  _$ManageHostCompany _build() {
    final _$result = _$v ??
        new _$ManageHostCompany._(
          companyId: companyId,
          name: name,
          logoImagePath: logoImagePath,
          messageCount: messageCount,
          totalApplicants: totalApplicants,
          newApplicants: newApplicants,
          unprocessedTask: unprocessedTask,
          lastStatusUpdate: lastStatusUpdate,
          activeJobListings: activeJobListings,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
