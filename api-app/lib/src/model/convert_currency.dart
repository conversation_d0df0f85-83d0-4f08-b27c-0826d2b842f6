//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'convert_currency.g.dart';

/// ConvertCurrency
///
/// Properties:
/// * [amount] 
/// * [fromCurrency] 
/// * [toCurrency] 
@BuiltValue()
abstract class ConvertCurrency implements Built<ConvertCurrency, ConvertCurrencyBuilder> {
  @BuiltValueField(wireName: r'amount')
  num get amount;

  @BuiltValueField(wireName: r'from_currency')
  String get fromCurrency;

  @BuiltValueField(wireName: r'to_currency')
  String get toCurrency;

  ConvertCurrency._();

  factory ConvertCurrency([void updates(ConvertCurrencyBuilder b)]) = _$ConvertCurrency;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(ConvertCurrencyBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<ConvertCurrency> get serializer => _$ConvertCurrencySerializer();
}

class _$ConvertCurrencySerializer implements PrimitiveSerializer<ConvertCurrency> {
  @override
  final Iterable<Type> types = const [ConvertCurrency, _$ConvertCurrency];

  @override
  final String wireName = r'ConvertCurrency';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    ConvertCurrency object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'amount';
    yield serializers.serialize(
      object.amount,
      specifiedType: const FullType(num),
    );
    yield r'from_currency';
    yield serializers.serialize(
      object.fromCurrency,
      specifiedType: const FullType(String),
    );
    yield r'to_currency';
    yield serializers.serialize(
      object.toCurrency,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    ConvertCurrency object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required ConvertCurrencyBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'amount':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(num),
          ) as num;
          result.amount = valueDes;
          break;
        case r'from_currency':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.fromCurrency = valueDes;
          break;
        case r'to_currency':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.toCurrency = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  ConvertCurrency deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = ConvertCurrencyBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

