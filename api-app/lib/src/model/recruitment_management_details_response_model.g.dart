// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recruitment_management_details_response_model.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$RecruitmentManagementDetailsResponseModel
    extends RecruitmentManagementDetailsResponseModel {
  @override
  final String? message;
  @override
  final RecruitmentManagementDetail data;
  @override
  final BuiltList<ErrorDetail>? errors;

  factory _$RecruitmentManagementDetailsResponseModel(
          [void Function(RecruitmentManagementDetailsResponseModelBuilder)?
              updates]) =>
      (new RecruitmentManagementDetailsResponseModelBuilder()..update(updates))
          ._build();

  _$RecruitmentManagementDetailsResponseModel._(
      {this.message, required this.data, this.errors})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        data, r'RecruitmentManagementDetailsResponseModel', 'data');
  }

  @override
  RecruitmentManagementDetailsResponseModel rebuild(
          void Function(RecruitmentManagementDetailsResponseModelBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  RecruitmentManagementDetailsResponseModelBuilder toBuilder() =>
      new RecruitmentManagementDetailsResponseModelBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is RecruitmentManagementDetailsResponseModel &&
        message == other.message &&
        data == other.data &&
        errors == other.errors;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, errors.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'RecruitmentManagementDetailsResponseModel')
          ..add('message', message)
          ..add('data', data)
          ..add('errors', errors))
        .toString();
  }
}

class RecruitmentManagementDetailsResponseModelBuilder
    implements
        Builder<RecruitmentManagementDetailsResponseModel,
            RecruitmentManagementDetailsResponseModelBuilder> {
  _$RecruitmentManagementDetailsResponseModel? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  RecruitmentManagementDetailBuilder? _data;
  RecruitmentManagementDetailBuilder get data =>
      _$this._data ??= new RecruitmentManagementDetailBuilder();
  set data(RecruitmentManagementDetailBuilder? data) => _$this._data = data;

  ListBuilder<ErrorDetail>? _errors;
  ListBuilder<ErrorDetail> get errors =>
      _$this._errors ??= new ListBuilder<ErrorDetail>();
  set errors(ListBuilder<ErrorDetail>? errors) => _$this._errors = errors;

  RecruitmentManagementDetailsResponseModelBuilder() {
    RecruitmentManagementDetailsResponseModel._defaults(this);
  }

  RecruitmentManagementDetailsResponseModelBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _data = $v.data.toBuilder();
      _errors = $v.errors?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(RecruitmentManagementDetailsResponseModel other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$RecruitmentManagementDetailsResponseModel;
  }

  @override
  void update(
      void Function(RecruitmentManagementDetailsResponseModelBuilder)?
          updates) {
    if (updates != null) updates(this);
  }

  @override
  RecruitmentManagementDetailsResponseModel build() => _build();

  _$RecruitmentManagementDetailsResponseModel _build() {
    _$RecruitmentManagementDetailsResponseModel _$result;
    try {
      _$result = _$v ??
          new _$RecruitmentManagementDetailsResponseModel._(
            message: message,
            data: data.build(),
            errors: _errors?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        data.build();
        _$failedField = 'errors';
        _errors?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'RecruitmentManagementDetailsResponseModel',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
