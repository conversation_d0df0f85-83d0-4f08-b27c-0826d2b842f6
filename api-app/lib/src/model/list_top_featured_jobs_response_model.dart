//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/error_detail.dart';
import 'package:openapi/src/model/featured_job.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'list_top_featured_jobs_response_model.g.dart';

/// ListTopFeaturedJobsResponseModel
///
/// Properties:
/// * [message] 
/// * [data] 
/// * [errors] 
@BuiltValue()
abstract class ListTopFeaturedJobsResponseModel implements Built<ListTopFeaturedJobsResponseModel, ListTopFeaturedJobsResponseModelBuilder> {
  @BuiltValueField(wireName: r'message')
  String? get message;

  @BuiltValueField(wireName: r'data')
  BuiltList<FeaturedJob> get data;

  @BuiltValueField(wireName: r'errors')
  BuiltList<ErrorDetail>? get errors;

  ListTopFeaturedJobsResponseModel._();

  factory ListTopFeaturedJobsResponseModel([void updates(ListTopFeaturedJobsResponseModelBuilder b)]) = _$ListTopFeaturedJobsResponseModel;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(ListTopFeaturedJobsResponseModelBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<ListTopFeaturedJobsResponseModel> get serializer => _$ListTopFeaturedJobsResponseModelSerializer();
}

class _$ListTopFeaturedJobsResponseModelSerializer implements PrimitiveSerializer<ListTopFeaturedJobsResponseModel> {
  @override
  final Iterable<Type> types = const [ListTopFeaturedJobsResponseModel, _$ListTopFeaturedJobsResponseModel];

  @override
  final String wireName = r'ListTopFeaturedJobsResponseModel';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    ListTopFeaturedJobsResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'message';
    yield object.message == null ? null : serializers.serialize(
      object.message,
      specifiedType: const FullType.nullable(String),
    );
    yield r'data';
    yield serializers.serialize(
      object.data,
      specifiedType: const FullType(BuiltList, [FullType(FeaturedJob)]),
    );
    yield r'errors';
    yield object.errors == null ? null : serializers.serialize(
      object.errors,
      specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    ListTopFeaturedJobsResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required ListTopFeaturedJobsResponseModelBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'message':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.message = valueDes;
          break;
        case r'data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(FeaturedJob)]),
          ) as BuiltList<FeaturedJob>;
          result.data.replace(valueDes);
          break;
        case r'errors':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
          ) as BuiltList<ErrorDetail>?;
          if (valueDes == null) continue;
          result.errors.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  ListTopFeaturedJobsResponseModel deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = ListTopFeaturedJobsResponseModelBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

