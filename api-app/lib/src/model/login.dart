//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'login.g.dart';

/// Login
///
/// Properties:
/// * [email] 
/// * [password] 
/// * [captchaKey] 
/// * [captchaValue] 
/// * [userType] 
@BuiltValue()
abstract class Login implements Built<Login, LoginBuilder> {
  @BuiltValueField(wireName: r'email')
  String get email;

  @BuiltValueField(wireName: r'password')
  String get password;

  @BuiltValueField(wireName: r'captcha_key')
  String? get captchaKey;

  @BuiltValueField(wireName: r'captcha_value')
  String? get captchaValue;

  @BuiltValueField(wireName: r'user_type')
  int? get userType;

  Login._();

  factory Login([void updates(LoginBuilder b)]) = _$Login;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(LoginBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<Login> get serializer => _$LoginSerializer();
}

class _$LoginSerializer implements PrimitiveSerializer<Login> {
  @override
  final Iterable<Type> types = const [Login, _$Login];

  @override
  final String wireName = r'Login';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    Login object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'email';
    yield serializers.serialize(
      object.email,
      specifiedType: const FullType(String),
    );
    yield r'password';
    yield serializers.serialize(
      object.password,
      specifiedType: const FullType(String),
    );
    yield r'captcha_key';
    yield object.captchaKey == null ? null : serializers.serialize(
      object.captchaKey,
      specifiedType: const FullType.nullable(String),
    );
    yield r'captcha_value';
    yield object.captchaValue == null ? null : serializers.serialize(
      object.captchaValue,
      specifiedType: const FullType.nullable(String),
    );
    if (object.userType != null) {
      yield r'user_type';
      yield serializers.serialize(
        object.userType,
        specifiedType: const FullType.nullable(int),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    Login object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required LoginBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'email':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.email = valueDes;
          break;
        case r'password':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.password = valueDes;
          break;
        case r'captcha_key':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.captchaKey = valueDes;
          break;
        case r'captcha_value':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.captchaValue = valueDes;
          break;
        case r'user_type':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.userType = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  Login deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = LoginBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

