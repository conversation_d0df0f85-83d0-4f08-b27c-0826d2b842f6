// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_company_apply_details.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GeneralCompanyApplyDetails extends GeneralCompanyApplyDetails {
  @override
  final int? applyId;
  @override
  final String? payrollPrice;
  @override
  final String? payrollCode;
  @override
  final int? recruitProgressCode;
  @override
  final DateTime? progressUpdateDatetime;
  @override
  final DateTime? interviewDatetime;
  @override
  final Date? joingDate;
  @override
  final String? jobCode;
  @override
  final String? employCode;
  @override
  final String? placeCode;
  @override
  final String? benefits;
  @override
  final Date? offerContractDate;
  @override
  final String? offerPdfPath;
  @override
  final Date? expiryDate;
  @override
  final int? isFromSupport;
  @override
  final DateTime? created;
  @override
  final DateTime? updated;
  @override
  final int? recruit;
  @override
  final int? group;
  @override
  final int engineer;
  @override
  final int? agencyCompany;
  @override
  final int? agencyAgent;
  @override
  final int? hostCompany;
  @override
  final int? hostAgent;
  @override
  final int? supportCompany;
  @override
  final int? supportAgent;
  @override
  final int? engineerAcceptSign;

  factory _$GeneralCompanyApplyDetails(
          [void Function(GeneralCompanyApplyDetailsBuilder)? updates]) =>
      (new GeneralCompanyApplyDetailsBuilder()..update(updates))._build();

  _$GeneralCompanyApplyDetails._(
      {this.applyId,
      this.payrollPrice,
      this.payrollCode,
      this.recruitProgressCode,
      this.progressUpdateDatetime,
      this.interviewDatetime,
      this.joingDate,
      this.jobCode,
      this.employCode,
      this.placeCode,
      this.benefits,
      this.offerContractDate,
      this.offerPdfPath,
      this.expiryDate,
      this.isFromSupport,
      this.created,
      this.updated,
      this.recruit,
      this.group,
      required this.engineer,
      this.agencyCompany,
      this.agencyAgent,
      this.hostCompany,
      this.hostAgent,
      this.supportCompany,
      this.supportAgent,
      this.engineerAcceptSign})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        engineer, r'GeneralCompanyApplyDetails', 'engineer');
  }

  @override
  GeneralCompanyApplyDetails rebuild(
          void Function(GeneralCompanyApplyDetailsBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GeneralCompanyApplyDetailsBuilder toBuilder() =>
      new GeneralCompanyApplyDetailsBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GeneralCompanyApplyDetails &&
        applyId == other.applyId &&
        payrollPrice == other.payrollPrice &&
        payrollCode == other.payrollCode &&
        recruitProgressCode == other.recruitProgressCode &&
        progressUpdateDatetime == other.progressUpdateDatetime &&
        interviewDatetime == other.interviewDatetime &&
        joingDate == other.joingDate &&
        jobCode == other.jobCode &&
        employCode == other.employCode &&
        placeCode == other.placeCode &&
        benefits == other.benefits &&
        offerContractDate == other.offerContractDate &&
        offerPdfPath == other.offerPdfPath &&
        expiryDate == other.expiryDate &&
        isFromSupport == other.isFromSupport &&
        created == other.created &&
        updated == other.updated &&
        recruit == other.recruit &&
        group == other.group &&
        engineer == other.engineer &&
        agencyCompany == other.agencyCompany &&
        agencyAgent == other.agencyAgent &&
        hostCompany == other.hostCompany &&
        hostAgent == other.hostAgent &&
        supportCompany == other.supportCompany &&
        supportAgent == other.supportAgent &&
        engineerAcceptSign == other.engineerAcceptSign;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, applyId.hashCode);
    _$hash = $jc(_$hash, payrollPrice.hashCode);
    _$hash = $jc(_$hash, payrollCode.hashCode);
    _$hash = $jc(_$hash, recruitProgressCode.hashCode);
    _$hash = $jc(_$hash, progressUpdateDatetime.hashCode);
    _$hash = $jc(_$hash, interviewDatetime.hashCode);
    _$hash = $jc(_$hash, joingDate.hashCode);
    _$hash = $jc(_$hash, jobCode.hashCode);
    _$hash = $jc(_$hash, employCode.hashCode);
    _$hash = $jc(_$hash, placeCode.hashCode);
    _$hash = $jc(_$hash, benefits.hashCode);
    _$hash = $jc(_$hash, offerContractDate.hashCode);
    _$hash = $jc(_$hash, offerPdfPath.hashCode);
    _$hash = $jc(_$hash, expiryDate.hashCode);
    _$hash = $jc(_$hash, isFromSupport.hashCode);
    _$hash = $jc(_$hash, created.hashCode);
    _$hash = $jc(_$hash, updated.hashCode);
    _$hash = $jc(_$hash, recruit.hashCode);
    _$hash = $jc(_$hash, group.hashCode);
    _$hash = $jc(_$hash, engineer.hashCode);
    _$hash = $jc(_$hash, agencyCompany.hashCode);
    _$hash = $jc(_$hash, agencyAgent.hashCode);
    _$hash = $jc(_$hash, hostCompany.hashCode);
    _$hash = $jc(_$hash, hostAgent.hashCode);
    _$hash = $jc(_$hash, supportCompany.hashCode);
    _$hash = $jc(_$hash, supportAgent.hashCode);
    _$hash = $jc(_$hash, engineerAcceptSign.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GeneralCompanyApplyDetails')
          ..add('applyId', applyId)
          ..add('payrollPrice', payrollPrice)
          ..add('payrollCode', payrollCode)
          ..add('recruitProgressCode', recruitProgressCode)
          ..add('progressUpdateDatetime', progressUpdateDatetime)
          ..add('interviewDatetime', interviewDatetime)
          ..add('joingDate', joingDate)
          ..add('jobCode', jobCode)
          ..add('employCode', employCode)
          ..add('placeCode', placeCode)
          ..add('benefits', benefits)
          ..add('offerContractDate', offerContractDate)
          ..add('offerPdfPath', offerPdfPath)
          ..add('expiryDate', expiryDate)
          ..add('isFromSupport', isFromSupport)
          ..add('created', created)
          ..add('updated', updated)
          ..add('recruit', recruit)
          ..add('group', group)
          ..add('engineer', engineer)
          ..add('agencyCompany', agencyCompany)
          ..add('agencyAgent', agencyAgent)
          ..add('hostCompany', hostCompany)
          ..add('hostAgent', hostAgent)
          ..add('supportCompany', supportCompany)
          ..add('supportAgent', supportAgent)
          ..add('engineerAcceptSign', engineerAcceptSign))
        .toString();
  }
}

class GeneralCompanyApplyDetailsBuilder
    implements
        Builder<GeneralCompanyApplyDetails, GeneralCompanyApplyDetailsBuilder> {
  _$GeneralCompanyApplyDetails? _$v;

  int? _applyId;
  int? get applyId => _$this._applyId;
  set applyId(int? applyId) => _$this._applyId = applyId;

  String? _payrollPrice;
  String? get payrollPrice => _$this._payrollPrice;
  set payrollPrice(String? payrollPrice) => _$this._payrollPrice = payrollPrice;

  String? _payrollCode;
  String? get payrollCode => _$this._payrollCode;
  set payrollCode(String? payrollCode) => _$this._payrollCode = payrollCode;

  int? _recruitProgressCode;
  int? get recruitProgressCode => _$this._recruitProgressCode;
  set recruitProgressCode(int? recruitProgressCode) =>
      _$this._recruitProgressCode = recruitProgressCode;

  DateTime? _progressUpdateDatetime;
  DateTime? get progressUpdateDatetime => _$this._progressUpdateDatetime;
  set progressUpdateDatetime(DateTime? progressUpdateDatetime) =>
      _$this._progressUpdateDatetime = progressUpdateDatetime;

  DateTime? _interviewDatetime;
  DateTime? get interviewDatetime => _$this._interviewDatetime;
  set interviewDatetime(DateTime? interviewDatetime) =>
      _$this._interviewDatetime = interviewDatetime;

  Date? _joingDate;
  Date? get joingDate => _$this._joingDate;
  set joingDate(Date? joingDate) => _$this._joingDate = joingDate;

  String? _jobCode;
  String? get jobCode => _$this._jobCode;
  set jobCode(String? jobCode) => _$this._jobCode = jobCode;

  String? _employCode;
  String? get employCode => _$this._employCode;
  set employCode(String? employCode) => _$this._employCode = employCode;

  String? _placeCode;
  String? get placeCode => _$this._placeCode;
  set placeCode(String? placeCode) => _$this._placeCode = placeCode;

  String? _benefits;
  String? get benefits => _$this._benefits;
  set benefits(String? benefits) => _$this._benefits = benefits;

  Date? _offerContractDate;
  Date? get offerContractDate => _$this._offerContractDate;
  set offerContractDate(Date? offerContractDate) =>
      _$this._offerContractDate = offerContractDate;

  String? _offerPdfPath;
  String? get offerPdfPath => _$this._offerPdfPath;
  set offerPdfPath(String? offerPdfPath) => _$this._offerPdfPath = offerPdfPath;

  Date? _expiryDate;
  Date? get expiryDate => _$this._expiryDate;
  set expiryDate(Date? expiryDate) => _$this._expiryDate = expiryDate;

  int? _isFromSupport;
  int? get isFromSupport => _$this._isFromSupport;
  set isFromSupport(int? isFromSupport) =>
      _$this._isFromSupport = isFromSupport;

  DateTime? _created;
  DateTime? get created => _$this._created;
  set created(DateTime? created) => _$this._created = created;

  DateTime? _updated;
  DateTime? get updated => _$this._updated;
  set updated(DateTime? updated) => _$this._updated = updated;

  int? _recruit;
  int? get recruit => _$this._recruit;
  set recruit(int? recruit) => _$this._recruit = recruit;

  int? _group;
  int? get group => _$this._group;
  set group(int? group) => _$this._group = group;

  int? _engineer;
  int? get engineer => _$this._engineer;
  set engineer(int? engineer) => _$this._engineer = engineer;

  int? _agencyCompany;
  int? get agencyCompany => _$this._agencyCompany;
  set agencyCompany(int? agencyCompany) =>
      _$this._agencyCompany = agencyCompany;

  int? _agencyAgent;
  int? get agencyAgent => _$this._agencyAgent;
  set agencyAgent(int? agencyAgent) => _$this._agencyAgent = agencyAgent;

  int? _hostCompany;
  int? get hostCompany => _$this._hostCompany;
  set hostCompany(int? hostCompany) => _$this._hostCompany = hostCompany;

  int? _hostAgent;
  int? get hostAgent => _$this._hostAgent;
  set hostAgent(int? hostAgent) => _$this._hostAgent = hostAgent;

  int? _supportCompany;
  int? get supportCompany => _$this._supportCompany;
  set supportCompany(int? supportCompany) =>
      _$this._supportCompany = supportCompany;

  int? _supportAgent;
  int? get supportAgent => _$this._supportAgent;
  set supportAgent(int? supportAgent) => _$this._supportAgent = supportAgent;

  int? _engineerAcceptSign;
  int? get engineerAcceptSign => _$this._engineerAcceptSign;
  set engineerAcceptSign(int? engineerAcceptSign) =>
      _$this._engineerAcceptSign = engineerAcceptSign;

  GeneralCompanyApplyDetailsBuilder() {
    GeneralCompanyApplyDetails._defaults(this);
  }

  GeneralCompanyApplyDetailsBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _applyId = $v.applyId;
      _payrollPrice = $v.payrollPrice;
      _payrollCode = $v.payrollCode;
      _recruitProgressCode = $v.recruitProgressCode;
      _progressUpdateDatetime = $v.progressUpdateDatetime;
      _interviewDatetime = $v.interviewDatetime;
      _joingDate = $v.joingDate;
      _jobCode = $v.jobCode;
      _employCode = $v.employCode;
      _placeCode = $v.placeCode;
      _benefits = $v.benefits;
      _offerContractDate = $v.offerContractDate;
      _offerPdfPath = $v.offerPdfPath;
      _expiryDate = $v.expiryDate;
      _isFromSupport = $v.isFromSupport;
      _created = $v.created;
      _updated = $v.updated;
      _recruit = $v.recruit;
      _group = $v.group;
      _engineer = $v.engineer;
      _agencyCompany = $v.agencyCompany;
      _agencyAgent = $v.agencyAgent;
      _hostCompany = $v.hostCompany;
      _hostAgent = $v.hostAgent;
      _supportCompany = $v.supportCompany;
      _supportAgent = $v.supportAgent;
      _engineerAcceptSign = $v.engineerAcceptSign;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GeneralCompanyApplyDetails other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GeneralCompanyApplyDetails;
  }

  @override
  void update(void Function(GeneralCompanyApplyDetailsBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GeneralCompanyApplyDetails build() => _build();

  _$GeneralCompanyApplyDetails _build() {
    final _$result = _$v ??
        new _$GeneralCompanyApplyDetails._(
          applyId: applyId,
          payrollPrice: payrollPrice,
          payrollCode: payrollCode,
          recruitProgressCode: recruitProgressCode,
          progressUpdateDatetime: progressUpdateDatetime,
          interviewDatetime: interviewDatetime,
          joingDate: joingDate,
          jobCode: jobCode,
          employCode: employCode,
          placeCode: placeCode,
          benefits: benefits,
          offerContractDate: offerContractDate,
          offerPdfPath: offerPdfPath,
          expiryDate: expiryDate,
          isFromSupport: isFromSupport,
          created: created,
          updated: updated,
          recruit: recruit,
          group: group,
          engineer: BuiltValueNullFieldError.checkNotNull(
              engineer, r'GeneralCompanyApplyDetails', 'engineer'),
          agencyCompany: agencyCompany,
          agencyAgent: agencyAgent,
          hostCompany: hostCompany,
          hostAgent: hostAgent,
          supportCompany: supportCompany,
          supportAgent: supportAgent,
          engineerAcceptSign: engineerAcceptSign,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
