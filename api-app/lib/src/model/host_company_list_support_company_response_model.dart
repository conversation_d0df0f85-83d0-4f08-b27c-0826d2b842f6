//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/host_company_get_support_company.dart';
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/error_detail.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'host_company_list_support_company_response_model.g.dart';

/// HostCompanyListSupportCompanyResponseModel
///
/// Properties:
/// * [message] 
/// * [errors] 
/// * [data] 
@BuiltValue()
abstract class HostCompanyListSupportCompanyResponseModel implements Built<HostCompanyListSupportCompanyResponseModel, HostCompanyListSupportCompanyResponseModelBuilder> {
  @BuiltValueField(wireName: r'message')
  String? get message;

  @BuiltValueField(wireName: r'errors')
  BuiltList<ErrorDetail>? get errors;

  @BuiltValueField(wireName: r'data')
  BuiltList<HostCompanyGetSupportCompany> get data;

  HostCompanyListSupportCompanyResponseModel._();

  factory HostCompanyListSupportCompanyResponseModel([void updates(HostCompanyListSupportCompanyResponseModelBuilder b)]) = _$HostCompanyListSupportCompanyResponseModel;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(HostCompanyListSupportCompanyResponseModelBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<HostCompanyListSupportCompanyResponseModel> get serializer => _$HostCompanyListSupportCompanyResponseModelSerializer();
}

class _$HostCompanyListSupportCompanyResponseModelSerializer implements PrimitiveSerializer<HostCompanyListSupportCompanyResponseModel> {
  @override
  final Iterable<Type> types = const [HostCompanyListSupportCompanyResponseModel, _$HostCompanyListSupportCompanyResponseModel];

  @override
  final String wireName = r'HostCompanyListSupportCompanyResponseModel';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    HostCompanyListSupportCompanyResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'message';
    yield object.message == null ? null : serializers.serialize(
      object.message,
      specifiedType: const FullType.nullable(String),
    );
    yield r'errors';
    yield object.errors == null ? null : serializers.serialize(
      object.errors,
      specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
    );
    yield r'data';
    yield serializers.serialize(
      object.data,
      specifiedType: const FullType(BuiltList, [FullType(HostCompanyGetSupportCompany)]),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    HostCompanyListSupportCompanyResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required HostCompanyListSupportCompanyResponseModelBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'message':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.message = valueDes;
          break;
        case r'errors':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
          ) as BuiltList<ErrorDetail>?;
          if (valueDes == null) continue;
          result.errors.replace(valueDes);
          break;
        case r'data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(HostCompanyGetSupportCompany)]),
          ) as BuiltList<HostCompanyGetSupportCompany>;
          result.data.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  HostCompanyListSupportCompanyResponseModel deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = HostCompanyListSupportCompanyResponseModelBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

