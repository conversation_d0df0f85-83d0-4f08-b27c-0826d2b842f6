//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/assessment_question.dart';
import 'package:openapi/src/model/assessment_answer.dart';
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'assessment_question_data.g.dart';

/// AssessmentQuestionData
///
/// Properties:
/// * [question] 
/// * [options] 
@BuiltValue()
abstract class AssessmentQuestionData implements Built<AssessmentQuestionData, AssessmentQuestionDataBuilder> {
  @BuiltValueField(wireName: r'question')
  AssessmentQuestion get question;

  @BuiltValueField(wireName: r'options')
  BuiltList<AssessmentAnswer> get options;

  AssessmentQuestionData._();

  factory AssessmentQuestionData([void updates(AssessmentQuestionDataBuilder b)]) = _$AssessmentQuestionData;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AssessmentQuestionDataBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AssessmentQuestionData> get serializer => _$AssessmentQuestionDataSerializer();
}

class _$AssessmentQuestionDataSerializer implements PrimitiveSerializer<AssessmentQuestionData> {
  @override
  final Iterable<Type> types = const [AssessmentQuestionData, _$AssessmentQuestionData];

  @override
  final String wireName = r'AssessmentQuestionData';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AssessmentQuestionData object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'question';
    yield serializers.serialize(
      object.question,
      specifiedType: const FullType(AssessmentQuestion),
    );
    yield r'options';
    yield serializers.serialize(
      object.options,
      specifiedType: const FullType(BuiltList, [FullType(AssessmentAnswer)]),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    AssessmentQuestionData object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AssessmentQuestionDataBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'question':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(AssessmentQuestion),
          ) as AssessmentQuestion;
          result.question.replace(valueDes);
          break;
        case r'options':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(AssessmentAnswer)]),
          ) as BuiltList<AssessmentAnswer>;
          result.options.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AssessmentQuestionData deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AssessmentQuestionDataBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

