//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'manage_host_company.g.dart';

/// ManageHostCompany
///
/// Properties:
/// * [companyId] 
/// * [name] 
/// * [logoImagePath] 
/// * [messageCount] 
/// * [totalApplicants] 
/// * [newApplicants] 
/// * [unprocessedTask] 
/// * [lastStatusUpdate] 
/// * [activeJobListings] 
@BuiltValue()
abstract class ManageHostCompany implements Built<ManageHostCompany, ManageHostCompanyBuilder> {
  @BuiltValueField(wireName: r'company_id')
  int? get companyId;

  @BuiltValueField(wireName: r'name')
  String? get name;

  @BuiltValueField(wireName: r'logo_image_path')
  String? get logoImagePath;

  @BuiltValueField(wireName: r'message_count')
  int? get messageCount;

  @BuiltValueField(wireName: r'total_applicants')
  int? get totalApplicants;

  @BuiltValueField(wireName: r'new_applicants')
  int? get newApplicants;

  @BuiltValueField(wireName: r'unprocessed_task')
  int? get unprocessedTask;

  @BuiltValueField(wireName: r'last_status_update')
  DateTime? get lastStatusUpdate;

  @BuiltValueField(wireName: r'active_job_listings')
  int? get activeJobListings;

  ManageHostCompany._();

  factory ManageHostCompany([void updates(ManageHostCompanyBuilder b)]) = _$ManageHostCompany;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(ManageHostCompanyBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<ManageHostCompany> get serializer => _$ManageHostCompanySerializer();
}

class _$ManageHostCompanySerializer implements PrimitiveSerializer<ManageHostCompany> {
  @override
  final Iterable<Type> types = const [ManageHostCompany, _$ManageHostCompany];

  @override
  final String wireName = r'ManageHostCompany';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    ManageHostCompany object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.companyId != null) {
      yield r'company_id';
      yield serializers.serialize(
        object.companyId,
        specifiedType: const FullType(int),
      );
    }
    if (object.name != null) {
      yield r'name';
      yield serializers.serialize(
        object.name,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.logoImagePath != null) {
      yield r'logo_image_path';
      yield serializers.serialize(
        object.logoImagePath,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.messageCount != null) {
      yield r'message_count';
      yield serializers.serialize(
        object.messageCount,
        specifiedType: const FullType(int),
      );
    }
    if (object.totalApplicants != null) {
      yield r'total_applicants';
      yield serializers.serialize(
        object.totalApplicants,
        specifiedType: const FullType(int),
      );
    }
    if (object.newApplicants != null) {
      yield r'new_applicants';
      yield serializers.serialize(
        object.newApplicants,
        specifiedType: const FullType(int),
      );
    }
    if (object.unprocessedTask != null) {
      yield r'unprocessed_task';
      yield serializers.serialize(
        object.unprocessedTask,
        specifiedType: const FullType(int),
      );
    }
    if (object.lastStatusUpdate != null) {
      yield r'last_status_update';
      yield serializers.serialize(
        object.lastStatusUpdate,
        specifiedType: const FullType(DateTime),
      );
    }
    if (object.activeJobListings != null) {
      yield r'active_job_listings';
      yield serializers.serialize(
        object.activeJobListings,
        specifiedType: const FullType(int),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    ManageHostCompany object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required ManageHostCompanyBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'company_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.companyId = valueDes;
          break;
        case r'name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.name = valueDes;
          break;
        case r'logo_image_path':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.logoImagePath = valueDes;
          break;
        case r'message_count':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.messageCount = valueDes;
          break;
        case r'total_applicants':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.totalApplicants = valueDes;
          break;
        case r'new_applicants':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.newApplicants = valueDes;
          break;
        case r'unprocessed_task':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.unprocessedTask = valueDes;
          break;
        case r'last_status_update':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(DateTime),
          ) as DateTime;
          result.lastStatusUpdate = valueDes;
          break;
        case r'active_job_listings':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.activeJobListings = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  ManageHostCompany deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = ManageHostCompanyBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

