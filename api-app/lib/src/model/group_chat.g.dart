// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'group_chat.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GroupChat extends GroupChat {
  @override
  final int id;
  @override
  final int group;
  @override
  final int? chat;
  @override
  final DateTime? updated;
  @override
  final DateTime? created;
  @override
  final Chat? lastedMessage;
  @override
  final BuiltList<UserChat>? users;
  @override
  final int? progressCode;
  @override
  final GeneralCompanyUserAppliedCompany? engineer;

  factory _$GroupChat([void Function(GroupChatBuilder)? updates]) =>
      (new GroupChatBuilder()..update(updates))._build();

  _$GroupChat._(
      {required this.id,
      required this.group,
      this.chat,
      this.updated,
      this.created,
      this.lastedMessage,
      this.users,
      this.progressCode,
      this.engineer})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(id, r'GroupChat', 'id');
    BuiltValueNullFieldError.checkNotNull(group, r'GroupChat', 'group');
  }

  @override
  GroupChat rebuild(void Function(GroupChatBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GroupChatBuilder toBuilder() => new GroupChatBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GroupChat &&
        id == other.id &&
        group == other.group &&
        chat == other.chat &&
        updated == other.updated &&
        created == other.created &&
        lastedMessage == other.lastedMessage &&
        users == other.users &&
        progressCode == other.progressCode &&
        engineer == other.engineer;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, group.hashCode);
    _$hash = $jc(_$hash, chat.hashCode);
    _$hash = $jc(_$hash, updated.hashCode);
    _$hash = $jc(_$hash, created.hashCode);
    _$hash = $jc(_$hash, lastedMessage.hashCode);
    _$hash = $jc(_$hash, users.hashCode);
    _$hash = $jc(_$hash, progressCode.hashCode);
    _$hash = $jc(_$hash, engineer.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GroupChat')
          ..add('id', id)
          ..add('group', group)
          ..add('chat', chat)
          ..add('updated', updated)
          ..add('created', created)
          ..add('lastedMessage', lastedMessage)
          ..add('users', users)
          ..add('progressCode', progressCode)
          ..add('engineer', engineer))
        .toString();
  }
}

class GroupChatBuilder implements Builder<GroupChat, GroupChatBuilder> {
  _$GroupChat? _$v;

  int? _id;
  int? get id => _$this._id;
  set id(int? id) => _$this._id = id;

  int? _group;
  int? get group => _$this._group;
  set group(int? group) => _$this._group = group;

  int? _chat;
  int? get chat => _$this._chat;
  set chat(int? chat) => _$this._chat = chat;

  DateTime? _updated;
  DateTime? get updated => _$this._updated;
  set updated(DateTime? updated) => _$this._updated = updated;

  DateTime? _created;
  DateTime? get created => _$this._created;
  set created(DateTime? created) => _$this._created = created;

  ChatBuilder? _lastedMessage;
  ChatBuilder get lastedMessage => _$this._lastedMessage ??= new ChatBuilder();
  set lastedMessage(ChatBuilder? lastedMessage) =>
      _$this._lastedMessage = lastedMessage;

  ListBuilder<UserChat>? _users;
  ListBuilder<UserChat> get users =>
      _$this._users ??= new ListBuilder<UserChat>();
  set users(ListBuilder<UserChat>? users) => _$this._users = users;

  int? _progressCode;
  int? get progressCode => _$this._progressCode;
  set progressCode(int? progressCode) => _$this._progressCode = progressCode;

  GeneralCompanyUserAppliedCompanyBuilder? _engineer;
  GeneralCompanyUserAppliedCompanyBuilder get engineer =>
      _$this._engineer ??= new GeneralCompanyUserAppliedCompanyBuilder();
  set engineer(GeneralCompanyUserAppliedCompanyBuilder? engineer) =>
      _$this._engineer = engineer;

  GroupChatBuilder() {
    GroupChat._defaults(this);
  }

  GroupChatBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _group = $v.group;
      _chat = $v.chat;
      _updated = $v.updated;
      _created = $v.created;
      _lastedMessage = $v.lastedMessage?.toBuilder();
      _users = $v.users?.toBuilder();
      _progressCode = $v.progressCode;
      _engineer = $v.engineer?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GroupChat other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GroupChat;
  }

  @override
  void update(void Function(GroupChatBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GroupChat build() => _build();

  _$GroupChat _build() {
    _$GroupChat _$result;
    try {
      _$result = _$v ??
          new _$GroupChat._(
            id: BuiltValueNullFieldError.checkNotNull(id, r'GroupChat', 'id'),
            group: BuiltValueNullFieldError.checkNotNull(
                group, r'GroupChat', 'group'),
            chat: chat,
            updated: updated,
            created: created,
            lastedMessage: _lastedMessage?.build(),
            users: _users?.build(),
            progressCode: progressCode,
            engineer: _engineer?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'lastedMessage';
        _lastedMessage?.build();
        _$failedField = 'users';
        _users?.build();

        _$failedField = 'engineer';
        _engineer?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'GroupChat', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
