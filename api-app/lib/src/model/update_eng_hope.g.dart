// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_eng_hope.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UpdateEngHope extends UpdateEngHope {
  @override
  final String? placeCode1;
  @override
  final String? placeCode2;
  @override
  final String? placeCode3;
  @override
  final String? employCode;
  @override
  final String? payrollPrice;
  @override
  final String? payrollCode;
  @override
  final BuiltList<UpdateHopeJobSkill> jobSkills;
  @override
  final String? remoteCode;

  factory _$UpdateEngHope([void Function(UpdateEngHopeBuilder)? updates]) =>
      (new UpdateEngHopeBuilder()..update(updates))._build();

  _$UpdateEngHope._(
      {this.placeCode1,
      this.placeCode2,
      this.placeCode3,
      this.employCode,
      this.payrollPrice,
      this.payrollCode,
      required this.jobSkills,
      this.remoteCode})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        jobSkills, r'UpdateEngHope', 'jobSkills');
  }

  @override
  UpdateEngHope rebuild(void Function(UpdateEngHopeBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UpdateEngHopeBuilder toBuilder() => new UpdateEngHopeBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UpdateEngHope &&
        placeCode1 == other.placeCode1 &&
        placeCode2 == other.placeCode2 &&
        placeCode3 == other.placeCode3 &&
        employCode == other.employCode &&
        payrollPrice == other.payrollPrice &&
        payrollCode == other.payrollCode &&
        jobSkills == other.jobSkills &&
        remoteCode == other.remoteCode;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, placeCode1.hashCode);
    _$hash = $jc(_$hash, placeCode2.hashCode);
    _$hash = $jc(_$hash, placeCode3.hashCode);
    _$hash = $jc(_$hash, employCode.hashCode);
    _$hash = $jc(_$hash, payrollPrice.hashCode);
    _$hash = $jc(_$hash, payrollCode.hashCode);
    _$hash = $jc(_$hash, jobSkills.hashCode);
    _$hash = $jc(_$hash, remoteCode.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UpdateEngHope')
          ..add('placeCode1', placeCode1)
          ..add('placeCode2', placeCode2)
          ..add('placeCode3', placeCode3)
          ..add('employCode', employCode)
          ..add('payrollPrice', payrollPrice)
          ..add('payrollCode', payrollCode)
          ..add('jobSkills', jobSkills)
          ..add('remoteCode', remoteCode))
        .toString();
  }
}

class UpdateEngHopeBuilder
    implements Builder<UpdateEngHope, UpdateEngHopeBuilder> {
  _$UpdateEngHope? _$v;

  String? _placeCode1;
  String? get placeCode1 => _$this._placeCode1;
  set placeCode1(String? placeCode1) => _$this._placeCode1 = placeCode1;

  String? _placeCode2;
  String? get placeCode2 => _$this._placeCode2;
  set placeCode2(String? placeCode2) => _$this._placeCode2 = placeCode2;

  String? _placeCode3;
  String? get placeCode3 => _$this._placeCode3;
  set placeCode3(String? placeCode3) => _$this._placeCode3 = placeCode3;

  String? _employCode;
  String? get employCode => _$this._employCode;
  set employCode(String? employCode) => _$this._employCode = employCode;

  String? _payrollPrice;
  String? get payrollPrice => _$this._payrollPrice;
  set payrollPrice(String? payrollPrice) => _$this._payrollPrice = payrollPrice;

  String? _payrollCode;
  String? get payrollCode => _$this._payrollCode;
  set payrollCode(String? payrollCode) => _$this._payrollCode = payrollCode;

  ListBuilder<UpdateHopeJobSkill>? _jobSkills;
  ListBuilder<UpdateHopeJobSkill> get jobSkills =>
      _$this._jobSkills ??= new ListBuilder<UpdateHopeJobSkill>();
  set jobSkills(ListBuilder<UpdateHopeJobSkill>? jobSkills) =>
      _$this._jobSkills = jobSkills;

  String? _remoteCode;
  String? get remoteCode => _$this._remoteCode;
  set remoteCode(String? remoteCode) => _$this._remoteCode = remoteCode;

  UpdateEngHopeBuilder() {
    UpdateEngHope._defaults(this);
  }

  UpdateEngHopeBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _placeCode1 = $v.placeCode1;
      _placeCode2 = $v.placeCode2;
      _placeCode3 = $v.placeCode3;
      _employCode = $v.employCode;
      _payrollPrice = $v.payrollPrice;
      _payrollCode = $v.payrollCode;
      _jobSkills = $v.jobSkills.toBuilder();
      _remoteCode = $v.remoteCode;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UpdateEngHope other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$UpdateEngHope;
  }

  @override
  void update(void Function(UpdateEngHopeBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UpdateEngHope build() => _build();

  _$UpdateEngHope _build() {
    _$UpdateEngHope _$result;
    try {
      _$result = _$v ??
          new _$UpdateEngHope._(
            placeCode1: placeCode1,
            placeCode2: placeCode2,
            placeCode3: placeCode3,
            employCode: employCode,
            payrollPrice: payrollPrice,
            payrollCode: payrollCode,
            jobSkills: jobSkills.build(),
            remoteCode: remoteCode,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'jobSkills';
        jobSkills.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'UpdateEngHope', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
