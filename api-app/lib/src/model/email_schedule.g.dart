// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'email_schedule.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$EmailSchedule extends EmailSchedule {
  @override
  final int? id;
  @override
  final int? type;
  @override
  final String? subject;
  @override
  final String? body;
  @override
  final String? weekday;
  @override
  final String? sendTime;
  @override
  final DateTime? sendDatetime;
  @override
  final int? isValid;
  @override
  final int? isDeleted;
  @override
  final int? isRepeat;
  @override
  final DateTime? created;

  factory _$EmailSchedule([void Function(EmailScheduleBuilder)? updates]) =>
      (new EmailScheduleBuilder()..update(updates))._build();

  _$EmailSchedule._(
      {this.id,
      this.type,
      this.subject,
      this.body,
      this.weekday,
      this.sendTime,
      this.sendDatetime,
      this.isValid,
      this.isDeleted,
      this.isRepeat,
      this.created})
      : super._();

  @override
  EmailSchedule rebuild(void Function(EmailScheduleBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  EmailScheduleBuilder toBuilder() => new EmailScheduleBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is EmailSchedule &&
        id == other.id &&
        type == other.type &&
        subject == other.subject &&
        body == other.body &&
        weekday == other.weekday &&
        sendTime == other.sendTime &&
        sendDatetime == other.sendDatetime &&
        isValid == other.isValid &&
        isDeleted == other.isDeleted &&
        isRepeat == other.isRepeat &&
        created == other.created;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, type.hashCode);
    _$hash = $jc(_$hash, subject.hashCode);
    _$hash = $jc(_$hash, body.hashCode);
    _$hash = $jc(_$hash, weekday.hashCode);
    _$hash = $jc(_$hash, sendTime.hashCode);
    _$hash = $jc(_$hash, sendDatetime.hashCode);
    _$hash = $jc(_$hash, isValid.hashCode);
    _$hash = $jc(_$hash, isDeleted.hashCode);
    _$hash = $jc(_$hash, isRepeat.hashCode);
    _$hash = $jc(_$hash, created.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'EmailSchedule')
          ..add('id', id)
          ..add('type', type)
          ..add('subject', subject)
          ..add('body', body)
          ..add('weekday', weekday)
          ..add('sendTime', sendTime)
          ..add('sendDatetime', sendDatetime)
          ..add('isValid', isValid)
          ..add('isDeleted', isDeleted)
          ..add('isRepeat', isRepeat)
          ..add('created', created))
        .toString();
  }
}

class EmailScheduleBuilder
    implements Builder<EmailSchedule, EmailScheduleBuilder> {
  _$EmailSchedule? _$v;

  int? _id;
  int? get id => _$this._id;
  set id(int? id) => _$this._id = id;

  int? _type;
  int? get type => _$this._type;
  set type(int? type) => _$this._type = type;

  String? _subject;
  String? get subject => _$this._subject;
  set subject(String? subject) => _$this._subject = subject;

  String? _body;
  String? get body => _$this._body;
  set body(String? body) => _$this._body = body;

  String? _weekday;
  String? get weekday => _$this._weekday;
  set weekday(String? weekday) => _$this._weekday = weekday;

  String? _sendTime;
  String? get sendTime => _$this._sendTime;
  set sendTime(String? sendTime) => _$this._sendTime = sendTime;

  DateTime? _sendDatetime;
  DateTime? get sendDatetime => _$this._sendDatetime;
  set sendDatetime(DateTime? sendDatetime) =>
      _$this._sendDatetime = sendDatetime;

  int? _isValid;
  int? get isValid => _$this._isValid;
  set isValid(int? isValid) => _$this._isValid = isValid;

  int? _isDeleted;
  int? get isDeleted => _$this._isDeleted;
  set isDeleted(int? isDeleted) => _$this._isDeleted = isDeleted;

  int? _isRepeat;
  int? get isRepeat => _$this._isRepeat;
  set isRepeat(int? isRepeat) => _$this._isRepeat = isRepeat;

  DateTime? _created;
  DateTime? get created => _$this._created;
  set created(DateTime? created) => _$this._created = created;

  EmailScheduleBuilder() {
    EmailSchedule._defaults(this);
  }

  EmailScheduleBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _type = $v.type;
      _subject = $v.subject;
      _body = $v.body;
      _weekday = $v.weekday;
      _sendTime = $v.sendTime;
      _sendDatetime = $v.sendDatetime;
      _isValid = $v.isValid;
      _isDeleted = $v.isDeleted;
      _isRepeat = $v.isRepeat;
      _created = $v.created;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(EmailSchedule other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$EmailSchedule;
  }

  @override
  void update(void Function(EmailScheduleBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  EmailSchedule build() => _build();

  _$EmailSchedule _build() {
    final _$result = _$v ??
        new _$EmailSchedule._(
          id: id,
          type: type,
          subject: subject,
          body: body,
          weekday: weekday,
          sendTime: sendTime,
          sendDatetime: sendDatetime,
          isValid: isValid,
          isDeleted: isDeleted,
          isRepeat: isRepeat,
          created: created,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
