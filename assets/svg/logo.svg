<svg id="Logo" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="129.778" height="40" viewBox="0 0 129.778 40">
  <defs>
    <linearGradient id="linear-gradient" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#e4352b"/>
      <stop offset="0.112" stop-color="#e9652d"/>
      <stop offset="0.331" stop-color="#f0932e"/>
      <stop offset="0.719" stop-color="#fbcc00"/>
      <stop offset="0.814" stop-color="#fbcf02"/>
      <stop offset="0.903" stop-color="#fcdb0b"/>
      <stop offset="0.989" stop-color="#feed1a"/>
      <stop offset="1" stop-color="#fff11d"/>
    </linearGradient>
    <radialGradient id="radial-gradient" cx="0.232" cy="0.237" r="0.656" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="0.084" stop-color="#99dbf2"/>
      <stop offset="0.173" stop-color="#34b7e6"/>
      <stop offset="0.212" stop-color="#0ca9e2"/>
      <stop offset="0.621" stop-color="#008bc4"/>
      <stop offset="1" stop-color="#006ba3"/>
    </radialGradient>
    <linearGradient id="linear-gradient-2" x1="0.5" y1="1" x2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#00a23c"/>
      <stop offset="0.419" stop-color="#7dbd27"/>
      <stop offset="0.497" stop-color="#80be27"/>
      <stop offset="0.57" stop-color="#8cc229"/>
      <stop offset="0.642" stop-color="#9fc92c"/>
      <stop offset="0.711" stop-color="#b9d331"/>
      <stop offset="0.749" stop-color="#cbda34"/>
    </linearGradient>
    <radialGradient id="radial-gradient-2" cx="0.384" cy="0.284" r="0.728" gradientUnits="objectBoundingBox">
      <stop offset="0.039" stop-color="#fff"/>
      <stop offset="0.052" stop-color="#f8fae5"/>
      <stop offset="0.077" stop-color="#ecf1b6"/>
      <stop offset="0.104" stop-color="#e2ea8e"/>
      <stop offset="0.131" stop-color="#d9e46d"/>
      <stop offset="0.158" stop-color="#d3df54"/>
      <stop offset="0.187" stop-color="#cedc42"/>
      <stop offset="0.217" stop-color="#cbda37"/>
      <stop offset="0.251" stop-color="#cbda34"/>
      <stop offset="0.504" stop-color="#93c52a"/>
      <stop offset="0.631" stop-color="#7dbd27"/>
      <stop offset="1" stop-color="#00a23c"/>
    </radialGradient>
    <linearGradient id="linear-gradient-3" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff11d"/>
      <stop offset="0.011" stop-color="#feed1a"/>
      <stop offset="0.097" stop-color="#fcdb0b"/>
      <stop offset="0.186" stop-color="#fbcf02"/>
      <stop offset="0.281" stop-color="#fbcc00"/>
      <stop offset="0.669" stop-color="#f0932e"/>
      <stop offset="0.888" stop-color="#e9652d"/>
      <stop offset="1" stop-color="#e4352b"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="0.5" y1="1" x2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#00a23c"/>
      <stop offset="0.419" stop-color="#7dbd27"/>
      <stop offset="0.643" stop-color="#b0d02f"/>
      <stop offset="0.749" stop-color="#cbda34"/>
    </linearGradient>
    <radialGradient id="radial-gradient-3" cx="0.266" cy="0.297" r="0.529" gradientUnits="objectBoundingBox">
      <stop offset="0.044" stop-color="#fff"/>
      <stop offset="0.163" stop-color="#fff11d"/>
      <stop offset="0.167" stop-color="#feed1a"/>
      <stop offset="0.204" stop-color="#fcdb0b"/>
      <stop offset="0.241" stop-color="#fbcf02"/>
      <stop offset="0.281" stop-color="#fbcc00"/>
      <stop offset="0.669" stop-color="#f0932e"/>
      <stop offset="1" stop-color="#e6502d"/>
    </radialGradient>
    <linearGradient id="linear-gradient-5" x1="8.046" y1="-9.811" x2="8.679" y2="-9.811" xlink:href="#linear-gradient-3"/>
  </defs>
  <g id="Group_9" data-name="Group 9">
    <path id="Path_59" data-name="Path 59" d="M11.677,46.014,29.042,58.568S23.961,49.3,31.818,34.462C24.073,45.255,19.416,45.878,11.677,46.014Z" transform="translate(-11.677 -32.055)" fill="url(#linear-gradient)"/>
    <path id="Path_60" data-name="Path 60" d="M83.379,26.779c-18.59,2.14-14.858,19.48-14.858,19.48-3.845-11.534,3.508-18.231,8.631-21.343-.336-.023-.675-.035-1.017-.035a14.748,14.748,0,1,0,7.244,1.9Z" transform="translate(-48.896 -24.88)" fill="url(#radial-gradient)"/>
    <path id="Path_61" data-name="Path 61" d="M182.059,56.542,169.639,72.2s4.989-5.92-3.937-26.611C174.031,56.613,175.983,55.154,182.059,56.542Z" transform="translate(-127.012 -40.39)" fill="url(#linear-gradient-2)"/>
    <path id="Path_62" data-name="Path 62" d="M205.543,54.969a3.776,3.776,0,1,1-4.876-2.177A3.776,3.776,0,0,1,205.543,54.969Z" transform="translate(-151.376 -45.593)" fill="url(#radial-gradient-2)"/>
    <path id="Path_63" data-name="Path 63" d="M21.651,92.827l31.562,22.932H37.432Z" transform="translate(-19.146 -75.759)" fill="url(#linear-gradient-3)"/>
    <path id="Path_64" data-name="Path 64" d="M126.441,87.962s5.624,18.211-7.985,22.984l-9.6-7.016S125.188,101.372,126.441,87.962Z" transform="translate(-84.448 -72.116)" fill="url(#linear-gradient-4)"/>
    <path id="Path_65" data-name="Path 65" d="M34.93,45.934a3.776,3.776,0,1,1-4.876-2.177A3.776,3.776,0,0,1,34.93,45.934Z" transform="translate(-23.62 -38.827)" fill="url(#radial-gradient-3)"/>
    <path id="Path_66" data-name="Path 66" d="M38.542,91.46s-2.949,11.5,7.907,6.5S65.2,81.851,65.352,80.444c0,0-4.942,13.029-17.193,20.541S37.223,92.358,38.542,91.46Z" transform="translate(-31.179 -66.487)" fill="url(#linear-gradient-5)"/>
  </g>
  <g id="Group_11" data-name="Group 11" transform="translate(63.75 31.829)">
    <g id="Group_10" data-name="Group 10">
      <path id="Path_67" data-name="Path 67" d="M265.468,160.418v-.267c.782-.086.859-.124.859-.935v-3.789c0-.821-.077-.859-.859-.945v-.267h2.558v.267c-.792.086-.868.124-.868.945v3.789c0,.821.076.849.868.935v.267Z" transform="translate(-265.468 -153.556)" fill="#ba2a21"/>
      <path id="Path_68" data-name="Path 68" d="M284.978,158.492a12.1,12.1,0,0,1-.525,1.441,8.7,8.7,0,0,1-1.747.229,3.154,3.154,0,0,1-3.445-3.188,3.307,3.307,0,0,1,3.617-3.293,9.764,9.764,0,0,1,1.8.22c.038.43.105,1,.181,1.48l-.3.067a1.646,1.646,0,0,0-.907-1.231,2.611,2.611,0,0,0-1-.181c-1.575,0-2.4,1.164-2.4,2.739,0,1.842.954,3.025,2.5,3.025.974,0,1.451-.448,1.937-1.4Z" transform="translate(-275.796 -153.157)" fill="#ba2a21"/>
      <path id="Path_69" data-name="Path 69" d="M310.273,155.059a3.635,3.635,0,0,0-.305-.792c-.153-.257-.248-.325-.773-.325h-.954v4.648c0,.744.076.849.983.907v.267h-2.777V159.5c.887-.057.964-.153.964-.907v-4.648h-.868c-.525,0-.678.058-.84.344a4.348,4.348,0,0,0-.315.773H305.1c.057-.582.115-1.183.143-1.718h.219c.143.229.248.22.515.22h3.779a.479.479,0,0,0,.487-.22h.229c0,.449.038,1.136.086,1.689Z" transform="translate(-295.145 -152.902)" fill="#ba2a21"/>
      <path id="Path_70" data-name="Path 70" d="M341.04,154.215a2.7,2.7,0,0,1,1.613.41,1.613,1.613,0,0,1,.63,1.355,1.977,1.977,0,0,1-2.1,1.956,1.719,1.719,0,0,1-.258-.019l-.658-.172v1.489c0,.8.067.821.945.916v.267h-2.634v-.267c.821-.086.859-.143.859-.935v-3.789c0-.85-.1-.868-.859-.945v-.267Zm-.773,3.2a1.734,1.734,0,0,0,.7.124,1.5,1.5,0,0,0-.058-3,.848.848,0,0,0-.534.115c-.076.067-.105.181-.105.439Z" transform="translate(-320.213 -153.556)" fill="#ba2a21"/>
      <path id="Path_71" data-name="Path 71" d="M362.052,165.995h-2.2v-.267c.6-.057.659-.1.659-.725v-1.985c0-.63-.038-.658-.592-.725v-.238a5.3,5.3,0,0,0,1.346-.391v1.05c.334-.5.735-1.021,1.212-1.021a.493.493,0,0,1,.553.458.64.64,0,0,1-.325.525.188.188,0,0,1-.248-.019.6.6,0,0,0-.43-.239c-.2,0-.554.3-.763.782v1.795c0,.63.048.677.783.735Z" transform="translate(-336.147 -159.133)" fill="#ba2a21"/>
      <path id="Path_72" data-name="Path 72" d="M375.961,161.775a2.052,2.052,0,0,1,2.08,2.138,2.139,2.139,0,0,1-2.08,2.281,2.1,2.1,0,0,1-2.119-2.128A2.186,2.186,0,0,1,375.961,161.775Zm-.115.325c-.582,0-1.107.563-1.107,1.67,0,1.212.534,2.09,1.317,2.09.592,0,1.088-.43,1.088-1.737C377.144,163.006,376.686,162.1,375.846,162.1Z" transform="translate(-346.619 -159.217)" fill="#ba2a21"/>
      <path id="Path_73" data-name="Path 73" d="M393.443,154.265v-.257a2.454,2.454,0,0,1,.84-1.909,1.98,1.98,0,0,1,1.069-.506,1.069,1.069,0,0,1,.773.305.306.306,0,0,1,.057.334.758.758,0,0,1-.229.305.216.216,0,0,1-.277-.019,1.235,1.235,0,0,0-.773-.391c-.706,0-.706.964-.706,1.689v.448h1.012a.363.363,0,0,1-.1.391H394.2v2.758c0,.687.105.7.821.773v.267h-2.243v-.267c.6-.057.668-.1.668-.716v-2.816h-.677l-.038-.1.21-.286Z" transform="translate(-360.76 -151.593)" fill="#ba2a21"/>
      <path id="Path_74" data-name="Path 74" d="M407.544,165.268a2.174,2.174,0,0,1-1.584.926,1.906,1.906,0,0,1-1.871-2.052,2.451,2.451,0,0,1,.62-1.67,1.959,1.959,0,0,1,1.422-.7,1.489,1.489,0,0,1,1.46,1.48c-.01.191-.038.277-.191.315-.191.038-1.412.124-2.548.162-.029,1.279.754,1.8,1.432,1.8a1.614,1.614,0,0,0,1.107-.487Zm-1.642-3.13c-.439,0-.869.41-1,1.183.535,0,1.069,0,1.632-.029.172,0,.229-.047.229-.191A.887.887,0,0,0,405.9,162.138Z" transform="translate(-369.268 -159.217)" fill="#ba2a21"/>
      <path id="Path_75" data-name="Path 75" d="M423.033,163.025c-.172-.554-.42-.935-1-.935a.579.579,0,0,0-.591.62c0,.41.277.6.811.85.821.391,1.231.677,1.231,1.307a1.421,1.421,0,0,1-1.556,1.327,2.494,2.494,0,0,1-1.136-.267c-.048-.277-.134-.8-.172-1.117l.277-.047c.172.611.506,1.107,1.174,1.107a.636.636,0,0,0,.668-.649c0-.429-.258-.649-.763-.9-.668-.325-1.212-.63-1.212-1.317a1.323,1.323,0,0,1,1.489-1.231,4.916,4.916,0,0,1,.916.124c.029.229.1.83.143,1.06Z" transform="translate(-381.646 -159.217)" fill="#ba2a21"/>
      <path id="Path_76" data-name="Path 76" d="M437.207,163.025c-.172-.554-.42-.935-1-.935a.579.579,0,0,0-.592.62c0,.41.277.6.811.85.821.391,1.231.677,1.231,1.307a1.421,1.421,0,0,1-1.556,1.327,2.5,2.5,0,0,1-1.136-.267c-.047-.277-.133-.8-.172-1.117l.277-.047c.172.611.506,1.107,1.174,1.107a.636.636,0,0,0,.668-.649c0-.429-.257-.649-.763-.9-.668-.325-1.212-.63-1.212-1.317a1.323,1.323,0,0,1,1.489-1.231,4.909,4.909,0,0,1,.916.124c.029.229.1.83.143,1.06Z" transform="translate(-392.26 -159.217)" fill="#ba2a21"/>
      <path id="Path_77" data-name="Path 77" d="M448.624,160.162V159.9c.6-.057.668-.1.668-.754v-1.966c0-.592-.028-.629-.6-.725v-.229a6.239,6.239,0,0,0,1.355-.382v3.3c0,.649.067.7.687.754v.267Zm.992-5.259a.522.522,0,0,1-.506-.506.516.516,0,0,1,.515-.525.5.5,0,0,1,.487.525A.507.507,0,0,1,449.616,154.9Z" transform="translate(-402.617 -153.3)" fill="#ba2a21"/>
      <path id="Path_78" data-name="Path 78" d="M461.117,161.775a2.052,2.052,0,0,1,2.081,2.138,2.139,2.139,0,0,1-2.081,2.281A2.1,2.1,0,0,1,459,164.066,2.186,2.186,0,0,1,461.117,161.775ZM461,162.1c-.582,0-1.107.563-1.107,1.67,0,1.212.534,2.09,1.317,2.09.592,0,1.088-.43,1.088-1.737C462.3,163.006,461.842,162.1,461,162.1Z" transform="translate(-410.385 -159.217)" fill="#ba2a21"/>
      <path id="Path_79" data-name="Path 79" d="M480.87,165.995v-.267c.554-.057.611-.124.611-.773V163.38c0-.668-.239-1.117-.868-1.117a1.668,1.668,0,0,0-1.06.5v2.233c0,.658.057.677.62.735v.267h-2.062v-.267c.63-.067.687-.1.687-.735v-1.966c0-.62-.067-.649-.582-.735v-.248a5.4,5.4,0,0,0,1.336-.382v.754c.191-.134.4-.277.649-.439a1.463,1.463,0,0,1,.792-.286c.763,0,1.241.563,1.241,1.489v1.813c0,.649.057.677.611.735v.267Z" transform="translate(-424.697 -159.133)" fill="#ba2a21"/>
      <path id="Path_80" data-name="Path 80" d="M502.493,166.194a.7.7,0,0,1-.449-.181.9.9,0,0,1-.229-.43,3,3,0,0,1-1.145.611,1.171,1.171,0,0,1-1.164-1.174.972.972,0,0,1,.782-.954,7.515,7.515,0,0,0,1.508-.62v-.191c0-.678-.344-1.069-.84-1.069a.554.554,0,0,0-.449.22,2.041,2.041,0,0,0-.257.62.266.266,0,0,1-.277.22.459.459,0,0,1-.42-.42c0-.134.124-.248.315-.382a4.012,4.012,0,0,1,1.374-.668,1.255,1.255,0,0,1,1.288,1.365v1.842c0,.439.162.573.334.573a.755.755,0,0,0,.344-.1l.1.268Zm-.7-2.414c-.21.105-.668.305-.888.4-.363.162-.582.344-.582.7a.694.694,0,0,0,.687.735,1.268,1.268,0,0,0,.783-.344Z" transform="translate(-440.717 -159.217)" fill="#ba2a21"/>
      <path id="Path_81" data-name="Path 81" d="M515.655,158.455v-.267c.611-.057.687-.1.687-.735v-4.572c0-.582-.048-.639-.659-.716v-.248a6.755,6.755,0,0,0,1.413-.325v5.86c0,.63.058.677.678.735v.267Z" transform="translate(-452.81 -151.593)" fill="#ba2a21"/>
    </g>
  </g>
  <g id="Group_12" data-name="Group 12" transform="translate(62.449 1.165)">
    <path id="Path_82" data-name="Path 82" d="M361.071,29.519c.046.137.275.152.443.183a7.209,7.209,0,0,1,3.917,2.317c-.869,1.265-1.737,2.546-2.576,3.826.046.137-.046.2-.152.214-.275-.076-.488-.442-.762-.625a6.531,6.531,0,0,0-6.859-1.128c-3.033,1.144-5.274,4.359-6.113,7.287a.586.586,0,0,1-.031-.5c.168-.365.031-.823.244-1.173a14.67,14.67,0,0,1,4.3-7.8,9.692,9.692,0,0,1,4.924-2.5v-.092Z" transform="translate(-326.638 -29.519)" fill="#ba2a21"/>
    <path id="Path_83" data-name="Path 83" d="M397.97,55.687c4.07,3.429,6.448,8.185,5.259,13.505A7.96,7.96,0,0,1,397.467,75c-.686-1.4-1.28-2.82-1.92-4.238a.217.217,0,0,0-.183-.091c.122-.274.5-.351.808-.412a7.115,7.115,0,0,0,4.588-6.051,11.171,11.171,0,0,0-3.048-8.582v-.107C397.864,55.458,397.833,55.672,397.97,55.687Z" transform="translate(-361.434 -48.98)" fill="#50468e"/>
    <path id="Path_84" data-name="Path 84" d="M487.84,59.98a1.505,1.505,0,0,0,.839.213,1.311,1.311,0,0,0,.442-.93c.168-.168.473-.091.686-.075l.061.075c.091,2.165.2,4.222.29,6.388-.168.122-.518.045-.732.06-.67-2.317-2.057-5.472-4.923-5.762a3.131,3.131,0,0,0-3.216,1.54,2.23,2.23,0,0,0,.366,2.226c1.265,1.234,3.048,1.463,4.588,2.164a8.84,8.84,0,0,1,4.192,2.53,6.736,6.736,0,0,1,.732,4.207,5.573,5.573,0,0,1-3.887,4.664,6.742,6.742,0,0,1-3.613-.152c-1.082-.168-1.921-.915-2.942-1.22-.3-.107-.473.2-.641.381a3.574,3.574,0,0,0-.335.9l-.625.03-.061-.091c-.2-2.485-.122-5.091-.32-7.591.137-.168.442-.062.655-.092.275.611.351,1.3.656,1.89.93,2.256,2.866,5.061,5.625,5.2a3.78,3.78,0,0,0,3.094-2.287,2.312,2.312,0,0,0-1.127-2.484c-2.7-1.555-6.433-1.677-7.987-4.985a6.735,6.735,0,0,1-.107-4.6,4.851,4.851,0,0,1,3.735-3.14,6.238,6.238,0,0,1,2.957.228A8.828,8.828,0,0,1,487.84,59.98Z" transform="translate(-423.867 -51.551)" fill="#50468e"/>
    <path id="Path_85" data-name="Path 85" d="M275.027,59.225c.167,2.332.183,4.725.335,7.057-.183.167-.564.06-.839.091-.259-.214-.137-.64-.351-.9A6.674,6.674,0,0,0,270.1,60.7a11.437,11.437,0,0,0-3.414-.5l-.107.122V67.5a1.255,1.255,0,0,0,.137.167,3.445,3.445,0,0,0,3.506-1.828A5.489,5.489,0,0,0,270.607,64c.2-.168.594-.091.868-.061l.076.122-.045,7.926c-.229.122-.564.045-.839.076-.183-.426-.122-1.036-.381-1.478a3.317,3.317,0,0,0-2.5-1.89,2.294,2.294,0,0,0-1.2.03l.046,6.494a1.014,1.014,0,0,0,.457.883,5.8,5.8,0,0,0,1.8.183,2.291,2.291,0,0,1,.031.915l-.229.061h-8.4v-.976c.777-.092,1.844.168,2.164-.716l.122-.686-.03-13.841a.951.951,0,0,0-.5-.656,10.9,10.9,0,0,0-1.753-.213v-.944l.122-.03Z" transform="translate(-260.287 -51.741)" fill="#50468e"/>
    <path id="Path_86" data-name="Path 86" d="M445.311,59.923c.168.228.061.609.092.915l-.092.091c-.626.061-1.387-.183-1.753.412V76.354c.29.685,1.1.655,1.783.641a2.177,2.177,0,0,1,.062.838l-.123.122-6.706-.03a1.611,1.611,0,0,1,0-.93c.64-.092,1.554.122,1.784-.671V61.34c-.3-.656-1.128-.32-1.722-.412a1.064,1.064,0,0,1-.031-1.005Z" transform="translate(-393.73 -52.286)" fill="#50468e"/>
    <path id="Path_87" data-name="Path 87" d="M333.731,76.738a7.4,7.4,0,0,0,1.037,6.295c2.469,3.156,6.357,4.009,10.213,3.262.076-.046.183-.137.29-.061h.031c-3.613,1.753-8.994,1.92-12.454-.321a8.427,8.427,0,0,1-4.1-8.932c.152-.214-.107-.732.335-.686C330.652,76.494,332.161,76.539,333.731,76.738Z" transform="translate(-311.431 -64.544)" fill="#50468e"/>
  </g>
</svg>
